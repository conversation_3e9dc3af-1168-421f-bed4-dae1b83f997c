// Script pour gérer l'affichage des détails du produit sélectionné
$(document).ready(function() {
    console.log('Script de gestion des détails du produit chargé');
    
    // Variable globale pour stocker les détails de la ville sélectionnée
    window.selectedCityDetails = null;
    
    // Fonction pour mettre à jour les détails du produit sélectionné
    function updateProductDetails(supplyId) {
        console.log('Mise à jour des détails du produit:', supplyId);
        
        // Récupérer la carte sélectionnée
        var selectedCard = $('#supply-card-' + supplyId);
        if (!selectedCard.length) {
            console.error('Carte non trouvée pour l\'ID:', supplyId);
            return;
        }
        
        // Récupérer les détails de la ville
        var cityDetailsElement = selectedCard.find('.city-details');
        if (!cityDetailsElement.length) {
            console.error('Détails de la ville non trouvés dans la carte');
            return;
        }
        
        // Stocker les détails de la ville dans une variable globale
        window.selectedCityDetails = {
            cityName: cityDetailsElement.data('city-name'),
            price: parseFloat(cityDetailsElement.data('price')),
            totalQuantity: parseFloat(cityDetailsElement.data('total-quantity')),
            remainingQuantity: parseFloat(cityDetailsElement.data('remaining-quantity')),
            usedQuantity: parseFloat(cityDetailsElement.data('used-quantity')),
            vehicleInfo: cityDetailsElement.data('vehicle-info'),
            vehicleCapacity: parseFloat(cityDetailsElement.data('vehicle-capacity')),
            vehicleId: cityDetailsElement.data('vehicle-id'),
            driverId: cityDetailsElement.data('driver-id')
        };
        
        console.log('Détails de la ville récupérés:', selectedCityDetails);
        
        // Mettre à jour les champs cachés
        $('#hiddenVehicleId').val(selectedCityDetails.vehicleId);
        $('#hiddenDriverId').val(selectedCityDetails.driverId);
        
        // Mettre à jour l'affichage des détails du produit
        $('#selectedProductInfo').html(`
            <div class="alert alert-success mb-4">
                <h5 class="alert-heading">
                    <i class="fas fa-check-circle me-2"></i>
                    Produit sélectionné
                </h5>
                <p class="mb-0">Vous avez sélectionné la destination: <strong>${selectedCityDetails.cityName}</strong></p>
            </div>
        `);
        
        // Mettre à jour les détails de la ville
        $('#selectedCityName').text(window.selectedCityDetails.cityName);
        $('#selectedCityPrice').text(formatNumber(window.selectedCityDetails.price));
        $('#selectedCityQuantity').text(formatNumber(window.selectedCityDetails.remainingQuantity) + ' tonnes');
        $('#selectedVehicleInfo').text(window.selectedCityDetails.vehicleInfo);
        $('#selectedVehicleCapacity').text(formatNumber(window.selectedCityDetails.vehicleCapacity));
        
        // Appeler la fonction de mise à jour des calculs si elle existe
        if (typeof window.updateCalculations === 'function') {
            window.updateCalculations();
        }
    }
    
    // Fonction pour formater les nombres
    function formatNumber(number) {
        return number.toLocaleString('fr-FR');
    }
    
    // Écouter les changements de sélection de produit
    $('.supply-radio').on('change', function() {
        var supplyId = $(this).val();
        updateProductDetails(supplyId);
    });
    
    // Exposer la fonction globalement pour qu'elle soit accessible depuis d'autres scripts
    window.updateProductDetails = updateProductDetails;
});
