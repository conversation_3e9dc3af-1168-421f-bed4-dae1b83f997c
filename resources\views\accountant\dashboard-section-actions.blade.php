<!-- Section des actions rapides et widgets d'information -->
<div class="animated-divider mb-4"></div>

<h3 class="section-title mb-4 fade-in">
    <i class="fas fa-bolt me-2"></i>Actions rapides
</h3>

<div class="row mb-4">
    <!-- Actions rapides -->
    <div class="col-lg-8">
        <div class="dashboard-card slide-in-up" style="--delay: 0.3s">
            <div class="dashboard-card-header">
                <div class="dashboard-card-title">
                    <i class="fas fa-tasks me-2"></i>Accès rapides
                </div>
            </div>
            <div class="dashboard-card-body">
                <div class="row">
                    <div class="col-xl-3 col-md-6 mb-3">
                        <a href="{{ route('accountant.payments.create') }}" class="quick-action-card hover-lift">
                            <div class="quick-action-icon success">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="quick-action-content">
                                <h4 class="quick-action-title">Nouveau paiement</h4>
                                <p class="quick-action-desc">Enregistrer un paiement reçu</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-3">
                        <a href="{{ route('accountant.invoices.create') }}" class="quick-action-card hover-lift">
                            <div class="quick-action-icon primary">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <div class="quick-action-content">
                                <h4 class="quick-action-title">Créer facture</h4>
                                <p class="quick-action-desc">Générer une nouvelle facture</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-3">
                        <a href="{{ route('accountant.reports.create') }}" class="quick-action-card hover-lift">
                            <div class="quick-action-icon info">
                                <i class="fas fa-chart-pie"></i>
                            </div>
                            <div class="quick-action-content">
                                <h4 class="quick-action-title">Nouveau rapport</h4>
                                <p class="quick-action-desc">Générer un rapport financier</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-3">
                        <a href="{{ route('accountant.supplies.create') }}" class="quick-action-card hover-lift">
                            <div class="quick-action-icon warning">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="quick-action-content">
                                <h4 class="quick-action-title">Approvisionnement</h4>
                                <p class="quick-action-desc">Enregistrer un nouvel achat</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-3">
                        <a href="{{ route('accountant.recoveries.index') }}" class="quick-action-card hover-lift">
                            <div class="quick-action-icon danger">
                                <i class="fas fa-hand-holding-usd"></i>
                            </div>
                            <div class="quick-action-content">
                                <h4 class="quick-action-title">Recouvrements</h4>
                                <p class="quick-action-desc">Gérer les recouvrements</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-3">
                        <a href="{{ route('accountant.expenses.index') }}" class="quick-action-card hover-lift">
                            <div class="quick-action-icon purple">
                                <i class="fas fa-wallet"></i>
                            </div>
                            <div class="quick-action-content">
                                <h4 class="quick-action-title">Dépenses</h4>
                                <p class="quick-action-desc">Gérer les dépenses</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-3">
                        <a href="{{ route('accountant.customers.index') }}" class="quick-action-card hover-lift">
                            <div class="quick-action-icon teal">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="quick-action-content">
                                <h4 class="quick-action-title">Clients</h4>
                                <p class="quick-action-desc">Base de données clients</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-3">
                        <a href="{{ route('accountant.exports.index') }}" class="quick-action-card hover-lift">
                            <div class="quick-action-icon indigo">
                                <i class="fas fa-file-export"></i>
                            </div>
                            <div class="quick-action-content">
                                <h4 class="quick-action-title">Exporter</h4>
                                <p class="quick-action-desc">Exporter les données</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Widgets d'information -->
    <div class="col-lg-4">
        <div class="dashboard-card slide-in-up" style="--delay: 0.4s">
            <div class="dashboard-card-header">
                <div class="dashboard-card-title">
                    <i class="fas fa-info-circle me-2"></i>Informations
                </div>
            </div>
            <div class="dashboard-card-body">
                <!-- Widget de trésorerie -->
                <div class="info-widget mb-3">
                    <div class="info-widget-icon success">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="info-widget-content">
                        <div class="info-widget-title">Trésorerie disponible</div>
                        <div class="info-widget-value">{{ number_format($cashBalance ?? 0) }} F</div>
                    </div>
                </div>
                
                <!-- Widget de factures impayées -->
                <div class="info-widget mb-3">
                    <div class="info-widget-icon danger">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <div class="info-widget-content">
                        <div class="info-widget-title">Factures impayées</div>
                        <div class="info-widget-value">{{ number_format($overdueInvoices ?? 0) }}</div>
                        <div class="info-widget-subtitle">{{ number_format($totalOverdueAmount ?? 0) }} F en attente</div>
                    </div>
                </div>
                
                <!-- Widget de budget mensuel -->
                <div class="info-widget">
                    <div class="info-widget-icon info">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="info-widget-content">
                        <div class="info-widget-title">Budget mensuel</div>
                        <div class="info-widget-subtitle">Utilisé : {{ $budgetUsedPercent ?? 0 }}%</div>
                        <div class="progress-bar-container mt-2">
                            <div class="progress-bar info" style="width: {{ $budgetUsedPercent ?? 0 }}%;"></div>
                        </div>
                        <div class="progress-text">
                            <span>{{ number_format($budgetUsed ?? 0) }} F dépensés</span>
                            <span>{{ number_format($totalBudget ?? 0) }} F total</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer du dashboard -->
<div class="dashboard-footer fade-in">
    <div class="row">
        <div class="col-md-6">
            <p class="dashboard-footer-text">
                <i class="fas fa-shield-alt me-2"></i>Dernière mise à jour : {{ now()->format('d/m/Y à H:i') }}
            </p>
        </div>
        <div class="col-md-6 text-end">
            <p class="dashboard-footer-text">
                <a href="{{ route('accountant.help') }}" class="footer-link"><i class="fas fa-question-circle me-1"></i> Aide</a>
                <span class="mx-2">|</span>
                <a href="{{ route('accountant.settings.index') }}" class="footer-link"><i class="fas fa-cog me-1"></i> Paramètres</a>
            </p>
        </div>
    </div>
</div>
