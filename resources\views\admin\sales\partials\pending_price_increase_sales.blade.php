<div class="card border-warning shadow mb-4" id="price-increase-validation-section">
    <div class="card-header bg-warning text-dark d-flex align-items-center justify-content-between">
        <span><i class="fas fa-arrow-up me-2"></i>Ventes avec augmentation de prix à valider</span>
        <span class="badge bg-dark">{{ $pendingPriceIncreaseSales->count() }} à valider</span>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover table-bordered m-0 align-middle">
                <thead class="table-warning">
                    <tr>
                        <th>#</th>
                        <th>Date</th>
                        <th>Client</th>
                        <th>Quantité</th>
                        <th>Prix original</th>
                        <th>Augmentation</th>
                        <th>Nouveau prix</th>
                        <th>Montant total</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($pendingPriceIncreaseSales as $sale)
                        <tr>
                            <td><strong>{{ $sale->id }}</strong></td>
                            <td>{{ $sale->created_at->format('d/m/Y H:i') }}</td>
                            <td>{{ $sale->customer_name }}</td>
                            <td>{{ $sale->quantity }}</td>
                            <td>{{ number_format($sale->original_price, 2, ',', ' ') }} FCFA</td>
                            <td class="text-danger fw-bold">
                                {{ number_format($sale->unit_price - $sale->original_price, 2, ',', ' ') }} FCFA
                            </td>
                            <td class="text-danger fw-bold">{{ number_format($sale->unit_price, 2, ',', ' ') }} FCFA</td>
                            <td class="fw-bold">{{ number_format($sale->total_amount, 2, ',', ' ') }} FCFA</td>
                            <td class="text-center">
                                <form action="{{ route('admin.sales.validate-discount', $sale) }}" method="POST" class="d-inline">
                                    @csrf
                                    <input type="hidden" name="action" value="approve">
                                    <button type="submit" class="btn btn-outline-success btn-sm" title="Valider">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </form>
                                <button type="button" class="btn btn-outline-danger btn-sm reject-price-increase-sale" 
                                    data-sale-id="{{ $sale->id }}"
                                    data-quantity="{{ $sale->quantity }}"
                                    data-customer="{{ $sale->customer_name }}"
                                    data-price="{{ number_format($sale->unit_price, 2, ',', ' ') }}"
                                    data-original-price="{{ number_format($sale->original_price, 2, ',', ' ') }}"
                                    title="Rejeter">
                                    <i class="fas fa-times"></i>
                                </button>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="9" class="text-center text-muted">Aucune vente avec augmentation de prix à valider</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialiser les boutons de rejet
        const rejectButtons = document.querySelectorAll('.reject-price-increase-sale');
        
        rejectButtons.forEach(button => {
            button.addEventListener('click', function() {
                const saleId = this.getAttribute('data-sale-id');
                const quantity = this.getAttribute('data-quantity');
                const customer = this.getAttribute('data-customer');
                const price = this.getAttribute('data-price');
                const originalPrice = this.getAttribute('data-original-price');
                
                Swal.fire({
                    title: 'Rejeter la vente avec augmentation de prix',
                    html: `<p>Vous êtes sur le point de rejeter une vente de <strong>${quantity} tonnes</strong> pour <strong>${customer}</strong>.</p>
                           <p>Prix original: <strong>${originalPrice} FCFA</strong> → Prix augmenté: <strong>${price} FCFA</strong></p>
                           <p>Veuillez indiquer la raison du rejet :</p>`,
                    input: 'textarea',
                    inputPlaceholder: 'Raison du rejet...',
                    inputAttributes: {
                        'aria-label': 'Raison du rejet',
                        'required': 'required'
                    },
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Rejeter la vente',
                    cancelButtonText: 'Annuler',
                    inputValidator: (value) => {
                        if (!value) {
                            return 'Vous devez indiquer une raison pour le rejet';
                        }
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Créer un formulaire dynamique pour soumettre la demande
                        const form = document.createElement('form');
                        form.method = 'POST';
                        form.action = `/admin/sales/${saleId}/validate-discount`;
                        form.style.display = 'none';
                        
                        // Token CSRF
                        const csrfToken = document.createElement('input');
                        csrfToken.type = 'hidden';
                        csrfToken.name = '_token';
                        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                        form.appendChild(csrfToken);
                        
                        // Action (rejeter)
                        const actionInput = document.createElement('input');
                        actionInput.type = 'hidden';
                        actionInput.name = 'action';
                        actionInput.value = 'reject';
                        form.appendChild(actionInput);
                        
                        // Raison du rejet
                        const reasonInput = document.createElement('input');
                        reasonInput.type = 'hidden';
                        reasonInput.name = 'admin_note';
                        reasonInput.value = result.value;
                        form.appendChild(reasonInput);
                        
                        document.body.appendChild(form);
                        form.submit();
                    }
                });
            });
        });
    });
</script>
@endpush
