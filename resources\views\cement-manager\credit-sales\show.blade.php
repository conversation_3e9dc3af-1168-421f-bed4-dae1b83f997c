@extends('layouts.cement-manager')

@section('title', 'Détails de la Vente à Crédit')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 text-gray-800">Détails de la Vente à Crédit</h1>
        <a href="{{ route('cement-manager.credit-sales.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour
        </a>
    </div>

    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informations de la Commande</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Référence:</strong> {{ $creditSale->cementOrderDetail->cementOrder->reference }}
                    </div>
                    <div class="mb-3">
                        <strong>Client:</strong> {{ $creditSale->cementOrderDetail->customer->name }}
                    </div>
                    <div class="mb-3">
                        <strong>Produit:</strong> {{ $creditSale->cementOrderDetail->product->name }}
                    </div>
                    <div class="mb-3">
                        <strong>Quantité livrée:</strong> {{ number_format($creditSale->quantity, 2) }} tonnes
                    </div>
                    <div class="mb-3">
                        <strong>Prix unitaire:</strong> {{ number_format($creditSale->unit_price, 0, ',', ' ') }} FCFA
                    </div>
                    <div class="mb-3">
                        <strong>Montant total:</strong> {{ number_format($creditSale->total_amount, 0, ',', ' ') }} FCFA
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informations de Livraison</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Chauffeur:</strong> {{ $creditSale->tripAssignment->driver->name }}
                    </div>
                    <div class="mb-3">
                        <strong>Camion:</strong> {{ $creditSale->tripAssignment->truck->registration_number }}
                    </div>
                    <div class="mb-3">
                        <strong>Date de livraison:</strong> {{ $creditSale->created_at->format('d/m/Y H:i') }}
                    </div>
                    <div class="mb-3">
                        <strong>Statut:</strong>
                        <span class="badge badge-{{ $creditSale->status === 'pending_payment' ? 'warning' : 'success' }}">
                            {{ $creditSale->status === 'pending_payment' ? 'En attente de paiement' : 'Payé' }}
                        </span>
                    </div>
                    @if($creditSale->notes)
                        <div class="mb-3">
                            <strong>Notes:</strong><br>
                            {{ $creditSale->notes }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
