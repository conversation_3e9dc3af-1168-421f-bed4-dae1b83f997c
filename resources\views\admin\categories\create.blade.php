@extends('layouts.admin_minimal')

@section('title', 'Créer une nouvelle catégorie')

@section('content')
<div class="container-fluid py-0">
    <!-- En-tête moderne et attrayant -->
    <div class="modern-header-wrapper">
        <div class="modern-header-background">
            <div class="header-gradient-overlay"></div>
            <div class="header-pattern"></div>
        </div>

        <div class="modern-header-content">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <!-- Navigation breadcrumb moderne -->
                    <nav aria-label="breadcrumb" class="modern-breadcrumb">
                        <ol class="breadcrumb-modern">
                            <li class="breadcrumb-item-modern">
                                <a href="{{ route('admin.dashboard') }}" class="breadcrumb-link">
                                    <i class="fas fa-home"></i>
                                    <span>Dashboard</span>
                                </a>
                            </li>
                            <li class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </li>
                            <li class="breadcrumb-item-modern">
                                <a href="{{ route('admin.categories.index') }}" class="breadcrumb-link">
                                    <i class="fas fa-layer-group"></i>
                                    <span>Catégories</span>
                                </a>
                            </li>
                            <li class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </li>
                            <li class="breadcrumb-item-modern active">
                                <span>Nouvelle catégorie</span>
                            </li>
                        </ol>
                    </nav>

                    <!-- Titre principal avec animation -->
                    <div class="header-title-section">
                        <div class="title-icon-wrapper">
                            <div class="title-icon-bg">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                        </div>
                        <div class="title-content">
                            <h1 class="header-title">
                                Créer une nouvelle
                                <span class="title-highlight">Catégorie</span>
                            </h1>
                            <p class="header-subtitle">
                                <i class="fas fa-info-circle me-2"></i>
                                Organisez vos produits en créant une nouvelle catégorie
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <!-- Boutons d'action -->
                    <div class="header-actions">
                        <div class="action-buttons-group">
                            <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-light btn-modern">
                                <i class="fas fa-arrow-left me-2"></i>
                                Retour aux catégories
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="main-content-wrapper">
        <div class="row">
            <!-- Formulaire principal -->
            <div class="col-lg-8">
                <form action="{{ route('admin.categories.store') }}" method="POST" id="category-form">
                    @csrf

                    <!-- Informations de base -->
                    <div class="card modern-card mb-4">
                        <div class="card-header bg-white border-0">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-edit text-primary me-2"></i>
                                Informations de base
                            </h5>
                            <p class="text-muted mb-0 small">Définissez les informations principales de votre catégorie</p>
                        </div>
                        <div class="card-body">
                            <div class="row g-4">
                                <!-- Nom de la catégorie -->
                                <div class="col-12">
                                    <div class="form-floating">
                                        <input type="text"
                                               class="form-control modern-input @error('name') is-invalid @enderror"
                                               id="categoryName"
                                               name="name"
                                               placeholder="Nom de la catégorie"
                                               value="{{ old('name') }}"
                                               required>
                                        <label for="categoryName">
                                            <i class="fas fa-tag me-2"></i>Nom de la catégorie *
                                        </label>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="form-help-text">
                                        <i class="fas fa-lightbulb text-warning me-1"></i>
                                        Le nom doit être unique et descriptif (ex: Ciment, Fer, Brique...)
                                    </div>
                                </div>

                                <!-- Slug automatique -->
                                <div class="col-12">
                                    <div class="slug-preview">
                                        <label class="form-label">
                                            <i class="fas fa-link me-2"></i>URL générée automatiquement
                                        </label>
                                        <div class="slug-display">
                                            <span class="slug-base">{{ url('/categories/') }}/</span>
                                            <span class="slug-value" id="slugPreview">nom-de-la-categorie</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Description -->
                                <div class="col-12">
                                    <div class="form-floating">
                                        <textarea class="form-control modern-textarea @error('description') is-invalid @enderror"
                                                  id="categoryDescription"
                                                  name="description"
                                                  placeholder="Description de la catégorie"
                                                  style="height: 120px;">{{ old('description') }}</textarea>
                                        <label for="categoryDescription">
                                            <i class="fas fa-align-left me-2"></i>Description (optionnel)
                                        </label>
                                        @error('description')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="form-help-text">
                                        <i class="fas fa-info-circle text-info me-1"></i>
                                        Une brève description pour aider à identifier la catégorie
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Aperçu de la catégorie -->
                    <div class="card modern-card mb-4">
                        <div class="card-header bg-white border-0">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-eye text-info me-2"></i>
                                Aperçu de la catégorie
                            </h5>
                            <p class="text-muted mb-0 small">Voici comment votre catégorie apparaîtra</p>
                        </div>
                        <div class="card-body">
                            <div class="category-preview" id="categoryPreview">
                                <div class="preview-card">
                                    <div class="preview-icon" id="previewIcon">
                                        <i class="fas fa-cube"></i>
                                    </div>
                                    <div class="preview-content">
                                        <h6 class="preview-name" id="previewName">Nom de la catégorie</h6>
                                        <p class="preview-description" id="previewDescription">Description de la catégorie</p>
                                        <div class="preview-stats">
                                            <span class="preview-badge">0 produit</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Boutons d'action -->
                    <div class="card modern-card">
                        <div class="card-body text-center">
                            <div class="action-buttons-final">
                                <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary btn-lg me-3">
                                    <i class="fas fa-times me-2"></i>
                                    Annuler
                                </a>
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-save me-2"></i>
                                    Créer la catégorie
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Panneau d'aide -->
            <div class="col-lg-4">
                <!-- Guide de création -->
                <div class="card modern-card mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0 fw-bold">
                            <i class="fas fa-question-circle text-success me-2"></i>
                            Guide de création
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="help-section">
                            <h6 class="help-title">
                                <i class="fas fa-lightbulb text-warning me-2"></i>
                                Conseils pour nommer votre catégorie
                            </h6>
                            <ul class="help-list">
                                <li>Utilisez des noms courts et descriptifs</li>
                                <li>Évitez les caractères spéciaux</li>
                                <li>Soyez cohérent avec vos autres catégories</li>
                                <li>Pensez à vos clients : le nom doit être clair</li>
                            </ul>
                        </div>

                        <div class="help-section">
                            <h6 class="help-title">
                                <i class="fas fa-info-circle text-info me-2"></i>
                                À propos des descriptions
                            </h6>
                            <ul class="help-list">
                                <li>La description est optionnelle mais recommandée</li>
                                <li>Gardez-la concise (1-2 phrases)</li>
                                <li>Expliquez le type de produits inclus</li>
                                <li>Utilisez un langage simple et clair</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Exemples de catégories -->
                <div class="card modern-card">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0 fw-bold">
                            <i class="fas fa-star text-warning me-2"></i>
                            Exemples de catégories
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="examples-list">
                            <div class="example-item">
                                <div class="example-icon">
                                    <i class="fas fa-industry"></i>
                                </div>
                                <div class="example-content">
                                    <strong>Ciment</strong>
                                    <small>Tous types de ciment pour construction</small>
                                </div>
                            </div>
                            <div class="example-item">
                                <div class="example-icon">
                                    <i class="fas fa-hammer"></i>
                                </div>
                                <div class="example-content">
                                    <strong>Fer</strong>
                                    <small>Barres de fer et acier pour béton</small>
                                </div>
                            </div>
                            <div class="example-item">
                                <div class="example-icon">
                                    <i class="fas fa-th-large"></i>
                                </div>
                                <div class="example-content">
                                    <strong>Brique</strong>
                                    <small>Briques et blocs de construction</small>
                                </div>
                            </div>
                            <div class="example-item">
                                <div class="example-icon">
                                    <i class="fas fa-mountain"></i>
                                </div>
                                <div class="example-content">
                                    <strong>Sable</strong>
                                    <small>Sable fin et gros pour maçonnerie</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* ===== STYLES POUR LA PAGE DE CRÉATION DE CATÉGORIE ===== */

/* Variables CSS */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

/* En-tête moderne */
.modern-header-wrapper {
    position: relative;
    margin: -1.5rem -1.5rem 2rem -1.5rem;
    overflow: hidden;
}

.modern-header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
}

.header-gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
}

.header-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 80% 50%, rgba(255,255,255,0.1) 2px, transparent 2px);
    background-size: 50px 50px;
}

.modern-header-content {
    position: relative;
    z-index: 2;
    padding: 2rem;
    color: white;
}

/* Breadcrumb moderne */
.modern-breadcrumb {
    margin-bottom: 1.5rem;
}

.breadcrumb-modern {
    display: flex;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
    flex-wrap: wrap;
}

.breadcrumb-item-modern {
    display: flex;
    align-items: center;
}

.breadcrumb-link {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
}

.breadcrumb-link:hover {
    color: white;
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.breadcrumb-link i {
    margin-right: 0.5rem;
}

.breadcrumb-separator {
    margin: 0 0.5rem;
    color: rgba(255,255,255,0.6);
}

.breadcrumb-item-modern.active span {
    color: white;
    font-weight: 600;
}

/* Titre principal */
.header-title-section {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.title-icon-wrapper {
    margin-right: 1.5rem;
}

.title-icon-bg {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.3);
}

.header-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
}

.title-highlight {
    color: rgba(255,255,255,0.8);
    font-weight: 400;
}

.header-subtitle {
    margin: 0.5rem 0 0 0;
    color: rgba(255,255,255,0.8);
    font-size: 1.1rem;
}

/* Boutons d'action dans l'en-tête */
.header-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 100%;
}

.action-buttons-group {
    display: flex;
    gap: 1rem;
}

.btn-modern {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-outline-light.btn-modern {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

.btn-outline-light.btn-modern:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

/* Contenu principal */
.main-content-wrapper {
    padding: 0 1.5rem;
}

/* Cartes modernes */
.modern-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.modern-card:hover {
    box-shadow: 0 20px 40px rgba(0,0,0,0.12);
}

.modern-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding: 1.5rem;
}

.modern-card .card-body {
    padding: 2rem;
}

/* Champs de formulaire modernes */
.modern-input, .modern-textarea {
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.8);
    backdrop-filter: blur(10px);
    padding: 0.75rem 1rem;
}

.modern-input:focus, .modern-textarea:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.form-floating > label {
    color: #6c757d;
    font-weight: 500;
}

/* Texte d'aide */
.form-help-text {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #6c757d;
    padding: 0.5rem 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
    border-left: 3px solid #667eea;
}

/* Aperçu du slug */
.slug-preview {
    margin-bottom: 1rem;
}

.slug-display {
    padding: 1rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 12px;
    border-left: 4px solid #667eea;
    font-family: 'Courier New', monospace;
}

.slug-base {
    color: #6c757d;
}

.slug-value {
    color: #667eea;
    font-weight: 600;
}

/* Aperçu de la catégorie */
.category-preview {
    padding: 1rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    border-radius: 12px;
}

.preview-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.preview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.preview-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 1rem;
    transition: all 0.3s ease;
}

.preview-content {
    flex: 1;
}

.preview-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.preview-description {
    color: #718096;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.preview-stats {
    display: flex;
    gap: 0.5rem;
}

.preview-badge {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Panneaux d'aide */
.help-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.help-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.help-title {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.help-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.help-list li {
    padding: 0.5rem 0;
    color: #4a5568;
    position: relative;
    padding-left: 1.5rem;
}

.help-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

/* Exemples de catégories */
.examples-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.example-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.example-item:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(5px);
}

.example-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
    font-size: 1rem;
}

.example-content {
    flex: 1;
}

.example-content strong {
    display: block;
    color: #2d3748;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.example-content small {
    color: #718096;
    font-size: 0.85rem;
}

/* Boutons d'action finaux */
.action-buttons-final {
    padding: 2rem 0;
}

.action-buttons-final .btn {
    border-radius: 25px;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.action-buttons-final .btn:hover {
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

/* Responsive */
@media (max-width: 768px) {
    .modern-header-content {
        padding: 1rem;
    }

    .header-title {
        font-size: 1.8rem;
    }

    .title-icon-bg {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .action-buttons-group {
        flex-direction: column;
        width: 100%;
    }

    .main-content-wrapper {
        padding: 0 0.5rem;
    }

    .modern-card .card-body {
        padding: 1rem;
    }

    .preview-card {
        flex-direction: column;
        text-align: center;
    }

    .preview-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modern-card {
    animation: slideInUp 0.5s ease-out;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.preview-icon {
    animation: pulse 2s infinite;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // ===== ÉLÉMENTS DU FORMULAIRE =====

    const categoryNameInput = document.getElementById('categoryName');
    const categoryDescriptionInput = document.getElementById('categoryDescription');
    const slugPreview = document.getElementById('slugPreview');
    const previewName = document.getElementById('previewName');
    const previewDescription = document.getElementById('previewDescription');
    const previewIcon = document.getElementById('previewIcon');
    const form = document.getElementById('category-form');

    // ===== GÉNÉRATION AUTOMATIQUE DU SLUG =====

    function generateSlug(text) {
        return text
            .toLowerCase()
            .trim()
            .replace(/[àáâãäå]/g, 'a')
            .replace(/[èéêë]/g, 'e')
            .replace(/[ìíîï]/g, 'i')
            .replace(/[òóôõö]/g, 'o')
            .replace(/[ùúûü]/g, 'u')
            .replace(/[ç]/g, 'c')
            .replace(/[ñ]/g, 'n')
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }

    // ===== MISE À JOUR DE L'APERÇU EN TEMPS RÉEL =====

    function updatePreview() {
        const name = categoryNameInput.value.trim();
        const description = categoryDescriptionInput.value.trim();

        // Mise à jour du nom
        if (name) {
            previewName.textContent = name;
            const slug = generateSlug(name);
            slugPreview.textContent = slug || 'nom-de-la-categorie';
        } else {
            previewName.textContent = 'Nom de la catégorie';
            slugPreview.textContent = 'nom-de-la-categorie';
        }

        // Mise à jour de la description
        if (description) {
            previewDescription.textContent = description;
            previewDescription.style.display = 'block';
        } else {
            previewDescription.textContent = 'Description de la catégorie';
            previewDescription.style.display = 'block';
        }

        // Mise à jour de l'icône selon le nom
        updatePreviewIcon(name);
    }

    // ===== MISE À JOUR DE L'ICÔNE SELON LE NOM =====

    function updatePreviewIcon(name) {
        const categoryName = name.toLowerCase();
        let iconClass = 'fas fa-cube'; // Icône par défaut
        let gradientClass = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';

        // Correspondance nom -> icône
        const iconMap = {
            'ciment': { icon: 'fas fa-industry', gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' },
            'fer': { icon: 'fas fa-hammer', gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)' },
            'brique': { icon: 'fas fa-th-large', gradient: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)' },
            'sable': { icon: 'fas fa-mountain', gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)' },
            'gravier': { icon: 'fas fa-gem', gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)' },
            'bois': { icon: 'fas fa-tree', gradient: 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)' },
            'acier': { icon: 'fas fa-cog', gradient: 'linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)' },
            'peinture': { icon: 'fas fa-paint-brush', gradient: 'linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%)' },
            'carrelage': { icon: 'fas fa-th', gradient: 'linear-gradient(135deg, #ee9ca7 0%, #ffdde1 100%)' },
            'plomberie': { icon: 'fas fa-wrench', gradient: 'linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)' },
            'électricité': { icon: 'fas fa-bolt', gradient: 'linear-gradient(135deg, #673ab7 0%, #512da8 100%)' }
        };

        // Recherche de correspondance
        for (const [key, value] of Object.entries(iconMap)) {
            if (categoryName.includes(key)) {
                iconClass = value.icon;
                gradientClass = value.gradient;
                break;
            }
        }

        // Application de l'icône et du gradient
        previewIcon.innerHTML = `<i class="${iconClass}"></i>`;
        previewIcon.style.background = gradientClass;

        // Animation de changement
        previewIcon.style.transform = 'scale(1.1)';
        setTimeout(() => {
            previewIcon.style.transform = 'scale(1)';
        }, 200);
    }

    // ===== ÉCOUTEURS D'ÉVÉNEMENTS =====

    // Mise à jour en temps réel
    categoryNameInput.addEventListener('input', updatePreview);
    categoryDescriptionInput.addEventListener('input', updatePreview);

    // Validation du formulaire
    form.addEventListener('submit', function(e) {
        const name = categoryNameInput.value.trim();

        if (!name) {
            e.preventDefault();
            alert('Veuillez saisir un nom pour la catégorie.');
            categoryNameInput.focus();
            return;
        }

        if (name.length < 2) {
            e.preventDefault();
            alert('Le nom de la catégorie doit contenir au moins 2 caractères.');
            categoryNameInput.focus();
            return;
        }

        // Animation de soumission
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Création en cours...';
            submitBtn.disabled = true;
        }
    });

    // ===== ANIMATIONS AU CHARGEMENT =====

    // Animation des cartes
    const cards = document.querySelectorAll('.modern-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Effet de focus sur les champs
    const inputs = document.querySelectorAll('.modern-input, .modern-textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.closest('.form-floating')?.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.closest('.form-floating')?.classList.remove('focused');
        });
    });

    // Initialisation de l'aperçu
    updatePreview();

    // ===== EFFETS VISUELS SUPPLÉMENTAIRES =====

    // Effet de parallaxe sur l'en-tête
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const headerBackground = document.querySelector('.modern-header-background');
        if (headerBackground) {
            headerBackground.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });

    // Animation des exemples au hover
    const exampleItems = document.querySelectorAll('.example-item');
    exampleItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.example-icon');
            icon.style.transform = 'scale(1.1) rotate(5deg)';
        });

        item.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.example-icon');
            icon.style.transform = 'scale(1) rotate(0deg)';
        });
    });
});

// Styles CSS supplémentaires pour les effets
const style = document.createElement('style');
style.textContent = `
    .focused {
        transform: translateY(-2px);
        transition: transform 0.3s ease;
    }

    .example-icon {
        transition: transform 0.3s ease;
    }
`;
document.head.appendChild(style);
</script>
@endpush
