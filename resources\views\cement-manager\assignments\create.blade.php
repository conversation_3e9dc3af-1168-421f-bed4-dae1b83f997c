@extends('layouts.cement-manager')

@section('title', 'Affecter des véhicules')

@push('styles')
<style>
    .product-card {
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }

    .trip-section {
        background-color: #f8f9fc;
        border-radius: 0.35rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border: 1px solid #e3e6f0;
    }

    .existing-assignment {
        background-color: #fff;
        padding: 1rem;
        border-radius: 0.35rem;
        border: 1px solid #4e73df;
        margin-bottom: 1rem;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .info-item {
        padding: 0.5rem;
        background-color: #fff;
        border-radius: 0.25rem;
        border: 1px solid #e3e6f0;
    }

    .info-item i {
        width: 20px;
        color: #4e73df;
        margin-right: 0.5rem;
    }

    .product-header {
        background-color: #4e73df;
        color: white;
        padding: 1rem;
        border-radius: 0.35rem;
        margin-bottom: 1rem;
    }
</style>
@endpush

@section('content')
<div class="container-fluid mt-4">
    <!-- En-tête -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Affecter des véhicules</h1>
        <a href="{{ route('cement-manager.dashboard') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour aux bons de commande
        </a>
    </div>

    <!-- Carte principale -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Bon de commande #{{ $order->reference }}</h6>
            <span class="badge bg-{{ $order->status === 'completed' ? 'success' : 'primary' }}">
                {{ ucfirst($order->status) }}
            </span>
        </div>
        <div class="card-body">
            @foreach($order->details as $detail)
                <div class="product-card">
                    <!-- En-tête du produit -->
                    <div class="product-header">
                        <h5 class="m-0">{{ $detail->product->name ?? 'Produit non défini' }}</h5>
                    </div>

                    <!-- Informations principales -->
                    <div class="info-grid">
                        <div class="info-item">
                            <i class="fas fa-box"></i>
                            <strong>Quantité totale:</strong> {{ number_format($detail->quantity, 2) }} T
                        </div>
                        <div class="info-item">
                            <i class="fas fa-check"></i>
                            <strong>Déjà livré:</strong> {{ number_format($detail->delivered_quantity, 2) }} T
                        </div>
                        <div class="info-item">
                            <i class="fas fa-clock"></i>
                            <strong>Restant:</strong> {{ number_format($detail->remaining_quantity, 2) }} T
                        </div>
                    </div>

                    <!-- Destination et client -->
                    <div class="info-grid mb-4">
                        <div class="info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <strong>Ville:</strong> {{ $detail->city?->name ?? 'N/A' }}
                        </div>
                        <div class="info-item">
                            <i class="fas fa-user"></i>
                            <strong>Client:</strong> {{ $detail->customer?->name ?? 'N/A' }}
                        </div>
                    </div>

                    <!-- Section des voyages -->
                    @for($trip = 1; $trip <= $detail->trips_count; $trip++)
                        <div class="trip-section">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0 font-weight-bold text-primary">Voyage #{{ $trip }}</h6>
                                <div class="text-primary">
                                    <i class="fas fa-truck"></i> Par voyage: {{ number_format($detail->tonnage_per_trip, 2) }} T
                                </div>
                            </div>
                            @php
                                $existingTrip = $existingAssignments
                                    ->where('cement_order_detail_id', $detail->id)
                                    ->filter(function($assignment) use ($trip) {
                                        return $assignment->trip_number === $trip;
                                    })
                                    ->first();
                            @endphp

                            @if($existingTrip)
                                <div class="alert alert-info">
                                    <strong>Affectation existante:</strong><br>
                                    Chauffeur: {{ $existingTrip->driver->full_name }}<br>
                                    Camion: {{ $existingTrip->truck->registration_number }}<br>
                                    Tonnage: {{ number_format($existingTrip->tonnage, 2) }} T<br>
                                    Statut: {{ ucfirst($existingTrip->status) }}
                                </div>
                            @else
                                <form action="{{ route('cement-manager.assignments.store', ['order' => $order->id]) }}" 
                                    method="POST" 
                                    class="assignment-form">
                                    @csrf
                                    <input type="hidden" name="cement_order_detail_id" value="{{ $detail->id }}">
                                    <input type="hidden" name="trip_number" value="{{ $trip }}">

                                    @if($errors->any())
                                        <div class="alert alert-danger mb-3">
                                            <ul class="mb-0">
                                                @foreach($errors->all() as $error)
                                                    <li>{{ $error }}</li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    @endif

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="truck_id_{{ $detail->id }}_{{ $trip }}" class="form-label">Camion</label>
                                            <select name="truck_id" 
                                                id="truck_id_{{ $detail->id }}_{{ $trip }}" 
                                                class="form-select @error('truck_id') is-invalid @enderror" 
                                                required>
                                                <option value="">Sélectionner un camion</option>
                                                @foreach($trucks as $truck)
                                                    <option value="{{ $truck->id }}" 
                                                        data-capacity="{{ optional($truck->capacity)->tonnage }}"
                                                        {{ old('truck_id') == $truck->id ? 'selected' : '' }}>
                                                        {{ $truck->registration_number }} ({{ optional($truck->capacity)->tonnage ?? 'N/A' }} T)
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('truck_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="driver_id_{{ $detail->id }}_{{ $trip }}" class="form-label">Chauffeur</label>
                                            <select name="driver_id" 
                                                id="driver_id_{{ $detail->id }}_{{ $trip }}" 
                                                class="form-select @error('driver_id') is-invalid @enderror" 
                                                required>
                                                <option value="">Sélectionner un chauffeur</option>
                                                @foreach($drivers as $driver)
                                                    <option value="{{ $driver->id }}"
                                                        {{ old('driver_id') == $driver->id ? 'selected' : '' }}>
                                                        {{ $driver->full_name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('driver_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="start_date_{{ $detail->id }}_{{ $trip }}" class="form-label">Date de début</label>
                                            <input type="datetime-local" 
                                                name="start_date" 
                                                id="start_date_{{ $detail->id }}_{{ $trip }}" 
                                                class="form-control @error('start_date') is-invalid @enderror"
                                                value="{{ old('start_date') }}"
                                                required>
                                            @error('start_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="end_date_{{ $detail->id }}_{{ $trip }}" class="form-label">Date de fin</label>
                                            <input type="datetime-local" 
                                                name="end_date" 
                                                id="end_date_{{ $detail->id }}_{{ $trip }}" 
                                                class="form-control @error('end_date') is-invalid @enderror"
                                                value="{{ old('end_date') }}"
                                                required>
                                            @error('end_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="notes_{{ $detail->id }}_{{ $trip }}" class="form-label">Notes</label>
                                        <textarea 
                                            name="notes" 
                                            id="notes_{{ $detail->id }}_{{ $trip }}" 
                                            class="form-control @error('notes') is-invalid @enderror"
                                            rows="2">{{ old('notes') }}</textarea>
                                        @error('notes')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="text-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Enregistrer l'affectation
                                        </button>
                                    </div>
                                </form>
                            @endif
                        </div>
                    @endfor
                </div>
            @endforeach
        </div>
    </div>
</div>
@endsection
