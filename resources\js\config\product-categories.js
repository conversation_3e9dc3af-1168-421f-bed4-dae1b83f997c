export const PRODUCT_CATEGORIES = {
    ciment: {
        name: 'C<PERSON>',
        units: [
            { value: 'tonne', label: 'Tonne', default: true },
            { value: 'kg', label: 'Kilogramme' }
        ],
        features: {
            requiresRegionalPricing: true,
            requiresBasePrice: true,
            showStock: true
        },
        validation: {
            requireAtLeastOnePrice: true
        }
    },
    fer: {
        name: 'Fer',
        units: [
            { value: 'barre', label: 'Barre', default: true },
            { value: 'tonne', label: 'Tonne' }
        ],
        specifications: {
            diameters: [
                { value: 6, label: '6 mm', unitsPerTon: 750, weightPerMeter: 0.222 },
                { value: 8, label: '8 mm', unitsPerTon: 422, weightPerMeter: 0.395 },
                { value: 10, label: '10 mm', unitsPerTon: 270, weightPerMeter: 0.617 },
                { value: 12, label: '12 mm', unitsPerTon: 188, weightPerMeter: 0.888 },
                { value: 14, label: '14 mm', unitsPerTon: 138, weightPerMeter: 1.208 },
                { value: 16, label: '16 mm', unitsPerTon: 106, weightPerMeter: 1.578 }
            ],
            defaultLength: 12
        },
        features: {
            requiresSpecifications: true,
            requiresBasePrice: false,
            showStock: true
        }
    },
    brique: {
        name: 'Brique',
        units: [
            { value: 'piece', label: 'Pièce', default: true },
            { value: 'palette', label: 'Palette' }
        ],
        features: {
            requiresBasePrice: true,
            showStock: true
        }
    },
    sable: {
        name: 'Sable',
        units: [
            { value: 'm3', label: 'Mètre cube', default: true },
            { value: 'tonne', label: 'Tonne' }
        ],
        features: {
            requiresBasePrice: true,
            showStock: true
        }
    },
    gravier: {
        name: 'Gravier',
        units: [
            { value: 'm3', label: 'Mètre cube', default: true },
            { value: 'tonne', label: 'Tonne' }
        ],
        features: {
            requiresBasePrice: true,
            showStock: true
        }
    }
};
