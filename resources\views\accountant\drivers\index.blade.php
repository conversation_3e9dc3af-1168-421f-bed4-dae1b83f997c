@extends('layouts.accountant')
@section('content')
<div class="container-fluid">
    <!-- En-tête de la page -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-user-tie fa-sm text-primary"></i>
            Gestion des Chauffeurs
        </h1>
        <div class="btn-group">
            <a href="{{ route('accountant.trucks.index') }}" class="btn btn-info btn-icon-split mr-2">
                <span class="icon text-white-50">
                    <i class="fas fa-truck"></i>
                </span>
                <span class="text">Liste des véhicules</span>
            </a>
            <a href="{{ route('accountant.trucks.create') }}" class="btn btn-success btn-icon-split mr-2">
                <span class="icon text-white-50">
                    <i class="fas fa-plus"></i>
                </span>
                <span class="text">Ajouter un véhicule</span>
            </a>
            <a href="{{ route('accountant.drivers.create') }}" class="btn btn-primary btn-icon-split">
                <span class="icon text-white-50">
                    <i class="fas fa-plus"></i>
                </span>
                <span class="text">Nouveau Chauffeur</span>
            </a>
        </div>
    </div>

    <!-- Messages de notification -->
    @if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle mr-1"></i>
        {{ session('success') }}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    @endif

    <!-- Carte principale -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-list fa-sm mr-2"></i>
                Liste des Chauffeurs
            </h6>
            <div class="input-group" style="width: 300px;">
                <input type="text" class="form-control bg-light border-0 small" placeholder="Rechercher un chauffeur..."
                    aria-label="Search" aria-describedby="basic-addon2">
                <div class="input-group-append">
                    <button class="btn btn-primary" type="button">
                        <i class="fas fa-search fa-sm"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-bordered" width="100%" cellspacing="0">
                    <thead class="thead-light">
                        <tr>
                            <th class="text-center" style="width: 50px;">#</th>
                            <th>
                                <i class="fas fa-user mr-1 text-gray-500"></i>
                                Chauffeur
                            </th>
                            <th>
                                <i class="fas fa-phone mr-1 text-gray-500"></i>
                                Contact
                            </th>
                            <th>
                                <i class="fas fa-id-card mr-1 text-gray-500"></i>
                                Permis
                            </th>
                            <th>
                                <i class="fas fa-car mr-1 text-gray-500"></i>
                                Véhicule
                            </th>
                            <th class="text-center" style="width: 150px;">
                                <i class="fas fa-cogs mr-1 text-gray-500"></i>
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($drivers as $driver)
                        <tr>
                            <td class="text-center">{{ $loop->iteration }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle bg-primary text-white mr-2">
                                        {{ strtoupper(substr($driver->first_name, 0, 1)) }}{{ strtoupper(substr($driver->last_name, 0, 1)) }}
                                    </div>
                                    <div>
                                        <div class="font-weight-bold text-gray-800">{{ $driver->full_name }}</div>
                                        <div class="small text-gray-600">
                                            <i class="fas fa-envelope fa-sm mr-1"></i>
                                            {{ $driver->email }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="small">
                                    <i class="fas fa-phone fa-sm mr-1"></i>
                                    {{ $driver->phone }}
                                </div>
                            </td>
                            <td>
                                <div class="small">
                                    <div><strong>N°:</strong> {{ $driver->license_number }}</div>
                                    <div class="text-muted">
                                        <i class="fas fa-calendar fa-sm mr-1"></i>
                                        Expire le: {{ $driver->license_expiry->format('d/m/Y') }}
                                    </div>
                                </div>
                            </td>
                            <td>
                                @if($driver->truck)
                                    <div class="small">
                                        <div class="font-weight-bold text-gray-800">
                                            <i class="fas fa-truck fa-sm mr-1"></i>
                                            {{ $driver->truck->registration_number }}
                                        </div>
                                        <div class="text-muted">
                                            Capacité: {{ $driver->truck?->capacity?->name ?? 'Non assigné' }}
                                        </div>
                                    </div>
                                @else
                                    <span class="badge badge-warning">Non assigné</span>
                                @endif
                            </td>
                            <td class="text-center">
                                <div class="btn-group">
                                    <a href="{{ route('accountant.drivers.show', $driver) }}" 
                                       class="btn btn-info btn-sm" 
                                       title="Voir les détails"
                                       style="padding: 0.25rem 0.5rem; font-size: 0.875rem;">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('accountant.drivers.edit', $driver) }}" 
                                       class="btn btn-primary btn-sm" 
                                       title="Modifier"
                                       style="padding: 0.25rem 0.5rem; font-size: 0.875rem;">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('accountant.drivers.destroy', $driver) }}" 
                                       class="btn btn-danger btn-sm delete" 
                                       title="Supprimer"
                                       style="padding: 0.25rem 0.5rem; font-size: 0.875rem;"
                                       data-has-truck="{{ $driver->truck ? 'true' : 'false' }}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="empty-state">
                                    <i class="fas fa-user-slash fa-3x text-gray-400 mb-3"></i>
                                    <p class="text-gray-500 mb-0">Aucun chauffeur enregistré pour le moment</p>
                                    <a href="{{ route('accountant.drivers.create') }}" class="btn btn-sm btn-primary mt-3">
                                        <i class="fas fa-plus mr-1"></i>
                                        Ajouter un chauffeur
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- CSRF Token -->
<meta name="csrf-token" content="{{ csrf_token() }}">

@push('styles')
<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        background-color: #4e73df;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
    }

    .table td {
        vertical-align: middle !important;
    }

    .badge {
        font-size: 85%;
        padding: 0.4em 0.8em;
    }

    .btn-group .btn {
        margin: 0 2px;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>
<script src="{{ asset('backend/assets/js/code.js') }}"></script>
<script>
    $(document).ready(function() {
        // Confirmation de suppression
        $('.delete').on('click', function(e) {
            e.preventDefault();
            var driverId = $(this).attr('href').split('/').pop();
            var hasTruck = $(this).data('has-truck');

            Swal.fire({
                title: 'Êtes-vous sûr?',
                text: "Vous ne pourrez pas annuler cette action!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Oui, supprimer!',
                cancelButtonText: 'Annuler'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: '/accountant/drivers/' + driverId,
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        data: {
                            _token: $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Succès!',
                                    text: response.message,
                                    timer: 1500,
                                    showConfirmButton: false
                                });
                                location.reload();
                            }
                        },
                        error: function(xhr) {
                            Swal.fire({
                                icon: 'error',
                                title: 'Erreur!',
                                text: xhr.responseJSON?.message || 'Une erreur est survenue lors de la suppression.',
                            });
                        }
                    });
                }
            });
        });
    });
</script>
@endpush

@endsection
