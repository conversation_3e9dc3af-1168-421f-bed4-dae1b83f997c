@extends('layouts.accountant')

@section('title', 'Commandes terminées')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="card-title mb-0">Commandes terminées</h4>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-centered table-hover">
                            <thead>
                                <tr>
                                    <th>N° Commande</th>
                                    <th>Client</th>
                                    <th>Date</th>
                                    <th>Montant</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($orders as $order)
                                    <tr>
                                        <td>{{ $order->reference }}</td>
                                        <td>{{ $order->user->name }}</td>
                                        <td>{{ $order->created_at->format('d/m/Y H:i') }}</td>
                                        <td class="currency">{{ number_format($order->total_amount, 0, ',', ' ') }}</td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#orderModal{{ $order->id }}">
                                                <i data-feather="eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="text-center">Aucune commande terminée</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $orders->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@foreach($orders as $order)
    <!-- Modal pour les détails de la commande -->
    <div class="modal fade" id="orderModal{{ $order->id }}" tabindex="-1" aria-labelledby="orderModalLabel{{ $order->id }}" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="orderModalLabel{{ $order->id }}">Détails de la commande #{{ $order->reference }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>Informations client</h6>
                            <p class="mb-1">Nom: {{ $order->user->name }}</p>
                            <p class="mb-1">Email: {{ $order->user->email }}</p>
                            <p class="mb-1">Téléphone: {{ $order->user->phone }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Informations commande</h6>
                            <p class="mb-1">Date: {{ $order->created_at->format('d/m/Y H:i') }}</p>
                            <p class="mb-1">Status: <span class="badge bg-success">Terminée</span></p>
                        </div>
                    </div>

                    <h6>Produits commandés</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Quantité</th>
                                    <th>Prix unitaire</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($order->items as $item)
                                    <tr>
                                        <td>{{ $item->product->name }}</td>
                                        <td>{{ $item->quantity }}</td>
                                        <td class="currency">{{ number_format($item->unit_price, 0, ',', ' ') }}</td>
                                        <td class="currency">{{ number_format($item->total_price, 0, ',', ' ') }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="3" class="text-end"><strong>Total</strong></td>
                                    <td class="currency"><strong>{{ number_format($order->total_amount, 0, ',', ' ') }}</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>
@endforeach

@push('scripts')
<script>
    // Initialiser les icônes Feather
    feather.replace();

    // Formatter les montants en FCFA
    document.querySelectorAll('.currency').forEach(element => {
        const amount = parseFloat(element.textContent.replace(/[^\d.-]/g, ''));
        element.textContent = new Intl.NumberFormat('fr-FR').format(amount) + ' FCFA';
    });
</script>
@endpush
