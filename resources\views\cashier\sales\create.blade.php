@extends('layouts.cashier')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Nouvelle Vente</h1>
        <a href="{{ route('cashier.sales.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <form action="{{ route('cashier.sales.store') }}" method="POST" id="saleForm">
                @csrf
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label class="form-label">Produits</label>
                            <div id="productList">
                                <!-- Les produits seront ajoutés ici dynamiquement -->
                            </div>
                            <button type="button" class="btn btn-secondary mt-2" id="addProduct">
                                <i class="fas fa-plus"></i> Ajouter un produit
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Résumé</h5>
                                <div class="mb-3">
                                    <strong>Total:</strong>
                                    <span id="totalAmount">0 FCFA</span>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-save"></i> Enregistrer la vente
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    const products = @json($products);
    let productCount = 0;

    function createProductSelect() {
        const select = document.createElement('select');
        select.className = 'form-select product-select';
        select.name = `products[${productCount}][id]`;
        select.required = true;

        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = 'Sélectionner un produit';
        select.appendChild(defaultOption);

        products.forEach(product => {
            const option = document.createElement('option');
            option.value = product.id;
            option.textContent = `${product.name} (Stock: ${product.stock_quantity})`;
            option.dataset.price = product.price;
            select.appendChild(option);
        });

        return select;
    }

    document.getElementById('addProduct').addEventListener('click', () => {
        const productDiv = document.createElement('div');
        productDiv.className = 'product-item mb-3 border p-3 position-relative';

        const row = document.createElement('div');
        row.className = 'row align-items-center';

        // Produit
        const productCol = document.createElement('div');
        productCol.className = 'col-md-6';
        const productSelect = createProductSelect();
        productCol.appendChild(productSelect);

        // Quantité
        const quantityCol = document.createElement('div');
        quantityCol.className = 'col-md-4';
        const quantityInput = document.createElement('input');
        quantityInput.type = 'number';
        quantityInput.className = 'form-control quantity-input';
        quantityInput.name = `products[${productCount}][quantity]`;
        quantityInput.min = '1';
        quantityInput.required = true;
        quantityInput.placeholder = 'Quantité';
        quantityCol.appendChild(quantityInput);

        // Bouton supprimer
        const removeCol = document.createElement('div');
        removeCol.className = 'col-md-2';
        const removeButton = document.createElement('button');
        removeButton.type = 'button';
        removeButton.className = 'btn btn-danger remove-product';
        removeButton.innerHTML = '<i class="fas fa-trash"></i>';
        removeButton.onclick = function() {
            productDiv.remove();
            updateTotal();
        };
        removeCol.appendChild(removeButton);

        row.appendChild(productCol);
        row.appendChild(quantityCol);
        row.appendChild(removeCol);
        productDiv.appendChild(row);

        document.getElementById('productList').appendChild(productDiv);
        productCount++;

        // Événements pour mettre à jour le total
        productSelect.addEventListener('change', updateTotal);
        quantityInput.addEventListener('input', updateTotal);
    });

    function updateTotal() {
        let total = 0;
        const productItems = document.querySelectorAll('.product-item');
        
        productItems.forEach(item => {
            const select = item.querySelector('.product-select');
            const quantity = item.querySelector('.quantity-input');
            
            if (select.value && quantity.value) {
                const selectedOption = select.options[select.selectedIndex];
                const price = parseFloat(selectedOption.dataset.price);
                total += price * parseInt(quantity.value);
            }
        });

        document.getElementById('totalAmount').textContent = `${total.toLocaleString()} FCFA`;
    }

    // Ajouter un premier produit au chargement
    document.getElementById('addProduct').click();
</script>
@endpush
