@extends('layouts.cement-manager')

@section('title', 'Détails du Véhicule')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card mt-5">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title m-0">Détails du Véhicule</h3>
                    <div class="card-tools">
                        <a href="{{ route('cement-manager.trucks.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour à la liste
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <h5 class="text-muted mb-2">Informations Générales</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 200px;">N° d'Immatriculation</th>
                                            <td>{{ $truck->registration_number }}</td>
                                        </tr>
                                        <tr>
                                            <th>Marque</th>
                                            <td>{{ $truck->brand }}</td>
                                        </tr>
                                        <tr>
                                            <th>Modèle</th>
                                            <td>{{ $truck->model }}</td>
                                        </tr>
                                        <tr>
                                            <th>Année</th>
                                            <td>{{ $truck->year }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-4">
                                <h5 class="text-muted mb-2">Caractéristiques</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 200px;">Capacité</th>
                                            <td>{{ $truck->capacity->description }}</td>
                                        </tr>
                                        <tr>
                                            <th>Statut</th>
                                            <td>
                                                @switch($truck->status)
                                                    @case('available')
                                                        <span class="badge bg-success">Disponible</span>
                                                        @break
                                                    @case('maintenance')
                                                        <span class="badge bg-warning">En maintenance</span>
                                                        @break
                                                    @case('busy')
                                                        <span class="badge bg-danger">Occupé</span>
                                                        @break
                                                    @default
                                                        <span class="badge bg-secondary">{{ $truck->status }}</span>
                                                @endswitch
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Notes</th>
                                            <td>{{ $truck->notes ?? 'Aucune note' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .table th {
        background-color: #f8f9fa;
    }
</style>
@endpush
