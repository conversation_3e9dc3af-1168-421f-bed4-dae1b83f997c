{{-- LOG: <PERSON><PERSON>er show.blade.php chargé --}}
@extends('layouts.cashier')

@section('styles')
<style>
    /* Variables */
    :root {
        --primary-color: #2196F3;
        --primary-light: #BBDEFB;
        --primary-dark: #1976D2;
        --success-color: #4CAF50;
        --success-light: #E8F5E9;
        --warning-color: #FF9800;
        --warning-light: #FFF3E0;
        --danger-color: #F44336;
        --danger-light: #FFEBEE;
        --info-color: #00BCD4;
        --info-light: #E0F7FA;
        --dark-color: #101828;
        --text-color: #344054;
        --border-color: #EAECF0;
        --background-color: #F9FAFB;
        --card-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    }
    
    /* Styles généraux */
    body {
        background-color: var(--background-color);
        color: var(--text-color);
    }
    
    .container-fluid {
        padding: 2rem 2.5rem;
        max-width: 1600px;
        margin: 0 auto;
        animation: fadeIn 0.5s ease-out;
    }
    
    .payment-details-page {
        background-color: var(--background-color);
        min-height: calc(100vh - 100px);
        padding: 1.5rem 0;
    }
    
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--border-color);
    }
    
    .page-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--dark-color);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin: 0;
    }
    
    .page-title i {
        color: var(--primary-color);
    }
    
    .btn-back {
        background-color: white;
        color: var(--text-color);
        border: 2px solid var(--border-color);
        padding: 0.5rem 1.25rem;
        border-radius: 50px;
        font-weight: 600;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
    }
    
    .btn-back:hover {
        background-color: var(--primary-light);
        color: var(--primary-dark);
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(33, 150, 243, 0.2);
    }
    
    /* Animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .animate-fade-in {
        animation: fadeIn 0.5s ease-out forwards;
    }
    
    .btn-back i {
        margin-right: 8px;
    }
    
    .payment-card {
        background-color: white;
        border-radius: 12px;
        box-shadow: var(--card-shadow);
        overflow: hidden;
        margin-bottom: 1.5rem;
        border: 1px solid var(--border-color);
        animation: fadeIn 0.5s ease-out;
    }
    
    .payment-card-header {
        background-color: #FCFCFD;
        color: var(--dark-color);
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid var(--border-color);
        position: relative;
    }
    
    .payment-card-header h5 {
        margin: 0;
        font-weight: 600;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--dark-color);
    }
    
    .payment-card-header h5 i {
        color: var(--primary-color);
    }
    
    .payment-card-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 6px;
        background-image: linear-gradient(45deg, transparent 33.33%, white 33.33%, white 66.66%, transparent 66.66%);
        background-size: 12px 6px;
    }
    
    .payment-card-body {
        padding: 1.5rem;
    }
    
    .payment-info-row {
        display: flex;
        margin-bottom: 1rem;
        align-items: flex-start;
    }
    
    .payment-info-label {
        font-weight: 600;
        color: var(--text-light);
        width: 140px;
        flex-shrink: 0;
    }
    
    .payment-info-value {
        color: var(--text-dark);
        flex-grow: 1;
    }
    
    .payment-status {
        display: inline-flex;
        align-items: center;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }
    
    .payment-status.credit {
        background-color: #FFF3E0;
        color: #E65100;
    }
    
    .payment-status.direct {
        background-color: #E1F5FE;
        color: #0288D1;
    }
    
    .payment-status i {
        margin-right: 5px;
        font-size: 0.8rem;
    }
    
    .payment-method-badge {
        display: inline-flex;
        align-items: center;
        background-color: var(--primary-light);
        color: white;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }
    
    .payment-method-badge i {
        margin-right: 5px;
    }
    
    .payment-amount {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-dark);
        text-align: center;
        margin: 1.5rem 0;
    }
    
    .payment-amount-label {
        display: block;
        font-size: 0.9rem;
        color: var(--text-light);
        margin-bottom: 0.5rem;
    }
    
    .payment-notes {
        background-color: #FFF8E1;
        border-left: 4px solid var(--accent-color);
        padding: 1rem;
        border-radius: 5px;
        margin-top: 1.5rem;
    }
    
    .payment-notes-title {
        font-weight: 600;
        color: #795548;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }
    
    .payment-notes-title i {
        color: var(--accent-color);
        margin-right: 8px;
    }
    
    .payment-notes-content {
        color: #795548;
    }
    
    .action-card {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 5px 20px rgba(13, 71, 161, 0.08);
        overflow: hidden;
        margin-bottom: 1.5rem;
        border: none;
    }
    
    .action-card-header {
        background-color: var(--primary-light);
        color: white;
        padding: 1.25rem 1.5rem;
    }
    
    .action-card-header h5 {
        font-weight: 700;
        margin-bottom: 0;
        display: flex;
        align-items: center;
    }
    
    .action-card-header h5 i {
        margin-right: 10px;
    }
    
    .action-card-body {
        padding: 1.5rem;
    }
    
    .btn-action {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.8rem 1.5rem;
        border-radius: 50px;
        font-weight: 600;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }
    
    .btn-action:last-child {
        margin-bottom: 0;
    }
    
    .btn-action i {
        margin-right: 10px;
    }
    
    .btn-action.primary {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
    }
    
    .btn-action.primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(30, 136, 229, 0.3);
    }
    
    .btn-action.secondary {
        background-color: white;
        color: var(--text-dark);
        border: 2px solid var(--border-color);
    }
    
    .btn-action.secondary:hover {
        background-color: var(--background-light);
        color: var(--primary-color);
        border-color: var(--primary-light);
    }
    
    .payment-summary-card {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        border-radius: 12px;
        color: white;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 5px 20px rgba(13, 71, 161, 0.2);
    }
    
    .payment-summary-title {
        font-weight: 700;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }
    
    .payment-summary-title i {
        margin-right: 10px;
        background-color: rgba(255, 255, 255, 0.2);
        width: 30px;
        height: 30px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .payment-summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.8rem;
        font-size: 0.95rem;
    }
    
    .payment-summary-row:last-child {
        margin-bottom: 0;
        padding-top: 0.8rem;
        border-top: 1px solid rgba(255, 255, 255, 0.3);
        font-weight: 700;
    }
</style>
@endsection

@section('content')
<div class="payment-details-page">
    <div class="container-fluid">
        <!-- En-tête de page -->
        <div class="page-header d-flex justify-content-between align-items-center">
            <h1 class="page-title">
                <i class="fas fa-money-bill-wave"></i>
                <span>Détails du paiement #{{ $payment->id }}</span>
            </h1>
            
            <div class="d-flex gap-2">
                <a href="{{ route('cashier.payments.receipt', $payment->id) }}" class="btn btn-outline-primary" target="_blank">
                    <i class="fas fa-print me-2"></i> Imprimer reçu
                </a>
                <a href="{{ route('cashier.payments.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Retour à la liste
                </a>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="payment-card animate-fade-in" style="animation-delay: 0.1s;">
                    <div class="payment-card-header">
                        <h5><i class="fas fa-info-circle"></i> Informations du paiement</h5>
                    </div>
                    <div class="payment-card-body">
                        <div class="payment-amount mb-4">
                            <span class="payment-amount-label d-block text-muted mb-1">Montant du paiement</span>
                            <span class="h3 fw-bold" style="color: var(--primary-dark);">{{ number_format($payment->amount, 0, ',', ' ') }} FCFA</span>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="payment-info-group mb-3">
                                    <h6 class="d-flex align-items-center mb-3" style="color: var(--primary-color);">
                                        <i class="fas fa-calendar-alt me-2"></i> Détails du paiement
                                    </h6>
                                    
                                    <div class="payment-info-row d-flex justify-content-between mb-2">
                                        <div class="payment-info-label text-muted">Date :</div>
                                        <div class="payment-info-value fw-medium">{{ $payment->created_at->format('d/m/Y H:i') }}</div>
                                    </div>
                                    
                                    <div class="payment-info-row d-flex justify-content-between mb-2">
                                        <div class="payment-info-label text-muted">Type :</div>
                                        <div class="payment-info-value">
                                            @if($payment->sale)
                                                <span class="badge bg-primary text-white px-2 py-1 rounded-pill">
                                                    <i class="fas fa-shopping-cart me-1"></i> Vente directe
                                                </span>
                                            @else
                                                <span class="badge bg-warning text-dark px-2 py-1 rounded-pill">
                                                    <i class="fas fa-credit-card me-1"></i> Vente à crédit
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                    
                                    <div class="payment-info-row d-flex justify-content-between mb-2">
                                        <div class="payment-info-label text-muted">Client :</div>
                                        <div class="payment-info-value fw-medium">
                                            @if($payment->sale)
                                                {{ $payment->sale->customer_name }}
                                            @else
                                                N/A
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="payment-info-group mb-3">
                                    <h6 class="d-flex align-items-center mb-3" style="color: var(--primary-color);">
                                        <i class="fas fa-file-invoice-dollar me-2"></i> Détails de la transaction
                                    </h6>
                                    
                                    <div class="payment-info-row d-flex justify-content-between mb-2">
                                        <div class="payment-info-label text-muted">Référence :</div>
                                        <div class="payment-info-value fw-medium">
                                            @if($payment->sale)
                                                <span class="text-primary">{{ $payment->sale->invoice_number ?? 'VNT-' . str_pad($payment->sale->id, 6, '0', STR_PAD_LEFT) }}</span>
                                            @else
                                                N/A
                                            @endif
                                        </div>
                                    </div>
                                    
                                    <div class="payment-info-row d-flex justify-content-between mb-2">
                                        <div class="payment-info-label text-muted">Caissier :</div>
                                        <div class="payment-info-value fw-medium">{{ $payment->cashier->name }}</div>
                                    </div>
                                    
                                    <div class="payment-info-row d-flex justify-content-between mb-2">
                                        <div class="payment-info-label text-muted">Mode de paiement :</div>
                                        <div class="payment-info-value">
                                            @switch($payment->payment_method)
                                                @case('cash')
                                                    <span class="badge bg-success text-white px-2 py-1 rounded-pill">
                                                        <i class="fas fa-money-bill-wave me-1"></i> Espèces
                                                    </span>
                                                    @break
                                                @case('bank_transfer')
                                                    <span class="badge bg-primary text-white px-2 py-1 rounded-pill">
                                                        <i class="fas fa-university me-1"></i> Virement bancaire
                                                    </span>
                                                    @break
                                                @case('check')
                                                    <span class="badge bg-info text-white px-2 py-1 rounded-pill">
                                                        <i class="fas fa-money-check me-1"></i> Chèque
                                                    </span>
                                                    @break
                                                @case('mobile_money')
                                                    <span class="badge bg-warning text-dark px-2 py-1 rounded-pill">
                                                        <i class="fas fa-mobile-alt me-1"></i> Mobile Money
                                                    </span>
                                                    @break
                                                @default
                                                    <span class="badge bg-secondary text-white px-2 py-1 rounded-pill">
                                                        <i class="fas fa-wallet me-1"></i> {{ $payment->payment_method }}
                                                    </span>
                                            @endswitch
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if($payment->notes)
                            <div class="payment-notes mt-4 p-3" style="background-color: var(--primary-light); border-radius: 12px; border-left: 4px solid var(--primary-color);">
                                <div class="payment-notes-title d-flex align-items-center mb-2">
                                    <i class="fas fa-sticky-note me-2" style="color: var(--primary-color);"></i> 
                                    <span class="fw-medium">Notes</span>
                                </div>
                                <div class="payment-notes-content">
                                    {{ $payment->notes }}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
                
                @if($payment->sale)
                <div class="payment-card animate-fade-in" style="animation-delay: 0.2s;">
                    <div class="payment-card-header">
                        <h5><i class="fas fa-shopping-cart"></i> Détails de la vente</h5>
                    </div>
                    <div class="payment-card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="payment-info-group mb-3">
                                    <h6 class="d-flex align-items-center mb-3" style="color: var(--primary-color);">
                                        <i class="fas fa-box me-2"></i> Produit
                                    </h6>
                                    
                                    <div class="payment-info-row d-flex justify-content-between mb-2">
                                        <div class="payment-info-label text-muted">Nom :</div>
                                        <div class="payment-info-value fw-medium">
                                            @if($payment->sale->product)
                                                {{ $payment->sale->product->name }}
                                            @elseif($payment->sale->supply && $payment->sale->supply->details->isNotEmpty())
                                                {{ $payment->sale->supply->details->first()->product->name }}
                                            @else
                                                N/A
                                            @endif
                                        </div>
                                    </div>
                                    <div class="payment-info-row d-flex justify-content-between mb-2">
                                        <div class="payment-info-label text-muted">Quantité :</div>
                                        <div class="payment-info-value fw-medium">{{ number_format($payment->sale->quantity, 2, ',', ' ') }} T</div>
                                    </div>
                                    <div class="payment-info-row d-flex justify-content-between mb-2">
                                        <div class="payment-info-label text-muted">Prix unitaire :</div>
                                        <div class="payment-info-value fw-medium">{{ number_format($payment->sale->unit_price, 0, ',', ' ') }} FCFA/T</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="payment-info-group mb-3">
                                    <h6 class="d-flex align-items-center mb-3" style="color: var(--primary-color);">
                                        <i class="fas fa-calculator me-2"></i> Résumé financier
                                    </h6>
                                    
                                    <div class="payment-summary-card p-3" style="background-color: var(--background-color); border-radius: 12px; border: 1px solid var(--border-color);">
                                        <div class="payment-summary-row d-flex justify-content-between mb-2">
                                            <div class="text-muted">Montant total :</div>
                                            <div class="fw-medium">{{ number_format($payment->sale->total_amount, 0, ',', ' ') }} FCFA</div>
                                        </div>
                                        <div class="payment-summary-row d-flex justify-content-between mb-2">
                                            <div class="text-muted">Montant payé :</div>
                                            <div class="fw-medium text-success">{{ number_format($payment->sale->amount_paid, 0, ',', ' ') }} FCFA</div>
                                        </div>
                                        <div class="payment-summary-row d-flex justify-content-between pt-2" style="border-top: 1px solid var(--border-color);">
                                            <div class="fw-medium">Reste à payer :</div>
                                            <div class="fw-bold text-danger">{{ number_format($payment->sale->total_amount - $payment->sale->amount_paid, 0, ',', ' ') }} FCFA</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>

            <div class="col-md-4">
                <div class="payment-card animate-fade-in" style="animation-delay: 0.3s;">
                    <div class="payment-card-header">
                        <h5><i class="fas fa-cogs"></i> Actions</h5>
                    </div>
                    <div class="payment-card-body">
                        <div class="d-grid gap-3">
                            <a href="{{ route('cashier.payments.receipt', $payment->id) }}" class="btn btn-primary d-flex align-items-center justify-content-center gap-2" target="_blank">
                                <i class="fas fa-file-invoice"></i> Voir le reçu
                            </a>
                            <a href="#" class="btn btn-outline-primary d-flex align-items-center justify-content-center gap-2" onclick="window.print(); return false;">
                                <i class="fas fa-print"></i> Imprimer les détails
                            </a>
                            @if($payment->sale && $payment->sale->total_amount > $payment->sale->amount_paid)
                            <a href="{{ route('cashier.payments.process', $payment->sale_id) }}" class="btn btn-success d-flex align-items-center justify-content-center gap-2">
                                <i class="fas fa-plus-circle"></i> Ajouter un paiement
                            </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
