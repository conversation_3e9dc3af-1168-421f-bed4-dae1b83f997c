@extends('layouts.cashier')

@section('styles')
<link rel="stylesheet" href="{{ asset('css/cashier-dashboard-modern.css') }}">
<link rel="stylesheet" href="{{ asset('css/cashier-credit-sales-responsive.css') }}">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
@endsection

@section('title', 'Ventes à Crédit')

@section('content')
<div class="container-fluid dashboard-container">
    <!-- En-tête de la page -->
    <div class="dashboard-header">
        <div>
            <h1 class="dashboard-title">
                <i class="fas fa-credit-card"></i>
                <span>Ventes à Crédit</span>
            </h1>
            <p class="dashboard-subtitle">Gestion des ventes à crédit et suivi des paiements</p>
        </div>
        <div class="dashboard-actions">
            <button class="btn btn-outline-primary" onclick="window.print()">
                <i class="fas fa-print me-2"></i> Imprimer
            </button>
            <button class="btn btn-primary" onclick="window.location.reload()">
                <i class="fas fa-sync-alt me-2"></i> Actualiser
            </button>
        </div>
    </div>

    <!-- Statistiques des ventes à crédit -->
    <div class="stats-overview">
        <!-- Ventes à crédit totales -->
        <div class="stat-card fade-in fade-in-delay-1">
            <div class="stat-card-header">
                <div class="stat-card-icon credit">
                    <i class="fas fa-credit-card"></i>
                </div>
            </div>
            <div class="stat-card-title">Total ventes à crédit</div>
            <div class="stat-card-value">{{ $creditSales->total() }}</div>
            <div class="stat-card-subtitle">Toutes les ventes à crédit</div>
        </div>

        <!-- Ventes partiellement payées -->
        <div class="stat-card fade-in fade-in-delay-2">
            <div class="stat-card-header">
                <div class="stat-card-icon payments">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
            </div>
            <div class="stat-card-title">Partiellement payées</div>
            <div class="stat-card-value">{{ $creditSales->where('status', 'partially_paid')->count() }}</div>
            <div class="stat-card-subtitle">Ventes avec paiement partiel</div>
        </div>

        <!-- Ventes en attente -->
        <div class="stat-card fade-in fade-in-delay-3">
            <div class="stat-card-header">
                <div class="stat-card-icon sales">
                    <i class="fas fa-hourglass-half"></i>
                </div>
            </div>
            <div class="stat-card-title">En attente</div>
            <div class="stat-card-value">{{ $creditSales->where('status', 'pending_payment')->count() }}</div>
            <div class="stat-card-subtitle">Ventes sans paiement</div>
        </div>
    </div>

    <!-- Tableau des ventes à crédit -->
    <div class="dashboard-section fade-in fade-in-delay-1">
        <div class="dashboard-section-header">
            <h2 class="dashboard-section-title">
                <i class="fas fa-list"></i>
                Liste des ventes à crédit
            </h2>
        </div>
        <div class="dashboard-section-body">
            <div class="table-responsive">
                <table class="modern-table">
                    <thead>
                        <tr>
                            <th>N° Vente</th>
                            <th>Client</th>
                            <th>Date</th>
                            <th>Montant Total</th>
                            <th>Montant Payé</th>
                            <th>Reste à Payer</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($creditSales as $sale)
                            <tr class="{{ $sale->status === 'paid' ? 'table-row-paid' : ($sale->status === 'partially_paid' ? 'table-row-partial' : 'table-row-pending') }}">
                                <td><strong>{{ $sale->id }}</strong></td>
                                <td>{{ $sale->detail->customer->name ?? 'Client non défini' }}</td>
                                <td>{{ $sale->created_at->format('d/m/Y H:i') }}</td>
                                <td><strong>{{ number_format($sale->amount, 0, ',', ' ') }} FCFA</strong></td>
                                <td>{{ number_format($sale->paid_amount, 0, ',', ' ') }} FCFA</td>
                                <td>{{ number_format($sale->remaining_amount, 0, ',', ' ') }} FCFA</td>
                                <td>
                                    @if($sale->status === 'paid')
                                        <span class="status-badge paid">
                                            <i class="fas fa-check-circle"></i>
                                            Payé
                                        </span>
                                    @elseif($sale->status === 'partially_paid')
                                        <span class="status-badge pending">
                                            <i class="fas fa-clock"></i>
                                            Partiel
                                        </span>
                                    @else
                                        <span class="status-badge failed">
                                            <i class="fas fa-exclamation-circle"></i>
                                            En attente
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ route('cashier.credit-sales.show', $sale) }}" class="action-button" title="Voir les détails">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="empty-state">
                                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                        <h4>Aucune vente à crédit trouvée</h4>
                                        <p class="text-muted">Les ventes à crédit apparaîtront ici</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination responsive -->
            @if($creditSales->hasPages())
            <div class="pagination-container mt-4">
                <div class="row align-items-center">
                    <div class="col-md-6 mb-3 mb-md-0">
                        <p class="mb-0 text-muted">
                            Affichage de 
                            <span class="fw-semibold">{{ $creditSales->firstItem() ?? 0 }}</span>
                            à 
                            <span class="fw-semibold">{{ $creditSales->lastItem() ?? 0 }}</span>
                            sur
                            <span class="fw-semibold">{{ $creditSales->total() }}</span>
                            ventes
                        </p>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-md-end justify-content-center">
                            {{ $creditSales->links() }}
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
    
    <!-- Pagination -->
    @if($creditSales->hasPages())
        <div class="card shadow">
            <div class="card-body py-3">
                <div class="row align-items-center">
                    <div class="col-md-6 mb-2 mb-md-0">
                        <p class="mb-0">
                            Affichage de 
                            <span class="fw-semibold">{{ $creditSales->firstItem() ?? 0 }}</span>
                            à 
                            <span class="fw-semibold">{{ $creditSales->lastItem() ?? 0 }}</span>
                            sur
                            <span class="fw-semibold">{{ $creditSales->total() }}</span>
                            ventes
                        </p>
                    </div>
                    <div class="col-md-6">
                        <div class="pagination-container d-flex justify-content-md-end justify-content-center">
                            {{ $creditSales->withQueryString()->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
@endsection
