@extends('layouts.cement_manager')

@section('title', '<PERSON> Prof<PERSON>')

@section('content')
<div class="profile-header mb-4">
    <div class="container-fluid">
        <div class="profile-cover">
            <div class="profile-avatar-wrapper">
                @if($user->avatar && file_exists(public_path($user->avatar)))
                    <img src="{{ asset($user->avatar) }}" alt="Avatar" class="profile-avatar">
                @else
                    <div class="profile-avatar-placeholder">
                        {{ substr($user->name, 0, 1) }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-4">
            <!-- Carte de profil -->
            <div class="card profile-card mb-4">
                <div class="card-body">
                    <div class="text-center mb-4">
                        <h4 class="mb-1">{{ $user->first_name }} {{ $user->last_name }}</h4>
                        <span class="badge bg-primary rounded-pill px-3 py-2 mb-3">Gestionnaire de Ciment</span>
                        <div class="d-flex justify-content-center gap-2 mt-3">
                            <a href="{{ route('cement-manager.profile.edit') }}" class="btn btn-primary btn-sm rounded-pill px-3">
                                <i class="fas fa-edit me-2"></i> Modifier
                            </a>
                            <a href="{{ route('cement-manager.profile.password') }}" class="btn btn-outline-secondary btn-sm rounded-pill px-3">
                                <i class="fas fa-key me-2"></i> Mot de passe
                            </a>
                        </div>
                    </div>
                    
                    <div class="profile-info">
                        <div class="profile-info-item">
                            <div class="profile-info-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="profile-info-content">
                                <h6>Email</h6>
                                <p>{{ $user->email }}</p>
                            </div>
                        </div>
                        
                        <div class="profile-info-item">
                            <div class="profile-info-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="profile-info-content">
                                <h6>Téléphone</h6>
                                <p>{{ $user->phone ?? 'Non spécifié' }}</p>
                            </div>
                        </div>
                        
                        <!-- Le champ position a été temporairement retiré -->
                    </div>
                </div>
            </div>
            
            <!-- Carte de statistiques personnelles -->
            <div class="card mb-4">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-chart-pie text-primary me-2"></i>
                    <h5 class="card-title mb-0">Statistiques personnelles</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-shopping-cart text-primary me-2"></i>
                                Ventes ce mois
                            </div>
                            <span class="badge bg-primary rounded-pill">24</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-money-bill-wave text-success me-2"></i>
                                Chiffre d'affaires
                            </div>
                            <span class="badge bg-success rounded-pill">1.2M FCFA</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-truck text-info me-2"></i>
                                Livraisons
                            </div>
                            <span class="badge bg-info rounded-pill">18</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-8">
            <!-- Statistiques visuelles -->
            <div class="card mb-4">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-chart-bar text-primary me-2"></i>
                    <h5 class="card-title mb-0">Performance</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-4 mb-md-0">
                            <div class="stat-card">
                                <div class="stat-card-info">
                                    <div class="stat-card-icon bg-primary">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                    <div>
                                        <h6 class="stat-card-title">Ventes</h6>
                                        <h3 class="stat-card-value">24</h3>
                                    </div>
                                </div>
                                <div class="stat-card-progress">
                                    <div class="progress">
                                        <div class="progress-bar bg-primary" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <small class="text-muted">+12% ce mois</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4 mb-md-0">
                            <div class="stat-card">
                                <div class="stat-card-info">
                                    <div class="stat-card-icon bg-success">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </div>
                                    <div>
                                        <h6 class="stat-card-title">Chiffre d'affaires</h6>
                                        <h3 class="stat-card-value">1.2M</h3>
                                    </div>
                                </div>
                                <div class="stat-card-progress">
                                    <div class="progress">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: 65%" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <small class="text-muted">+8% ce mois</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <div class="stat-card-info">
                                    <div class="stat-card-icon bg-info">
                                        <i class="fas fa-truck"></i>
                                    </div>
                                    <div>
                                        <h6 class="stat-card-title">Livraisons</h6>
                                        <h3 class="stat-card-value">18</h3>
                                    </div>
                                </div>
                                <div class="stat-card-progress">
                                    <div class="progress">
                                        <div class="progress-bar bg-info" role="progressbar" style="width: 85%" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <small class="text-muted">+15% ce mois</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Activité récente -->
            <div class="card">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-history text-primary me-2"></i>
                    <h5 class="card-title mb-0">Activité récente</h5>
                </div>
                <div class="card-body p-0">
                    <div class="activity-feed">
                        <div class="activity-item">
                            <div class="activity-date">
                                <span class="activity-day">13</span>
                                <span class="activity-month">Mai</span>
                            </div>
                            <div class="activity-icon bg-primary">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="activity-content">
                                <h6 class="mb-1">Nouvelle vente enregistrée</h6>
                                <p class="mb-0">Vous avez enregistré une vente de ciment pour un montant de 250,000 FCFA</p>
                                <small class="text-muted">Il y a 2 heures</small>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-date">
                                <span class="activity-day">12</span>
                                <span class="activity-month">Mai</span>
                            </div>
                            <div class="activity-icon bg-success">
                                <i class="fas fa-truck-loading"></i>
                            </div>
                            <div class="activity-content">
                                <h6 class="mb-1">Bon de commande consulté</h6>
                                <p class="mb-0">Vous avez consulté le bon de commande #BC-2025-042</p>
                                <small class="text-muted">Il y a 1 jour</small>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-date">
                                <span class="activity-day">10</span>
                                <span class="activity-month">Mai</span>
                            </div>
                            <div class="activity-icon bg-info">
                                <i class="fas fa-user-edit"></i>
                            </div>
                            <div class="activity-content">
                                <h6 class="mb-1">Profil mis à jour</h6>
                                <p class="mb-0">Vous avez mis à jour vos informations de profil</p>
                                <small class="text-muted">Il y a 3 jours</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* Styles de l'en-tête du profil */
    .profile-header {
        position: relative;
    }
    
    .profile-cover {
        height: 200px;
        background: linear-gradient(135deg, #2563eb, #1e40af);
        border-radius: 0.5rem;
        position: relative;
        margin-bottom: 60px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    .profile-avatar-wrapper {
        position: absolute;
        bottom: -60px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 10;
    }
    
    .profile-avatar, .profile-avatar-placeholder {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        border: 5px solid white;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .profile-avatar {
        object-fit: cover;
    }
    
    .profile-avatar-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #2563eb;
        color: white;
        font-size: 3rem;
        font-weight: 600;
    }
    
    /* Carte de profil */
    .profile-card {
        border: none;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        border-radius: 0.5rem;
        overflow: hidden;
    }
    
    .profile-info {
        margin-top: 1.5rem;
    }
    
    .profile-info-item {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .profile-info-item:last-child {
        border-bottom: none;
    }
    
    .profile-info-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: rgba(37, 99, 235, 0.1);
        color: #2563eb;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
    }
    
    .profile-info-content h6 {
        font-size: 0.8rem;
        color: #6b7280;
        margin-bottom: 0.25rem;
    }
    
    .profile-info-content p {
        font-size: 0.95rem;
        font-weight: 500;
        margin-bottom: 0;
    }
    
    /* Cartes de statistiques */
    .card {
        border: none;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        border-radius: 0.5rem;
        overflow: hidden;
        margin-bottom: 1.5rem;
    }
    
    .card-header {
        background-color: white;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1rem 1.25rem;
    }
    
    .card-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0;
    }
    
    /* Statistiques */
    .stat-card {
        padding: 1.25rem;
        border-radius: 0.5rem;
        background-color: white;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
    }
    
    .stat-card-info {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .stat-card-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-right: 1rem;
    }
    
    .stat-card-title {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 0.25rem;
    }
    
    .stat-card-value {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0;
    }
    
    .stat-card-progress {
        margin-top: 0.5rem;
    }
    
    .progress {
        height: 6px;
        margin-bottom: 0.5rem;
        border-radius: 3px;
    }
    
    /* Activité feed */
    .activity-feed {
        padding: 1.25rem;
    }
    
    .activity-item {
        display: flex;
        align-items: flex-start;
        padding: 1rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-date {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 1rem;
        min-width: 40px;
    }
    
    .activity-day {
        font-size: 1.25rem;
        font-weight: 700;
        line-height: 1;
    }
    
    .activity-month {
        font-size: 0.75rem;
        color: #6b7280;
    }
    
    .activity-icon {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-right: 1rem;
    }
    
    .activity-content {
        flex: 1;
    }
    
    .activity-content h6 {
        font-size: 0.95rem;
        margin-bottom: 0.25rem;
    }
    
    .activity-content p {
        font-size: 0.875rem;
        color: #4b5563;
        margin-bottom: 0.25rem;
    }
</style>
@endpush
