# Guide de test - Système d'approvisionnement avec facture

## Tests à effectuer

### 🧪 **Test 1 : Catégorie Ciment avec facture**

1. **Accéder à la page** : `http://127.0.0.1:8000/accountant/supplies/create`

2. **Remplir les champs de base** :
   - Référence : `TEST-CIMENT-001`
   - Catégorie : Sélectionner **"Ciment"** (ou toute catégorie contenant "ciment")
   - Fournisseur : Choisir un fournisseur
   - Date d'approvisionnement : Date du jour

3. **Vérifier le basculement des champs** :
   - ✅ Le champ "Date de livraison prévue" doit **disparaître**
   - ✅ Le champ "Facture d'approvisionnement" doit **apparaître**

4. **Sélectionner un produit** dans la liste déroulante

5. **Ajouter une ville** :
   - Cliquer sur "Ajouter une ville" pour une région
   - Remplir : ville, quantité, véhicule, prix, nombre de tours
   - Sauvegarder la ville

6. **Tester la facture** :
   - Cliquer sur "Choisir un fichier"
   - Sélectionner une image (JPG/PNG) ou un PDF
   - ✅ La **prévisualisation** doit apparaître automatiquement
   - ✅ Vérifier les informations du fichier (nom, taille, type)

7. **Ajouter des notes** (optionnel)

8. **Soumettre le formulaire** :
   - Cliquer sur "Enregistrer"
   - ✅ Doit réussir et rediriger vers la liste des approvisionnements

### 🧪 **Test 2 : Catégorie non-Ciment**

1. **Nouvelle page** : Rafraîchir ou rouvrir la page

2. **Remplir les champs de base** :
   - Référence : `TEST-AUTRE-001`
   - Catégorie : Sélectionner une catégorie **autre que Ciment**
   - Fournisseur et date

3. **Vérifier le basculement** :
   - ✅ Le champ "Facture d'approvisionnement" doit être **masqué**
   - ✅ Le champ "Date de livraison prévue" doit être **visible**

4. **Remplir la date de livraison prévue**

5. **Compléter et soumettre** :
   - Ajouter produit et ville
   - ✅ Doit réussir sans fichier

### 🧪 **Test 3 : Validation des fichiers**

1. **Sélectionner "Ciment"**

2. **Tester les validations** :
   - **Fichier trop volumineux** : Essayer un fichier > 5MB
     - ✅ Doit afficher une erreur
   - **Type non supporté** : Essayer un fichier .txt ou .doc
     - ✅ Doit afficher une erreur
   - **Fichier valide** : JPG, PNG ou PDF < 5MB
     - ✅ Doit afficher la prévisualisation

### 🧪 **Test 4 : Prévisualisation interactive**

1. **Avec une image** :
   - Sélectionner un fichier JPG/PNG
   - ✅ L'image doit s'afficher en miniature
   - ✅ Cliquer sur l'image → plein écran
   - ✅ Bouton X pour supprimer

2. **Avec un PDF** :
   - Sélectionner un fichier PDF
   - ✅ Icône PDF animée
   - ✅ Bouton "Ouvrir le PDF" → nouvel onglet
   - ✅ Bouton X pour supprimer

### 🧪 **Test 5 : Changement de catégorie**

1. **Sélectionner "Ciment"** et choisir un fichier
2. **Changer pour une autre catégorie**
   - ✅ La prévisualisation doit disparaître
   - ✅ Le champ fichier doit être réinitialisé
   - ✅ Le champ date doit apparaître

3. **Revenir à "Ciment"**
   - ✅ Le champ fichier doit réapparaître
   - ✅ Pas de fichier sélectionné (réinitialisé)

## Résultats attendus

### ✅ **Succès complet si** :
- Tous les basculements de champs fonctionnent
- La prévisualisation s'affiche correctement
- Les validations de fichier fonctionnent
- L'upload et la sauvegarde réussissent
- Aucune erreur dans la console du navigateur

### ❌ **Problèmes possibles** :
- Erreurs JavaScript dans la console
- Champs qui ne basculent pas
- Prévisualisation qui ne s'affiche pas
- Erreurs de validation côté serveur

## Vérification côté serveur

Après un test réussi avec fichier :
1. Vérifier dans `storage/app/public/invoices/`
2. Le fichier doit être présent avec le nom : `timestamp_reference.extension`
3. Dans la base de données, le champ `invoice_file` doit contenir le chemin

## Console du navigateur

Ouvrir les outils de développement (F12) pour surveiller :
- Messages de log de SupplyManager
- Erreurs JavaScript éventuelles
- Requêtes réseau (onglet Network)

Le système est maintenant robuste et prêt pour la production ! 🚀
