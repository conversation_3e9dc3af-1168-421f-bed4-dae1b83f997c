@extends('layouts.accountant')

@section('content')
<div class="container-fluid">
    <!-- En-tête de la page -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Modifier le véhicule</h1>
        <a href="{{ route('accountant.trucks.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Retour à la liste
        </a>
    </div>

    <!-- Formulaire de modification -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Informations du véhicule</h6>
        </div>
        <div class="card-body">
            @if($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form action="{{ route('accountant.trucks.update', $truck) }}" method="POST">
                @csrf
                @method('PUT')
                
                <div class="row g-3">
                    <!-- Immatriculation -->
                    <div class="col-md-6">
                        <label for="registration_number" class="form-label">Immatriculation</label>
                        <input type="text" 
                               class="form-control @error('registration_number') is-invalid @enderror" 
                               id="registration_number" 
                               name="registration_number" 
                               value="{{ old('registration_number', $truck->registration_number) }}"
                               required>
                        @error('registration_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Marque -->
                    <div class="col-md-6">
                        <label for="brand" class="form-label">Marque</label>
                        <input type="text" 
                               class="form-control @error('brand') is-invalid @enderror" 
                               id="brand" 
                               name="brand" 
                               value="{{ old('brand', $truck->brand) }}"
                               required>
                        @error('brand')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Modèle -->
                    <div class="col-md-6">
                        <label for="model" class="form-label">Modèle</label>
                        <input type="text" 
                               class="form-control @error('model') is-invalid @enderror" 
                               id="model" 
                               name="model" 
                               value="{{ old('model', $truck->model) }}"
                               required>
                        @error('model')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Capacité -->
                    <div class="col-md-6">
                        <label for="capacity_id" class="form-label">Capacité <span class="text-danger">*</span></label>
                        <select class="form-select @error('capacity_id') is-invalid @enderror" 
                                id="capacity_id" 
                                name="capacity_id" 
                                required>
                            <option value="">Sélectionnez une capacité</option>
                            @foreach($capacities as $capacity)
                                <option value="{{ $capacity->id }}" 
                                        {{ (old('capacity_id', $truck->capacity_id) == $capacity->id) ? 'selected' : '' }}>
                                    {{ $capacity->tonnage }} T
                                </option>
                            @endforeach
                        </select>
                        @error('capacity_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Année -->
                    <div class="col-md-6">
                        <label for="year" class="form-label">Année</label>
                        <input type="number" 
                               class="form-control @error('year') is-invalid @enderror" 
                               id="year" 
                               name="year" 
                               value="{{ old('year', $truck->year) }}"
                               min="1900"
                               max="{{ date('Y') + 1 }}"
                               required>
                        @error('year')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Statut -->
                    <div class="col-md-6">
                        <label for="status" class="form-label">Statut</label>
                        <select class="form-select @error('status') is-invalid @enderror" 
                                id="status" 
                                name="status" 
                                required>
                            <option value="">Sélectionner un statut</option>
                            <option value="available" {{ old('status', $truck->status) == 'available' ? 'selected' : '' }}>Disponible</option>
                            <option value="maintenance" {{ old('status', $truck->status) == 'maintenance' ? 'selected' : '' }}>En maintenance</option>
                            <option value="busy" {{ old('status', $truck->status) == 'busy' ? 'selected' : '' }}>Occupé</option>
                        </select>
                        @error('status')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Notes -->
                    <div class="col-12">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control @error('notes') is-invalid @enderror" 
                                  id="notes" 
                                  name="notes" 
                                  rows="3">{{ old('notes', $truck->notes) }}</textarea>
                        @error('notes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Boutons -->
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Enregistrer les modifications
                        </button>
                        <a href="{{ route('accountant.trucks.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Annuler
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
