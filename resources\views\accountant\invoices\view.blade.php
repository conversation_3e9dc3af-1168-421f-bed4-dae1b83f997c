@extends('layouts.accountant')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h4 class="card-title mb-0">Facture #{{ $order->number }}</h4>
                            <small class="text-muted">Émise le {{ $order->created_at->format('d/m/Y') }}</small>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="{{ route('accountant.download-invoice', $order->id) }}" class="btn btn-primary">
                                <i data-feather="download" class="me-1"></i>
                                Télécharger
                            </a>
                            <button class="btn btn-outline-primary" onclick="window.print()">
                                <i data-feather="printer" class="me-1"></i>
                                Imprimer
                            </button>
                        </div>
                    </div>

                    <div class="invoice-details">
                        <div class="row mb-4">
                            <div class="col-sm-6">
                                <h5 class="mb-3">De</h5>
                                <h6 class="mb-2">GRADIS</h6>
                                <p class="mb-1">123 Rue du Commerce</p>
                                <p class="mb-1">75001 Paris</p>
                                <p class="mb-1">France</p>
                                <p class="mb-0"><EMAIL></p>
                            </div>
                            <div class="col-sm-6 text-sm-end">
                                <h5 class="mb-3">À</h5>
                                <h6 class="mb-2">{{ $order->user->name }}</h6>
                                <p class="mb-1">{{ $order->user->address ?? 'Adresse non spécifiée' }}</p>
                                <p class="mb-1">{{ $order->user->city ?? 'Ville non spécifiée' }}</p>
                                <p class="mb-0">{{ $order->user->email }}</p>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Description</th>
                                        <th class="text-end">Quantité</th>
                                        <th class="text-end">Prix unitaire</th>
                                        <th class="text-end">Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($order->items as $item)
                                    <tr>
                                        <td>{{ $item->product->name }}</td>
                                        <td class="text-end">{{ $item->quantity }}</td>
                                        <td class="text-end">{{ number_format($item->unit_price, 0, ',', ' ') }} FCFA</td>
                                        <td class="text-end">{{ number_format($item->total, 0, ',', ' ') }} FCFA</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="3" class="text-end">Sous-total</td>
                                        <td class="text-end">{{ number_format($order->subtotal, 0, ',', ' ') }} FCFA</td>
                                    </tr>
                                    @if($order->tax > 0)
                                    <tr>
                                        <td colspan="3" class="text-end">TVA ({{ $order->tax_rate }}%)</td>
                                        <td class="text-end">{{ number_format($order->tax, 0, ',', ' ') }} FCFA</td>
                                    </tr>
                                    @endif
                                    @if($order->discount > 0)
                                    <tr>
                                        <td colspan="3" class="text-end">Remise</td>
                                        <td class="text-end">-{{ number_format($order->discount, 0, ',', ' ') }} FCFA</td>
                                    </tr>
                                    @endif
                                    <tr>
                                        <td colspan="3" class="text-end fw-bold">Total</td>
                                        <td class="text-end fw-bold">{{ number_format($order->total, 0, ',', ' ') }} FCFA</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="mb-3">Notes</h5>
                                <p class="mb-0">{{ $order->notes ?? 'Aucune note' }}</p>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="alert alert-info mb-0">
                                    <h6 class="mb-2">Informations de paiement</h6>
                                    <p class="mb-1">Méthode de paiement : {{ $order->payment_method }}</p>
                                    <p class="mb-1">Statut du paiement : {{ $order->payment_status }}</p>
                                    @if($order->payment_date)
                                    <p class="mb-0">Date de paiement : {{ $order->payment_date->format('d/m/Y H:i') }}</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
@media print {
    .navbar, .sidebar, .btn, .footer {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    .card-body {
        padding: 0 !important;
    }
}
</style>
@endpush
