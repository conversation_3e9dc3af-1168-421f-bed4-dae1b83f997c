<?php
// Test direct de l'API getMySupplies

require_once __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';
$kernel = $app->make(\Illuminate\Contracts\Http\Kernel::class);

// Afficher les informations de débogage
echo "<h1>Test de l'API getMySupplies</h1>";

// Connexion directe à la base de données pour vérifier les données
try {
    $pdo = new PDO('mysql:host=localhost;dbname=gradis', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Vérifier la structure de la table supplies
    echo "<h2>Structure de la table supplies</h2>";
    $stmt = $pdo->query("DESCRIBE supplies");
    echo "<table border='1'><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        foreach ($row as $key => $value) {
            echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    
    // Compter les enregistrements dans la table supplies
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM supplies");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p>Nombre total d'enregistrements dans la table supplies: <strong>$count</strong></p>";
    
    // Vérifier les utilisateurs avec le rôle accountant
    echo "<h2>Utilisateurs avec le rôle accountant</h2>";
    $stmt = $pdo->query("
        SELECT u.id, u.name, u.email 
        FROM users u
        JOIN model_has_roles mhr ON u.id = mhr.model_id
        JOIN roles r ON mhr.role_id = r.id
        WHERE r.name = 'accountant'
    ");
    
    echo "<table border='1'><tr><th>ID</th><th>Nom</th><th>Email</th></tr>";
    $accountants = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        foreach ($row as $key => $value) {
            echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
        }
        echo "</tr>";
        $accountants[] = $row['id'];
    }
    echo "</table>";
    
    // Vérifier les approvisionnements créés par les comptables
    if (!empty($accountants)) {
        echo "<h2>Approvisionnements créés par les comptables</h2>";
        $placeholders = implode(',', array_fill(0, count($accountants), '?'));
        $stmt = $pdo->prepare("
            SELECT id, reference, created_by, status, created_at 
            FROM supplies 
            WHERE created_by IN ($placeholders)
            LIMIT 20
        ");
        $stmt->execute($accountants);
        
        if ($stmt->rowCount() > 0) {
            echo "<table border='1'><tr><th>ID</th><th>Référence</th><th>Créé par</th><th>Statut</th><th>Date création</th></tr>";
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr>";
                foreach ($row as $key => $value) {
                    echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>Aucun approvisionnement créé par des comptables.</p>";
        }
        
        // Compter les approvisionnements par comptable
        echo "<h2>Nombre d'approvisionnements par comptable</h2>";
        $stmt = $pdo->prepare("
            SELECT created_by, COUNT(*) as total 
            FROM supplies 
            WHERE created_by IN ($placeholders)
            GROUP BY created_by
        ");
        $stmt->execute($accountants);
        
        if ($stmt->rowCount() > 0) {
            echo "<table border='1'><tr><th>ID Comptable</th><th>Nombre d'approvisionnements</th></tr>";
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr><td>" . $row['created_by'] . "</td><td>" . $row['total'] . "</td></tr>";
            }
            echo "</table>";
        } else {
            echo "<p>Aucun approvisionnement créé par des comptables.</p>";
        }
    }
    
    // Vérifier les approvisionnements récents (tous utilisateurs confondus)
    echo "<h2>10 derniers approvisionnements (tous utilisateurs)</h2>";
    $stmt = $pdo->query("
        SELECT s.id, s.reference, s.created_by, u.name as creator_name, s.status, s.created_at 
        FROM supplies s
        LEFT JOIN users u ON s.created_by = u.id
        ORDER BY s.created_at DESC
        LIMIT 10
    ");
    
    if ($stmt->rowCount() > 0) {
        echo "<table border='1'><tr><th>ID</th><th>Référence</th><th>Créé par ID</th><th>Créé par Nom</th><th>Statut</th><th>Date création</th></tr>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($row['reference'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($row['created_by'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($row['creator_name'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($row['status'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($row['created_at'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Aucun approvisionnement trouvé.</p>";
    }
    
} catch (PDOException $e) {
    echo "<div style='color:red'>Erreur de base de données: " . $e->getMessage() . "</div>";
}

// Ajouter un bouton pour tester l'API
echo "<h2>Test manuel de l'API</h2>";
echo "<p>Cliquez sur le bouton ci-dessous pour tester l'API getMySupplies:</p>";
echo "<button onclick=\"testApi()\">Tester l'API</button>";
echo "<div id=\"api-result\" style=\"margin-top: 20px; padding: 10px; border: 1px solid #ccc;\">Résultat de l'API s'affichera ici</div>";

// Script JavaScript pour tester l'API
echo "<script>
function testApi() {
    document.getElementById('api-result').innerHTML = 'Chargement...';
    
    fetch('/accountant/supplies/my-supplies')
        .then(response => {
            if (!response.ok) {
                throw new Error('Erreur réseau: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            document.getElementById('api-result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            document.getElementById('api-result').innerHTML = '<div style=\"color:red\">Erreur: ' + error.message + '</div>';
        });
}
</script>";
?>
