@extends('layouts.cashier')

@section('styles')
<style>
    /* Variables globales */
    :root {
        --primary-color: #1E88E5; /* Bleu principal */
        --primary-light: #BBDEFB; /* Bleu clair */
        --primary-dark: #0D47A1; /* Bleu foncé */
        --secondary-color: #4CAF50; /* Vert */
        --warning-color: #FF9800; /* Orange */
        --danger-color: #F44336; /* Rouge */
        --info-color: #00BCD4; /* Cyan */
        --light-color: #F5F9FF; /* Bleu très clair */
        --dark-color: #263238; /* Bleu-gris foncé */
        --text-color: #37474F; /* Bleu-gris */
        --border-radius: 0.75rem;
        --card-radius: 1rem;
        --box-shadow: 0 8px 20px rgba(13, 71, 161, 0.08);
        --transition: all 0.3s ease;
        --gradient-blue: linear-gradient(135deg, #1E88E5, #0D47A1);
    }
    
    /* Styles généraux */
    .sale-container {
        padding: 1.5rem 1.5rem 2.5rem;
        background: var(--light-color);
        min-height: calc(100vh - 70px);
        position: relative;
    }
    
    .sale-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 220px;
        background: var(--gradient-blue);
        z-index: 0;
    }
    
    /* En-tête moderne avec design épuré */
    .sale-header {
        background: white;
        color: var(--text-color);
        border-radius: var(--card-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        box-shadow: var(--box-shadow);
        z-index: 1;
        border: none;
        transition: var(--transition);
    }
    
    .sale-header:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(13, 71, 161, 0.1);
    }
    
    /* Badges colorés pour les statuts avec animation */
    .badge {
        padding: 0.5em 0.8em;
        font-weight: 500;
        letter-spacing: 0.5px;
        border-radius: 30px;
        transition: all 0.3s ease;
    }
    
    .badge:hover {
        transform: scale(1.05);
    }
    
    .badge.bg-success {
        background: linear-gradient(to right, #43A047, #2E7D32) !important;
        box-shadow: 0 4px 10px rgba(76, 175, 80, 0.3);
    }
    
    .badge.bg-warning {
        background: linear-gradient(to right, #FB8C00, #EF6C00) !important;
        box-shadow: 0 4px 10px rgba(255, 152, 0, 0.3);
    }
    
    .badge.bg-danger {
        background: linear-gradient(to right, #E53935, #C62828) !important;
        box-shadow: 0 4px 10px rgba(244, 67, 54, 0.3);
    }
    
    .badge.bg-info {
        background: linear-gradient(to right, #039BE5, #0277BD) !important;
        box-shadow: 0 4px 10px rgba(0, 188, 212, 0.3);
    }
    
    /* Barre de progression pour visualiser l'état des paiements */
    .payment-progress {
        height: 12px;
        background-color: #E3F2FD;
        border-radius: 30px;
        overflow: hidden;
        margin-bottom: 10px;
        box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .payment-progress .progress-bar {
        background: linear-gradient(to right, #1E88E5, #4CAF50);
        height: 100%;
        border-radius: 30px;
        position: relative;
        overflow: hidden;
        transition: width 1s ease;
    }
    
    .payment-progress .progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
        animation: shimmer 2s infinite;
    }
    
    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }
    
    /* Cartes modernes avec animations subtiles */
    .sale-card {
        border: none;
        border-radius: var(--card-radius);
        box-shadow: var(--box-shadow);
        margin-bottom: 1.5rem;
        transition: var(--transition);
        overflow: hidden;
        background-color: white;
        position: relative;
        z-index: 1;
    }
    
    .sale-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(13, 71, 161, 0.12);
    }
    
    .sale-card .card-header {
        background: white;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: 1.25rem 1.5rem;
        font-weight: 600;
        color: var(--primary-dark);
    }
    
    .sale-card .card-header i {
        background: var(--primary-light);
        color: var(--primary-dark);
        padding: 0.5rem;
        border-radius: 50%;
        margin-right: 0.75rem;
        width: 32px;
        height: 32px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    
    .sale-card .card-body {
        padding: 1.5rem;
    }
    
    /* Styles pour les informations */
    .info-item {
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .info-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }
    
    .info-label {
        display: block;
        color: #607D8B;
        font-size: 0.85rem;
        margin-bottom: 0.25rem;
    }
    
    .info-value {
        font-weight: 500;
        color: var(--dark-color);
    }
    
    /* Boutons stylisés */
    .btn {
        border-radius: 30px;
        padding: 0.5rem 1.25rem;
        font-weight: 500;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .btn::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255,255,255,0.2);
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .btn:hover::after {
        transform: translateX(0);
    }
    
    .btn-primary {
        background: var(--gradient-blue);
        border: none;
        box-shadow: 0 4px 15px rgba(30, 136, 229, 0.3);
    }
    
    .btn-success {
        background: linear-gradient(to right, #43A047, #2E7D32);
        border: none;
        box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
    }
    
    .btn-outline-secondary {
        border: 2px solid #90A4AE;
        color: #546E7A;
    }
    
    .btn-outline-secondary:hover {
        background: #90A4AE;
        color: white;
    }
</style>
@endsection

@section('content')
<div class="container-fluid sale-container">
    <!-- En-tête avec les actions -->
    <div class="sale-header d-flex flex-column flex-md-row justify-content-between align-items-md-center">
        <div class="sale-info">
            <div class="d-flex align-items-center mb-3">
                <h1 class="h3 mb-0 me-3 text-primary">Détails de la Vente</h1>
                @if($sale->status)
                    @if($sale->status == 'paid')
                        <span class="badge bg-success"><i class="fas fa-check-circle me-1"></i> Payé</span>
                    @elseif($sale->status == 'pending')
                        <span class="badge bg-warning"><i class="fas fa-clock me-1"></i> En attente</span>
                    @elseif($sale->status == 'cancelled')
                        <span class="badge bg-danger"><i class="fas fa-times-circle me-1"></i> Annulé</span>
                    @else
                        <span class="badge bg-secondary"><i class="fas fa-question-circle me-1"></i> {{ $sale->status }}</span>
                    @endif
                @endif
            </div>
            
            <div class="sale-info-grid">
                <div class="info-chip">
                    <i class="fas fa-hashtag info-icon"></i>
                    <div>
                        <span class="info-label">Référence</span>
                        <span class="info-value">{{ $sale->reference }}</span>
                    </div>
                </div>
                
                <div class="info-chip">
                    <i class="fas fa-calendar-alt info-icon"></i>
                    <div>
                        <span class="info-label">Date</span>
                        <span class="info-value">{{ $sale->created_at->format('d/m/Y à H:i') }}</span>
                    </div>
                </div>
                
                @if($sale->customer_name)
                <div class="info-chip">
                    <i class="fas fa-user-tie info-icon"></i>
                    <div>
                        <span class="info-label">Client</span>
                        <span class="info-value">{{ $sale->customer_name }}</span>
                    </div>
                </div>
                @endif
                
                @if($sale->customer_phone)
                <div class="info-chip">
                    <i class="fas fa-phone info-icon"></i>
                    <div>
                        <span class="info-label">Téléphone</span>
                        <a href="tel:{{ $sale->customer_phone }}" class="info-value text-decoration-none">{{ $sale->customer_phone }}</a>
                    </div>
                </div>
                @endif
            </div>
        </div>
        
        <div class="sale-actions mt-4 mt-md-0">
            <div class="payment-summary p-3 rounded mb-3 bg-white shadow-sm">
                <h6 class="mb-3 d-flex align-items-center fw-bold">
                    <span class="payment-icon me-2"><i class="fas fa-chart-pie"></i></span> 
                    Progression du paiement
                </h6>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="payment-percent {{ $sale->payment_progress > 50 ? 'text-success' : 'text-warning' }}">
                        <i class="fas {{ $sale->payment_progress == 100 ? 'fa-check-circle' : 'fa-chart-pie' }} me-1"></i>
                        {{ number_format($sale->payment_progress ?? 0, 0) }}% payé
                    </span>
                    <span class="fw-bold">{{ number_format($sale->amount_paid ?? 0, 0, ',', ' ') }} / {{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</span>
                </div>
                
                <div class="progress payment-progress">
                    <div class="progress-bar" role="progressbar" style="width: {{ $sale->payment_progress ?? 0 }}%" aria-valuenow="{{ $sale->payment_progress ?? 0 }}" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
            
            <div class="action-buttons d-flex flex-wrap gap-2">
                <a href="{{ route('cashier.sales.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Retour
                </a>
                
                <div class="dropdown ms-2">
                    <button class="btn btn-primary dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-cog me-1"></i> Actions
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end shadow-sm border-0" aria-labelledby="dropdownMenuButton">
                        <li>
                            <a class="dropdown-item" href="#" onclick="window.print()">
                                <i class="fas fa-print me-2 text-primary"></i> Imprimer
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#payment">
                                <i class="fas fa-cash-register me-2 text-success"></i> Encaisser
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Navigation par onglets -->
    <div class="sale-tabs mb-4">
        <ul class="nav nav-pills nav-fill bg-white shadow-sm rounded-pill p-1" id="saleTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active rounded-pill" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab">
                    <i class="fas fa-info-circle me-2"></i> Détails
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link rounded-pill" id="delivery-tab" data-bs-toggle="tab" data-bs-target="#delivery" type="button" role="tab">
                    <i class="fas fa-truck me-2"></i> Livraison
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link rounded-pill" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment" type="button" role="tab">
                    <i class="fas fa-money-bill-wave me-2"></i> Paiement
                </button>
            </li>
        </ul>
    </div>
    
    <style>
        .sale-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .info-chip {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .info-icon {
            background: var(--primary-light);
            color: var(--primary-dark);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        
        .payment-icon {
            background: linear-gradient(135deg, #1E88E5, #0D47A1);
            color: white;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .payment-percent {
            font-weight: 500;
        }
        
        .nav-pills .nav-link {
            color: var(--text-color);
            transition: all 0.3s ease;
        }
        
        .nav-pills .nav-link.active {
            background: var(--gradient-blue);
            box-shadow: 0 4px 10px rgba(30, 136, 229, 0.3);
            transform: translateY(-2px);
        }
        
        .nav-pills .nav-link:not(.active):hover {
            background: rgba(30, 136, 229, 0.1);
            color: var(--primary-color);
        }
    </style>
    
    <div class="tab-content" id="saleTabContent">
        <!-- Onglet Détails -->
        <div class="tab-pane fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
            <div class="row">
                <!-- Informations générales -->
                <div class="col-lg-8">
                    <div class="sale-card">
                        <div class="card-header">
                            <i class="fas fa-info-circle"></i> Informations générales
                        </div>
                        <div class="card-body p-0">
                            <!-- Carte d'information avec design moderne -->
                            <div class="info-dashboard p-4">
                                <div class="row g-4">
                                    <!-- Colonne 1 -->
                                    <div class="col-md-6">
                                        <div class="info-card">
                                            <div class="info-card-header">
                                                <i class="fas fa-file-invoice"></i>
                                                <h6>Détails de la transaction</h6>
                                            </div>
                                            <div class="info-card-body">
                                                <div class="info-row">
                                                    <div class="info-label">Référence</div>
                                                    <div class="info-value fw-bold">{{ $sale->reference }}</div>
                                                </div>
                                                <div class="info-row">
                                                    <div class="info-label">Date</div>
                                                    <div class="info-value">{{ $sale->created_at->format('d/m/Y à H:i') }}</div>
                                                </div>
                                                <div class="info-row">
                                                    <div class="info-label">Créé par</div>
                                                    <div class="info-value">{{ $sale->user ? $sale->user->name : 'N/A' }}</div>
                                                </div>
                                                <div class="info-row">
                                                    <div class="info-label">Ville</div>
                                                    <div class="info-value">{{ $sale->city ? $sale->city->name : 'N/A' }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Colonne 2 -->
                                    <div class="col-md-6">
                                        <div class="info-card">
                                            <div class="info-card-header">
                                                <i class="fas fa-user-circle"></i>
                                                <h6>Informations client</h6>
                                            </div>
                                            <div class="info-card-body">
                                                <div class="info-row">
                                                    <div class="info-label">Client</div>
                                                    <div class="info-value">{{ $sale->customer_name ?: 'Client occasionnel' }}</div>
                                                </div>
                                                
                                                @if($sale->customer_phone)
                                                <div class="info-row">
                                                    <div class="info-label">Téléphone</div>
                                                    <div class="info-value">
                                                        <a href="tel:{{ $sale->customer_phone }}" class="text-decoration-none">{{ $sale->customer_phone }}</a>
                                                    </div>
                                                </div>
                                                @endif
                                                
                                                <div class="info-row">
                                                    <div class="info-label">Statut</div>
                                                    <div class="info-value">
                                                        @if($sale->status == 'paid')
                                                            <span class="badge bg-success"><i class="fas fa-check-circle me-1"></i> Payé</span>
                                                        @elseif($sale->status == 'pending')
                                                            <span class="badge bg-warning"><i class="fas fa-clock me-1"></i> En attente</span>
                                                        @elseif($sale->status == 'cancelled')
                                                            <span class="badge bg-danger"><i class="fas fa-times-circle me-1"></i> Annulé</span>
                                                        @else
                                                            <span class="badge bg-secondary"><i class="fas fa-question-circle me-1"></i> {{ $sale->status }}</span>
                                                        @endif
                                                    </div>
                                                </div>
                                                
                                                <div class="info-row">
                                                    <div class="info-label">Livraison</div>
                                                    <div class="info-value">
                                                        @if($sale->delivery_status == 'delivered')
                                                            <span class="badge bg-success"><i class="fas fa-truck-loading me-1"></i> Livrée</span>
                                                        @elseif($sale->delivery_status == 'in_transit')
                                                            <span class="badge bg-info"><i class="fas fa-shipping-fast me-1"></i> En cours</span>
                                                        @elseif($sale->delivery_status == 'pending')
                                                            <span class="badge bg-warning"><i class="fas fa-dolly me-1"></i> En attente</span>
                                                        @else
                                                            <span class="badge bg-secondary"><i class="fas fa-question-circle me-1"></i> {{ $sale->delivery_status ?: 'Non défini' }}</span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <style>
                        .info-dashboard {
                            background-color: #FAFBFF;
                        }
                        
                        .info-card {
                            background: white;
                            border-radius: var(--card-radius);
                            box-shadow: 0 3px 10px rgba(13, 71, 161, 0.06);
                            height: 100%;
                            transition: var(--transition);
                        }
                        
                        .info-card:hover {
                            transform: translateY(-3px);
                            box-shadow: 0 5px 15px rgba(13, 71, 161, 0.1);
                        }
                        
                        .info-card-header {
                            display: flex;
                            align-items: center;
                            padding: 1rem;
                            border-bottom: 1px solid rgba(0,0,0,0.05);
                            background: linear-gradient(to right, rgba(30, 136, 229, 0.05), rgba(13, 71, 161, 0.02));
                        }
                        
                        .info-card-header i {
                            background: var(--gradient-blue);
                            color: white;
                            width: 32px;
                            height: 32px;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-right: 0.75rem;
                        }
                        
                        .info-card-header h6 {
                            margin: 0;
                            font-weight: 600;
                            color: var(--primary-dark);
                        }
                        
                        .info-card-body {
                            padding: 1rem;
                        }
                        
                        .info-row {
                            display: flex;
                            flex-direction: column;
                            margin-bottom: 0.75rem;
                            padding-bottom: 0.75rem;
                            border-bottom: 1px solid rgba(0,0,0,0.05);
                        }
                        
                        .info-row:last-child {
                            margin-bottom: 0;
                            padding-bottom: 0;
                            border-bottom: none;
                        }
                        
                        .info-row .info-label {
                            font-size: 0.8rem;
                            color: #607D8B;
                            margin-bottom: 0.25rem;
                        }
                        
                        .info-row .info-value {
                            font-weight: 500;
                        }
                    </style>

                    <!-- Détails du produit -->
                    <div class="sale-card product-card">
                        <div class="card-header">
                            <i class="fas fa-box-open"></i> Détails du produit
                        </div>
                        <div class="card-body p-0">
                            <div class="product-hero p-4">
                                <div class="row align-items-center">
                                    <div class="col-md-2 text-center mb-3 mb-md-0">
                                        <div class="product-icon-wrapper">
                                            <div class="product-icon">
                                                <i class="fas fa-box"></i>
                                            </div>
                                            <div class="product-icon-ring"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-7">
                                        <h4 class="product-name mb-2">{{ $sale->product ? $sale->product->name : 'Ciment' }}</h4>
                                        <p class="product-description mb-0">{{ $sale->product ? $sale->product->description : 'Ciment de qualité supérieure' }}</p>
                                    </div>
                                    <div class="col-md-3 text-md-end">
                                        <div class="product-quantity">
                                            <span class="quantity-label">Quantité</span>
                                            <span class="quantity-value">{{ number_format($sale->quantity, 2, ',', ' ') }} T</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="product-details p-4">
                                <div class="product-details-grid">
                                    <!-- Détail 1 : Prix unitaire -->
                                    <div class="product-detail-item">
                                        <div class="detail-icon">
                                            <i class="fas fa-tag"></i>
                                        </div>
                                        <div class="detail-content">
                                            <span class="detail-label">Prix unitaire</span>
                                            <span class="detail-value">{{ number_format($sale->unit_price, 0, ',', ' ') }} FCFA</span>
                                        </div>
                                    </div>
                                    
                                    <!-- Détail 2 : Remise -->
                                    <div class="product-detail-item">
                                        <div class="detail-icon">
                                            <i class="fas fa-percentage"></i>
                                        </div>
                                        <div class="detail-content">
                                            <span class="detail-label">Remise</span>
                                            <span class="detail-value">
                                                @if($sale->discount_per_ton > 0)
                                                    {{ number_format($sale->discount_per_ton, 0, ',', ' ') }} FCFA/T
                                                @else
                                                    Aucune remise
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <!-- Détail 3 : Montant total -->
                                    <div class="product-detail-item total-amount">
                                        <div class="detail-icon">
                                            <i class="fas fa-money-bill-wave"></i>
                                        </div>
                                        <div class="detail-content">
                                            <span class="detail-label">Montant total</span>
                                            <span class="detail-value">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <style>
                                .product-card {
                                    overflow: visible;
                                }
                                
                                .product-hero {
                                    background: linear-gradient(to right, rgba(30, 136, 229, 0.05), rgba(13, 71, 161, 0.02));
                                    border-bottom: 1px solid rgba(0,0,0,0.05);
                                }
                                
                                .product-icon-wrapper {
                                    position: relative;
                                    width: 80px;
                                    height: 80px;
                                    margin: 0 auto;
                                }
                                
                                .product-icon {
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                    right: 0;
                                    bottom: 0;
                                    background: var(--gradient-blue);
                                    color: white;
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-size: 1.5rem;
                                    box-shadow: 0 5px 15px rgba(13, 71, 161, 0.3);
                                    z-index: 2;
                                }
                                
                                .product-icon-ring {
                                    position: absolute;
                                    top: -5px;
                                    left: -5px;
                                    right: -5px;
                                    bottom: -5px;
                                    border: 2px solid rgba(30, 136, 229, 0.3);
                                    border-radius: 50%;
                                    animation: pulse 2s infinite;
                                }
                                
                                @keyframes pulse {
                                    0% { transform: scale(1); opacity: 1; }
                                    50% { transform: scale(1.1); opacity: 0.5; }
                                    100% { transform: scale(1); opacity: 1; }
                                }
                                
                                .product-name {
                                    color: var(--primary-dark);
                                    font-weight: 600;
                                }
                                
                                .product-description {
                                    color: #607D8B;
                                }
                                
                                .product-quantity {
                                    background: white;
                                    padding: 0.75rem 1rem;
                                    border-radius: var(--border-radius);
                                    box-shadow: 0 3px 10px rgba(13, 71, 161, 0.06);
                                    display: inline-flex;
                                    flex-direction: column;
                                }
                                
                                .quantity-label {
                                    font-size: 0.75rem;
                                    color: #607D8B;
                                    margin-bottom: 0.25rem;
                                }
                                
                                .quantity-value {
                                    font-weight: 700;
                                    color: var(--primary-dark);
                                    font-size: 1.1rem;
                                }
                                
                                .product-details-grid {
                                    display: grid;
                                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                                    gap: 1.5rem;
                                }
                                
                                .product-detail-item {
                                    display: flex;
                                    align-items: center;
                                    gap: 1rem;
                                    padding: 1rem;
                                    background: white;
                                    border-radius: var(--border-radius);
                                    box-shadow: 0 3px 10px rgba(13, 71, 161, 0.06);
                                    transition: var(--transition);
                                }
                                
                                .product-detail-item:hover {
                                    transform: translateY(-3px);
                                    box-shadow: 0 5px 15px rgba(13, 71, 161, 0.1);
                                }
                                
                                .detail-icon {
                                    width: 40px;
                                    height: 40px;
                                    border-radius: 50%;
                                    background: var(--primary-light);
                                    color: var(--primary-dark);
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    flex-shrink: 0;
                                }
                                
                                .total-amount .detail-icon {
                                    background: var(--gradient-blue);
                                    color: white;
                                }
                                
                                .detail-content {
                                    display: flex;
                                    flex-direction: column;
                                }
                                
                                .detail-label {
                                    font-size: 0.8rem;
                                    color: #607D8B;
                                    margin-bottom: 0.25rem;
                                }
                                
                                .detail-value {
                                    font-weight: 600;
                                    color: var(--dark-color);
                                }
                                
                                .total-amount .detail-value {
                                    color: var(--primary-dark);
                                    font-size: 1.1rem;
                                }
                            </style>
                            
                            <div class="additional-info-section mt-4">
                                <div class="row g-4">
                                    <div class="col-md-6">
                                        <div class="info-card notes-card h-100">
                                            <div class="info-card-header">
                                                <i class="fas fa-clipboard-list"></i>
                                                <h6>Informations supplémentaires</h6>
                                            </div>
                                            <div class="info-card-body">
                                                @if($sale->notes || $sale->delivery_address)
                                                    @if($sale->notes)
                                                    <div class="note-item">
                                                        <div class="note-icon">
                                                            <i class="fas fa-comment-alt"></i>
                                                        </div>
                                                        <div class="note-content">
                                                            <span class="note-label">Notes</span>
                                                            <p class="note-text">{{ $sale->notes }}</p>
                                                        </div>
                                                    </div>
                                                    @endif
                                                    
                                                    @if($sale->delivery_address)
                                                    <div class="note-item">
                                                        <div class="note-icon">
                                                            <i class="fas fa-map-marker-alt"></i>
                                                        </div>
                                                        <div class="note-content">
                                                            <span class="note-label">Adresse de livraison</span>
                                                            <p class="note-text">{{ $sale->delivery_address }}</p>
                                                        </div>
                                                    </div>
                                                    @endif
                                                @else
                                                    <div class="empty-notes">
                                                        <i class="fas fa-info-circle mb-3"></i>
                                                        <p>Aucune information supplémentaire disponible pour cette vente.</p>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="info-card financial-card h-100">
                                            <div class="info-card-header">
                                                <i class="fas fa-file-invoice-dollar"></i>
                                                <h6>Résumé financier</h6>
                                            </div>
                                            <div class="info-card-body p-0">
                                                <div class="financial-summary">
                                                    <div class="financial-item">
                                                        <div class="financial-label">Total</div>
                                                        <div class="financial-value total">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</div>
                                                    </div>
                                                    
                                                    <div class="financial-item">
                                                        <div class="financial-label">Payé</div>
                                                        <div class="financial-value paid">{{ number_format($sale->amount_paid ?? 0, 0, ',', ' ') }} FCFA</div>
                                                    </div>
                                                    
                                                    @if(($sale->total_amount - ($sale->amount_paid ?? 0)) > 0)
                                                    <div class="financial-item">
                                                        <div class="financial-label">Restant à payer</div>
                                                        <div class="financial-value remaining">{{ number_format($sale->total_amount - ($sale->amount_paid ?? 0), 0, ',', ' ') }} FCFA</div>
                                                    </div>
                                                    @endif
                                                </div>
                                                
                                                @if(($sale->total_amount - ($sale->amount_paid ?? 0)) > 0)
                                                    @if($sale->status != 'cancelled' && $sale->admin_validation_status != 'rejected')
                                                        <div class="payment-action">
                                                            <a href="{{ route('cashier.payments.process', $sale->id) }}" class="btn btn-success w-100">
                                                                <i class="fas fa-cash-register me-2"></i> Encaisser le paiement
                                                            </a>
                                                        </div>
                                                    @elseif($sale->status == 'cancelled' || $sale->admin_validation_status == 'rejected')
                                                        <div class="payment-action">
                                                            <div class="payment-blocked">
                                                                <i class="fas fa-ban"></i>
                                                                <p>Paiement non autorisé - Vente {{ $sale->status == 'cancelled' ? 'annulée' : 'rejetée' }}</p>
                                                            </div>
                                                        </div>
                                                    @endif
                                                @else
                                                    <div class="payment-action">
                                                        <div class="payment-complete">
                                                            <i class="fas fa-check-circle"></i>
                                                            <p>Paiement complet</p>
                                                        </div>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <style>
                                .additional-info-section {
                                    margin-bottom: 1rem;
                                }
                                
                                .notes-card .info-card-body {
                                    padding: 1.25rem;
                                }
                                
                                .note-item {
                                    display: flex;
                                    gap: 1rem;
                                    margin-bottom: 1.25rem;
                                }
                                
                                .note-item:last-child {
                                    margin-bottom: 0;
                                }
                                
                                .note-icon {
                                    width: 36px;
                                    height: 36px;
                                    border-radius: 50%;
                                    background: var(--primary-light);
                                    color: var(--primary-dark);
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    flex-shrink: 0;
                                }
                                
                                .note-content {
                                    flex: 1;
                                }
                                
                                .note-label {
                                    display: block;
                                    font-size: 0.8rem;
                                    color: #607D8B;
                                    margin-bottom: 0.25rem;
                                }
                                
                                .note-text {
                                    margin: 0;
                                    color: var(--dark-color);
                                }
                                
                                .empty-notes {
                                    text-align: center;
                                    color: #90A4AE;
                                    padding: 2rem 1rem;
                                }
                                
                                .empty-notes i {
                                    font-size: 2rem;
                                    display: block;
                                }
                                
                                .empty-notes p {
                                    margin: 0;
                                }
                                
                                .financial-card .info-card-body {
                                    padding: 0;
                                }
                                
                                .financial-summary {
                                    padding: 1.25rem;
                                }
                                
                                .financial-item {
                                    display: flex;
                                    justify-content: space-between;
                                    padding: 0.75rem 0;
                                    border-bottom: 1px solid rgba(0,0,0,0.05);
                                }
                                
                                .financial-item:last-child {
                                    border-bottom: none;
                                }
                                
                                .financial-label {
                                    color: #607D8B;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.5rem;
                                }
                                
                                .financial-value {
                                    font-weight: 600;
                                }
                                
                                .financial-value.total {
                                    color: var(--primary-dark);
                                    font-size: 1.1rem;
                                }
                                
                                .financial-value.paid {
                                    color: var(--secondary-color);
                                }
                                
                                .financial-value.remaining {
                                    color: var(--warning-color);
                                }
                                
                                .payment-action {
                                    padding: 1.25rem;
                                    background: linear-gradient(to right, rgba(30, 136, 229, 0.05), rgba(13, 71, 161, 0.02));
                                    border-top: 1px solid rgba(0,0,0,0.05);
                                }
                                
                                .payment-blocked, .payment-complete {
                                    text-align: center;
                                    padding: 1rem;
                                    border-radius: var(--border-radius);
                                }
                                
                                .payment-blocked {
                                    background-color: rgba(244, 67, 54, 0.1);
                                    color: var(--danger-color);
                                }
                                
                                .payment-complete {
                                    background-color: rgba(76, 175, 80, 0.1);
                                    color: var(--secondary-color);
                                }
                                
                                .payment-blocked i, .payment-complete i {
                                    font-size: 1.5rem;
                                    margin-bottom: 0.5rem;
                                }
                                
                                .payment-blocked p, .payment-complete p {
                                    margin: 0;
                                    font-weight: 500;
                                }
                            </style>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <!-- Validation administrative -->
                    @if($sale->discount_per_ton > 0 || $sale->price_modified)
                        <div class="sale-card">
                            <div class="card-header">
                                <i class="fas fa-check-circle"></i> Validation administrative
                            </div>
                            <div class="card-body">
                                <div class="info-item">
                                    <span class="info-label">Statut:</span>
                                    <span class="info-value">
                                        @if($sale->admin_validation_status == 'pending')
                                            <span class="badge bg-warning">En attente</span>
                                        @elseif($sale->admin_validation_status == 'approved')
                                            <span class="badge bg-success">Approuvée</span>
                                        @elseif($sale->admin_validation_status == 'rejected')
                                            <span class="badge bg-danger">Rejetée</span>
                                        @else
                                            <span class="badge bg-secondary">{{ $sale->admin_validation_status }}</span>
                                        @endif
                                    </span>
                                </div>
                                @if($sale->admin_note)
                                    <div class="info-item">
                                        <span class="info-label">Note:</span>
                                        <span class="info-value">{{ $sale->admin_note }}</span>
                                    </div>
                                @endif
                                <div class="alert alert-info mt-2 mb-0">
                                    <small><i class="fas fa-info-circle"></i> Cette vente nécessite une validation administrative car elle comporte 
                                    @if($sale->discount_per_ton > 0 && $sale->price_modified)
                                        une remise et une modification de prix.
                                    @elseif($sale->discount_per_ton > 0)
                                        une remise.
                                    @elseif($sale->price_modified)
                                        une modification de prix.
                                    @endif
                                    </small>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- Onglet Livraison -->
        <div class="tab-pane fade" id="delivery" role="tabpanel" aria-labelledby="delivery-tab">
            <div class="row">
                <div class="col-lg-6">
                    <!-- Informations du véhicule -->
                    <div class="sale-card">
                        <div class="card-header">
                            <i class="fas fa-truck"></i> Informations du véhicule
                        </div>
                        <div class="card-body">
                            <div class="vehicle-info">
                                <h6 class="mb-3"><i class="fas fa-truck"></i> Véhicule assigné</h6>
                                <div class="info-item">
                                    <span class="info-label">Immatriculation:</span>
                                    <span class="info-value">
                                        {{ $supplyCity && $supplyCity->vehicle ? $supplyCity->vehicle->registration_number : ($sale->vehicle ? $sale->vehicle->registration_number : 'Non assigné') }}
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Marque:</span>
                                    <span class="info-value">
                                        {{ $sale->vehicle ? $sale->vehicle->brand : 'N/A' }}
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Modèle:</span>
                                    <span class="info-value">
                                        {{ $sale->vehicle ? $sale->vehicle->model : 'N/A' }}
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Capacité:</span>
                                    <span class="info-value">
                                        {{ number_format($supplyCity && $supplyCity->vehicle && $supplyCity->vehicle->capacity ? $supplyCity->vehicle->capacity->capacity : ($sale->vehicle && $sale->vehicle->capacity ? $sale->vehicle->capacity->capacity : 0), 2, ',', ' ') }} T
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <!-- Informations du chauffeur -->
                    <div class="sale-card">
                        <div class="card-header">
                            <i class="fas fa-user"></i> Informations du chauffeur
                        </div>
                        <div class="card-body">
                            <div class="driver-info">
                                <h6 class="mb-3"><i class="fas fa-user"></i> Chauffeur assigné</h6>
                                <div class="info-item">
                                    <span class="info-label">Nom:</span>
                                    <span class="info-value">
                                        {{ $supplyCity && $supplyCity->driver ? $supplyCity->driver->first_name . ' ' . $supplyCity->driver->last_name : ($sale->driver ? $sale->driver->first_name . ' ' . $sale->driver->last_name : 'Non assigné') }}
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Téléphone:</span>
                                    <span class="info-value">
                                        @if($supplyCity && $supplyCity->driver && $supplyCity->driver->phone)
                                            <a href="tel:{{ $supplyCity->driver->phone }}" class="text-decoration-none">{{ $supplyCity->driver->phone }}</a>
                                        @elseif($sale->driver && $sale->driver->phone)
                                            <a href="tel:{{ $sale->driver->phone }}" class="text-decoration-none">{{ $sale->driver->phone }}</a>
                                        @else
                                            N/A
                                        @endif
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Statut:</span>
                                    <span class="info-value">
                                        @if($sale->driver && $sale->driver->is_active)
                                            <span class="badge bg-success">Actif</span>
                                        @else
                                            <span class="badge bg-secondary">Inactif</span>
                                        @endif
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Informations de l'approvisionnement -->
                <div class="col-lg-12 mt-4">
                    <div class="sale-card">
                        <div class="card-header">
                            <i class="fas fa-warehouse"></i> Informations de l'approvisionnement
                        </div>
                        <div class="card-body">
                            @if($sale->supply)
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-item">
                                            <span class="info-label"><i class="fas fa-hashtag text-primary me-1"></i> Référence:</span>
                                            <span class="info-value">{{ $sale->supply->reference ?? 'N/A' }}</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label"><i class="fas fa-calendar-alt text-primary me-1"></i> Date:</span>
                                            <span class="info-value">{{ $sale->supply->created_at ? $sale->supply->created_at->format('d/m/Y') : 'N/A' }}</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        @if($supplyCity)
                                            <div class="info-item">
                                                <span class="info-label"><i class="fas fa-weight-hanging text-primary me-1"></i> Quantité totale:</span>
                                                <span class="info-value">{{ number_format($supplyCity->quantity ?? 0, 2, ',', ' ') }} T</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label"><i class="fas fa-balance-scale text-primary me-1"></i> Quantité restante:</span>
                                                <span class="info-value">{{ number_format($supplyCity->remaining_quantity ?? 0, 2, ',', ' ') }} T</span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @else
                                <p class="text-muted">Aucune information d'approvisionnement disponible</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Onglet Paiement -->
        <div class="tab-pane fade" id="payment" role="tabpanel" aria-labelledby="payment-tab">
            <div class="row">
                <div class="col-lg-6">
                    <div class="sale-card">
                        <div class="card-header">
                            <i class="fas fa-file-invoice-dollar"></i> Résumé financier
                        </div>
                        <div class="card-body">
                            <div class="financial-summary p-0 box-shadow-none">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span><i class="fas fa-receipt text-primary me-2"></i>Montant avant remise:</span>
                                    <span>{{ number_format($sale->total_before_discount ?? $sale->total_amount, 0, ',', ' ') }} FCFA</span>
                                </div>
                                
                                @if($sale->discount_total > 0)
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <span><i class="fas fa-percentage text-success me-2"></i>Remise:</span>
                                        <span class="text-success">-{{ number_format($sale->discount_total, 0, ',', ' ') }} FCFA</span>
                                    </div>
                                @endif
                                
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span><i class="fas fa-money-bill-wave text-primary me-2"></i>Montant total:</span>
                                    <span class="fw-bold">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</span>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span><i class="fas fa-check-circle text-success me-2"></i>Montant payé:</span>
                                    <span class="text-success">{{ number_format($sale->amount_paid ?? 0, 0, ',', ' ') }} FCFA</span>
                                </div>
                                
                                <hr>
                                
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span><i class="fas fa-exclamation-circle text-danger me-2"></i>Reste à payer:</span>
                                    <span class="fw-bold text-danger fs-5">{{ number_format(($sale->total_amount - ($sale->amount_paid ?? 0)), 0, ',', ' ') }} FCFA</span>
                                </div>
                            </div>
                            
                            <!-- Barre de progression du paiement animée -->
                            <div class="progress payment-progress mt-3">
                                <div class="progress-bar" role="progressbar" 
                                    style="width: {{ $sale->payment_progress ?? 0 }}%" 
                                    aria-valuenow="{{ $sale->payment_progress ?? 0 }}" aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                            
                            <div class="text-center mt-2">
                                <span class="badge {{ $sale->payment_progress > 50 ? 'bg-success' : 'bg-warning' }} p-2">
                                    <i class="fas {{ $sale->payment_progress == 100 ? 'fa-check-circle' : 'fa-chart-pie' }} me-1"></i>
                                    {{ number_format($sale->payment_progress ?? 0, 0) }}% payé
                                </span>
                            </div>
                            
                            @if(($sale->total_amount - ($sale->amount_paid ?? 0)) > 0 && $sale->status != 'cancelled' && $sale->admin_validation_status != 'rejected')
                                <div class="text-center mt-4">
                                    <a href="{{ route('cashier.payments.process', $sale->id) }}" class="btn btn-success">
                                        <i class="fas fa-cash-register me-1"></i> Effectuer un paiement
                                    </a>
                                </div>
                            @elseif($sale->status == 'cancelled' || $sale->admin_validation_status == 'rejected')
                                <div class="text-center mt-3">
                                    <div class="alert alert-danger py-2">
                                        <i class="fas fa-ban me-1"></i> Paiement non autorisé - Vente {{ $sale->status == 'cancelled' ? 'annulée' : 'rejetée' }}
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="sale-card">
                        <div class="card-header">
                            <i class="fas fa-history"></i> Historique des paiements
                        </div>
                        <div class="card-body">
                            <!-- Cette section pourrait être complétée avec les données réelles des paiements -->
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> L'historique des paiements sera affiché ici lorsque des paiements seront effectués.
                            </div>
                            
                            <!-- Exemple de tableau d'historique des paiements -->
                            <table class="table table-striped mt-3">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Montant</th>
                                        <th>Méthode</th>
                                        <th>Statut</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Si aucun paiement n'existe encore -->
                                    <tr>
                                        <td colspan="4" class="text-center">Aucun paiement enregistré</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
