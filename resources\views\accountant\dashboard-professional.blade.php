@extends('layouts.accountant')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="{{ asset('css/accountant-pro-dashboard-base.css') }}?v={{ time() }}">
<link rel="stylesheet" href="{{ asset('css/accountant-pro-dashboard-components.css') }}?v={{ time() }}">
<link rel="stylesheet" href="{{ asset('css/accountant-pro-dashboard-effects.css') }}?v={{ time() }}">
<link rel="stylesheet" href="{{ asset('css/accountant-pro-dashboard-responsive.css') }}?v={{ time() }}">
<link rel="stylesheet" href="{{ asset('css/accountant-dashboard-advanced.css') }}?v={{ time() }}">
<style>
    /* Styles optimisés pour les cartes de la bannière */
    .dashboard-header-data {
        display: flex;
        justify-content: space-between;
        width: 100%;
        gap: 10px; /* réduire l'écart entre les cartes */
    }
    
    .header-data-item {
        flex: 1;
        background: linear-gradient(135deg, #2563eb, #3b82f6); /* Fond dégradé bleu */
        border-radius: 10px;
        padding: 12px 15px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(31, 45, 61, 0.15);
        min-width: 150px; /* Largeur minimale pour contenir des chiffres volumineux */
        transition: all 0.3s ease;
        border-left: 4px solid #93c5fd;
    }
    
    .header-data-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 18px rgba(31, 45, 61, 0.1);
    }
    
    .header-data-value {
        font-size: 1.25rem; /* Police encore plus réduite */
        font-weight: 600;
        color: #ffffff; /* Changement en blanc */
        margin-bottom: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
    
    .header-data-label {
        font-size: 0.7rem;
        color: #ffffff; /* Changement en blanc */
        text-transform: uppercase;
        letter-spacing: 0.5px;
        white-space: nowrap;
        opacity: 0.9;
    }
    
    /* Style pour les cartes statistiques */
    .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(36, 97, 255, 0.15);
    }
    
    .stat-value {
        font-size: 1.4rem; /* Police réduite */
        font-weight: 600;
        transition: all 0.3s ease;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    /* Style pour les animations */
    .updated {
        animation: highlight-value 2s ease;
    }
    
    @keyframes highlight-value {
        0% {
            color: #0d6efd;
            text-shadow: 0 0 8px rgba(13, 110, 253, 0.4);
            transform: scale(1.05);
        }
        100% {
            color: inherit;
            text-shadow: none;
            transform: scale(1);
        }
    }
    
    /* Media queries pour assurer la responsivité */
    @media (max-width: 992px) {
        .header-data-value {
            font-size: 1.3rem;
        }
        
        .stat-value {
            font-size: 1.3rem;
        }
    }
    
    @media (max-width: 576px) {
        .header-data-value {
            font-size: 1.2rem;
        }
        
        .stat-value {
            font-size: 1.2rem;
        }
    }
</style>
@endpush

@section('title', 'Tableau de Bord Professionnel')

@section('content')
<!-- Conteneur principal du dashboard -->
<div class="accountant-dashboard-wrapper">
    <div class="container dashboard-container">
        <!-- Le panneau de débogage a été supprimé -->
        <!-- En-tête du dashboard avec fond dégradé bleu -->
        <div class="dashboard-header fade-in">
            <div class="header-bg-animation">
                <div class="bg-circle bg-circle-1"></div>
                <div class="bg-circle bg-circle-2"></div>
                <div class="bg-circle bg-circle-3"></div>
            </div>
            <div class="dashboard-header-content">
                <div class="row align-items-center">
                    <div class="col-lg-7">
                        <div class="date-display mb-2 slide-in-left" style="animation-delay: 0.1s;">
                            <i class="fas fa-calendar-alt me-2"></i>{{ now()->format('l, d F Y') }}
                        </div>
                        <h1 class="dashboard-title slide-in-left" style="animation-delay: 0.2s;">Tableau de bord financier</h1>
                        <p class="dashboard-subtitle slide-in-left" style="animation-delay: 0.3s;">Bienvenue dans votre espace de gestion financière et comptable.</p>
                        
                        <div class="header-actions slide-in-left" style="animation-delay: 0.4s;">
                            <a href="{{ route('accountant.reports.index') }}" class="header-action-btn">
                                <i class="fas fa-chart-bar"></i> Rapports
                            </a>
                            <a href="{{ route('accountant.sales.index', ['payment_status' => 'partial']) }}" class="header-action-btn">
                                <i class="fas fa-hand-holding-usd"></i> Recouvrements
                            </a>
                            <a href="{{ route('accountant.supplies.index') }}" class="header-action-btn">
                                <i class="fas fa-truck"></i> Approvisionnements
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-5 d-none d-lg-block">
                        <div class="dashboard-header-data slide-in-right">
                            <div class="header-data-item">
                                <div class="header-data-value">{{ number_format($totalRevenue) }}</div>
                                <div class="header-data-label">Chiffre d'affaires total</div>
                            </div>
                            <div class="header-data-item">
                                <div class="header-data-value">{{ number_format($totalPayments) }}</div>
                                <div class="header-data-label">Paiements reçus</div>
                            </div>
                            <div class="header-data-item">
                                <div class="header-data-value">{{ number_format($pendingPayments) }}</div>
                                <div class="header-data-label">À recouvrer</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Système de Filtres Avancés -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="advanced-chart-container">
                    <div class="advanced-chart-header">
                        <div class="advanced-chart-title">
                            <i class="fas fa-filter"></i> Filtres et Contrôles
                        </div>
                        <div class="filter-controls">
                            <button class="btn btn-sm btn-outline-primary" id="refreshData">
                                <i class="fas fa-sync-alt"></i> Actualiser
                            </button>
                            <button class="btn btn-sm btn-outline-success" id="exportData">
                                <i class="fas fa-download"></i> Exporter
                            </button>
                        </div>
                    </div>
                    <div class="filters-container">
                        <div class="row g-3">
                            <!-- Filtre par période -->
                            <div class="col-lg-3 col-md-6">
                                <label class="filter-label">Période</label>
                                <select class="form-select advanced-filter" id="periodFilter" data-filter="period">
                                    <option value="today">Aujourd'hui</option>
                                    <option value="week">Cette semaine</option>
                                    <option value="month" selected>Ce mois</option>
                                    <option value="quarter">Ce trimestre</option>
                                    <option value="year">Cette année</option>
                                    <option value="custom">Personnalisé</option>
                                </select>
                            </div>

                            <!-- Filtre par statut de paiement -->
                            <div class="col-lg-3 col-md-6">
                                <label class="filter-label">Statut de paiement</label>
                                <select class="form-select advanced-filter" id="paymentStatusFilter" data-filter="payment_status">
                                    <option value="all">Tous</option>
                                    <option value="paid">Payé</option>
                                    <option value="partial">Partiel</option>
                                    <option value="unpaid">Impayé</option>
                                </select>
                            </div>

                            <!-- Filtre par montant -->
                            <div class="col-lg-3 col-md-6">
                                <label class="filter-label">Montant minimum</label>
                                <input type="number" class="form-control advanced-filter" id="amountFilter" data-filter="min_amount" placeholder="0">
                            </div>

                            <!-- Filtre par client -->
                            <div class="col-lg-3 col-md-6">
                                <label class="filter-label">Client</label>
                                <select class="form-select advanced-filter" id="customerFilter" data-filter="customer_id">
                                    <option value="all">Tous les clients</option>
                                    <!-- Options dynamiques chargées via AJAX -->
                                </select>
                            </div>
                        </div>

                        <!-- Filtres de date personnalisés -->
                        <div class="row g-3 mt-2" id="customDateFilters" style="display: none;">
                            <div class="col-md-6">
                                <label class="filter-label">Date de début</label>
                                <input type="date" class="form-control advanced-filter" id="startDateFilter" data-filter="start_date">
                            </div>
                            <div class="col-md-6">
                                <label class="filter-label">Date de fin</label>
                                <input type="date" class="form-control advanced-filter" id="endDateFilter" data-filter="end_date">
                            </div>
                        </div>

                        <!-- Indicateurs de filtre actif -->
                        <div class="active-filters mt-3" id="activeFilters" style="display: none;">
                            <span class="filter-label">Filtres actifs:</span>
                            <div class="filter-tags" id="filterTags"></div>
                            <button class="btn btn-sm btn-outline-secondary ms-2" id="clearFilters">
                                <i class="fas fa-times"></i> Effacer tout
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Widgets KPI Avancés et Interactifs -->
        <div class="row g-4 mb-4 sequential-fade-in">
            <!-- Widget Ventes avec Sparkline -->
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="advanced-stat-card" data-tooltip="Cliquez pour voir les détails des ventes">
                    <div class="advanced-stat-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="advanced-stat-title">Total des ventes</div>
                    <div class="advanced-stat-value animated-counter" data-target="{{ $totalSales }}" data-type="number" data-stat="totalSales">{{ number_format($totalSales) }}</div>
                    <div class="advanced-stat-trend {{ ($performanceMetrics['salesGrowth'] ?? 0) >= 0 ? 'up' : 'down' }}">
                        <i class="fas fa-arrow-{{ ($performanceMetrics['salesGrowth'] ?? 0) >= 0 ? 'up' : 'down' }}"></i>
                        {{ number_format(abs($performanceMetrics['salesGrowth'] ?? 0), 1) }}% ce mois
                    </div>
                    <div class="mini-chart">
                        <canvas id="salesSparkline" width="100" height="30"></canvas>
                    </div>
                </div>
            </div>

            <!-- Widget Chiffre d'Affaires avec Comparaison -->
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="advanced-stat-card success" data-tooltip="Évolution du chiffre d'affaires">
                    <div class="advanced-stat-icon success">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="advanced-stat-title">Chiffre d'affaires</div>
                    <div class="advanced-stat-value animated-counter" data-target="{{ $totalRevenue }}" data-type="currency" data-stat="totalRevenue">{{ number_format($totalRevenue) }} F</div>
                    <div class="advanced-stat-trend up">
                        <i class="fas fa-arrow-up"></i> {{ number_format($performanceMetrics['currentMonthSales'] ?? 0) }} F ce mois
                    </div>
                    <div class="progress-indicator">
                        <div class="progress-bar" style="width: {{ min(100, ($performanceMetrics['currentMonthSales'] ?? 0) / max(1, $performanceMetrics['previousMonthSales'] ?? 1) * 100) }}%"></div>
                    </div>
                </div>
            </div>

            <!-- Widget Taux de Conversion -->
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="advanced-stat-card info" data-tooltip="Pourcentage de ventes payées">
                    <div class="advanced-stat-icon info">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="advanced-stat-title">Taux de conversion</div>
                    <div class="advanced-stat-value animated-counter" data-target="{{ $performanceMetrics['conversionRate'] ?? 0 }}" data-type="percentage" data-stat="conversionRate">{{ number_format($performanceMetrics['conversionRate'] ?? 0, 1) }}%</div>
                    <div class="advanced-stat-trend {{ ($performanceMetrics['conversionRate'] ?? 0) >= 70 ? 'up' : 'neutral' }}">
                        <i class="fas fa-{{ ($performanceMetrics['conversionRate'] ?? 0) >= 70 ? 'check-circle' : 'exclamation-triangle' }}"></i>
                        {{ ($performanceMetrics['conversionRate'] ?? 0) >= 70 ? 'Excellent' : 'À améliorer' }}
                    </div>
                    <div class="circular-progress" data-percentage="{{ $performanceMetrics['conversionRate'] ?? 0 }}">
                        <svg viewBox="0 0 36 36" class="circular-chart">
                            <path class="circle-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                            <path class="circle" stroke-dasharray="{{ $performanceMetrics['conversionRate'] ?? 0 }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Widget Paiements en Attente -->
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="advanced-stat-card warning" data-tooltip="Montant des paiements en attente">
                    <div class="advanced-stat-icon warning">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="advanced-stat-title">Montants à recouvrer</div>
                    <div class="advanced-stat-value animated-counter" data-target="{{ $pendingPayments }}" data-type="currency" data-stat="pendingPayments">{{ number_format($pendingPayments) }} F</div>
                    <div class="advanced-stat-trend {{ $pendingPayments > 0 ? 'down' : 'up' }}">
                        <i class="fas fa-{{ $pendingPayments > 0 ? 'exclamation-triangle' : 'check-circle' }}"></i>
                        {{ $trendAnalysis['overduePayments'] ?? 0 }} factures en retard
                    </div>
                    <div class="alert-indicator {{ $pendingPayments > 1000000 ? 'high' : ($pendingPayments > 500000 ? 'medium' : 'low') }}">
                        <div class="pulse-dot"></div>
                        {{ $pendingPayments > 1000000 ? 'Urgent' : ($pendingPayments > 500000 ? 'Attention' : 'Normal') }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Métriques de Performance Avancées -->
        <div class="row g-4 mb-4">
            <!-- Panier Moyen -->
            <div class="col-lg-3 col-md-6">
                <div class="advanced-stat-card info">
                    <div class="advanced-stat-icon info">
                        <i class="fas fa-shopping-basket"></i>
                    </div>
                    <div class="advanced-stat-title">Panier moyen</div>
                    <div class="advanced-stat-value animated-counter" data-target="{{ $performanceMetrics['averageOrderValue'] ?? 0 }}" data-type="currency">0</div>
                    <div class="advanced-stat-trend neutral">
                        <i class="fas fa-chart-line"></i> Évolution stable
                    </div>
                </div>
            </div>

            <!-- Clients Actifs -->
            <div class="col-lg-3 col-md-6">
                <div class="advanced-stat-card success">
                    <div class="advanced-stat-icon success">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="advanced-stat-title">Clients actifs</div>
                    <div class="advanced-stat-value animated-counter" data-target="{{ $performanceMetrics['activeCustomers'] ?? 0 }}" data-type="number">0</div>
                    <div class="advanced-stat-trend up">
                        <i class="fas fa-arrow-up"></i> Ce mois
                    </div>
                </div>
            </div>

            <!-- Top Produits -->
            <div class="col-lg-6">
                <div class="advanced-chart-container">
                    <div class="advanced-chart-header">
                        <div class="advanced-chart-title">
                            <i class="fas fa-trophy"></i> Top 5 Produits
                        </div>
                    </div>
                    <div class="top-products-list">
                        @if(isset($trendAnalysis['topProducts']) && count($trendAnalysis['topProducts']) > 0)
                            @foreach($trendAnalysis['topProducts'] as $index => $product)
                                <div class="product-item">
                                    <div class="product-rank">#{{ $index + 1 }}</div>
                                    <div class="product-info">
                                        <div class="product-name">{{ $product->name }}</div>
                                        <div class="product-stats">{{ number_format($product->total_quantity) }} unités - {{ number_format($product->total_amount) }} F</div>
                                    </div>
                                    <div class="product-progress">
                                        <div class="progress-bar" style="width: {{ $index == 0 ? 100 : (($product->total_amount / $trendAnalysis['topProducts'][0]->total_amount) * 100) }}%"></div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="no-data">Aucune donnée disponible</div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Section des statistiques d'approvisionnement -->
        <div class="animated-divider mb-4"></div>

        <h3 class="section-title mb-4 fade-in">
            <i class="fas fa-truck me-2"></i>Approvisionnements et Stocks
        </h3>
        @include('accountant.dashboard-section-approvisionnements')
        
        <!-- Section des graphiques de performance avancés -->
        <div class="animated-divider mb-4"></div>

        <h3 class="section-title mb-4 fade-in">
            <i class="fas fa-chart-line me-2"></i>Performance et Analyses Avancées
        </h3>

        <!-- Graphiques Interactifs Avancés -->
        <div class="row g-4 mb-4">
            <!-- Graphique Principal - Comparaison des Ventes -->
            <div class="col-lg-8">
                <div class="advanced-chart-container">
                    <div class="advanced-chart-header">
                        <div class="advanced-chart-title">
                            <i class="fas fa-chart-area"></i> Évolution des Ventes (Comparaison Annuelle)
                        </div>
                        <div class="advanced-period-filters">
                            <button class="advanced-period-filter active" data-period="year">Année</button>
                            <button class="advanced-period-filter" data-period="semester">Semestre</button>
                            <button class="advanced-period-filter" data-period="quarter">Trimestre</button>
                            <button class="advanced-period-filter" data-period="month">Mois</button>
                        </div>
                    </div>
                    <div class="advanced-chart-area">
                        <canvas id="salesComparisonChart"></canvas>
                        <div id="loadingIndicator" class="loading-indicator">
                            <div class="loading-spinner"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Graphique des Statuts de Paiement -->
            <div class="col-lg-4">
                <div class="advanced-chart-container">
                    <div class="advanced-chart-header">
                        <div class="advanced-chart-title">
                            <i class="fas fa-pie-chart"></i> Répartition des Paiements
                        </div>
                    </div>
                    <div class="advanced-chart-area" style="height: 300px;">
                        <canvas id="advancedPaymentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Deuxième ligne de graphiques -->
        <div class="row g-4 mb-4">
            <!-- Tendance Quotidienne -->
            <div class="col-lg-6">
                <div class="advanced-chart-container">
                    <div class="advanced-chart-header">
                        <div class="advanced-chart-title">
                            <i class="fas fa-chart-line"></i> Tendance des 7 Derniers Jours
                        </div>
                    </div>
                    <div class="advanced-chart-area" style="height: 250px;">
                        <canvas id="dailyTrendChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Approvisionnements par Produit -->
            <div class="col-lg-6">
                <div class="advanced-chart-container">
                    <div class="advanced-chart-header">
                        <div class="advanced-chart-title">
                            <i class="fas fa-boxes"></i> Approvisionnements par Produit
                        </div>
                    </div>
                    <div class="advanced-chart-area" style="height: 250px;">
                        <canvas id="supplyProductChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Section des Rapports et Exports -->
        <div class="animated-divider mb-4"></div>

        <h3 class="section-title mb-4 fade-in">
            <i class="fas fa-file-export me-2"></i>Rapports et Exports
        </h3>

        <div class="row g-4 mb-4">
            <!-- Options d'Export -->
            <div class="col-lg-6">
                <div class="advanced-chart-container">
                    <div class="advanced-chart-header">
                        <div class="advanced-chart-title">
                            <i class="fas fa-download"></i> Exports Rapides
                        </div>
                    </div>
                    <div class="export-options">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <button class="export-btn" data-export="sales-excel">
                                    <i class="fas fa-file-excel"></i>
                                    <span>Ventes Excel</span>
                                    <small>Toutes les ventes avec détails</small>
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button class="export-btn" data-export="payments-pdf">
                                    <i class="fas fa-file-pdf"></i>
                                    <span>Paiements PDF</span>
                                    <small>Rapport des paiements</small>
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button class="export-btn" data-export="dashboard-pdf">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Dashboard PDF</span>
                                    <small>Capture du tableau de bord</small>
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button class="export-btn" data-export="custom-report">
                                    <i class="fas fa-cog"></i>
                                    <span>Rapport Personnalisé</span>
                                    <small>Configurer les données</small>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rapports Prédéfinis -->
            <div class="col-lg-6">
                <div class="advanced-chart-container">
                    <div class="advanced-chart-header">
                        <div class="advanced-chart-title">
                            <i class="fas fa-file-alt"></i> Rapports Prédéfinis
                        </div>
                    </div>
                    <div class="predefined-reports">
                        <div class="report-item">
                            <div class="report-icon">
                                <i class="fas fa-calendar-week"></i>
                            </div>
                            <div class="report-info">
                                <div class="report-title">Rapport Hebdomadaire</div>
                                <div class="report-description">Synthèse des activités de la semaine</div>
                            </div>
                            <button class="btn btn-sm btn-outline-primary generate-report" data-report="weekly">
                                Générer
                            </button>
                        </div>

                        <div class="report-item">
                            <div class="report-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="report-info">
                                <div class="report-title">Rapport Mensuel</div>
                                <div class="report-description">Bilan complet du mois</div>
                            </div>
                            <button class="btn btn-sm btn-outline-primary generate-report" data-report="monthly">
                                Générer
                            </button>
                        </div>

                        <div class="report-item">
                            <div class="report-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="report-info">
                                <div class="report-title">Analyse de Performance</div>
                                <div class="report-description">Métriques et KPI détaillés</div>
                            </div>
                            <button class="btn btn-sm btn-outline-primary generate-report" data-report="performance">
                                Générer
                            </button>
                        </div>

                        <div class="report-item">
                            <div class="report-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="report-info">
                                <div class="report-title">Rapport Clients</div>
                                <div class="report-description">Analyse de la clientèle</div>
                            </div>
                            <button class="btn btn-sm btn-outline-primary generate-report" data-report="customers">
                                Générer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section d'impression optimisée -->
        <div class="row g-4 mb-4">
            <div class="col-12">
                <div class="advanced-chart-container">
                    <div class="advanced-chart-header">
                        <div class="advanced-chart-title">
                            <i class="fas fa-print"></i> Impression et Partage
                        </div>
                        <div class="print-controls">
                            <button class="btn btn-outline-secondary" id="printDashboard">
                                <i class="fas fa-print"></i> Imprimer
                            </button>
                            <button class="btn btn-outline-info" id="shareDashboard">
                                <i class="fas fa-share-alt"></i> Partager
                            </button>
                            <button class="btn btn-outline-success" id="scheduledReport">
                                <i class="fas fa-clock"></i> Programmer
                            </button>
                        </div>
                    </div>
                    <div class="print-options">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Format</label>
                                <select class="form-select" id="printFormat">
                                    <option value="a4">A4 Portrait</option>
                                    <option value="a4-landscape">A4 Paysage</option>
                                    <option value="a3">A3</option>
                                    <option value="letter">Letter</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Qualité</label>
                                <select class="form-select" id="printQuality">
                                    <option value="high">Haute</option>
                                    <option value="medium" selected>Moyenne</option>
                                    <option value="low">Basse</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Sections</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="includeCharts" checked>
                                    <label class="form-check-label" for="includeCharts">
                                        Graphiques
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="includeData" checked>
                                    <label class="form-check-label" for="includeData">
                                        Données détaillées
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section des tableaux de données -->
        <div class="animated-divider mb-4"></div>

        <h3 class="section-title mb-4 fade-in">
            <i class="fas fa-table me-2"></i>Données et Activités
        </h3>
        @include('accountant.dashboard-section-tableaux')
        
        <!-- Section des actions rapides -->
        <div class="animated-divider mb-4"></div>

        <h3 class="section-title mb-4 fade-in">
            <i class="fas fa-bolt me-2"></i>Actions Rapides
        </h3>
        @include('accountant.dashboard-section-actions')
        
        <!-- Footer du dashboard -->
        <div class="dashboard-footer">
            <div class="row">
                <div class="col-md-6">
                    <p class="footer-text">
                        <i class="fas fa-sync-alt me-1"></i> Mise à jour : {{ now()->format('d/m/Y H:i') }}
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="footer-links">
                        <a href="#" class="footer-link"><i class="fas fa-question-circle me-1"></i>Aide</a>
                        <a href="#" class="footer-link"><i class="fas fa-cog me-1"></i>Paramètres</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- Chart.js pour les graphiques avancés -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>

@php
    // Préparer les données pour les graphiques avancés
    $chartData = [
        'monthlySales' => $monthlySales ?? [],
        'paymentStats' => [
            'data' => $paymentStats['data'] ?? [33.3, 33.3, 33.4],
            'labels' => $paymentStats['labels'] ?? ['Payé', 'Partiel', 'Impayé'],
            'backgroundColor' => $paymentStats['backgroundColor'] ?? ['#10b981', '#f59e0b', '#ef4444'],
            'borderColor' => $paymentStats['borderColor'] ?? ['#10b981', '#f59e0b', '#ef4444'],
            'borderWidth' => $paymentStats['borderWidth'] ?? 2
        ],
        'products' => [
            'labels' => $supplyChartData['products']['labels'] ?? ['Produit 1', 'Produit 2', 'Produit 3', 'Produit 4', 'Produit 5'],
            'data' => $supplyChartData['products']['data'] ?? [10, 15, 8, 12, 5],
            'backgroundColor' => $supplyChartData['products']['backgroundColor'] ?? [
                'rgba(16, 185, 129, 0.8)',
                'rgba(14, 165, 233, 0.8)',
                'rgba(168, 85, 247, 0.8)',
                'rgba(245, 158, 11, 0.8)',
                'rgba(239, 68, 68, 0.8)'
            ],
            'amounts' => $supplyChartData['products']['amounts'] ?? [1000, 1500, 800, 1200, 500]
        ],
        'dailyTrend' => [
            'labels' => $trendAnalysis['labels'] ?? ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
            'sales' => $trendAnalysis['sales'] ?? [12, 15, 10, 18, 20, 17, 22],
            'revenue' => $trendAnalysis['revenue'] ?? [1200, 1500, 1000, 1800, 2000, 1700, 2200]
        ],
        'trendAnalysis' => $trendAnalysis ?? [],
        'performanceMetrics' => $performanceMetrics ?? []
    ];
@endphp

<!-- Déboguer les données dans la console -->
<script>
    console.log('Données du tableau de bord avancé:', @json($chartData));
</script>

<input type="hidden" id="primaryChartData" value='@json($chartData)'>

<!-- Scripts avancés du tableau de bord -->
<script src="{{ asset('js/accountant-dashboard-advanced.js') }}?v={{ time() }}"></script>

<!-- Scripts de compatibilité et débogage -->
<script>
    // Données de compatibilité pour l'ancien système
    let statsData = {
        totalSales: {{ $totalSales }},
        totalRevenue: {{ $totalRevenue }},
        totalPayments: {{ $totalPayments }},
        pendingPayments: {{ $pendingPayments }},
        monthlyCementOrders: {{ $stats['monthly_cement_orders'] ?? 0 }},
        monthlyRevenue: {{ $stats['monthly_revenue'] ?? 0 }},
        monthlyExpenses: {{ $stats['monthly_expenses'] ?? 0 }}
    };

    // Données des métriques de performance
    let performanceData = @json($performanceMetrics ?? []);
    
    console.log('Données récupérées depuis PHP:', statsData);
    console.log('Métriques de performance:', performanceData);
    
    // Fonction pour formatter les nombres
    function formatNumber(number) {
        return new Intl.NumberFormat('fr-FR').format(number);
    }
    
    // Mettre à jour les éléments du DOM avec les valeurs
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Script de débogage chargé');
        
        // Mettre à jour TOUTES les valeurs numériques dans l'interface
        try {
            // 1. Mettre à jour le panneau de débogage
            document.querySelectorAll('.debug-value').forEach(function(element, index) {
                if (index === 0) element.textContent = `${statsData.totalSales} (type: ${typeof statsData.totalSales})`;
                if (index === 1) element.textContent = `${statsData.totalRevenue} (type: ${typeof statsData.totalRevenue})`;
                if (index === 2) element.textContent = `${statsData.totalPayments} (type: ${typeof statsData.totalPayments})`;
                if (index === 3) element.textContent = `${statsData.pendingPayments} (type: ${typeof statsData.pendingPayments})`;
            });
            
            // 2. En-tête - Données financières
            const headerValues = document.querySelectorAll('.header-data-value');
            if (headerValues.length >= 3) {
                headerValues[0].textContent = formatNumber(statsData.totalRevenue);
                headerValues[1].textContent = formatNumber(statsData.totalPayments);
                headerValues[2].textContent = formatNumber(statsData.pendingPayments);
                
                // Débogage
                console.log('Valeurs d\'en-tête mises à jour:', {
                    revenue: statsData.totalRevenue,
                    payments: statsData.totalPayments,
                    pending: statsData.pendingPayments
                });
            } else {
                console.warn('Les éléments .header-data-value n\'ont pas été trouvés ou sont insuffisants');
            }
            
            // 3. Cartes statistiques avancées
            const advancedStatValues = document.querySelectorAll('.advanced-stat-value');
            console.log('Nombre d\'éléments .advanced-stat-value trouvés:', advancedStatValues.length);

            // Mettre à jour chaque carte individuellement par data-stat
            const totalSalesElement = document.querySelector('[data-stat="totalSales"]');
            if (totalSalesElement) {
                totalSalesElement.textContent = formatNumber(statsData.totalSales);
                console.log('Total Sales mis à jour:', statsData.totalSales);
            }

            const totalRevenueElement = document.querySelector('[data-stat="totalRevenue"]');
            if (totalRevenueElement) {
                totalRevenueElement.textContent = formatNumber(statsData.totalRevenue) + ' F';
                console.log('Total Revenue mis à jour:', statsData.totalRevenue);
            }

            const pendingPaymentsElement = document.querySelector('[data-stat="pendingPayments"]');
            if (pendingPaymentsElement) {
                pendingPaymentsElement.textContent = formatNumber(statsData.pendingPayments) + ' F';
                console.log('Pending Payments mis à jour:', statsData.pendingPayments);
            }

            // Mettre à jour tous les éléments avec data-target
            advancedStatValues.forEach(function(element, index) {
                const target = element.getAttribute('data-target');
                const type = element.getAttribute('data-type');
                const stat = element.getAttribute('data-stat');

                if (target && !isNaN(target)) {
                    let value = parseFloat(target);
                    let formattedValue = '';

                    switch(type) {
                        case 'currency':
                            formattedValue = formatNumber(value) + ' F';
                            break;
                        case 'percentage':
                            formattedValue = value.toFixed(1) + '%';
                            break;
                        case 'number':
                        default:
                            formattedValue = formatNumber(value);
                            break;
                    }

                    element.textContent = formattedValue;
                    console.log(`Élément ${stat || index} mis à jour:`, formattedValue);
                }
            });

            // 4. Ajouter des animations pour attirer l'attention
            document.querySelectorAll('.advanced-stat-value, .header-data-value').forEach(function(element) {
                element.classList.add('updated');
                setTimeout(function() {
                    element.classList.remove('updated');
                }, 2000);
            });

            console.log('Valeurs mises à jour avec succès');
        } catch (error) {
            console.error('Erreur lors de la mise à jour des valeurs:', error);
        }
    });
    
    // Fonction utilitaire pour mettre à jour un élément
    function updateElementValue(selector, value) {
        const element = document.querySelector(selector);
        if (element) {
            element.textContent = value;
        } else {
            console.warn('Élément non trouvé:', selector);
        }
    }
</script>
<script src="{{ asset('js/accountant-pro-dashboard.js') }}?v={{ time() }}"></script>
<script src="{{ asset('js/accountant-dashboard-advanced.js') }}?v={{ time() }}"></script>

<!-- Données pour les graphiques -->
<input type="hidden" id="chartData" value='{"sales":{"monthlySales":{{ json_encode($monthlySales) }},"monthlyLabels":{{ json_encode($monthlySales) }},"paymentStatus":{{ json_encode($paymentStats) }}},"supplies":{{ json_encode($supplyChartData) }}}'>

<!-- Données pour les graphiques avancés -->
<input type="hidden" id="advancedChartData" value='{
  "monthlySales": {{ json_encode($monthlySales ?? []) }},
  "paymentStats": {{ json_encode($paymentStats ?? []) }},
  "supplyChartData": {{ json_encode($supplyChartData ?? []) }},
  "performanceMetrics": {{ json_encode($performanceMetrics ?? []) }},
  "trendAnalysis": {{ json_encode($trendAnalysis ?? []) }}
}'>
@endpush
