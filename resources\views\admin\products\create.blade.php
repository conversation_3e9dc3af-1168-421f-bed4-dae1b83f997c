@extends('layouts.admin_minimal')

@section('title', 'Créer un nouveau produit')

@section('content')
<div class="container-fluid py-0">
    <!-- En-tête moderne et attrayant -->
    <div class="modern-header-wrapper">
        <div class="modern-header-background">
            <div class="header-gradient-overlay"></div>
            <div class="header-pattern"></div>
        </div>

        <div class="modern-header-content">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <!-- Navigation breadcrumb moderne -->
                    <nav aria-label="breadcrumb" class="modern-breadcrumb">
                        <ol class="breadcrumb-modern">
                            <li class="breadcrumb-item-modern">
                                <a href="{{ route('admin.dashboard') }}" class="breadcrumb-link">
                                    <i class="fas fa-home"></i>
                                    <span>Dashboard</span>
                                </a>
                            </li>
                            <li class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </li>
                            <li class="breadcrumb-item-modern">
                                <a href="{{ route('admin.products.index') }}" class="breadcrumb-link">
                                    <i class="fas fa-boxes"></i>
                                    <span>Produits</span>
                                </a>
                            </li>
                            <li class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </li>
                            <li class="breadcrumb-item-modern active">
                                <span>Nouveau produit</span>
                            </li>
                        </ol>
                    </nav>

                    <!-- Titre principal avec animation -->
                    <div class="header-title-section">
                        <div class="title-icon-wrapper">
                            <div class="title-icon-bg">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                        </div>
                        <div class="title-content">
                            <h1 class="header-title">
                                Créer un nouveau produit
                                <span class="title-highlight">Catalogue</span>
                            </h1>
                            <p class="header-subtitle">
                                <i class="fas fa-info-circle me-2"></i>
                                Ajoutez un nouveau produit à votre catalogue avec toutes ses spécifications
                            </p>
                        </div>
                    </div>
                <div class="col-lg-4">
                    <!-- Boutons d'action -->
                    <div class="header-actions">
                        <div class="action-buttons-group">
                            <a href="{{ route('admin.products.index') }}" class="btn btn-outline-light btn-modern">
                                <i class="fas fa-arrow-left me-2"></i>
                                Retour à la liste
                            </a>
                            <button type="submit" form="product-form" class="btn btn-success btn-modern">
                                <i class="fas fa-save me-2"></i>
                                Créer le produit
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="main-content-wrapper">
        <div class="row">
            <div class="col-12">
                <!-- Formulaire principal -->
                <form action="{{ route('admin.products.store') }}" method="POST" id="product-form" class="modern-form">
                    @csrf

                    <!-- Section Informations générales -->
                    <div class="card modern-card mb-4">
                        <div class="card-header bg-white border-0">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-info-circle text-primary me-2"></i>
                                Informations générales
                            </h5>
                            <p class="text-muted mb-0 small">Renseignez les informations de base du produit</p>
                        </div>
                        <div class="card-body">
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" class="form-control modern-input @error('name') is-invalid @enderror"
                                               id="productName" name="name" placeholder="Nom du produit"
                                               value="{{ old('name') }}" required>
                                        <label for="productName">
                                            <i class="fas fa-tag me-2"></i>Nom du produit *
                                        </label>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <select class="form-select modern-select @error('category_id') is-invalid @enderror"
                                                id="categorySelect" name="category_id" required>
                                            <option value="">Sélectionnez une catégorie</option>
                                            @foreach($categories as $category)
                                                <option value="{{ $category->id }}"
                                                        {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                                    {{ $category->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <label for="categorySelect">
                                            <i class="fas fa-layer-group me-2"></i>Catégorie *
                                        </label>
                                        @error('category_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <select class="form-select modern-select @error('unit') is-invalid @enderror"
                                                id="unitSelect" name="unit" required>
                                            <option value="">Sélectionnez une unité</option>
                                            <option value="Tonne" {{ old('unit') == 'Tonne' ? 'selected' : '' }}>Tonne</option>
                                            <option value="Kg" {{ old('unit') == 'Kg' ? 'selected' : '' }}>Kilogramme</option>
                                            <option value="Barre" {{ old('unit') == 'Barre' ? 'selected' : '' }}>Barre</option>
                                            <option value="Unité" {{ old('unit') == 'Unité' ? 'selected' : '' }}>Unité</option>
                                            <option value="Pièce" {{ old('unit') == 'Pièce' ? 'selected' : '' }}>Pièce</option>
                                            <option value="Mètre cube" {{ old('unit') == 'Mètre cube' ? 'selected' : '' }}>Mètre cube</option>
                                            <option value="Mètre carré" {{ old('unit') == 'Mètre carré' ? 'selected' : '' }}>Mètre carré</option>
                                        </select>
                                        <label for="unitSelect">
                                            <i class="fas fa-balance-scale me-2"></i>Unité de mesure *
                                        </label>
                                        @error('unit')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="number" class="form-control modern-input @error('stock_quantity') is-invalid @enderror"
                                               id="stockQuantity" name="stock_quantity" placeholder="Quantité en stock"
                                               value="{{ old('stock_quantity', 0) }}" min="0" required>
                                        <label for="stockQuantity">
                                            <i class="fas fa-boxes me-2"></i>Quantité en stock *
                                        </label>
                                        @error('stock_quantity')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Statut actif -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="form-check form-switch modern-switch">
                                        <input class="form-check-input" type="checkbox" id="isActive"
                                               name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="form-check-label fw-medium" for="isActive">
                                            <i class="fas fa-toggle-on text-success me-2"></i>
                                            Produit actif
                                        </label>
                                        <small class="text-muted d-block">Le produit sera visible et disponible à la vente</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section Prix par ville pour le ciment (masquée par défaut) -->
                    <div class="card modern-card mb-4" id="cement-pricing-section" style="display: none;">
                        <div class="card-header bg-white border-0">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-map-marker-alt text-info me-2"></i>
                                Prix par Ville
                            </h5>
                            <p class="text-muted mb-0 small">Configurez des prix spécifiques pour chaque ville selon les coûts de transport</p>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach($regions as $region)
                                <div class="col-lg-6 mb-4">
                                    <div class="region-card-modern" data-region-id="{{ $region->id }}">
                                        <div class="region-header">
                                            <div class="region-icon">
                                                <i class="fas fa-map-marked-alt"></i>
                                            </div>
                                            <div class="region-info">
                                                <h6 class="region-name">{{ $region->name }}</h6>
                                                <small class="region-cities-count">{{ $region->cities->count() }} villes disponibles</small>
                                            </div>
                                            <button type="button" class="btn btn-outline-primary btn-sm add-city-btn"
                                                    onclick="showCityModal({{ $region->id }}, '{{ $region->name }}')">
                                                <i class="fas fa-plus me-1"></i>Ajouter ville
                                            </button>
                                        </div>
                                        <div class="selected-cities" id="selected-cities-{{ $region->id }}">
                                            <div class="empty-state text-center py-3">
                                                <i class="fas fa-city text-muted mb-2"></i>
                                                <p class="text-muted mb-0">Aucune ville sélectionnée</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <!-- Section Spécifications du fer (masquée par défaut) -->
                    <div class="card modern-card mb-4" id="iron-specifications-section" style="display: none;">
                        <div class="card-header bg-white border-0">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-hammer text-dark me-2"></i>
                                Spécifications du Fer
                            </h5>
                            <p class="text-muted mb-0 small">Définissez les caractéristiques techniques et les prix du fer</p>
                        </div>
                        <div class="card-body">
                            <div class="row g-4">
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <select class="form-select modern-select @error('iron_diameter') is-invalid @enderror"
                                                id="ironDiameter" name="iron_diameter">
                                            <option value="">Sélectionnez un diamètre</option>
                                            <option value="6" {{ old('iron_diameter') == '6' ? 'selected' : '' }}>6 mm</option>
                                            <option value="8" {{ old('iron_diameter') == '8' ? 'selected' : '' }}>8 mm</option>
                                            <option value="10" {{ old('iron_diameter') == '10' ? 'selected' : '' }}>10 mm</option>
                                            <option value="12" {{ old('iron_diameter') == '12' ? 'selected' : '' }}>12 mm</option>
                                            <option value="14" {{ old('iron_diameter') == '14' ? 'selected' : '' }}>14 mm</option>
                                            <option value="16" {{ old('iron_diameter') == '16' ? 'selected' : '' }}>16 mm</option>
                                        </select>
                                        <label for="ironDiameter">
                                            <i class="fas fa-ruler me-2"></i>Diamètre (mm) *
                                        </label>
                                        @error('iron_diameter')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <input type="number" class="form-control modern-input @error('iron_length') is-invalid @enderror"
                                               id="ironLength" name="iron_length" placeholder="Longueur (m)"
                                               value="{{ old('iron_length', '12') }}" min="1" step="0.1">
                                        <label for="ironLength">
                                            <i class="fas fa-arrows-alt-h me-2"></i>Longueur (m) *
                                        </label>
                                        @error('iron_length')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <input type="number" class="form-control modern-input @error('iron_unit_price') is-invalid @enderror"
                                               id="ironUnitPrice" name="iron_unit_price" placeholder="Prix unitaire (FCFA)"
                                               value="{{ old('iron_unit_price') }}" min="0" step="0.01">
                                        <label for="ironUnitPrice">
                                            <i class="fas fa-tag me-2"></i>Prix unitaire (FCFA) *
                                        </label>
                                        @error('iron_unit_price')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Calcul automatique du prix par tonne -->
                            <div class="mt-4">
                                <div class="calculation-display">
                                    <div class="calculation-header">
                                        <i class="fas fa-calculator text-primary me-2"></i>
                                        <strong>Calcul automatique</strong>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="calculation-item">
                                                <span class="calculation-label">Unités par tonne :</span>
                                                <span class="calculation-value" id="calculatedUnitsPerTon">-</span>
                                                <input type="hidden" name="units_per_ton" id="unitsPerTonHidden">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="calculation-item">
                                                <span class="calculation-label">Poids par unité :</span>
                                                <span class="calculation-value" id="calculatedWeightPerUnit">- kg</span>
                                                <input type="hidden" name="weight_per_unit" id="weightPerUnitHidden">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="calculation-item">
                                                <span class="calculation-label">Prix par tonne :</span>
                                                <span class="calculation-value" id="calculatedTonPrice">0 FCFA</span>
                                                <input type="hidden" name="ton_price" id="tonPriceHidden">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section Prix de base pour les autres produits (masquée par défaut) -->
                    <div class="card modern-card mb-4" id="base-pricing-section" style="display: none;">
                        <div class="card-header bg-white border-0">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-money-bill-wave text-success me-2"></i>
                                Prix de vente
                            </h5>
                            <p class="text-muted mb-0 small">Définissez le prix de vente du produit</p>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="number" class="form-control modern-input @error('base_price') is-invalid @enderror"
                                               id="basePrice" name="base_price" placeholder="Prix de vente (FCFA)"
                                               value="{{ old('base_price') }}" min="0" step="0.01">
                                        <label for="basePrice">
                                            <i class="fas fa-tag me-2"></i>Prix de vente (FCFA) *
                                        </label>
                                        @error('base_price')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Boutons d'action -->
                    <div class="card modern-card">
                        <div class="card-body text-center">
                            <div class="action-buttons-final">
                                <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary btn-lg me-3">
                                    <i class="fas fa-times me-2"></i>
                                    Annuler
                                </a>
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-save me-2"></i>
                                    Créer le produit
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal de sélection de ville pour le ciment -->
<div class="modal fade" id="citySelectionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content modern-modal">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-city me-2"></i>
                    Ajouter une ville
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="citySelect" class="form-label">Ville</label>
                    <select class="form-select" id="citySelect">
                        <option value="">Sélectionnez une ville</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="cityPrice" class="form-label">Prix de vente (FCFA)</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="cityPrice" min="0" step="0.01" required>
                        <span class="input-group-text">FCFA</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="confirmCitySelection">
                    <i class="fas fa-plus me-2"></i>Ajouter
                </button>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
/* ===== STYLES POUR LA PAGE DE CRÉATION MODERNE ===== */

/* Variables CSS */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

/* En-tête moderne */
.modern-header-wrapper {
    position: relative;
    margin: -1.5rem -1.5rem 2rem -1.5rem;
    overflow: hidden;
}

.modern-header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
}

.header-gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
}

.header-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 80% 50%, rgba(255,255,255,0.1) 2px, transparent 2px);
    background-size: 50px 50px;
}

.modern-header-content {
    position: relative;
    z-index: 2;
    padding: 2rem;
    color: white;
}

/* Breadcrumb moderne */
.modern-breadcrumb {
    margin-bottom: 1.5rem;
}

.breadcrumb-modern {
    display: flex;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
    flex-wrap: wrap;
}

.breadcrumb-item-modern {
    display: flex;
    align-items: center;
}

.breadcrumb-link {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
}

.breadcrumb-link:hover {
    color: white;
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.breadcrumb-link i {
    margin-right: 0.5rem;
}

.breadcrumb-separator {
    margin: 0 0.5rem;
    color: rgba(255,255,255,0.6);
}

.breadcrumb-item-modern.active span {
    color: white;
    font-weight: 600;
}

/* Titre principal */
.header-title-section {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.title-icon-wrapper {
    margin-right: 1.5rem;
}

.title-icon-bg {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.3);
}

.header-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
}

.title-highlight {
    color: rgba(255,255,255,0.8);
    font-weight: 400;
}

.header-subtitle {
    margin: 0.5rem 0 0 0;
    color: rgba(255,255,255,0.8);
    font-size: 1.1rem;
}

/* Boutons d'action dans l'en-tête */
.header-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 100%;
}

.action-buttons-group {
    display: flex;
    gap: 1rem;
}

.btn-modern {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-outline-light.btn-modern {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

.btn-outline-light.btn-modern:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.btn-success.btn-modern {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.btn-success.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

/* Contenu principal */
.main-content-wrapper {
    padding: 0 1.5rem;
}

/* Cartes modernes */
.modern-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.modern-card:hover {
    box-shadow: 0 20px 40px rgba(0,0,0,0.12);
}

.modern-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding: 1.5rem;
}

.modern-card .card-body {
    padding: 2rem;
}

/* Champs de formulaire modernes */
.modern-input, .modern-select {
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.8);
    backdrop-filter: blur(10px);
    padding: 0.75rem 1rem;
}

.modern-input:focus, .modern-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.form-floating > label {
    color: #6c757d;
    font-weight: 500;
}

/* Switch moderne */
.modern-switch .form-check-input {
    width: 3rem;
    height: 1.5rem;
    border-radius: 1rem;
    background-color: #dee2e6;
    border: none;
    transition: all 0.3s ease;
}

.modern-switch .form-check-input:checked {
    background-color: #28a745;
    background-image: none;
}

.modern-switch .form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Sections spécifiques aux types de produits */
.region-card-modern {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.region-card-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.region-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.region-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
}

.region-info {
    flex: 1;
}

.region-name {
    margin: 0;
    font-weight: 600;
    color: #495057;
}

.region-cities-count {
    color: #6c757d;
}

/* Calcul automatique pour le fer */
.calculation-display {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 12px;
    padding: 1.5rem;
    border-left: 4px solid #667eea;
}

.calculation-header {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: #495057;
}

.calculation-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.calculation-item:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

.calculation-label {
    display: block;
    font-weight: 500;
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.calculation-value {
    display: block;
    font-size: 1.3rem;
    font-weight: 700;
    color: #667eea;
    font-family: 'Courier New', monospace;
}

/* Boutons d'action finaux */
.action-buttons-final {
    padding: 2rem 0;
}

.action-buttons-final .btn {
    border-radius: 25px;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.action-buttons-final .btn:hover {
    transform: translateY(-2px);
}

/* Modal moderne */
.modern-modal {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

.modern-modal .modal-header {
    background: var(--primary-gradient);
    color: white;
    border: none;
}

/* États vides */
.empty-state {
    padding: 2rem;
    text-align: center;
    color: #6c757d;
}

.empty-state i {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
}

/* Responsive */
@media (max-width: 768px) {
    .modern-header-content {
        padding: 1rem;
    }

    .header-title {
        font-size: 1.8rem;
    }

    .title-icon-bg {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .action-buttons-group {
        flex-direction: column;
        width: 100%;
    }

    .main-content-wrapper {
        padding: 0 0.5rem;
    }

    .modern-card .card-body {
        padding: 1rem;
    }
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modern-card {
    animation: slideInUp 0.5s ease-out;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // ===== GESTION DYNAMIQUE DES SECTIONS SELON LA CATÉGORIE =====

    const categorySelect = document.getElementById('categorySelect');
    const cementSection = document.getElementById('cement-pricing-section');
    const ironSection = document.getElementById('iron-specifications-section');
    const baseSection = document.getElementById('base-pricing-section');

    // Données des régions et villes (à récupérer du backend)
    const regionsData = @json($regions->load('cities'));
    let currentRegionId = null;

    // Gestion du changement de catégorie
    categorySelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const categoryName = selectedOption.text.toLowerCase();

        console.log('Catégorie sélectionnée:', categoryName);

        // Masquer toutes les sections
        if (cementSection) {
            cementSection.style.display = 'none';
            // Désactiver les champs requis du ciment
            const cementInputs = cementSection.querySelectorAll('input[required], select[required]');
            cementInputs.forEach(input => input.required = false);
        }
        if (ironSection) {
            ironSection.style.display = 'none';
            // Désactiver les champs requis du fer
            const ironInputs = ironSection.querySelectorAll('input[required], select[required]');
            ironInputs.forEach(input => input.required = false);
        }
        if (baseSection) {
            baseSection.style.display = 'none';
            // Désactiver les champs requis de base
            const baseInputs = baseSection.querySelectorAll('input[required], select[required]');
            baseInputs.forEach(input => input.required = false);
        }

        // Afficher la section appropriée selon la catégorie
        if (categoryName.includes('ciment')) {
            if (cementSection) {
                cementSection.style.display = 'block';
                console.log('Section ciment affichée');
                // Pas de champs requis pour le ciment car les prix sont gérés par ville
            }
        } else if (categoryName.includes('fer')) {
            if (ironSection) {
                ironSection.style.display = 'block';
                console.log('Section fer affichée');
                // Activer les champs requis du fer
                const ironDiameter = document.getElementById('ironDiameter');
                const ironLength = document.getElementById('ironLength');
                const ironUnitPrice = document.getElementById('ironUnitPrice');
                if (ironDiameter) ironDiameter.required = true;
                if (ironLength) ironLength.required = true;
                if (ironUnitPrice) ironUnitPrice.required = true;
            }
        } else if (this.value) {
            if (baseSection) {
                baseSection.style.display = 'block';
                console.log('Section prix de base affichée');
                // Activer le champ requis du prix de base
                const basePrice = document.getElementById('basePrice');
                if (basePrice) basePrice.required = true;
            }
        }

        // Animation d'apparition
        setTimeout(() => {
            const visibleSection = document.querySelector('#cement-pricing-section[style*="block"], #iron-specifications-section[style*="block"], #base-pricing-section[style*="block"]');
            if (visibleSection) {
                visibleSection.style.animation = 'slideInUp 0.5s ease-out';
                visibleSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }
        }, 100);
    });

    // ===== GESTION DES PRIX PAR VILLE POUR LE CIMENT =====

    window.showCityModal = function(regionId, regionName) {
        currentRegionId = regionId;
        const modal = new bootstrap.Modal(document.getElementById('citySelectionModal'));
        const citySelect = document.getElementById('citySelect');

        // Vider et remplir la liste des villes
        citySelect.innerHTML = '<option value="">Sélectionnez une ville</option>';

        const region = regionsData.find(r => r.id == regionId);
        if (region && region.cities) {
            region.cities.forEach(city => {
                const option = document.createElement('option');
                option.value = city.id;
                option.textContent = city.name;
                citySelect.appendChild(option);
            });
        }

        // Réinitialiser le prix
        document.getElementById('cityPrice').value = '';

        // Afficher le modal
        modal.show();
    };

    // Confirmation d'ajout de ville
    document.getElementById('confirmCitySelection').addEventListener('click', function() {
        const citySelect = document.getElementById('citySelect');
        const cityPrice = document.getElementById('cityPrice');

        if (!citySelect.value || !cityPrice.value) {
            alert('Veuillez sélectionner une ville et saisir un prix.');
            return;
        }

        const cityId = citySelect.value;
        const cityName = citySelect.options[citySelect.selectedIndex].text;
        const price = cityPrice.value;

        // Trouver la carte de région correspondante
        const regionCard = document.querySelector(`[data-region-id="${currentRegionId}"]`).closest('.region-card-modern');
        const selectedCities = regionCard.querySelector('.selected-cities');
        const emptyState = regionCard.querySelector('.empty-state');

        // Masquer l'état vide s'il existe
        if (emptyState) {
            emptyState.style.display = 'none';
        }

        // Créer l'élément ville
        const cityElement = document.createElement('div');
        cityElement.className = 'city-price-item mb-2';
        cityElement.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${cityName}</strong>
                    <small class="text-muted d-block">ID: ${cityId}</small>
                </div>
                <div class="d-flex align-items-center">
                    <span class="fw-bold text-success me-2">${parseInt(price).toLocaleString()} FCFA</span>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeCity(this, ${cityId})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <input type="hidden" name="cities[${cityId}][price]" value="${price}">
        `;

        selectedCities.appendChild(cityElement);

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('citySelectionModal'));
        modal.hide();

        // Animation d'ajout
        cityElement.style.animation = 'slideInUp 0.3s ease-out';
    });

    // Fonction pour supprimer une ville
    window.removeCity = function(button, cityId) {
        const cityElement = button.closest('.city-price-item');
        const regionCard = button.closest('.region-card-modern');
        const selectedCities = regionCard.querySelector('.selected-cities');
        const emptyState = regionCard.querySelector('.empty-state');

        // Animation de suppression
        cityElement.style.animation = 'slideOutDown 0.3s ease-out';
        setTimeout(() => {
            cityElement.remove();

            // Afficher l'état vide si aucune ville n'est sélectionnée
            const remainingCities = selectedCities.querySelectorAll('.city-price-item');
            if (remainingCities.length === 0 && emptyState) {
                emptyState.style.display = 'block';
            }
        }, 300);
    };

    // ===== CALCUL AUTOMATIQUE POUR LE FER =====

    const ironDiameterSelect = document.getElementById('ironDiameter');
    const ironLengthInput = document.getElementById('ironLength');
    const ironUnitPriceInput = document.getElementById('ironUnitPrice');
    const calculatedUnitsPerTon = document.getElementById('calculatedUnitsPerTon');
    const calculatedWeightPerUnit = document.getElementById('calculatedWeightPerUnit');
    const calculatedTonPrice = document.getElementById('calculatedTonPrice');
    const unitsPerTonHidden = document.getElementById('unitsPerTonHidden');
    const weightPerUnitHidden = document.getElementById('weightPerUnitHidden');
    const tonPriceHidden = document.getElementById('tonPriceHidden');

    // Table de référence pour les unités par tonne (barres de 12m)
    const unitsPerTonReference = {
        '6': 750,   // 6mm = 750 barres/tonne
        '8': 422,   // 8mm = 422 barres/tonne
        '10': 270,  // 10mm = 270 barres/tonne
        '12': 188,  // 12mm = 188 barres/tonne
        '14': 138,  // 14mm = 138 barres/tonne
        '16': 106   // 16mm = 106 barres/tonne
    };

    function calculateIronSpecifications() {
        const diameter = ironDiameterSelect?.value;
        const length = parseFloat(ironLengthInput?.value) || 12;
        const unitPrice = parseFloat(ironUnitPriceInput?.value) || 0;

        if (diameter && unitsPerTonReference[diameter]) {
            // Calcul des unités par tonne (ajusté selon la longueur)
            const baseUnitsPerTon = unitsPerTonReference[diameter];
            const adjustedUnitsPerTon = Math.round(baseUnitsPerTon * (12 / length));

            // Calcul du poids par unité
            const weightPerUnit = (1000 / adjustedUnitsPerTon).toFixed(4);

            // Calcul du prix par tonne
            const tonPrice = adjustedUnitsPerTon * unitPrice;

            // Mise à jour des affichages
            if (calculatedUnitsPerTon) {
                calculatedUnitsPerTon.textContent = adjustedUnitsPerTon.toLocaleString();
            }
            if (calculatedWeightPerUnit) {
                calculatedWeightPerUnit.textContent = weightPerUnit + ' kg';
            }
            if (calculatedTonPrice) {
                calculatedTonPrice.textContent = tonPrice.toLocaleString() + ' FCFA';
            }

            // Mise à jour des champs cachés
            if (unitsPerTonHidden) {
                unitsPerTonHidden.value = adjustedUnitsPerTon;
            }
            if (weightPerUnitHidden) {
                weightPerUnitHidden.value = weightPerUnit;
            }
            if (tonPriceHidden) {
                tonPriceHidden.value = tonPrice;
            }
        } else {
            // Réinitialiser les affichages
            if (calculatedUnitsPerTon) calculatedUnitsPerTon.textContent = '-';
            if (calculatedWeightPerUnit) calculatedWeightPerUnit.textContent = '- kg';
            if (calculatedTonPrice) calculatedTonPrice.textContent = '0 FCFA';

            // Réinitialiser les champs cachés
            if (unitsPerTonHidden) unitsPerTonHidden.value = '';
            if (weightPerUnitHidden) weightPerUnitHidden.value = '';
            if (tonPriceHidden) tonPriceHidden.value = '';
        }
    }

    // Écouter les changements pour le calcul automatique
    if (ironDiameterSelect) {
        ironDiameterSelect.addEventListener('change', calculateIronSpecifications);
    }
    if (ironLengthInput) {
        ironLengthInput.addEventListener('input', calculateIronSpecifications);
    }
    if (ironUnitPriceInput) {
        ironUnitPriceInput.addEventListener('input', calculateIronSpecifications);
    }

    // ===== VALIDATION DU FORMULAIRE =====

    const form = document.getElementById('product-form');
    form.addEventListener('submit', function(e) {
        const categorySelect = document.getElementById('categorySelect');
        const selectedCategory = categorySelect.options[categorySelect.selectedIndex].text.toLowerCase();

        console.log('Validation pour catégorie:', selectedCategory);

        // Validation spécifique selon le type de produit
        if (selectedCategory.includes('ciment')) {
            const selectedCities = document.querySelectorAll('input[name^="cities"]');
            if (selectedCities.length === 0) {
                e.preventDefault();
                alert('Veuillez définir au moins un prix par ville pour les produits de type ciment.');
                return;
            }
        } else if (selectedCategory.includes('fer')) {
            const ironDiameter = document.getElementById('ironDiameter')?.value;
            const ironLength = document.getElementById('ironLength')?.value;
            const ironUnitPrice = document.getElementById('ironUnitPrice')?.value;

            if (!ironDiameter || !ironLength || !ironUnitPrice) {
                e.preventDefault();
                alert('Veuillez remplir toutes les spécifications du fer (diamètre, longueur et prix unitaire).');
                return;
            }
        } else if (categorySelect.value) {
            const basePrice = document.getElementById('basePrice')?.value;
            if (!basePrice) {
                e.preventDefault();
                alert('Veuillez définir un prix de vente pour ce produit.');
                return;
            }
        }

        // Animation de soumission
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Création en cours...';
            submitBtn.disabled = true;
        }
    });

    // ===== ANIMATIONS ET EFFETS VISUELS =====

    // Animation des cartes au chargement
    const cards = document.querySelectorAll('.modern-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Effet de focus sur les champs
    const inputs = document.querySelectorAll('.modern-input, .modern-select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.closest('.form-floating')?.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.closest('.form-floating')?.classList.remove('focused');
        });
    });

    // Déclencher le changement de catégorie initial si une catégorie est déjà sélectionnée
    if (categorySelect.value) {
        categorySelect.dispatchEvent(new Event('change'));
    }
});

// Animation CSS pour slideOutDown
const style = document.createElement('style');
style.textContent = `
    @keyframes slideOutDown {
        from {
            opacity: 1;
            transform: translateY(0);
        }
        to {
            opacity: 0;
            transform: translateY(30px);
        }
    }

    .focused {
        transform: translateY(-2px);
        transition: transform 0.3s ease;
    }
`;
document.head.appendChild(style);
</script>
@endpush
@endsection
