/* Style premium pour la vue des ventes - inspiré du design Rocker avec améliorations */

/* Utilisation des variables globales définies dans le layout cashier */

/* Styles généraux pour améliorer l'apparence globale */
body {
    background-color: #F9FAFB !important;
    color: #1D2939;
    font-family: 'Inter', 'Segoe UI', sans-serif;
}

.container-fluid {
    padding: 2rem 2.5rem;
    max-width: 1600px;
    margin: 0 auto;
    animation: fadeIn 0.5s ease-out;
    transition: all 0.3s ease-in-out;
}

/* Compatibilité avec la sidebar responsive */
.dashboard-container {
    transition: all 0.3s ease-in-out;
}

/* En-tête de page améliorée */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #EAECF0;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #101828;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
}

.page-title i {
    color: #2196F3;
    font-size: 1.25rem;
}

.btn-primary {
    background-color: #2196F3;
    border-color: #2196F3;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05);
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background-color: #1976D2;
    border-color: #1976D2;
    box-shadow: 0 2px 4px rgba(16, 24, 40, 0.1);
}

/* Styles pour les cartes de statistiques */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 1.25rem;
    margin-bottom: 2rem;
}

.stat-card {
    position: relative;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border-left: 4px solid transparent;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.stat-card:nth-child(1) {
    border-left-color: #2196F3;
    background: linear-gradient(to right, rgba(33, 150, 243, 0.05), white);
}

.stat-card:nth-child(2) {
    border-left-color: #F44336;
    background: linear-gradient(to right, rgba(244, 67, 54, 0.05), white);
}

.stat-card:nth-child(3) {
    border-left-color: #4CAF50;
    background: linear-gradient(to right, rgba(76, 175, 80, 0.05), white);
}

.stat-card:nth-child(4) {
    border-left-color: #FF9800;
    background: linear-gradient(to right, rgba(255, 152, 0, 0.05), white);
}

.stat-title {
    font-size: 0.875rem;
    color: #667085;
    font-weight: 500;
    margin-bottom: 0.75rem;
    letter-spacing: 0.02em;
    text-transform: uppercase;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #101828;
    margin-bottom: 0.75rem;
    letter-spacing: -0.02em;
}

.stat-trend {
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.35rem;
    padding: 0.25rem 0.5rem;
    border-radius: 16px;
    width: fit-content;
}

.stat-trend.positive {
    color: #027A48;
    background-color: #ECFDF3;
}

.stat-trend.negative {
    color: #B42318;
    background-color: #FEF3F2;
}

.stat-icon {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1);
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #2196F3, #1976D2);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #F44336, #D32F2F);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #4CAF50, #388E3C);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #FF9800, #F57C00);
}

/* Reset et styles généraux */
body {
    background-color: var(--light-color);
    color: var(--text-color);
    font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
    margin: 0;
    padding: 0;
}

.container-fluid {
    padding: 1.5rem;
    max-width: 1400px;
    margin: 0 auto;
}

/* En-tête de page */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
}

.page-title i {
    color: var(--primary-color);
}

/* Cartes */
.card {
    background-color: white;
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    border: none;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

/* Stats en haut de page */
.stats-container {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.stat-card {
    background-color: white;
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    padding: 1.25rem;
    flex: 1;
    min-width: 200px;
    display: flex;
    flex-direction: column;
}

.stat-title {
    font-size: 0.85rem;
    color: var(--text-color);
    opacity: 0.8;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stat-title i {
    color: var(--primary-color);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-trend.positive {
    color: var(--paid-color);
}

.stat-trend.negative {
    color: var(--failed-color);
}

/* Filtres améliorés */
.filters-container {
    display: flex;
    gap: 1.25rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    padding: 1.25rem;
    background-color: #F9FAFB;
    border-radius: 12px;
    border: 1px solid #EAECF0;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.filter-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #344054;
    white-space: nowrap;
}

.filter-select {
    padding: 0.625rem 1rem;
    border: 1px solid #D0D5DD;
    border-radius: 8px;
    font-size: 0.875rem;
    color: #101828;
    background-color: white;
    cursor: pointer;
    box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05);
    transition: all 0.2s ease;
    min-width: 160px;
    font-weight: 500;
}

.filter-select:hover {
    border-color: #98A2B3;
}

.filter-select:focus {
    border-color: #2196F3;
    box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
    outline: none;
}

/* Boutons d'action premium */
.action-button {
    background-color: white;
    border: 1px solid #D0D5DD;
    border-radius: 8px;
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #344054;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05);
}

.action-button:hover {
    background-color: #F9FAFB;
    border-color: #98A2B3;
    color: #101828;
}

.action-button.primary {
    background-color: #2196F3;
    color: white;
    border-color: #2196F3;
}

.action-button.primary:hover {
    background-color: #1976D2;
    border-color: #1976D2;
    box-shadow: 0 2px 4px rgba(16, 24, 40, 0.1);
}

.action-button.success {
    background-color: #4CAF50;
    color: white;
    border-color: #4CAF50;
}

.action-button.success:hover {
    background-color: #388E3C;
    border-color: #388E3C;
}

.action-button.warning {
    background-color: #FF9800;
    color: white;
    border-color: #FF9800;
}

.action-button.warning:hover {
    background-color: #F57C00;
    border-color: #F57C00;
}

.action-button.danger {
    background-color: #F44336;
    color: white;
    border-color: #F44336;
}

.action-button.danger:hover {
    background-color: #D32F2F;
    border-color: #D32F2F;
}

/* Tableau des ventes récentes */
.recent-orders {
    margin-bottom: 2rem;
}

.recent-orders-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.recent-orders-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
}

.recent-orders-title i {
    color: var(--primary-color);
}

/* Tableau moderne style Rocker premium */
.card {
    background-color: white;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid #EAECF0;
}

.card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid #EAECF0;
    background-color: #FCFCFD;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #101828;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    letter-spacing: -0.01em;
}

.card-title i {
    color: #2196F3;
    font-size: 1.1rem;
}

.card-actions {
    display: flex;
    gap: 0.75rem;
}

/* Boutons d'action dans l'en-tête */
.btn-outline-primary {
    border: 1px solid #D0D5DD;
    background-color: white;
    color: #2196F3;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.btn-outline-primary:hover {
    background-color: rgba(33, 150, 243, 0.05);
    border-color: #2196F3;
    color: #1976D2;
}

.modern-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.modern-table thead th {
    background-color: #F9FAFB;
    color: #667085;
    font-weight: 600;
    padding: 0.875rem 1.25rem;
    text-align: left;
    border-bottom: 1px solid #EAECF0;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.modern-table tbody tr {
    transition: all 0.2s ease;
    border-bottom: 1px solid #EAECF0;
}

/* Styles pour la coloration des lignes selon l'état de règlement */
.modern-table tbody tr.row-paid {
    background-color: rgba(30, 136, 229, 0.12); /* Fond BLEU pour les ventes avec règlement terminé */
    border-left: 6px solid #1E88E5; /* Bordure gauche bleue */
    box-shadow: inset 0 0 8px rgba(33, 150, 243, 0.15);
    transition: all 0.3s ease;
}

.modern-table tbody tr.row-paid td {
    color: #0D47A1; /* Texte en bleu foncé pour les ventes payées */
    font-weight: 500;
}

.modern-table tbody tr.row-paid:hover {
    background-color: rgba(30, 136, 229, 0.2);
    box-shadow: inset 0 0 12px rgba(33, 150, 243, 0.25);
    transform: translateX(3px);
}

.modern-table tbody tr.row-partially-paid {
    background-color: rgba(255, 152, 0, 0.12); /* Fond ORANGE pour les ventes avec règlement commencé */
    border-left: 6px solid #FF9800; /* Bordure gauche orange */
    box-shadow: inset 0 0 8px rgba(255, 152, 0, 0.15);
    transition: all 0.3s ease;
}

.modern-table tbody tr.row-partially-paid td {
    color: #E65100; /* Texte en orange foncé pour les ventes partiellement payées */
    font-weight: 500;
}

.modern-table tbody tr.row-partially-paid:hover {
    background-color: rgba(255, 152, 0, 0.2);
    box-shadow: inset 0 0 12px rgba(255, 152, 0, 0.25);
    transform: translateX(3px);
}

.modern-table tbody tr.row-not-paid {
    background-color: rgba(244, 67, 54, 0.12); /* Fond ROUGE pour les ventes avec règlement non démarré */
    border-left: 6px solid #F44336; /* Bordure gauche rouge */
    box-shadow: inset 0 0 8px rgba(244, 67, 54, 0.15);
    transition: all 0.3s ease;
}

.modern-table tbody tr.row-not-paid td {
    color: #B71C1C; /* Texte en rouge foncé pour les ventes non payées */
    font-weight: 500;
}

.modern-table tbody tr.row-not-paid:hover {
    background-color: rgba(244, 67, 54, 0.2);
    box-shadow: inset 0 0 12px rgba(244, 67, 54, 0.25);
    transform: translateX(3px);
}

.modern-table tbody tr.row-failed {
    background-color: rgba(158, 158, 158, 0.15); /* Fond gris plus visible pour les ventes annulées */
    border-left: 6px solid #9E9E9E; /* Bordure gauche grise plus épaisse */
    box-shadow: inset 0 0 8px rgba(158, 158, 158, 0.2);
    transition: all 0.3s ease;
}

.modern-table tbody tr.row-failed td {
    color: #616161; /* Texte en gris foncé pour les ventes annulées */
    font-weight: 500;
}

.modern-table tbody tr.row-failed:hover {
    background-color: rgba(158, 158, 158, 0.25);
    box-shadow: inset 0 0 12px rgba(158, 158, 158, 0.3);
    transform: translateX(3px);
}

.modern-table tbody tr:not(.row-paid):not(.row-partially-paid):not(.row-not-paid):not(.row-failed):hover {
    background-color: #F9FAFB;
    cursor: pointer;
}

.modern-table tbody tr {
    cursor: pointer;
}

.modern-table td {
    padding: 1rem 1.25rem;
    vertical-align: middle;
    color: #101828;
    font-size: 0.875rem;
    line-height: 1.5;
}

.modern-table td:first-child,
.modern-table th:first-child {
    padding-left: 1.5rem;
}

.modern-table td:last-child,
.modern-table th:last-child {
    padding-right: 1.5rem;
}

/* Effet de survol sur les lignes */
.modern-table tbody tr:hover td {
    color: #1D2939;
}

/* Style pour les cellules de référence */
.modern-table td:nth-child(2) {
    font-family: 'Roboto Mono', monospace;
    color: #475467;
    font-size: 0.8125rem;
    letter-spacing: -0.02em;
}

/* Pagination style Rocker */
.pagination {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.25rem;
    padding: 1rem 1.5rem;
}

.pagination .page-item .page-link {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    font-size: 0.85rem;
    color: #637381;
    border: 1px solid #F2F4F7;
    background-color: white;
    transition: all 0.2s ease;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .page-item .page-link:hover {
    background-color: #F9FAFB;
}

/* Badges de statut améliorés */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.35rem 0.75rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
    gap: 0.35rem;
    letter-spacing: 0.02em;
    text-transform: uppercase;
    box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05);
    transition: all 0.2s ease;
}

.badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(16, 24, 40, 0.1);
}

.badge i {
    font-size: 0.7rem;
}

.badge-paid {
    background-color: #ECFDF3;
    color: #027A48;
    border: 1px solid #ABEFC6;
}

.badge-pending {
    background-color: #FFF8ED;
    color: #B93815;
    border: 1px solid #FEDF89;
}

.badge-cancelled {
    background-color: #FEF3F2;
    color: #B42318;
    border: 1px solid #FDA29B;
}

/* Barre de progression premium */
.progress-container {
    width: 100%;
    height: 8px;
    background-color: #F2F4F7;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(16, 24, 40, 0.05);
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #8BC34A);
    border-radius: 16px;
    transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

.progress-bar.warning {
    background: linear-gradient(90deg, #FF9800, #FFC107);
}

.progress-bar.danger {
    background: linear-gradient(90deg, #F44336, #FF5722);
}

.shipping-progress-bar.paid {
    background-color: #12B76A;
}

.shipping-progress-bar.pending {
    background-color: #F79009;
}

.shipping-progress-bar.failed {
    background-color: #F04438;
}

/* Styles pour l'affichage du montant déjà payé */
.amount-paid-container {
    display: flex;
    flex-direction: column;
}

.amount-paid {
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    display: inline-block;
    margin-bottom: 0.25rem;
    transition: all 0.2s ease;
}

.amount-paid.fully-paid {
    color: #027A48;
    background-color: rgba(2, 122, 72, 0.1);
}

.amount-paid.partially-paid {
    color: #B54708;
    background-color: rgba(181, 71, 8, 0.1);
}

.amount-paid.not-paid {
    color: #B42318;
    background-color: rgba(180, 35, 24, 0.1);
}

.amount-remaining {
    font-size: 0.75rem;
    color: #667085;
    font-style: italic;
}

/* Indicateurs de paiement */
.payment-indicator {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 0.75rem;
    font-size: 0.75rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    animation: pulse 2s infinite;
}

.payment-indicator.paid {
    background: linear-gradient(135deg, #1E88E5, #0D47A1);
}

.payment-indicator.partially-paid {
    background: linear-gradient(135deg, #FF9800, #E65100);
}

.payment-indicator.not-paid {
    background: linear-gradient(135deg, #F44336, #B71C1C);
}

.payment-indicator.failed {
    background: linear-gradient(135deg, #9E9E9E, #424242);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(33, 150, 243, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(33, 150, 243, 0);
    }
}

.payment-indicator.paid {
    animation: pulse-blue 2s infinite;
}

.payment-indicator.partially-paid {
    animation: pulse-orange 2s infinite;
}

.payment-indicator.not-paid {
    animation: pulse-red 2s infinite;
}

@keyframes pulse-blue {
    0% {
        box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(33, 150, 243, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(33, 150, 243, 0);
    }
}

@keyframes pulse-orange {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(255, 152, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 152, 0, 0);
    }
}

@keyframes pulse-red {
    0% {
        box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(244, 67, 54, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(244, 67, 54, 0);
    }
}

/* Boutons d'action style Rocker */
.action-button {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #475467;
    background-color: white;
    border: 1px solid #E4E7EC;
    transition: all 0.2s ease;
    margin-right: 0.25rem;
    font-size: 0.85rem;
    text-decoration: none;
}

.action-button:hover {
    background-color: #F9FAFB;
    color: #1D2939;
    border-color: #D0D5DD;
}

.action-button.view:hover {
    color: var(--primary-color);
}

.action-button.edit:hover {
    color: var(--warning-color);
}

.action-button.delete:hover {
    color: var(--danger-color);
}

/* Image produit style Rocker */
.product-photo {
    width: 36px;
    height: 36px;
    border-radius: 6px;
    object-fit: cover;
    background-color: #F2F4F7;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.product-photo i {
    color: #475467;
    font-size: 1rem;
}

/* Responsive design pour les tablettes et mobiles */
@media (max-width: 992px) {
    .container-fluid {
        padding: 1.5rem;
    }
    
    .stats-container {
        flex-direction: column;
    }
    
    .stat-card {
        width: 100%;
    }
    
    .filter-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        margin-bottom: 1rem;
    }
}

/* Compatibilité avec la sidebar responsive sur tablette */
@media (max-width: 768px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .page-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .page-title {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    /* Compatibilité avec la sidebar cachée */
    body.sidebar-hidden-body .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
}

/* Compatibilité avec la sidebar responsive sur mobile */
@media (max-width: 576px) {
    .container-fluid {
        padding: 0.75rem;
    }
    
    .page-title {
        font-size: 1.125rem;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
    
    /* Optimisations pour les très petits écrans */
    .stat-card {
        margin-bottom: 0.75rem;
    }
    
    .stat-card .card-body {
        padding: 0.5rem;
    }
    
    .stat-value {
        font-size: 1.25rem;
    }
    
    .stat-label {
        font-size: 0.75rem;
    }
    
    /* Compatibilité avec la sidebar */
    body.sidebar-hidden-body .main-content {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
}

/* Responsive */
@media (max-width: 768px) {
    .stats-container {
        flex-direction: column;
    }
    
    .filter-section {
        flex-direction: column;
    }
    
    .modern-table {
        display: block;
        overflow-x: auto;
    }
}
