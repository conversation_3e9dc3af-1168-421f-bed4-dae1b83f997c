/* Base Layout */
.sb-nav-fixed {
    padding-top: 56px;
}

.sb-nav-fixed #layoutSidenav #layoutSidenav_nav {
    width: 225px;
    height: 100vh;
    z-index: 1038;
    position: fixed;
    left: 0;
    top: 56px;
}

.sb-nav-fixed #layoutSidenav #layoutSidenav_content {
    padding-left: 225px;
    top: 56px;
}

.sb-topnav {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1039;
}

#layoutSidenav {
    display: flex;
}

.sb-sidenav-dark {
    background-color: #212529;
    color: rgba(255, 255, 255, 0.5);
}

/* Navigation */
.sb-sidenav .nav {
    flex-direction: column;
    flex-wrap: nowrap;
}

.sb-sidenav .nav-link {
    color: rgba(255, 255, 255, 0.5);
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
}

.sb-sidenav .nav-link:hover {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
}

.sb-sidenav .nav-link.active {
    color: #fff;
    background: rgba(255, 255, 255, 0.2);
}

/* Content */
main {
    padding: 1.5rem;
}

.card {
    margin-bottom: 1.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(33, 40, 50, 0.15);
}

.card-header {
    padding: 1rem 1.35rem;
    margin-bottom: 0;
    background-color: rgba(33, 40, 50, 0.03);
    border-bottom: 1px solid rgba(33, 40, 50, 0.125);
}

.table thead th {
    background-color: #f8f9fa;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.1em;
}
