/* Modern Dashboard Styles */
:root {
    --primary-gradient: linear-gradient(135deg, #6B73FF 0%, #000DFF 100%);
    --success-gradient: linear-gradient(135deg, #1DE9B6 0%, #1DC4E9 100%);
    --warning-gradient: linear-gradient(135deg, #FFB64D 0%, #FFCB80 100%);
    --danger-gradient: linear-gradient(135deg, #FF5370 0%, #FF869A 100%);
    --card-shadow: 0 2px 22px 0 rgba(0, 0, 0, 0.1), 0 4px 20px 0 rgba(0, 0, 0, 0.15);
    --hover-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.1), 0 5px 25px 0 rgba(0, 0, 0, 0.15);
}

/* Smooth Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Stats Cards */
.stats-card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
}

.stats-card:hover {
    transform: translateY(-7px);
    box-shadow: var(--hover-shadow);
}

.stats-card .icon-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-left: 1rem;
    backdrop-filter: blur(5px);
}

.stats-card .card-footer {
    background: rgba(0,0,0,0.1);
    border-top: 1px solid rgba(255,255,255,0.1);
}

.stats-card .display-6 {
    font-size: 2rem;
    font-weight: 600;
    margin: 0.5rem 0;
}

/* Enhanced Table Design */
.table-container {
    background: white;
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    padding: 1rem;
    margin-bottom: 2rem;
}

.table {
    margin: 0;
}

.table thead th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    color: #495057;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 1rem;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

.table td {
    padding: 1rem;
    vertical-align: middle;
}

/* Enhanced Badges */
.badge {
    padding: 0.5em 1em;
    font-weight: 500;
    letter-spacing: 0.3px;
    border-radius: 6px;
}

.badge.bg-success {
    background: var(--success-gradient) !important;
}

.badge.bg-warning {
    background: var(--warning-gradient) !important;
}

.badge.bg-danger {
    background: var(--danger-gradient) !important;
}

/* Button Styles */
.btn {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    letter-spacing: 0.3px;
    transition: all 0.2s ease;
}

.btn-primary {
    background: var(--primary-gradient);
    border: none;
}

.btn-primary:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
}

.btn-outline-primary {
    border: 2px solid #6B73FF;
    color: #6B73FF;
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    border-color: transparent;
    color: white;
}

/* Card Headers */
.card-header {
    background: white;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding: 1.25rem;
}

.card-header .title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #344767;
}

/* Breadcrumb */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item {
    font-size: 0.9rem;
    color: #6c757d;
}

.breadcrumb-item.active {
    color: #344767;
    font-weight: 600;
}

/* Search Bar */
.search-bar {
    position: relative;
}

.search-bar input {
    padding-left: 2.5rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    background: #f8f9fa;
}

.search-bar i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: 15px;
    }
    
    .card-header {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Loading Animation */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.2s ease;
}

.hover-lift:hover {
    transform: translateY(-3px);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
