<!-- Modal de sélection de véhicule -->
<div class="modal fade" id="cementVehicleSelectionModal" tabindex="-1" aria-labelledby="cementVehicleSelectionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-gradient-custom">
                <div class="modal-title-wrapper">
                    <div class="d-flex align-items-center">
                        <div class="modal-icon-wrapper me-3">
                            <i class="fas fa-truck fa-2x text-white animate-bounce"></i>
                        </div>
                        <div>
                            <h5 class="modal-title text-white mb-0">Sélection de véhicule</h5>
                            <p class="text-white-50 mb-0 mt-1 small">Choisissez un véhicule pour la livraison</p>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn-close text-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            
            <div class="modal-body custom-gradient">
                <div class="vehicle-info-card mb-4">
                    <div class="d-flex align-items-center p-3 bg-white rounded-3 shadow-sm">
                        <i class="fas fa-map-marked-alt text-accent me-3 fa-lg pulse-icon"></i>
                        <div>
                            <h6 class="mb-0 text-accent">Destination sélectionnée</h6>
                            <p class="mb-0 mt-1 text-dark" id="cementSelectedDestination">-</p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="form-label d-flex align-items-center text-white">
                                <i class="fas fa-truck text-warning me-2 bounce-icon"></i>
                                <span>Sélectionnez un véhicule</span>
                            </label>
                            <div class="select-wrapper">
                                <select id="cementVehicleSelect" class="form-select form-select-lg shadow-none">
                                    <option value="" selected disabled>Sélectionnez un véhicule</option>
                                </select>
                                <div class="select-icon">
                                    <i class="fas fa-chevron-down text-accent"></i>
                                </div>
                            </div>
                            <small class="form-text text-white-50 mt-2 cement-vehicle-info" style="display: none;">
                                <i class="fas fa-info-circle me-1"></i>
                                <span class="cement-driver-info"></span>
                            </small>
                        </div>
                    </div>
                </div>

                <div class="form-group mt-3">
                    <label class="form-label d-flex align-items-center text-white">
                        <i class="fas fa-sync text-warning me-2 bounce-icon"></i>
                        <span>Nombre de tours</span>
                    </label>
                    <div class="input-group">
                        <input type="number" id="cementNumberOfTours" class="form-control form-control-lg" min="1" value="1">
                        <span class="input-group-text bg-white">
                            <i class="fas fa-rotate text-accent"></i>
                        </span>
                    </div>
                    <small class="form-text text-white-50">
                        <i class="fas fa-info-circle me-1"></i>
                        Indiquez le nombre de voyages prévus pour ce véhicule
                    </small>
                </div>
            </div>

            <div class="modal-footer border-0 custom-gradient">
                <button type="button" class="btn btn-light-custom" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>
                    Annuler
                </button>
                <button type="button" class="btn btn-accent" id="cementSaveVehicleBtn">
                    <i class="fas fa-check me-2"></i>
                    Ajouter
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.modal-body .form-control,
.modal-body .form-select {
    background-color: white;
    color: #333;
    border: 1px solid #ced4da;
    height: 48px;
    font-size: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
}

.modal-body .form-select option {
    background-color: white;
    color: #333;
    padding: 8px;
}

.modal-body .form-select:focus {
    border-color: #0ea5e9;
    box-shadow: 0 0 0 0.25rem rgba(14, 165, 233, 0.25);
}

.modal-body .form-select:hover {
    border-color: #0ea5e9;
}

.select-wrapper {
    position: relative;
}

.select-wrapper .select-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
}

.select-wrapper .form-select {
    padding-right: 40px; /* Espace pour l'icône */
}

.bg-gradient-custom {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
}

.custom-gradient {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
}

.text-accent {
    color: #0ea5e9;
}

.btn-accent {
    background: #0ea5e9;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-accent:hover {
    background: #0284c7;
    transform: translateY(-1px);
}

.btn-light-custom {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-light-custom:hover {
    background: white;
    transform: translateY(-1px);
}

.bounce-icon {
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-3px);
    }
}

.pulse-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.animate-bounce {
    animation: bounce 2s infinite;
}

.modal-icon-wrapper {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
