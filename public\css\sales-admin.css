/* Styles spécifiques pour l'interface admin des ventes */

/* Animation d'entrée pour les cartes */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.sales-stat-card {
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.sales-stat-card:nth-child(1) { animation-delay: 0.1s; }
.sales-stat-card:nth-child(2) { animation-delay: 0.2s; }
.sales-stat-card:nth-child(3) { animation-delay: 0.3s; }
.sales-stat-card:nth-child(4) { animation-delay: 0.4s; }

/* Gradient pour les icônes de statistiques */
.stat-icon.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.stat-icon.bg-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
}

.stat-icon.bg-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.stat-icon.bg-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}

/* Amélioration des badges de statut */
.status-badge {
    position: relative;
    overflow: hidden;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.status-badge:hover::before {
    left: 100%;
}

/* Effet de survol pour les lignes du tableau */
.modern-table tbody tr {
    position: relative;
    overflow: hidden;
}

.modern-table tbody tr::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.5s;
    z-index: 1;
}

.modern-table tbody tr:hover::before {
    left: 100%;
}

.modern-table tbody tr > * {
    position: relative;
    z-index: 2;
}

/* Amélioration des boutons d'action */
.action-buttons .btn {
    position: relative;
    overflow: hidden;
}

.action-buttons .btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.action-buttons .btn:hover::before {
    width: 100px;
    height: 100px;
}

/* Amélioration de la barre de recherche */
.search-box input {
    transition: all 0.3s ease;
    border: 2px solid #e2e8f0;
}

.search-box input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

/* Effet de pulsation pour les notifications */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.alert {
    animation: pulse 0.5s ease-in-out;
}

/* Amélioration des cartes de vente en vue grille */
.sale-card {
    position: relative;
    overflow: hidden;
}

.sale-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.sale-card:hover::before {
    transform: scaleX(1);
}

/* Amélioration de la pagination */
.pagination-wrapper .page-link {
    transition: all 0.3s ease;
}

.pagination-wrapper .page-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Amélioration des modales */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.modal-header {
    border-bottom: 1px solid #e2e8f0;
    border-radius: 15px 15px 0 0;
}

.modal-footer {
    border-top: 1px solid #e2e8f0;
    border-radius: 0 0 15px 15px;
}

/* Amélioration de la barre d'actions en lot */
.bulk-actions-bar {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    border-top: 1px solid #e2e8f0;
    color: #2d3748;
}

/* Effet de chargement pour les boutons */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Amélioration des tooltips */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background: #2d3748;
    border-radius: 8px;
    padding: 8px 12px;
}

/* Responsive amélioré */
@media (max-width: 1200px) {
    .sales-stat-card .stat-value {
        font-size: 1.3rem;
    }
}

@media (max-width: 992px) {
    .action-buttons .btn-group {
        flex-direction: column;
    }
    
    .action-buttons .btn {
        margin-bottom: 2px;
    }
}

@media (max-width: 768px) {
    .modern-table {
        font-size: 0.8rem;
    }
    
    .sale-id-badge {
        font-size: 0.75rem;
        padding: 0.2rem 0.5rem;
    }
    
    .status-badge {
        font-size: 0.75rem;
        padding: 0.3rem 0.5rem;
    }
    
    .bulk-actions-bar {
        padding: 0.5rem 0;
    }
}

/* Amélioration de la lisibilité */
.sales-stat-card {
    background: white;
    color: #2d3748;
    border: 1px solid #e2e8f0;
}

.modern-table {
    background: white;
    color: #2d3748;
}

.sale-card {
    background: white;
    color: #2d3748;
    border: 1px solid #e2e8f0;
}

/* Amélioration des contrastes pour les badges */
.status-pending {
    background-color: #fef3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-paid {
    background-color: #d1edff;
    color: #0c5460;
    border: 1px solid #74b9ff;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #fd79a8;
}

/* Amélioration des transitions globales */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

/* Effet de focus amélioré pour l'accessibilité */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}
