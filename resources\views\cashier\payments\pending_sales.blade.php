@extends('layouts.cashier')

@section('title', 'Ventes en attente de paiement')

@section('styles')
<link rel="stylesheet" href="{{ asset('css/modern-pending-sales.css') }}">
@endsection

@section('content')
<div class="container-fluid">
    <!-- En-tête de page avec titre -->
    <div class="page-header fade-in">
        <div>
            <h1 class="page-title">
                <i class="fas fa-money-bill-wave"></i>
                <span>Ventes en attente de paiement</span>
            </h1>
            <p class="page-subtitle">G<PERSON>rez les paiements des ventes en attente de règlement</p>
        </div>
        
        <!-- Barre de recherche -->
        <form action="{{ route('cashier.payments.pending') }}" method="GET" class="search-container fade-in fade-in-delay-1">
            <i class="fas fa-search search-icon"></i>
            <input type="text" class="search-input" name="search" placeholder="Rechercher une vente..." value="{{ request('search') }}">
            <button type="submit" class="search-button">Rechercher</button>
        </form>
    </div>
    
    <!-- Fil d'Ariane -->
    <div class="breadcrumb-container fade-in fade-in-delay-1">
        <ul class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('cashier.dashboard') }}"><i class="fas fa-home"></i> Tableau de bord</a></li>
            <li class="breadcrumb-item active">Ventes en attente</li>
        </ul>
    </div>
    
    @include('partials.alerts')
    
    <!-- Carte principale -->
    <div class="main-card fade-in fade-in-delay-2">
        <div class="card-header">
            <div class="card-header-title">
                <i class="fas fa-file-invoice-dollar"></i>
                <span>Liste des ventes à régler</span>
            </div>
            <div class="filters">
                <!-- Filtres supplémentaires pourraient être ajoutés ici -->
            </div>
        </div>
        <div class="card-body">
            <div class="modern-table-container">
                <table class="modern-table">
                    <thead>
                        <tr>
                            <th>Référence</th>
                            <th>Date</th>
                            <th>Client</th>
                            <th>Produit</th>
                            <th>Quantité</th>
                            <th>Montant total</th>
                            <th>Montant payé</th>
                            <th>Reste à payer</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($sales as $sale)
                            <tr>
                                <td>
                                    <strong>{{ $sale->invoice_number ?? 'VNT-' . str_pad($sale->id, 6, '0', STR_PAD_LEFT) }}</strong>
                                </td>
                                <td>
                                    <div>{{ $sale->created_at->format('d/m/Y') }}</div>
                                    <div style="font-size: 0.75rem; color: var(--text-light);">{{ $sale->created_at->format('H:i') }}</div>
                                </td>
                                <td>
                                    <div style="font-weight: 600;">{{ $sale->customer_name }}</div>
                                    <div style="font-size: 0.75rem; color: var(--text-light);">{{ $sale->customer_phone }}</div>
                                </td>
                                <td>
                                    @if($sale->product)
                                        {{ $sale->product->name }}
                                    @elseif($sale->supply && $sale->supply->details->isNotEmpty())
                                        {{ $sale->supply->details->first()->product->name }}
                                    @else
                                        <span style="color: var(--text-light);">Non spécifié</span>
                                    @endif
                                </td>
                                <td>{{ number_format($sale->quantity, 2, ',', ' ') }} T</td>
                                <td style="font-weight: 600;">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</td>
                                <td>{{ number_format($sale->amount_paid, 0, ',', ' ') }} FCFA</td>
                                <td style="font-weight: 700; color: var(--danger-color);">{{ number_format($sale->total_amount - $sale->amount_paid, 0, ',', ' ') }} FCFA</td>
                                <td>
                                    @if($sale->status === 'pending_payment')
                                        <span class="status-badge pending">
                                            <i class="fas fa-clock"></i>
                                            <span>En attente</span>
                                        </span>
                                    @elseif($sale->status === 'partially_paid')
                                        <span class="status-badge partial">
                                            <i class="fas fa-sync-alt"></i>
                                            <span>Partiel</span>
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ route('cashier.payments.process', $sale->id) }}" class="action-button primary">
                                            <i class="fas fa-cash-register"></i>
                                            <span>Encaisser</span>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="10">
                                    <div class="empty-state">
                                        <i class="fas fa-money-bill-wave empty-state-icon"></i>
                                        <h3 class="empty-state-title">Aucune vente en attente</h3>
                                        <p class="empty-state-message">Toutes les ventes ont été réglées ou aucune vente n'est éligible pour le règlement.</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            <div class="pagination-container">
                {{ $sales->links() }}
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Animation d'entrée pour les lignes du tableau
        const tableRows = document.querySelectorAll('.modern-table tbody tr');
        tableRows.forEach((row, index) => {
            row.style.opacity = '0';
            row.style.transform = 'translateY(10px)';
            row.style.animation = `fadeIn 0.5s ease forwards ${0.1 + index * 0.05}s`;
        });
    });
</script>
@endsection
