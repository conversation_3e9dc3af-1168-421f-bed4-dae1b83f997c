@extends('layouts.accountant')

@section('title', 'Centre d\'aide')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="dashboard-card slide-in-up">
                <div class="dashboard-card-header">
                    <div class="dashboard-card-title">
                        <i class="fas fa-question-circle me-2"></i>Centre d'aide
                    </div>
                </div>
                <div class="dashboard-card-body">
                    <div class="help-intro mb-4">
                        <div class="row align-items-center">
                            <div class="col-lg-6">
                                <h3 class="help-title">Comment pouvons-nous vous aider aujourd'hui ?</h3>
                                <p class="help-description">
                                    Bienvenue dans le centre d'aide de l'espace comptable. Vous trouverez ci-dessous les réponses aux questions les plus fréquentes.
                                    Si vous ne trouvez pas l'information que vous cherchez, n'hésitez pas à contacter l'administrateur système.
                                </p>
                                <div class="search-container mb-4">
                                    <input type="text" id="help-search" class="form-control" placeholder="Rechercher une question...">
                                    <i class="fas fa-search search-icon"></i>
                                </div>
                            </div>
                            <div class="col-lg-6 text-center">
                                <img src="{{ asset('images/help.svg') }}" alt="Centre d'aide" class="help-image" onerror="this.src='https://via.placeholder.com/400x300?text=Centre+d%27aide'">
                            </div>
                        </div>
                    </div>

                    <div class="help-sections">
                        @foreach($helpSections as $section)
                        <div class="help-section mb-4">
                            <div class="help-section-header" data-bs-toggle="collapse" data-bs-target="#section-{{ $loop->index }}" aria-expanded="{{ $loop->first ? 'true' : 'false' }}">
                                <div class="d-flex align-items-center">
                                    <div class="help-section-icon bg-{{ $section['color'] }}">
                                        <i class="fas {{ $section['icon'] }}"></i>
                                    </div>
                                    <h4 class="help-section-title">{{ $section['title'] }}</h4>
                                </div>
                                <i class="fas fa-chevron-down toggle-icon"></i>
                            </div>
                            <div class="collapse {{ $loop->first ? 'show' : '' }}" id="section-{{ $loop->index }}">
                                <div class="help-section-content">
                                    <div class="accordion" id="accordion-{{ $loop->index }}">
                                        @foreach($section['items'] as $itemIndex => $item)
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="heading-{{ $loop->parent->index }}-{{ $itemIndex }}">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                    data-bs-target="#collapse-{{ $loop->parent->index }}-{{ $itemIndex }}" 
                                                    aria-expanded="false" 
                                                    aria-controls="collapse-{{ $loop->parent->index }}-{{ $itemIndex }}">
                                                    <i class="fas fa-question-circle me-2 text-{{ $section['color'] }}"></i>
                                                    {{ $item['question'] }}
                                                </button>
                                            </h2>
                                            <div id="collapse-{{ $loop->parent->index }}-{{ $itemIndex }}" 
                                                class="accordion-collapse collapse" 
                                                aria-labelledby="heading-{{ $loop->parent->index }}-{{ $itemIndex }}" 
                                                data-bs-parent="#accordion-{{ $loop->parent->index }}">
                                                <div class="accordion-body">
                                                    {{ $item['answer'] }}
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <div class="help-contact mt-5">
                        <div class="card">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-lg-8">
                                        <h5><i class="fas fa-headset me-2"></i>Vous ne trouvez pas ce que vous cherchez ?</h5>
                                        <p class="mb-lg-0">
                                            Notre équipe est disponible pour vous aider. N'hésitez pas à nous contacter
                                            directement pour obtenir une assistance personnalisée.
                                        </p>
                                    </div>
                                    <div class="col-lg-4 text-lg-end mt-3 mt-lg-0">
                                        <a href="mailto:<EMAIL>" class="btn btn-primary">
                                            <i class="fas fa-envelope me-2"></i>Contacter le support
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .help-intro {
        padding: 20px;
        background-color: rgba(30, 136, 229, 0.05);
        border-radius: 8px;
        margin-bottom: 30px;
    }
    
    .help-title {
        color: #1E88E5;
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 15px;
    }
    
    .help-description {
        color: #546E7A;
        font-size: 1.1rem;
        margin-bottom: 20px;
    }
    
    .help-image {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    }
    
    .search-container {
        position: relative;
    }
    
    .search-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #90A4AE;
    }
    
    .help-section {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        overflow: hidden;
    }
    
    .help-section-header {
        padding: 15px 20px;
        background-color: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.3s ease;
    }
    
    .help-section-header:hover {
        background-color: rgba(30, 136, 229, 0.03);
    }
    
    .help-section-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 1.2rem;
    }
    
    .help-section-title {
        margin: 0;
        color: #37474F;
        font-size: 1.2rem;
        font-weight: 600;
    }
    
    .help-section-content {
        padding: 20px;
        border-top: 1px solid #f0f0f0;
    }
    
    .accordion-button:not(.collapsed) {
        background-color: rgba(30, 136, 229, 0.05);
        color: #1E88E5;
        box-shadow: none;
    }
    
    .accordion-button:focus {
        box-shadow: none;
        border-color: rgba(30, 136, 229, 0.1);
    }
    
    .accordion-body {
        padding: 15px 20px;
        background-color: #f9f9f9;
        border-radius: 0 0 4px 4px;
        color: #455A64;
        line-height: 1.6;
    }
    
    .toggle-icon {
        transition: transform 0.3s;
        color: #90A4AE;
    }
    
    .help-section-header[aria-expanded="true"] .toggle-icon {
        transform: rotate(180deg);
    }
    
    .help-contact .card {
        border: 1px solid rgba(30, 136, 229, 0.15);
        border-radius: 8px;
        background-color: rgba(30, 136, 229, 0.02);
    }
    
    /* Animation */
    .slide-in-up {
        animation: slide-in-up 0.5s ease forwards;
    }
    
    @keyframes slide-in-up {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    /* Script pour la recherche */
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('help-search');
        
        searchInput.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const accordionButtons = document.querySelectorAll('.accordion-button');
            
            accordionButtons.forEach(button => {
                const text = button.textContent.toLowerCase();
                const accordionItem = button.closest('.accordion-item');
                
                if (text.includes(searchTerm)) {
                    accordionItem.style.display = 'block';
                    if (searchTerm.length > 2) {
                        button.click();
                    }
                } else {
                    accordionItem.style.display = 'none';
                }
            });
            
            const helpSections = document.querySelectorAll('.help-section');
            helpSections.forEach(section => {
                const visibleItems = section.querySelectorAll('.accordion-item[style="display: block"]');
                if (visibleItems.length === 0 && searchTerm.length > 0) {
                    section.style.display = 'none';
                } else {
                    section.style.display = 'block';
                }
            });
        });
    });
</style>

<script>
    // Script pour maintenir l'état des sections lors du clic
    document.addEventListener('DOMContentLoaded', function() {
        const sectionHeaders = document.querySelectorAll('.help-section-header');
        
        sectionHeaders.forEach(header => {
            header.addEventListener('click', function() {
                const isExpanded = this.getAttribute('aria-expanded') === 'true';
                this.setAttribute('aria-expanded', !isExpanded);
            });
        });
    });
</script>
@endsection
