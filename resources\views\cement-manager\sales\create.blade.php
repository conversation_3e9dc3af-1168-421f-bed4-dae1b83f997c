@extends('layouts.cement_manager')

@section('title', 'Nouvelle vente')

@push('styles')
<style>
    :root {
        --primary-rgb: 37, 99, 235;
        --secondary-rgb: 107, 114, 128;
    }

    .product-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
    }

    .product-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #4e73df, #36b9cc);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .product-card:hover::before {
        opacity: 1;
    }

    .product-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }
    
    .product-card:hover {
        border-color: #0d6efd;
        box-shadow: 0 4px 12px rgba(13, 110, 253, 0.2);
    }
    
    .product-card.selected {
        border: 3px solid #0d6efd !important;
        box-shadow: 0 0 15px rgba(13, 110, 253, 0.5) !important;
        transform: translateY(-5px) !important;
        position: relative;
        z-index: 10;
    }

    .product-card.selected::before {
        opacity: 1 !important;
    }
    
    .product-card.selected .card-body {
        background-color: rgba(13, 110, 253, 0.05) !important;
    }
    
    .product-card.selected::after {
        content: '\f00c';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        position: absolute;
        top: -10px;
        right: -10px;
        background-color: #0d6efd;
        color: white;
        width: 25px;
        height: 25px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
    }

    .product-card .badge {
        transition: all 0.3s ease;
    }

    .product-card:hover .badge {
        transform: scale(1.05);
    }

    .product-card .card-title {
        color: #2e384d;
        transition: color 0.3s ease;
    }

    .product-card.selected .card-title {
        color: #4e73df;
    }

    .city-details {
        background-color: #f8fafc;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 0.5rem;
        border: 1px solid #e2e8f0;
        transition: all 0.2s ease;
    }

    .city-details:hover {
        background-color: #f1f5f9;
        border-color: #4e73df;
        transform: translateX(5px);
    }

    .transport-info {
        margin-top: 1rem;
        opacity: 0.9;
        transition: opacity 0.2s ease;
    }

    .city-details:hover .transport-info {
        opacity: 1;
    }

    .badge {
        padding: 0.5em 0.8em;
        font-weight: 500;
    }

    .badge.bg-info {
        background-color: #36b9cc !important;
    }

    .badge.bg-success {
        background-color: #1cc88a !important;
    }

    .badge.bg-primary {
        background-color: #4e73df !important;
    }

    .text-primary {
        color: #4e73df !important;
    }

    .navigation-buttons {
        position: sticky;
        bottom: 1rem;
        background-color: #fff;
        padding: 1rem;
        box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.05);
        border-radius: 0.5rem;
        z-index: 100;
    }

    #loadingOverlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .progress-step {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        background-color: #fff;
        border: 2px solid #e3e6f0;
        color: #858796;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        position: relative;
        z-index: 1;
    }

    .progress-step.active {
        border-color: #4e73df;
        color: #4e73df;
    }

    .progress-step.completed {
        background-color: #4e73df;
        border-color: #4e73df;
        color: #fff;
    }

    .progress-line {
        flex: 1;
        height: 2px;
        background-color: #e3e6f0;
        margin: 0 -10px;
    }

    .progress-step.completed + .progress-line {
        background-color: #4e73df;
    }

    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .quantity-btn {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: 1px solid #4e73df;
        background-color: #fff;
        color: #4e73df;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .quantity-btn:hover:not(:disabled) {
        background-color: #4e73df;
        color: #fff;
    }

    .quantity-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .step-content {
        display: none;
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
    }

    .step-content.active {
        display: block;
        opacity: 1;
    }

    .form-control:focus, .form-select:focus {
        border-color: rgb(var(--primary-rgb));
        box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .loading-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    .loading-spinner {
        width: 4rem;
        height: 4rem;
        border: 4px solid #f3f3f3;
        border-top: 4px solid rgb(var(--primary-rgb));
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .step-indicator {
        color: #6c757d;
        font-weight: 500;
    }
    .step-indicator.active {
        color: #0d6efd;
    }
    .step-indicator.completed {
        color: #198754;
    }
    .product-card.selected {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
    .product-card.opacity-50 {
        pointer-events: none;
    }
</style>
@endpush

@section('content')
<div class="container-fluid py-4">
    <!-- Zone de notification pour les ventes rejetées -->
    <div id="rejectedSalesNotification" class="alert alert-danger d-none mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-triangle me-3 fs-4"></i>
            <div>
                <h5 class="alert-heading mb-1">Vente rejetée par l'administrateur</h5>
                <p class="mb-1" id="rejectedSaleMessage"></p>
                <p class="mb-0"><strong>Raison du rejet:</strong> <span id="rejectedSaleReason"></span></p>
                <hr>
                <p class="mb-0">La quantité a été retournée au stock et à la carte correspondante. <a href="#" onclick="location.reload(); return false;" class="alert-link">Rafraîchir la page</a> pour voir les quantités mises à jour.</p>
            </div>
        </div>
    </div>
    
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Nouvelle vente</h1>
        <a href="{{ route('cement-manager.sales.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> Retour
        </a>
    </div>

    <form id="saleForm" method="POST" action="{{ route('cement-manager.sales.store') }}">
        @csrf
        <input type="hidden" name="supply_id" id="hiddenSupplyId">
        <input type="hidden" name="quantity" id="hiddenQuantity">
        <input type="hidden" name="discount_per_ton" id="hiddenDiscountPerTon">
        <input type="hidden" name="nouveau_prix" id="hiddenNouveauPrix">
        <input type="hidden" name="vehicle_id" id="hiddenVehicleId">
        <input type="hidden" name="driver_id" id="hiddenDriverId">
        
        <!-- Étapes -->
        <div class="step-content active" id="step1">
            <h4 class="mb-4">1. Sélection du produit</h4>
            <div class="row">
                @foreach($supplies as $supply)
                    @foreach($supply->details as $detail)
                        <div class="col-md-4 mb-4" id="card-container-{{ $supply->id }}-{{ $loop->index }}">
                            <div class="product-card card h-100" id="supply-card-{{ $supply->id }}-{{ $loop->index }}" data-supply-id="{{ $supply->id }}" data-detail-index="{{ $loop->index }}" onclick="selectExactCard(event, {{ $supply->id }}, {{ $loop->index }})">
                                <div class="card-body">
                                    <div class="form-check text-end mb-2">
                                        <input class="form-check-input supply-radio" type="radio" name="supply_radio" id="supply-radio-{{ $supply->id }}-{{ $loop->index }}" value="{{ $supply->id }}" data-detail-index="{{ $loop->index }}" onclick="event.stopPropagation(); selectExactCard(event, {{ $supply->id }}, {{ $loop->index }});">
                                    </div>
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h5 class="card-title mb-0">
                                                <i class="fas fa-box text-primary me-2"></i>
                                                {{ $detail->product->name }}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Quantité totale:</label>
                                            <div class="d-flex align-items-center">
                                                <strong>{{ number_format($detail->quantity, 2) }}</strong>
                                                <span class="badge bg-success">
                                                    {{ number_format($detail->cities_details->first()['quantity'], 2) }} {{ $detail->product->unit }}
                                                </span>
                                            </div>
                                        </div>

                                    <div class="mb-3">
                                        <label class="form-label text-muted">Détails par destination:</label>
                                        <div class="mt-2">
                                            @if($detail->cities_details && $detail->cities_details->count() > 0)
                                                @foreach($detail->cities_details as $cityDetail)
                                                    <div class="city-details p-3 bg-light rounded mb-2" 
                                                        data-price="{{ number_format($cityDetail['price'], 2, '.', '') }}"
                                                        data-total-quantity="{{ number_format($cityDetail['quantity'], 2, '.', '') }}"
                                                        data-remaining-quantity="{{ number_format($cityDetail['remaining_quantity'], 2, '.', '') }}"
                                                        data-used-quantity="{{ number_format($cityDetail['quantity'] - $cityDetail['remaining_quantity'], 2, '.', '') }}"
                                                        data-vehicle-id="{{ $cityDetail['vehicle']->id }}"
                                                        data-city-id="{{ $cityDetail['city']->id }}"
                                                        data-city-name="{{ $cityDetail['city']->name }}"
                                                        data-vehicle-info="{{ $cityDetail['vehicle']->registration_number }}"
                                                        data-vehicle-capacity="{{ number_format($cityDetail['vehicle_capacity'], 2, '.', '') }}"
                                                        data-driver-id="{{ $cityDetail['driver'] ? $cityDetail['driver']->id : '' }}">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <div>
                                                                <h6 class="mb-1">
                                                                    <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                                                    {{ $cityDetail['city']->name }}
                                                                </h6>
                                                                <div class="d-flex align-items-center mb-2">
                                                                    <i class="fas fa-weight-hanging text-primary me-2"></i>
                                                                    <div>
                                                                        <small class="text-muted d-block">Quantité totale:</small>
                                                                        <strong>{{ number_format($cityDetail['quantity'], 2, ',', ' ') }} tonnes</strong>
                                                                    </div>
                                                                </div>
                                                                <div class="d-flex align-items-center mb-2">
                                                                    <i class="fas fa-chart-pie text-primary me-2"></i>
                                                                    <div>
                                                                        <small class="text-muted d-block">Quantité vendue:</small>
                                                                        <strong>{{ number_format($cityDetail['quantity'] - $cityDetail['remaining_quantity'], 2, ',', ' ') }} tonnes</strong>
                                                                    </div>
                                                                </div>
                                                                <div class="d-flex align-items-center">
                                                                    <i class="fas fa-box-open text-primary me-2"></i>
                                                                    <div>
                                                                        <small class="text-muted d-block">Quantité restante:</small>
                                                                        <strong>{{ number_format($cityDetail['remaining_quantity'], 2, ',', ' ') }} tonnes</strong>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <div class="badge bg-primary">
                                                                    {{ number_format($cityDetail['price'], 0, ',', ' ') }} FCFA/tonne
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <hr>
                                                        <div class="card-info-container p-2 mb-3 border rounded">
                                                            <h6 class="fw-bold text-primary mb-3">Informations détaillées</h6>
                                                            <div class="row mb-3">
                                                                <div class="col-md-6">
                                                                    <small class="text-muted d-block">Quantité totale:</small>
                                                                    <strong class="quantity-display total-quantity">{{ number_format($cityDetail['quantity'], 2, ',', ' ') }} T</strong>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <small class="text-muted d-block">Quantité vendue:</small>
                                                                    <strong class="quantity-display used-quantity">{{ number_format($cityDetail['quantity'] - $cityDetail['remaining_quantity'], 2, ',', ' ') }} T</strong>
                                                                </div>
                                                            </div>
                                                            <div class="row mb-3">
                                                                <div class="col-md-6">
                                                                    <small class="text-muted d-block">Quantité restante:</small>
                                                                    <strong class="quantity-display remaining-quantity">{{ number_format($cityDetail['remaining_quantity'], 2, ',', ' ') }} T</strong>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <small class="text-muted d-block">Prix de vente/tonne:</small>
                                                                    <strong>{{ number_format($cityDetail['price'], 0, ',', ' ') }} FCFA</strong>
                                                                </div>
                                                            </div>
                                                            
                                                            <!-- Barre de progression pour visualiser les quantités -->
                                                            <div class="progress mb-3" style="height: 15px;">
                                                                <div class="progress-bar used-progress bg-success" role="progressbar" 
                                                                    style="width: {{ (($cityDetail['quantity'] - $cityDetail['remaining_quantity']) / $cityDetail['quantity']) * 100 }}%" 
                                                                    aria-valuenow="{{ (($cityDetail['quantity'] - $cityDetail['remaining_quantity']) / $cityDetail['quantity']) * 100 }}" 
                                                                    aria-valuemin="0" aria-valuemax="100" 
                                                                    title="Quantité vendue: {{ number_format($cityDetail['quantity'] - $cityDetail['remaining_quantity'], 2, ',', ' ') }} T">
                                                                </div>
                                                                <div class="progress-bar remaining-progress bg-info" role="progressbar" 
                                                                    style="width: {{ ($cityDetail['remaining_quantity'] / $cityDetail['quantity']) * 100 }}%" 
                                                                    aria-valuenow="{{ ($cityDetail['remaining_quantity'] / $cityDetail['quantity']) * 100 }}" 
                                                                    aria-valuemin="0" aria-valuemax="100"
                                                                    title="Quantité restante: {{ number_format($cityDetail['remaining_quantity'], 2, ',', ' ') }} T">
                                                                </div>
                                                            </div>
                                                            
                                                            <div class="row mb-3">
                                                                <div class="col-md-6">
                                                                    <small class="text-muted d-block">Véhicule:</small>
                                                                    <strong>{{ $cityDetail['vehicle']->registration_number }}</strong>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <small class="text-muted d-block">Capacité:</small>
                                                                    <strong>{{ $cityDetail['vehicle_capacity'] }} T</strong>
                                                                </div>
                                                            </div>
                                                            
                                                            <!-- Informations du chauffeur -->
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <small class="text-muted d-block">Chauffeur:</small>
                                                                    <strong>{{ $cityDetail['driver'] ? $cityDetail['driver']->first_name . ' ' . $cityDetail['driver']->last_name : 'Non assigné' }}</strong>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <small class="text-muted d-block">Téléphone:</small>
                                                                    <strong>{{ $cityDetail['driver'] ? $cityDetail['driver']->phone : 'N/A' }}</strong>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @endforeach
            </div>
        </div>

        <div class="step-content" id="step2">
            <h4 class="mb-4">2. Détails de la vente</h4>
            
            <div class="alert alert-info mb-4" id="selectedProductInfo">
                <p class="mb-0"><strong>Veuillez d'abord sélectionner un produit à l'étape 1</strong></p>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle text-primary me-2"></i>
                                Détails de la commande
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="selected-city-details mb-4">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Ville de livraison</label>
                                            <p class="mb-1"><strong id="selectedCityName"></strong></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Prix unitaire</label>
                                            <p class="mb-1"><strong id="selectedCityPrice"></strong> FCFA</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Quantité disponible</label>
                                            <p class="mb-1"><strong id="selectedCityQuantity">0</strong></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Véhicule assigné</label>
                                            <p class="mb-1">
                                                <strong id="selectedVehicleInfo"></strong>
                                                <br>
                                                <small class="text-muted">Capacité: <span id="selectedVehicleCapacity">0</span> tonnes</small>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mb-4">
                                <label for="quantity" class="form-label">Quantité à vendre (en tonnes)</label>
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control" 
                                           id="quantity" 
                                           name="quantity" 
                                           min="0.01" 
                                           step="0.01" 
                                           oninput="calculerMontant()" 
                                           required>
                                    <span class="input-group-text">tonnes</span>
                                    <button type="button" class="btn btn-primary" onclick="calculerMontant()">Calculer</button>
                                </div>
                                <script>
                                // Fonction simple et directe pour calculer le montant total et les voyages
                                function calculerMontant() {
                                    // Récupérer la quantité saisie
                                    var quantite = parseFloat(document.getElementById('quantity').value) || 0;
                                    
                                    // Récupérer le prix unitaire depuis l'élément HTML
                                    var prixElement = document.getElementById('selectedCityPrice');
                                    var prix = 0;
                                    if (prixElement) {
                                        prix = parseFloat(prixElement.textContent.replace(/[^0-9.,]/g, '').replace(',', '.')) || 0;
                                    }
                                    
                                    // Récupérer la capacité du véhicule
                                    var capaciteElement = document.getElementById('selectedVehicleCapacity');
                                    var capacite = 1; // Valeur par défaut pour éviter division par zéro
                                    if (capaciteElement) {
                                        capacite = parseFloat(capaciteElement.textContent.replace(/[^0-9.,]/g, '').replace(',', '.')) || 1;
                                    }
                                    
                                    // Vérifier si une remise est appliquée
                                    var remiseCheck = document.getElementById('remiseCheck');
                                    var remiseTonne = document.getElementById('remise_tonne');
                                    var prixFinal = prix;
                                    
                                    if (remiseCheck && remiseCheck.checked && remiseTonne) {
                                        var valeurRemise = parseFloat(remiseTonne.value) || 0;
                                        if (valeurRemise > prix) {
                                            alert('La remise ne peut pas être supérieure au prix par tonne!');
                                            remiseTonne.value = prix;
                                            valeurRemise = prix;
                                        }
                                        prixFinal = prix - valeurRemise;
                                        
                                        // Mettre à jour le montant total de la remise
                                        var remiseMontant = document.getElementById('remise_montant');
                                        if (remiseMontant) {
                                            remiseMontant.value = (valeurRemise * quantite).toLocaleString('fr-FR');
                                        }
                                        
                                        // Mettre à jour le champ caché pour la remise
                                        var hiddenRemise = document.getElementById('hiddenDiscountPerTon');
                                        if (hiddenRemise) {
                                            hiddenRemise.value = valeurRemise;
                                        }
                                    }
                                    
                                    // Vérifier si une augmentation de prix est appliquée
                                    var augmentationCheck = document.getElementById('modifierPrixCheck');
                                    var nouveauPrix = document.getElementById('nouveau_prix');
                                    
                                    if (augmentationCheck && augmentationCheck.checked && nouveauPrix) {
                                        var valeurAugmentation = parseFloat(nouveauPrix.value) || 0;
                                        prixFinal = prix + valeurAugmentation;
                                        
                                        // Mettre à jour l'affichage du nouveau prix
                                        var differencePrix = document.getElementById('difference_prix');
                                        if (differencePrix) {
                                            differencePrix.value = prixFinal.toLocaleString('fr-FR');
                                        }
                                        
                                        // Mettre à jour le champ caché pour l'augmentation
                                        var hiddenAugmentation = document.getElementById('hiddenNouveauPrix');
                                        if (hiddenAugmentation) {
                                            hiddenAugmentation.value = valeurAugmentation;
                                        }
                                    }
                                    
                                    // Calculer le montant total et le nombre de voyages
                                    var montantTotal = prixFinal * quantite;
                                    var voyages = Math.ceil(quantite / capacite);
                                    
                                    // Mettre à jour les éléments d'affichage
                                    var totalAmountElement = document.getElementById('totalAmount');
                                    if (totalAmountElement) {
                                        totalAmountElement.textContent = montantTotal.toLocaleString('fr-FR') + ' FCFA';
                                    }
                                    
                                    var requiredTripsElement = document.getElementById('requiredTrips');
                                    if (requiredTripsElement) {
                                        requiredTripsElement.textContent = voyages;
                                    }
                                    
                                    // Mettre à jour le champ caché pour la quantité
                                    var hiddenQuantity = document.getElementById('hiddenQuantity');
                                    if (hiddenQuantity) {
                                        hiddenQuantity.value = quantite;
                                    }
                                    
                                    // Mettre à jour le champ d'affichage du prix unitaire final
                                    var prixUnitaireElement = document.getElementById('prix_unitaire');
                                    if (prixUnitaireElement) {
                                        prixUnitaireElement.value = prixFinal.toLocaleString('fr-FR');
                                    }
                                    
                                    console.log('Calculs effectués:', {
                                        quantite: quantite,
                                        prix: prix,
                                        prixFinal: prixFinal,
                                        montantTotal: montantTotal,
                                        voyages: voyages
                                    });
                                }
                                </script>
                                <div class="invalid-feedback">
                                    Veuillez entrer une quantité valide
                                </div>
                                <small class="form-text text-muted">
                                    La quantité ne peut pas dépasser la quantité disponible
                                </small>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-group form-check">
                                        <input type="checkbox" class="form-check-input" id="remiseCheck" onchange="calculerMontant(); toggleRemiseFields(this.checked);">
                                        <label class="form-check-label" for="remiseCheck">Ajouter une remise/tonne</label>
                                    </div>
                                    <script>
                                    function toggleRemiseFields(checked) {
                                        if (checked) {
                                            // Désactiver l'autre option
                                            var modifierPrixCheck = document.getElementById('modifierPrixCheck');
                                            if (modifierPrixCheck) {
                                                modifierPrixCheck.checked = false;
                                            }
                                            
                                            // Afficher les champs de remise
                                            document.getElementById('remiseTonneGroup').style.display = 'block';
                                            document.getElementById('remiseMontantGroup').style.display = 'block';
                                            
                                            // Masquer les champs d'augmentation
                                            document.getElementById('nouveauPrixGroup').style.display = 'none';
                                        } else {
                                            // Masquer les champs de remise
                                            document.getElementById('remiseTonneGroup').style.display = 'none';
                                            document.getElementById('remiseMontantGroup').style.display = 'none';
                                        }
                                    }
                                    </script>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group form-check">
                                        <input type="checkbox" class="form-check-input" id="modifierPrixCheck" onchange="calculerMontant(); toggleAugmentationFields(this.checked);">
                                        <label class="form-check-label" for="modifierPrixCheck">Augmenter le Prix/Tonne</label>
                                    </div>
                                    <script>
                                    function toggleAugmentationFields(checked) {
                                        if (checked) {
                                            // Désactiver l'autre option
                                            var remiseCheck = document.getElementById('remiseCheck');
                                            if (remiseCheck) {
                                                remiseCheck.checked = false;
                                            }
                                            
                                            // Afficher les champs d'augmentation
                                            document.getElementById('nouveauPrixGroup').style.display = 'block';
                                            
                                            // Masquer les champs de remise
                                            document.getElementById('remiseTonneGroup').style.display = 'none';
                                            document.getElementById('remiseMontantGroup').style.display = 'none';
                                        } else {
                                            // Masquer les champs d'augmentation
                                            document.getElementById('nouveauPrixGroup').style.display = 'none';
                                        }
                                    }
                                    </script>
                                </div>
                            </div>
                            <div class="form-group mb-4" id="remiseTonneGroup" style="display: none;">
                                <div class="alert alert-info mb-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-arrow-down me-2"></i>
                                        <span class="fw-bold">VENTE AVEC REMISE</span>
                                    </div>
                                    <small>Vous appliquez une remise sur le prix standard. Cette vente nécessitera une validation administrative.</small>
                                </div>
                                <label for="remise_tonne" class="form-label">Remise/tonne</label>
                                <div class="input-group">
                                    <input type="number" step="0.01" min="0" class="form-control" id="remise_tonne" name="remise_tonne" oninput="calculerMontant()">
                                    <span class="input-group-text">FCFA</span>
                                </div>
                                <small class="form-text text-muted">Saisissez la remise à appliquer par tonne.</small>
                            </div>

                            <div class="form-group mb-4" id="remiseMontantGroup" style="display: none;">
                                <label class="form-label">Montant de remise</label>
                                <div class="input-group">
                                    <input type="text" class="form-control text-info" id="remise_montant" readonly>
                                    <span class="input-group-text">FCFA</span>
                                </div>
                            </div>

                            <div class="form-group mb-4" id="nouveauPrixGroup" style="display: none;">
                                <div class="alert alert-warning mb-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-arrow-up me-2"></i>
                                        <span class="fw-bold">VENTE AVEC PRIX AUGMENTÉ</span>
                                    </div>
                                    <small>Vous augmentez le prix standard. Cette vente nécessitera une validation administrative.</small>
                                </div>
                                <label for="nouveau_prix" class="form-label">Montant d'augmentation/tonne</label>
                                <div class="input-group">
                                    <input type="number" step="0.01" min="0.01" class="form-control" id="nouveau_prix" name="nouveau_prix" oninput="calculerMontant()">
                                    <span class="input-group-text">FCFA</span>
                                </div>
                                <small class="form-text text-muted">Saisissez le montant à ajouter au prix par tonne.</small>
                            </div>

                            <div class="form-group mb-4" id="differenceGroup" style="display: none;">
                                <label class="form-label">Nouveau prix total/tonne</label>
                                <div class="input-group">
                                    <input type="text" class="form-control text-warning" id="difference_prix" readonly>
                                    <span class="input-group-text">FCFA</span>
                                </div>
                            </div>

                            <div class="calculated-info bg-light p-3 rounded">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-2">
                                            <span class="text-muted">Montant total:</span>
                                            <br>
                                            <strong class="h4" id="totalAmount">0 FCFA</strong>
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-2">
                                            <span class="text-muted">Nombre de voyages nécessaires:</span>
                                            <br>
                                            <strong class="h4" id="requiredTrips">0</strong>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="step-content" id="step3">
            <h4 class="mb-4">3. Informations client</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="customer_name">Nom du client <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="customer_name" name="customer_name" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="customer_phone">Téléphone <span class="text-danger">*</span></label>
                        <input type="tel" class="form-control" id="customer_phone" name="customer_phone" required>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group mb-3">
                        <label for="customer_address">Adresse <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="customer_address" name="customer_address" rows="3" required></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Boutons de navigation -->
        <div class="d-flex justify-content-between mt-4">
            <button type="button" class="btn btn-secondary" id="prevBtn" style="display: none;">
                <i class="fas fa-arrow-left me-1"></i> Précédent
            </button>
            <button type="button" class="btn btn-primary" id="nextBtn" disabled>Suivant <i class="fas fa-arrow-right ms-1"></i></button>
            <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                <i class="fas fa-check me-1"></i> Valider la vente
            </button>
        </div>
    </form>
</div>

@push('scripts')
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script src="{{ asset('js/step-navigation.js') }}"></script>
<script src="{{ asset('js/product-details.js') }}"></script>
<script src="{{ asset('js/form-controls.js') }}"></script>
<script src="{{ asset('js/form-submit.js') }}"></script>
<script>
// Fonction pour sélectionner exactement la carte sur laquelle on a cliqué
function selectExactCard(event, supplyId, detailIndex) {
    // Arrêter la propagation de l'événement pour éviter les conflits
    if (event) {
        event.stopPropagation();
    }
    
    console.log('Sélection exacte de la carte avec ID:', supplyId, 'et index de détail:', detailIndex);
    
    // Désélectionner toutes les cartes et boutons radio
    $('.supply-radio').prop('checked', false);
    $('.product-card').removeClass('selected');
    
    // Utiliser l'ID unique qui combine l'ID d'approvisionnement et l'index de détail
    var uniqueId = supplyId + '-' + detailIndex;
    
    // Sélectionner uniquement la carte spécifiée
    var cardSelector = '#supply-card-' + uniqueId;
    var radioSelector = '#supply-radio-' + uniqueId;
    
    console.log('Recherche de la carte avec le sélecteur:', cardSelector);
    console.log('Recherche du radio avec le sélecteur:', radioSelector);
    
    // Vérifier si les éléments existent dans le DOM
    if ($(cardSelector).length === 0) {
        console.log('Carte non trouvée avec le sélecteur:', cardSelector);
        // Essayer une alternative avec un sélecteur d'attribut
        var alternativeCardSelector = '.product-card[data-supply-id="' + supplyId + '"][data-detail-index="' + detailIndex + '"]';
        console.log('Essai avec sélecteur alternatif:', alternativeCardSelector);
        if ($(alternativeCardSelector).length > 0) {
            console.log('Carte trouvée avec le sélecteur alternatif');
            $(alternativeCardSelector).addClass('selected');
        } else {
            console.log('Carte non trouvée même avec le sélecteur alternatif');
            return; // Sortir si la carte n'est pas trouvée
        }
    } else {
        // Carte trouvée avec le sélecteur normal
        $(cardSelector).addClass('selected');
        console.log('Carte sélectionnée avec succès');
    }
    
    // Vérifier et sélectionner le bouton radio
    if ($(radioSelector).length === 0) {
        console.log('Radio non trouvé avec le sélecteur:', radioSelector);
        // Essayer une alternative
        var alternativeRadioSelector = '.supply-radio[value="' + supplyId + '"][data-detail-index="' + detailIndex + '"]';
        console.log('Essai avec sélecteur alternatif pour radio:', alternativeRadioSelector);
        if ($(alternativeRadioSelector).length > 0) {
            console.log('Radio trouvé avec le sélecteur alternatif');
            $(alternativeRadioSelector).prop('checked', true);
        } else {
            console.log('Radio non trouvé même avec le sélecteur alternatif');
        }
    } else {
        // Radio trouvé avec le sélecteur normal
        $(radioSelector).prop('checked', true);
        console.log('Radio sélectionné avec succès');
    }
    
    // Mettre à jour le champ caché pour l'ID de l'approvisionnement
    $('#hiddenSupplyId').val(supplyId);
    
    // Activer le bouton Suivant
    $('#nextBtn').prop('disabled', false);
    console.log('Bouton Suivant activé');
    
    // Extraire et stocker les informations de la carte sélectionnée
    // Utiliser le sélecteur qui a fonctionné pour trouver la carte
    var selectedCard;
    if ($(cardSelector).length > 0) {
        selectedCard = $(cardSelector);
    } else {
        selectedCard = $('.product-card[data-supply-id="' + supplyId + '"][data-detail-index="' + detailIndex + '"]');
    }
    
    if (selectedCard.length === 0) {
        console.log('Impossible de trouver la carte sélectionnée pour extraire les informations');
        return;
    }
    
    var cityDetails = selectedCard.find('.city-details');
    if (cityDetails.length > 0) {
        console.log('Détails de la ville trouvés, extraction des informations...');
        
        // Récupérer les informations directement depuis les attributs data-*
        var cityName = cityDetails.data('city-name');
        var remainingQuantity = cityDetails.data('remaining-quantity');
        var price = cityDetails.data('price');
        var vehicleInfo = cityDetails.data('vehicle-info');
        var vehicleCapacity = cityDetails.data('vehicle-capacity');
        var vehicleId = cityDetails.data('vehicle-id');
        var driverId = cityDetails.data('driver-id');
        
        console.log('Informations récupérées:', {
            cityName: cityName,
            remainingQuantity: remainingQuantity,
            price: price,
            vehicleInfo: vehicleInfo,
            vehicleCapacity: vehicleCapacity,
            vehicleId: vehicleId,
            driverId: driverId
        });
        
        // Stocker les informations dans une variable globale pour une utilisation ultérieure
        window.selectedCardInfo = {
            supplyId: supplyId,
            detailIndex: detailIndex,
            uniqueId: uniqueId,
            cityName: cityName,
            remainingQuantity: remainingQuantity,
            price: price,
            vehicleInfo: vehicleInfo,
            vehicleCapacity: vehicleCapacity,
            vehicleId: vehicleId,
            driverId: driverId
        };
        
        // Pré-remplir les champs de l'étape 2 dès maintenant
        $('#selectedCityName').text(cityName || 'Non défini');
        $('#selectedCityPrice').text(price ? price.toLocaleString('fr-FR') : '0');
        $('#selectedCityQuantity').text((remainingQuantity ? remainingQuantity.toLocaleString('fr-FR') : '0') + ' tonnes');
        $('#selectedVehicleInfo').text(vehicleInfo || 'Non assigné');
        $('#selectedVehicleCapacity').text((vehicleCapacity ? vehicleCapacity.toLocaleString('fr-FR') : '0') + ' tonnes');
        
        // Mettre à jour les champs cachés
        $('#hiddenVehicleId').val(vehicleId);
        $('#hiddenDriverId').val(driverId);
        
        // Mettre à jour l'alerte de sélection de produit
        var productName = selectedCard.find('.card-title').text().trim();
        var infoElement = $('#selectedProductInfo');
        if (infoElement.length > 0) {
            infoElement.removeClass('alert-info').addClass('alert-success');
            infoElement.html(`
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <strong class="d-block mb-2">Produit sélectionné: ${productName}</strong>
                        <small class="d-block text-muted">Cliquez sur "Précédent" pour changer de produit</small>
                    </div>
                    <span class="badge bg-success">Produit disponible</span>
                </div>
            `);
        }
    } else {
        console.log('Détails de la ville non trouvés dans la carte sélectionnée');
    }
    
    // Si la fonction updateProductDetails est disponible, l'appeler
    if (typeof updateProductDetails === 'function') {
        updateProductDetails(supplyId, detailIndex);
    } else {
        console.log('La fonction updateProductDetails n\'est pas disponible, mise à jour manuelle des détails');
    }
}
</script>
<script>
// Fonction directe pour calculer et mettre à jour les valeurs
function calculateAndUpdateValues() {
    console.log('Début de la fonction calculateAndUpdateValues');
    
    try {
        // Récupérer les éléments nécessaires
        var quantityInput = document.getElementById('quantity');
        console.log('Champ de quantité trouvé:', quantityInput ? 'Oui' : 'Non');
        
        if (!quantityInput) {
            console.error('Champ de quantité non trouvé, impossible de continuer les calculs');
            return;
        }
        
        // Récupérer la quantité saisie
        var quantity = parseFloat(quantityInput.value) || 0;
        console.log('Quantité saisie:', quantity);
        
        // Récupérer le prix et la capacité du véhicule
        var price = 0;
        var vehicleCapacity = 1; // Éviter la division par zéro
        
        // Essayer de récupérer les valeurs depuis window.selectedCardInfo
        if (window.selectedCardInfo) {
            console.log('window.selectedCardInfo disponible:', window.selectedCardInfo);
            price = parseFloat(window.selectedCardInfo.price) || 0;
            vehicleCapacity = parseFloat(window.selectedCardInfo.vehicleCapacity) || 1;
        } 
        // Sinon, essayer de récupérer les valeurs depuis les éléments HTML
        else {
            console.log('window.selectedCardInfo non disponible, tentative de récupération depuis les éléments HTML');
            
            var priceElement = document.getElementById('selectedCityPrice');
            if (priceElement) {
                var priceText = priceElement.textContent.replace(/[^0-9.,]/g, '').replace(',', '.');
                price = parseFloat(priceText) || 0;
            }
            
            var capacityElement = document.getElementById('selectedVehicleCapacity');
            if (capacityElement) {
                var capacityText = capacityElement.textContent.replace(/[^0-9.,]/g, '').replace(',', '.');
                vehicleCapacity = parseFloat(capacityText) || 1;
            }
        }
        
        var prixUnitaireFinal = price; // Valeur par défaut
        
        console.log('Valeurs initiales:', {
            quantity: quantity,
            price: price,
            vehicleCapacity: vehicleCapacity
        });
        
        // Vérifier si une augmentation de prix est appliquée
        var modifierPrixCheck = document.getElementById('modifierPrixCheck');
        var nouveauPrixInput = document.getElementById('nouveau_prix');
        var differencePrixEl = document.getElementById('difference_prix');
        
        if (modifierPrixCheck && modifierPrixCheck.checked && nouveauPrixInput) {
            var augmentation = parseFloat(nouveauPrixInput.value) || 0;
            
            // Ajouter l'augmentation au prix original
            prixUnitaireFinal = price + augmentation;
            
            // Afficher le nouveau prix total
            if (differencePrixEl) {
                differencePrixEl.value = prixUnitaireFinal.toLocaleString('fr-FR');
                
                // Toujours en orange car c'est une augmentation
                differencePrixEl.classList.add('text-warning');
                differencePrixEl.classList.remove('text-success', 'text-info');
            }
            
            // Mettre à jour le champ caché
            var hiddenNouveauPrix = document.getElementById('hiddenNouveauPrix');
            if (hiddenNouveauPrix) {
                hiddenNouveauPrix.value = augmentation;
            }
            
            console.log('Augmentation de prix appliquée:', {
                augmentation: augmentation,
                prixUnitaireFinal: prixUnitaireFinal
            });
        }
        
        // Vérifier si une remise est appliquée
        var remiseCheck = document.getElementById('remiseCheck');
        var remiseTonneInput = document.getElementById('remise_tonne');
        var remiseMontantEl = document.getElementById('remise_montant');
        
        if (remiseCheck && remiseCheck.checked && remiseTonneInput) {
            var remiseParTonne = parseFloat(remiseTonneInput.value) || 0;
            
            // Vérifier que la remise n'est pas supérieure au prix
            if (remiseParTonne > price) {
                alert('La remise ne peut pas être supérieure au prix par tonne!');
                remiseTonneInput.value = price;
                remiseParTonne = price;
            }
            
            var remiseTotale = remiseParTonne * quantity;
            
            // Afficher le montant de la remise
            if (remiseMontantEl) {
                remiseMontantEl.value = remiseTotale.toLocaleString('fr-FR');
            }
            
            // Mettre à jour le champ caché pour la remise
            var hiddenDiscountPerTon = document.getElementById('hiddenDiscountPerTon');
            if (hiddenDiscountPerTon) {
                hiddenDiscountPerTon.value = remiseParTonne;
            }
            
            // Recalculer le prix unitaire final (après remise)
            prixUnitaireFinal = price - remiseParTonne;
            
            console.log('Remise appliquée:', {
                remiseParTonne: remiseParTonne,
                remiseTotale: remiseTotale,
                prixUnitaireFinal: prixUnitaireFinal
            });
        }
        
        // Calculer le montant total avec le prix unitaire final
        var totalAmount = prixUnitaireFinal * quantity;
        var trips = Math.ceil(quantity / vehicleCapacity);
        
        // Mettre à jour les champs d'affichage
        var prixUnitaireEl = document.getElementById('prix_unitaire');
        if (prixUnitaireEl) {
            prixUnitaireEl.value = prixUnitaireFinal.toLocaleString('fr-FR');
        }
        
        // Mettre à jour le montant total et le nombre de voyages nécessaires
        var totalAmountEl = document.getElementById('totalAmount');
        if (totalAmountEl) {
            totalAmountEl.textContent = totalAmount.toLocaleString('fr-FR') + ' FCFA';
        }
        
        var requiredTripsEl = document.getElementById('requiredTrips');
        if (requiredTripsEl) {
            requiredTripsEl.textContent = trips;
        }
        
        // Mettre à jour le champ caché pour la quantité
        var hiddenQuantity = document.getElementById('hiddenQuantity');
        if (hiddenQuantity) {
            hiddenQuantity.value = quantity;
        }
        
        console.log('Calculs finaux:', {
            prixUnitaireFinal: prixUnitaireFinal,
            totalAmount: totalAmount,
            trips: trips
        });
    } catch (error) {
        console.error('Erreur lors des calculs:', error);
    }
}

// Fonction pour initialiser les calculs automatiques à l'étape 2
function initializeStep2Calculations() {
    console.log('Initialisation des calculs automatiques pour l\'\u00e9tape 2');
    
    // Vérifier si nous sommes à l'étape 2
    if ($('#step2').hasClass('active')) {
        console.log('Nous sommes à l\'\u00e9tape 2, initialisation des calculs');
        
        // Récupérer le champ de quantité
        var quantityInput = $('#quantity');
        
        if (quantityInput.length > 0) {
            console.log('Champ de quantité trouvé, ajout des écouteurs d\'\u00e9vénements');
            
            // Détacher tous les écouteurs d'événements existants
            quantityInput.off('input');
            
            // Ajouter un nouvel écouteur d'événement
            quantityInput.on('input', function() {
                console.log('Changement de quantité détecté:', $(this).val());
                calculateAndUpdateValues();
            });
            
            // Déclencher manuellement l'événement input pour initialiser les calculs
            setTimeout(function() {
                console.log('Déclenchement manuel de l\'\u00e9vénement input sur le champ de quantité');
                quantityInput.trigger('input');
            }, 500);
        } else {
            console.warn('Champ de quantité non trouvé à l\'\u00e9tape 2');
        }
    } else {
        console.log('Nous ne sommes pas à l\'\u00e9tape 2, initialisation ignorée');
    }
}

$(document).ready(function() {
    console.log('Script de navigation entre les étapes chargé');
    
    // Variables globales
    var currentStep = 1;
    var totalSteps = 3;
    
    // Initialiser les calculs automatiques après le chargement de la page
    setTimeout(initializeStep2Calculations, 1000);
    
    // Empêcher la propagation des clics sur les détails de ville
    $(document).ready(function() {
        $('.city-details').on('click', function(e) {
            e.stopPropagation();
        });
    });
    
    // Fonction pour passer à l'étape suivante
    function nextStep() {
        console.log('Fonction nextStep appelée, étape actuelle:', currentStep);
        
        // Vérifier si une carte est sélectionnée à l'étape 1
        if (currentStep === 1) {
            var selectedRadio = $('input.supply-radio:checked');
            console.log('Radio sélectionné:', selectedRadio.length > 0 ? selectedRadio.val() : 'aucun');
            
            if (selectedRadio.length === 0) {
                alert('Veuillez sélectionner un produit avant de continuer.');
                return false;
            }
            
            var supplyId = selectedRadio.val();
            var detailIndex = selectedRadio.data('detail-index');
            var uniqueId = supplyId + '-' + detailIndex;
            
            console.log('ID unique de la carte sélectionnée:', uniqueId);
            
            // Vérifier si les informations de la carte ont déjà été stockées par selectExactCard
            if (!window.selectedCardInfo || window.selectedCardInfo.uniqueId !== uniqueId) {
                console.log('Les informations de la carte ne sont pas stockées ou sont différentes, appel de selectExactCard');
                // Appeler selectExactCard pour récupérer et stocker les informations
                selectExactCard(null, supplyId, detailIndex);
            } else {
                console.log('Informations de la carte déjà stockées:', window.selectedCardInfo);
            }
            
            // Mettre à jour le champ caché pour l'ID de l'approvisionnement
            $('#hiddenSupplyId').val(supplyId);
        }
        
        // Masquer l'étape actuelle
        $('#step' + currentStep).removeClass('active');
        $('.progress-step:nth-child(' + currentStep + ')').removeClass('active');
        
        // Passer à l'étape suivante
        currentStep++;
        
        // Afficher la nouvelle étape
        $('#step' + currentStep).addClass('active');
        $('.progress-step:nth-child(' + currentStep + ')').addClass('active');
        
        // Afficher/masquer les boutons selon l'étape
        if (currentStep > 1) {
            $('#prevBtn').show();
        }
        
        if (currentStep === totalSteps) {
            $('#nextBtn').hide();
            $('#submitBtn').show();
        }
        
        // Mettre à jour les informations selon l'étape
        updateStepInfo();
        
        return true;
    }
    
    // Fonction pour revenir à l'étape précédente
    function prevStep() {
        // Masquer l'étape actuelle
        $('#step' + currentStep).removeClass('active');
        $('.progress-step:nth-child(' + currentStep + ')').removeClass('active');
        
        // Revenir à l'étape précédente
        currentStep--;
        
        // Afficher la nouvelle étape
        $('#step' + currentStep).addClass('active');
        $('.progress-step:nth-child(' + currentStep + ')').addClass('active');
        
        // Masquer le bouton précédent si on est à la première étape
        if (currentStep === 1) {
            $('#prevBtn').hide();
        }
        
        // Afficher le bouton suivant et masquer le bouton de soumission
        $('#nextBtn').show();
        $('#submitBtn').hide();
        
        return true;
    }
    
    // Fonction pour mettre à jour les informations selon l'étape
    function updateStepInfo() {
        console.log('Fonction updateStepInfo appelée, étape actuelle:', currentStep);
        
        if (currentStep === 2) {
            console.log('Mise à jour des informations pour l\'\u00e9tape 2');
            
            // Vérifier si nous avons des informations stockées sur la carte sélectionnée
            if (window.selectedCardInfo) {
                console.log('Informations de carte trouvées:', window.selectedCardInfo);
                
                // Récupérer les informations directement depuis window.selectedCardInfo
                var cityName = window.selectedCardInfo.cityName;
                var remainingQuantity = window.selectedCardInfo.remainingQuantity;
                var price = window.selectedCardInfo.price;
                var vehicleInfo = window.selectedCardInfo.vehicleInfo;
                var vehicleCapacity = window.selectedCardInfo.vehicleCapacity;
                var vehicleId = window.selectedCardInfo.vehicleId;
                var driverId = window.selectedCardInfo.driverId;
                
                // Définir la variable globale pour les calculs automatiques
                window.selectedCityDetails = {
                    price: parseFloat(price) || 0,
                    vehicleCapacity: parseFloat(vehicleCapacity) || 0,
                    remainingQuantity: parseFloat(remainingQuantity) || 0
                };
                
                console.log('Variable window.selectedCityDetails initialisée pour les calculs:', window.selectedCityDetails);
                
                // Appeler updateCalculations si la fonction est disponible
                if (typeof updateCalculations === 'function') {
                    console.log('Appel de updateCalculations depuis updateStepInfo');
                    setTimeout(function() {
                        updateCalculations();
                    }, 200);
                } else {
                    console.warn('La fonction updateCalculations n\'est pas disponible');
                }
                
                // Mettre à jour les champs cachés
                $('#hiddenVehicleId').val(vehicleId);
                $('#hiddenDriverId').val(driverId);
                
                // Trouver le nom du produit
                var productName = "Produit sélectionné";
                var selectedCard = $('#supply-card-' + window.selectedCardInfo.uniqueId);
                if (selectedCard.length > 0) {
                    productName = selectedCard.find('.card-title').text().trim();
                } else {
                    // Essayer une approche alternative
                    var alternativeCardSelector = '.product-card[data-supply-id="' + window.selectedCardInfo.supplyId + '"][data-detail-index="' + window.selectedCardInfo.detailIndex + '"]';
                    var altCard = $(alternativeCardSelector);
                    if (altCard.length > 0) {
                        productName = altCard.find('.card-title').text().trim();
                    }
                }
                
                // Mettre à jour le résumé du produit sélectionné
                var infoElement = $('#selectedProductInfo');
                if (infoElement.length > 0) {
                    infoElement.removeClass('alert-info').addClass('alert-success');
                    infoElement.html(`
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong class="d-block mb-2">Produit sélectionné: ${productName}</strong>
                                <small class="d-block text-muted">Cliquez sur "Précédent" pour changer de produit</small>
                            </div>
                            <span class="badge bg-success">Produit disponible</span>
                        </div>
                    `);
                }
                
                // Mettre à jour les détails dans le tableau "Détails de la commande"
                $('#selectedCityName').text(cityName || 'Non défini');
                $('#selectedCityPrice').text(price ? parseFloat(price).toLocaleString('fr-FR') : '0');
                $('#selectedCityQuantity').text((remainingQuantity ? parseFloat(remainingQuantity).toLocaleString('fr-FR') : '0') + ' tonnes');
                $('#selectedVehicleInfo').text(vehicleInfo || 'Non assigné');
                $('#selectedVehicleCapacity').text((vehicleCapacity ? parseFloat(vehicleCapacity).toLocaleString('fr-FR') : '0') + ' tonnes');
                
                // Ajouter une animation pour mettre en évidence les informations mises à jour
                $('.selected-city-details .form-label').each(function() {
                    $(this).addClass('text-primary');
                    setTimeout(() => {
                        $(this).removeClass('text-primary');
                    }, 2000);
                });
                
                // Configurer le champ de quantité
                var quantityInput = $('#quantity');
                if (quantityInput.length > 0) {
                    quantityInput.prop('disabled', false);
                    quantityInput.attr('min', 0.01);
                    quantityInput.attr('max', remainingQuantity);
                    
                    // Réinitialiser la valeur du champ de quantité
                    quantityInput.val('');
                    
                    // Les variables supplyId, detailIndex, etc. sont déjà définies plus haut
                    // Nous utilisons les valeurs déjà récupérées de window.selectedCardInfo
                    
                    console.log('window.selectedCardInfo mis à jour:', window.selectedCardInfo);
                    
                    // Ajouter un événement pour calculer le montant total lorsque la quantité change
                    quantityInput.off('input').on('input', function() {
                        console.log('Événement input sur le champ de quantité déclenché');
                        // Appeler la fonction globale de calcul
                        calculateAndUpdateValues();
                    });
                    
                    // Déclencher manuellement l'événement input pour initialiser les calculs
                    setTimeout(function() {
                        console.log('Déclenchement manuel de l\'événement input sur le champ de quantité');
                        quantityInput.trigger('input');
                    }, 300);
                    
                    // Ajouter des gestionnaires d'événements pour les remises et augmentations de prix
                    var remiseCheck = $('#remiseCheck');
                    var remiseTonneInput = $('#remise_tonne');
                    var modifierPrixCheck = $('#modifierPrixCheck');
                    var nouveauPrixInput = $('#nouveau_prix');
                    
                    if (remiseCheck && remiseCheck.length > 0) {
                        remiseCheck.off('change').on('change', function() {
                            if (this.checked) {
                                // Désactiver l'autre option
                                if (modifierPrixCheck && modifierPrixCheck.length > 0) {
                                    modifierPrixCheck.prop('checked', false);
                                }
                                
                                // Afficher le groupe de remise par tonne
                                $('#remiseTonneGroup').show();
                                $('#remiseMontantGroup').show();
                                
                                // Masquer le groupe de nouveau prix
                                $('#nouveauPrixGroup').hide();
                                
                                // Réinitialiser le champ de nouveau prix
                                if (nouveauPrixInput && nouveauPrixInput.length > 0) {
                                    nouveauPrixInput.val('');
                                }
                            } else {
                                // Masquer les groupes de remise
                                $('#remiseTonneGroup').hide();
                                $('#remiseMontantGroup').hide();
                                
                                // Réinitialiser le champ de remise
                                if (remiseTonneInput && remiseTonneInput.length > 0) {
                                    remiseTonneInput.val('');
                                }
                            }
                            
                            // Mettre à jour les calculs
                            calculateAndUpdateValues();
                        });
                    }
                    
                    if (modifierPrixCheck && modifierPrixCheck.length > 0) {
                        modifierPrixCheck.off('change').on('change', function() {
                            if (this.checked) {
                                // Désactiver l'autre option
                                if (remiseCheck && remiseCheck.length > 0) {
                                    remiseCheck.prop('checked', false);
                                }
                                
                                // Afficher le groupe de nouveau prix
                                $('#nouveauPrixGroup').show();
                                
                                // Masquer les groupes de remise
                                $('#remiseTonneGroup').hide();
                                $('#remiseMontantGroup').hide();
                                
                                // Réinitialiser le champ de remise
                                if (remiseTonneInput && remiseTonneInput.length > 0) {
                                    remiseTonneInput.val('');
                                }
                            } else {
                                // Masquer le groupe de nouveau prix
                                $('#nouveauPrixGroup').hide();
                                
                                // Réinitialiser le champ de nouveau prix
                                if (nouveauPrixInput && nouveauPrixInput.length > 0) {
                                    nouveauPrixInput.val('');
                                }
                            }
                            
                            // Mettre à jour les calculs
                            calculateAndUpdateValues();
                        });
                    }
                    
                    if (remiseTonneInput && remiseTonneInput.length > 0) {
                        remiseTonneInput.off('input').on('input', function() {
                            calculateAndUpdateValues();
                        });
                    }
                    
                    if (nouveauPrixInput && nouveauPrixInput.length > 0) {
                        nouveauPrixInput.off('input').on('input', function() {
                            calculateAndUpdateValues();
                        });
                    }
                }
            } else {
                console.error('Aucune information de carte sélectionnée disponible');
                
                // Essayer de récupérer les informations depuis le radio sélectionné
                var selectedRadio = $('input.supply-radio:checked');
                if (selectedRadio.length > 0) {
                    var supplyId = selectedRadio.val();
                    var detailIndex = selectedRadio.data('detail-index');
                    console.log('Radio sélectionné trouvé, appel de selectExactCard');
                    // Appeler selectExactCard pour récupérer et stocker les informations
                    selectExactCard(null, supplyId, detailIndex);
                } else {
                    console.error('Aucun bouton radio sélectionné');
                }
            }
        }
    }
    
    // Gestionnaires d'événements pour les boutons de navigation
    $('#nextBtn').on('click', function(e) {
        e.preventDefault();
        console.log('Bouton Suivant cliqué');
        
        // Si nous sommes à l'étape 1, vérifier qu'une carte est sélectionnée
        if (currentStep === 1) {
            var selectedRadio = $('input.supply-radio:checked');
            if (selectedRadio.length === 0) {
                alert('Veuillez sélectionner un produit avant de continuer.');
                return;
            }
            
            // Récupérer les informations de la carte sélectionnée
            var supplyId = selectedRadio.val();
            var detailIndex = selectedRadio.data('detail-index');
            var uniqueId = supplyId + '-' + detailIndex;
            
            console.log('Carte sélectionnée, ID unique:', uniqueId);
            
            // Vérifier si les informations complètes sont déjà stockées par selectExactCard
            if (!window.selectedCardInfo || window.selectedCardInfo.uniqueId !== uniqueId) {
                console.log('Les informations complètes ne sont pas stockées, appel de selectExactCard');
                // Appeler selectExactCard pour récupérer et stocker les informations complètes
                selectExactCard(null, supplyId, detailIndex);
            } else {
                console.log('Informations complètes déjà stockées:', window.selectedCardInfo);
            }
            
            // Mettre à jour le champ caché pour l'ID de l'approvisionnement
            $('#hiddenSupplyId').val(supplyId);
        }
        
        // Passer à l'étape suivante
        nextStep();
        
        // Si nous passons à l'étape 2, forcer la mise à jour des informations après un court délai
        if (currentStep === 2) {
            setTimeout(function() {
                console.log('Mise à jour forcée des informations à l\'\u00e9tape 2');
                updateStepInfo();
                
                // Déclencher les calculs automatiques après un court délai supplémentaire
                setTimeout(function() {
                    console.log('Déclenchement des calculs automatiques à l\'\u00e9tape 2');
                    
                    // Appeler notre nouvelle fonction de calcul directe
                    if (typeof calculerMontant === 'function') {
                        console.log('Appel de calculerMontant depuis le gestionnaire du bouton Suivant');
                        calculerMontant();
                        
                        // Ajouter un gestionnaire d'événement pour le champ de quantité
                        var quantityInput = document.getElementById('quantity');
                        if (quantityInput) {
                            console.log('Configuration des événements pour le champ de quantité');
                            // S'assurer que le champ est prêt à recevoir des entrées
                            quantityInput.disabled = false;
                            
                            // Déclencher un événement input pour initialiser les calculs
                            // Utiliser un petit délai pour s'assurer que tout est chargé
                            setTimeout(function() {
                                // Simuler une saisie pour déclencher les calculs
                                if (quantityInput.value === '') {
                                    quantityInput.value = '0.01';
                                    calculerMontant();
                                }
                            }, 500);
                        }
                    } else {
                        console.warn('La fonction calculerMontant n\'est pas disponible');
                    }
                }, 300);
            }, 100);
        }
    });
    
    $('#prevBtn').on('click', function(e) {
        e.preventDefault();
        prevStep();
    });
});

    // Variable pour stocker l'ID de la dernière vente rejetée vérifiée
    let lastCheckedRejectedSaleId = 0;
    
    // Fonction pour vérifier les ventes rejetées
    function checkRejectedSales() {
        fetch('/cement-manager/check-rejected-sales?last_checked=' + lastCheckedRejectedSaleId)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.rejectedSales && data.rejectedSales.length > 0) {
                    // Mettre à jour l'ID de la dernière vente rejetée vérifiée
                    lastCheckedRejectedSaleId = data.rejectedSales[0].id;
                    
                    // Afficher la notification
                    const sale = data.rejectedSales[0];
                    document.getElementById('rejectedSaleMessage').textContent = `Vente #${sale.id} de ${sale.quantity} tonnes pour ${sale.customer_name} a été rejetée.`;
                    document.getElementById('rejectedSaleReason').textContent = sale.admin_note;
                    document.getElementById('rejectedSalesNotification').classList.remove('d-none');
                    
                    // Mettre à jour les quantités des cartes correspondantes
                    if (sale.supply_id && sale.vehicle_id) {
                        console.log('Mise à jour des cartes pour la vente rejetée:', sale);
                        
                        // Afficher une notification pour informer l'utilisateur
                        Swal.fire({
                            title: 'Vente rejetée',
                            html: `<p>La vente #${sale.id} ${sale.discount_per_ton > 0 ? 'avec remise' : (sale.price_modified ? 'avec augmentation de prix' : '')} a été rejetée.</p>
                                  <p><strong>Raison:</strong> ${sale.admin_note || 'Non spécifiée'}</p>
                                  <p><strong>Quantité retournée:</strong> ${sale.quantity} tonnes</p>
                                  <p>Les quantités vont être mises à jour...</p>`,
                            icon: 'info',
                            confirmButtonText: 'OK',
                            allowOutsideClick: false
                        }).then(() => {
                            // Utiliser une requête AJAX pour récupérer les données mises à jour
                            // au lieu de recharger toute la page
                            refreshSupplyData(sale.supply_id, sale.vehicle_id, sale.quantity);
                        });
                        return;
                        
                        // Si ce n'est pas une vente avec remise/augmentation, continuer avec la mise à jour dynamique
                        const cards = document.querySelectorAll('.product-card');
                        let cardUpdated = false; // Pour savoir si une carte a été mise à jour
                        
                        cards.forEach(card => {
                            const supplyId = card.getAttribute('data-supply-id');
                            if (supplyId == sale.supply_id) {
                                console.log('Carte trouvée pour supply_id:', supplyId);
                                
                                // Vérifier les détails de la ville dans cette carte
                                const cityDetails = card.querySelectorAll('.city-details');
                                cityDetails.forEach(cityDetail => {
                                    const vehicleId = cityDetail.getAttribute('data-vehicle-id');
                                    if (vehicleId == sale.vehicle_id) {
                                        console.log('Détails de ville trouvés pour vehicle_id:', vehicleId);
                                        cardUpdated = true; // Marquer qu'une carte a été mise à jour
                                        
                                        try {
                                            // Mettre à jour les quantités
                                            const currentRemainingQuantity = parseFloat(cityDetail.getAttribute('data-remaining-quantity'));
                                            const totalQuantity = parseFloat(cityDetail.getAttribute('data-total-quantity'));
                                            const newRemainingQuantity = currentRemainingQuantity + parseFloat(sale.quantity);
                                            const newUsedQuantity = totalQuantity - newRemainingQuantity;
                                            console.log('Quantité restante actuelle:', currentRemainingQuantity, 'Nouvelle quantité restante:', newRemainingQuantity);
                                            console.log('Quantité totale:', totalQuantity, 'Quantité utilisée mise à jour:', newUsedQuantity);
                                            
                                            // Mettre à jour les attributs de données
                                            cityDetail.setAttribute('data-remaining-quantity', newRemainingQuantity.toFixed(2));
                                            cityDetail.setAttribute('data-used-quantity', newUsedQuantity.toFixed(2));
                                            
                                            // Mettre à jour directement tous les éléments qui affichent la quantité restante
                                            // D'abord chercher les éléments avec la classe quantity-display
                                            const quantityDisplays = cityDetail.querySelectorAll('.quantity-display');
                                            if (quantityDisplays.length > 0) {
                                                quantityDisplays.forEach(el => {
                                                    if (el.classList.contains('remaining-quantity')) {
                                                        el.textContent = newRemainingQuantity.toFixed(2) + ' T';
                                                        // Ajouter une animation pour mettre en évidence la mise à jour
                                                        el.style.transition = 'background-color 0.5s';
                                                        el.style.backgroundColor = 'rgba(0, 255, 0, 0.2)';
                                                        setTimeout(() => {
                                                            el.style.backgroundColor = 'transparent';
                                                        }, 2000);
                                                    } else if (el.classList.contains('used-quantity')) {
                                                        el.textContent = newUsedQuantity.toFixed(2) + ' T';
                                                    }
                                                });
                                            } else {
                                                // Utiliser un sélecteur plus précis pour trouver l'élément exact (compatibilité avec l'ancien format)
                                                cityDetail.querySelectorAll('.d-flex.align-items-center').forEach(el => {
                                                    if (el.innerHTML.includes('fa-box-open')) {
                                                    const strongEl = el.querySelector('strong');
                                                    if (strongEl) {
                                                        const formattedQuantity = new Intl.NumberFormat('fr-FR', { 
                                                            minimumFractionDigits: 2, 
                                                            maximumFractionDigits: 2 
                                                        }).format(newRemainingQuantity);
                                                        strongEl.textContent = `${formattedQuantity} tonnes`;
                                                        console.log('Mise à jour de l\'affichage de la quantité restante:', formattedQuantity);
                                                        
                                                        // Ajouter une animation pour mettre en évidence la mise à jour
                                                        $(strongEl).fadeOut(200).fadeIn(200).fadeOut(200).fadeIn(200);
                                                    }
                                                }
                                                
                                                // Mettre à jour également la quantité vendue si l'élément contient l'icône chart-pie
                                                if (el.innerHTML.includes('fa-chart-pie')) {
                                                    const strongEl = el.querySelector('strong');
                                                    if (strongEl) {
                                                        const usedQuantity = parseFloat(cityDetail.getAttribute('data-used-quantity'));
                                                        const newUsedQuantity = Math.max(0, usedQuantity - parseFloat(sale.quantity));
                                                        cityDetail.setAttribute('data-used-quantity', newUsedQuantity.toFixed(2));
                                                        
                                                        const formattedQuantity = new Intl.NumberFormat('fr-FR', { 
                                                            minimumFractionDigits: 2, 
                                                            maximumFractionDigits: 2 
                                                        }).format(newUsedQuantity);
                                                        strongEl.textContent = `${formattedQuantity} tonnes`;
                                                        console.log('Mise à jour de l\'affichage de la quantité vendue:', formattedQuantity);
                                                    }
                                                }
                                            });
                                            
                                            // Mettre à jour les barres de progression si elles existent
                                            const progressBars = cityDetail.querySelectorAll('.progress-bar');
                                            if (progressBars.length > 0) {
                                                const percentUsed = (newUsedQuantity / totalQuantity) * 100;
                                                const percentRemaining = (newRemainingQuantity / totalQuantity) * 100;
                                                
                                                progressBars.forEach(bar => {
                                                    if (bar.classList.contains('used-progress')) {
                                                        bar.style.width = percentUsed.toFixed(1) + '%';
                                                        bar.setAttribute('aria-valuenow', percentUsed.toFixed(1));
                                                    } else if (bar.classList.contains('remaining-progress')) {
                                                        bar.style.width = percentRemaining.toFixed(1) + '%';
                                                        bar.setAttribute('aria-valuenow', percentRemaining.toFixed(1));
                                                    }
                                                });
                                            }
                                            
                                            // Si la vente rejetée avait une remise ou une augmentation de prix,
                                            // ajouter une notification spécifique dans la carte
                                            if (sale.discount_per_ton > 0 || sale.price_modified) {
                                                const notificationDiv = document.createElement('div');
                                                notificationDiv.className = 'alert alert-warning mt-2 mb-0 py-2 px-3';
                                                notificationDiv.style.fontSize = '0.85rem';
                                                
                                                if (sale.discount_per_ton > 0) {
                                                    notificationDiv.innerHTML = `<i class="fas fa-info-circle me-1"></i> Vente avec remise de ${sale.discount_per_ton} FCFA/tonne rejetée. Quantité de ${sale.quantity} tonnes retournée.`;
                                                } else if (sale.price_modified) {
                                                    notificationDiv.innerHTML = `<i class="fas fa-info-circle me-1"></i> Vente avec augmentation de prix rejetée. Quantité de ${sale.quantity} tonnes retournée.`;
                                                }
                                                
                                                // Vérifier si une notification similaire existe déjà
                                                const existingNotifications = cityDetail.querySelectorAll('.alert-warning');
                                                if (existingNotifications.length === 0) {
                                                    cityDetail.appendChild(notificationDiv);
                                                    
                                                    // Faire disparaître la notification après 30 secondes
                                                    setTimeout(() => {
                                                        $(notificationDiv).fadeOut(500, function() {
                                                            $(this).remove();
                                                        });
                                                    }, 30000);
                                                }
                                            }
                                        } catch (e) {
                                            console.error('Erreur lors de la mise à jour des quantités:', e);
                                        }
                                    }
                                });
                            }
                        });
                        
                        // Si aucune carte n'a été mise à jour mais que nous avons les informations de la carte sélectionnée
                        if (!cardUpdated && window.selectedCardInfo && window.selectedCardInfo.supplyId == sale.supply_id) {
                            console.log('Utilisation des informations de la carte sélectionnée pour la mise à jour');
                            const cardId = window.selectedCardInfo.cardId;
                            const card = document.getElementById(cardId);
                            
                            if (card) {
                                // Mettre en évidence la carte pour indiquer qu'elle a été mise à jour
                                $(card).addClass('border-warning');
                                setTimeout(() => {
                                    $(card).removeClass('border-warning');
                                }, 5000);
                                
                                // Ajouter une notification spécifique dans la carte
                                const notificationDiv = document.createElement('div');
                                notificationDiv.className = 'alert alert-warning mt-3 mb-0 py-2';
                                notificationDiv.style.fontSize = '0.85rem';
                                
                                if (sale.discount_per_ton > 0) {
                                    notificationDiv.innerHTML = `<i class="fas fa-info-circle me-1"></i> Vente avec remise de ${sale.discount_per_ton} FCFA/tonne rejetée. Quantité de ${sale.quantity} tonnes retournée.`;
                                } else if (sale.price_modified) {
                                    notificationDiv.innerHTML = `<i class="fas fa-info-circle me-1"></i> Vente avec augmentation de prix rejetée. Quantité de ${sale.quantity} tonnes retournée.`;
                                } else {
                                    notificationDiv.innerHTML = `<i class="fas fa-info-circle me-1"></i> Vente rejetée. Quantité de ${sale.quantity} tonnes retournée.`;
                                }
                                
                                // Vérifier si une notification similaire existe déjà
                                const existingNotifications = card.querySelectorAll('.alert-warning');
                                if (existingNotifications.length === 0) {
                                    // Ajouter la notification à la fin de la carte
                                    const cardBody = card.querySelector('.card-body');
                                    if (cardBody) {
                                        cardBody.appendChild(notificationDiv);
                                        
                                        // Faire disparaître la notification après 30 secondes
                                        setTimeout(() => {
                                            $(notificationDiv).fadeOut(500, function() {
                                                $(this).remove();
                                            });
                                        }, 30000);
                                    }
                                }
                            }
                        }
                    }
                }
            })
            .catch(error => console.error('Erreur lors de la vérification des ventes rejetées:', error));
    }
    
    // Vérifier les ventes rejetées toutes les 30 secondes
    setInterval(checkRejectedSales, 30000);
    
    // Vérifier les ventes rejetées au chargement de la page
    checkRejectedSales();
    
    // Initialiser la variable globale pour stocker les informations de la carte sélectionnée
    window.selectedCardInfo = null;
    
    // Fonction pour rafraîchir les données d'une carte spécifique après rejet d'une vente
    function refreshSupplyData(supplyId, vehicleId, returnedQuantity) {
        // Afficher un indicateur de chargement
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        }
        
        // Faire une requête AJAX pour obtenir les données mises à jour
        fetch(`/cement-manager/get-supply-card-data?supply_id=${supplyId}&vehicle_id=${vehicleId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Données mises à jour reçues:', data);
                    
                    // Trouver toutes les cartes correspondant à cet approvisionnement
                    const cards = document.querySelectorAll(`.product-card[data-supply-id="${supplyId}"]`);
                    let cardUpdated = false;
                    
                    cards.forEach(card => {
                        // Trouver les détails de la ville dans cette carte
                        const cityDetails = card.querySelectorAll('.city-details');
                        cityDetails.forEach(cityDetail => {
                            const vehicleIdAttr = cityDetail.getAttribute('data-vehicle-id');
                            if (vehicleIdAttr == vehicleId) {
                                cardUpdated = true;
                                console.log('Détails de ville trouvés pour vehicle_id:', vehicleId);
                                
                                // Mettre à jour les attributs de données avec les nouvelles valeurs
                                if (data.cityDetail) {
                                    const newRemainingQuantity = parseFloat(data.cityDetail.remaining_quantity);
                                    const newUsedQuantity = parseFloat(data.cityDetail.used_quantity);
                                    
                                    // Mettre à jour les attributs
                                    cityDetail.setAttribute('data-remaining-quantity', newRemainingQuantity.toFixed(2));
                                    cityDetail.setAttribute('data-used-quantity', newUsedQuantity.toFixed(2));
                                    
                                    // Mettre à jour l'affichage des quantités
                                    const quantityElements = cityDetail.querySelectorAll('.quantity-display');
                                    quantityElements.forEach(el => {
                                        if (el.classList.contains('remaining-quantity')) {
                                            el.textContent = newRemainingQuantity.toFixed(2) + ' T';
                                            // Ajouter une animation pour mettre en évidence la mise à jour
                                            el.style.transition = 'background-color 0.5s';
                                            el.style.backgroundColor = 'rgba(0, 255, 0, 0.2)';
                                            setTimeout(() => {
                                                el.style.backgroundColor = 'transparent';
                                            }, 2000);
                                        } else if (el.classList.contains('used-quantity')) {
                                            el.textContent = newUsedQuantity.toFixed(2) + ' T';
                                        }
                                    });
                                    
                                    // Mettre à jour les barres de progression si elles existent
                                    const progressBars = cityDetail.querySelectorAll('.progress-bar');
                                    if (progressBars.length > 0) {
                                        const percentUsed = (newUsedQuantity / totalQuantity) * 100;
                                        const percentRemaining = (newRemainingQuantity / totalQuantity) * 100;
                                        
                                        progressBars.forEach(bar => {
                                            if (bar.classList.contains('used-progress')) {
                                                bar.style.width = percentUsed.toFixed(1) + '%';
                                                bar.setAttribute('aria-valuenow', percentUsed.toFixed(1));
                                            } else if (bar.classList.contains('remaining-progress')) {
                                                bar.style.width = percentRemaining.toFixed(1) + '%';
                                                bar.setAttribute('aria-valuenow', percentRemaining.toFixed(1));
                                            }
                                        });
                                    }
                                        
                                        // Mettre à jour la quantité vendue
                                        if (el.innerHTML.includes('fa-chart-pie')) {
                                            const strongEl = el.querySelector('strong');
                                            if (strongEl) {
                                                const formattedQuantity = new Intl.NumberFormat('fr-FR', { 
                                                    minimumFractionDigits: 2, 
                                                    maximumFractionDigits: 2 
                                                }).format(newUsedQuantity);
                                                strongEl.textContent = `${formattedQuantity} tonnes`;
                                            }
                                        }
                                    });
                                    
                                    // Ajouter une notification dans la carte
                                    const notificationDiv = document.createElement('div');
                                    notificationDiv.className = 'alert alert-success mt-2 mb-0 py-2 px-3';
                                    notificationDiv.style.fontSize = '0.85rem';
                                    notificationDiv.innerHTML = `<i class="fas fa-check-circle me-1"></i> Quantité de ${returnedQuantity} tonnes retournée avec succès.`;
                                    
                                    // Vérifier si une notification similaire existe déjà
                                    const existingNotifications = cityDetail.querySelectorAll('.alert-success');
                                    if (existingNotifications.length === 0) {
                                        cityDetail.appendChild(notificationDiv);
                                        
                                        // Faire disparaître la notification après 10 secondes
                                        setTimeout(() => {
                                            $(notificationDiv).fadeOut(500, function() {
                                                $(this).remove();
                                            });
                                        }, 10000);
                                    }
                                    
                                    // Mettre en évidence la carte mise à jour
                                    $(card).addClass('border-success');
                                    setTimeout(() => {
                                        $(card).removeClass('border-success');
                                    }, 3000);
                                }
                            }
                        });
                    });
                    
                    // Si aucune carte n'a été mise à jour, afficher un message
                    if (!cardUpdated) {
                        console.warn('Aucune carte n\'a été mise à jour. Rechargement de la page...');
                        Swal.fire({
                            title: 'Information',
                            text: 'Impossible de mettre à jour les cartes automatiquement. La page va être rechargée.',
                            icon: 'info',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            window.location.reload();
                        });
                    }
                } else {
                    console.error('Erreur lors de la récupération des données mises à jour:', data.message);
                    Swal.fire({
                        title: 'Erreur',
                        text: 'Impossible de mettre à jour les cartes. La page va être rechargée.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        window.location.reload();
                    });
                }
            })
            .catch(error => {
                console.error('Erreur lors de la récupération des données mises à jour:', error);
                Swal.fire({
                    title: 'Erreur',
                    text: 'Impossible de mettre à jour les cartes. La page va être rechargée.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                }).then(() => {
                    window.location.reload();
                });
            })
            .finally(() => {
                // Masquer l'indicateur de chargement
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            });
    }
</script>

@endpush

@endsection
