/**
 * GRADIS - Scripts JavaScript pour les rapports modernes
 * Fonctionnalités interactives et animations avancées
 * 2025 MOMK-Solutions
 */

class ModernReportsManager {
    constructor() {
        this.charts = {};
        this.currentPeriod = 'month';
        this.currentChartType = 'line';
        this.isLoading = false;
        
        this.init();
    }

    init() {
        this.initializeAOS();
        this.initializeParticles();
        this.setupEventListeners();
        this.initializeCharts();
        this.startAutoRefresh();
    }

    initializeAOS() {
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 800,
                easing: 'ease-out-cubic',
                once: true,
                offset: 100,
                delay: 100
            });
        }
    }

    initializeParticles() {
        const particleContainer = document.getElementById('particleBackground');
        if (!particleContainer) return;

        const createParticle = () => {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            const size = Math.random() * 4 + 2;
            const left = Math.random() * 100;
            const duration = Math.random() * 10 + 15;
            const delay = Math.random() * 5;
            
            particle.style.cssText = `
                width: ${size}px;
                height: ${size}px;
                left: ${left}%;
                animation-duration: ${duration}s;
                animation-delay: ${delay}s;
                background: rgba(102, 126, 234, ${Math.random() * 0.3 + 0.1});
            `;
            
            particleContainer.appendChild(particle);
            
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.remove();
                }
            }, (duration + delay) * 1000);
        };

        // Créer des particules initiales
        for (let i = 0; i < 8; i++) {
            setTimeout(createParticle, i * 500);
        }

        // Créer des particules périodiquement
        setInterval(createParticle, 3000);
    }

    setupEventListeners() {
        // Recherche dans le tableau
        const searchInput = document.getElementById('salesSearch');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(this.filterSalesTable.bind(this), 300));
        }

        // Filtres de période
        document.querySelectorAll('.period-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                if (!e.target.classList.contains('active')) {
                    this.updateChartPeriod(e.target.dataset.period);
                }
            });
        });

        // Cartes statistiques cliquables
        document.querySelectorAll('.stats-card').forEach(card => {
            card.addEventListener('mouseenter', this.animateStatsCard);
            card.addEventListener('mouseleave', this.resetStatsCard);
        });

        // Boutons d'action
        document.querySelectorAll('[data-action]').forEach(btn => {
            btn.addEventListener('click', this.handleActionClick.bind(this));
        });
    }

    initializeCharts() {
        this.initSalesChart();
        this.initPaymentChart();
    }

    initSalesChart() {
        const ctx = document.getElementById('salesChart');
        if (!ctx) return;

        const chartCtx = ctx.getContext('2d');
        
        // Créer des dégradés
        const gradient1 = chartCtx.createLinearGradient(0, 0, 0, 350);
        gradient1.addColorStop(0, 'rgba(102, 126, 234, 0.8)');
        gradient1.addColorStop(0.5, 'rgba(118, 75, 162, 0.4)');
        gradient1.addColorStop(1, 'rgba(102, 126, 234, 0.1)');

        const gradient2 = chartCtx.createLinearGradient(0, 0, 0, 350);
        gradient2.addColorStop(0, 'rgba(72, 187, 120, 0.6)');
        gradient2.addColorStop(1, 'rgba(72, 187, 120, 0.1)');

        this.charts.sales = new Chart(chartCtx, {
            type: this.currentChartType,
            data: {
                labels: window.chartData?.monthLabels || [],
                datasets: [{
                    label: 'Ventes (FCFA)',
                    data: window.chartData?.salesData || [],
                    borderColor: 'rgba(102, 126, 234, 1)',
                    backgroundColor: gradient1,
                    borderWidth: 3,
                    pointBackgroundColor: 'rgba(102, 126, 234, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 3,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Objectif',
                    data: window.chartData?.salesData?.map(val => val * 1.2) || [],
                    borderColor: 'rgba(72, 187, 120, 1)',
                    backgroundColor: gradient2,
                    borderWidth: 2,
                    borderDash: [5, 5],
                    pointRadius: 0,
                    tension: 0.4,
                    fill: false
                }]
            },
            options: this.getChartOptions('sales')
        });
    }

    initPaymentChart() {
        const ctx = document.getElementById('paymentStatusChart');
        if (!ctx) return;

        const chartCtx = ctx.getContext('2d');
        const paymentStats = window.chartData?.paymentStats || { paid: 0, partial: 0, unpaid: 0 };

        this.charts.payment = new Chart(chartCtx, {
            type: 'doughnut',
            data: {
                labels: ['Payé', 'Partiel', 'Impayé'],
                datasets: [{
                    data: [paymentStats.paid, paymentStats.partial, paymentStats.unpaid],
                    backgroundColor: [
                        'rgba(72, 187, 120, 0.8)',
                        'rgba(237, 137, 54, 0.8)',
                        'rgba(245, 101, 101, 0.8)'
                    ],
                    borderColor: [
                        'rgba(72, 187, 120, 1)',
                        'rgba(237, 137, 54, 1)',
                        'rgba(245, 101, 101, 1)'
                    ],
                    borderWidth: 3,
                    hoverOffset: 8,
                    hoverBorderWidth: 4
                }]
            },
            options: this.getChartOptions('payment')
        });

        this.updatePaymentLegend(paymentStats);
    }

    getChartOptions(type) {
        const baseOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: type === 'sales',
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: { size: 12, weight: '500' }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    titleColor: '#2d3748',
                    bodyColor: '#4a5568',
                    borderColor: 'rgba(102, 126, 234, 0.2)',
                    borderWidth: 1,
                    cornerRadius: 12,
                    padding: 16,
                    boxPadding: 8,
                    usePointStyle: true,
                    titleFont: { size: 14, weight: '600' },
                    bodyFont: { size: 13 }
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeOutCubic'
            }
        };

        if (type === 'sales') {
            baseOptions.scales = {
                x: {
                    grid: { display: false, drawBorder: false },
                    ticks: { color: '#718096', font: { size: 11, weight: '500' } }
                },
                y: {
                    grid: { color: 'rgba(0, 0, 0, 0.05)', borderDash: [2, 2], drawBorder: false },
                    ticks: {
                        color: '#718096',
                        font: { size: 11 },
                        callback: value => (value / 1000).toFixed(0) + 'K F'
                    }
                }
            };
        } else if (type === 'payment') {
            baseOptions.cutout = '65%';
            baseOptions.onClick = (event, activeElements) => {
                if (activeElements.length > 0) {
                    const index = activeElements[0].index;
                    const status = ['paid', 'partial', 'unpaid'][index];
                    this.filterSalesByStatus(status);
                }
            };
        }

        return baseOptions;
    }

    updatePaymentLegend(stats) {
        const total = stats.paid + stats.partial + stats.unpaid;
        
        const updateElement = (id, percentage) => {
            const element = document.getElementById(id);
            if (element) element.textContent = `${Math.round(percentage)}%`;
        };

        updateElement('paidPercentage', (stats.paid / total) * 100);
        updateElement('partialPercentage', (stats.partial / total) * 100);
        updateElement('unpaidPercentage', (stats.unpaid / total) * 100);
    }

    updateChartPeriod(period) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.currentPeriod = period;
        
        // Mise à jour de l'UI
        document.querySelectorAll('.period-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.period === period);
        });

        const chartCard = document.getElementById('salesChartCard');
        if (chartCard) chartCard.classList.add('loading');

        // Simulation du chargement des données
        setTimeout(() => {
            const newData = this.generateMockData(period);
            
            if (this.charts.sales) {
                this.charts.sales.data.labels = newData.labels;
                this.charts.sales.data.datasets[0].data = newData.data;
                this.charts.sales.data.datasets[1].data = newData.data.map(val => val * 1.2);
                this.charts.sales.update('active');
            }

            if (chartCard) chartCard.classList.remove('loading');
            
            const countMap = { month: '12 mois', quarter: '4 trimestres', year: '5 années' };
            const countElement = document.getElementById('chartDataCount');
            if (countElement) countElement.textContent = countMap[period];

            this.isLoading = false;
            this.showNotification(`Données mises à jour pour la période: ${period}`, 'success');
        }, 1200);
    }

    generateMockData(period) {
        const data = {
            month: {
                labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'],
                data: Array.from({length: 12}, () => Math.floor(Math.random() * 1000000) + 500000)
            },
            quarter: {
                labels: ['T1 2024', 'T2 2024', 'T3 2024', 'T4 2024'],
                data: Array.from({length: 4}, () => Math.floor(Math.random() * 3000000) + 1500000)
            },
            year: {
                labels: ['2020', '2021', '2022', '2023', '2024'],
                data: Array.from({length: 5}, () => Math.floor(Math.random() * 10000000) + 5000000)
            }
        };
        return data[period];
    }

    filterSalesTable() {
        const searchTerm = document.getElementById('salesSearch')?.value.toLowerCase() || '';
        const tableBody = document.getElementById('salesTableBody');
        if (!tableBody) return;

        const rows = tableBody.querySelectorAll('tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const isVisible = text.includes(searchTerm);
            row.style.display = isVisible ? '' : 'none';
            if (isVisible) visibleCount++;
        });

        const countElement = document.getElementById('salesTableCount');
        if (countElement) {
            countElement.textContent = `${visibleCount} résultat${visibleCount > 1 ? 's' : ''}`;
        }
    }

    filterSalesByStatus(status) {
        const rows = document.querySelectorAll('#salesTableBody tr');
        rows.forEach(row => {
            const statusBadge = row.querySelector('.badge');
            const rowStatus = statusBadge ? statusBadge.textContent.toLowerCase() : '';
            const statusMap = { paid: 'payé', partial: 'partiel', unpaid: 'impayé' };
            const shouldShow = status === 'all' || rowStatus.includes(statusMap[status] || status);
            row.style.display = shouldShow ? '' : 'none';
        });

        this.showNotification(`Filtrage par statut: ${status}`, 'info');
    }

    animateStatsCard(event) {
        const card = event.currentTarget;
        const icon = card.querySelector('.card-icon');
        if (icon) {
            icon.style.transform = 'rotate(10deg) scale(1.1)';
        }
    }

    resetStatsCard(event) {
        const card = event.currentTarget;
        const icon = card.querySelector('.card-icon');
        if (icon) {
            icon.style.transform = '';
        }
    }

    handleActionClick(event) {
        const action = event.currentTarget.dataset.action;
        const actionMap = {
            sales: () => window.location.href = '/accountant/reports/sales',
            supplies: () => window.location.href = '/accountant/reports/supplies',
            payments: () => window.location.href = '/accountant/reports/payments',
            custom: () => window.location.href = '/accountant/reports/generate'
        };

        if (actionMap[action]) {
            actionMap[action]();
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed animate-slide-in-bottom`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 4000);
    }

    startAutoRefresh() {
        // Actualisation automatique toutes les 5 minutes
        setInterval(() => {
            if (!this.isLoading) {
                this.refreshData();
            }
        }, 300000);
    }

    refreshData() {
        console.log('Actualisation automatique des données...');
        // Ici, vous feriez des appels AJAX pour récupérer les dernières données
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialisation automatique
document.addEventListener('DOMContentLoaded', () => {
    window.reportsManager = new ModernReportsManager();
});

// Fonctions globales pour la compatibilité
window.updateChartPeriod = (period) => window.reportsManager?.updateChartPeriod(period);
window.refreshDashboard = () => location.reload();
window.toggleChartType = () => {
    if (window.reportsManager?.charts.sales) {
        const chart = window.reportsManager.charts.sales;
        chart.config.type = chart.config.type === 'line' ? 'bar' : 'line';
        chart.update('active');
        window.reportsManager.showNotification('Type de graphique modifié', 'info');
    }
};
window.exportChart = () => {
    if (window.reportsManager?.charts.sales) {
        const url = window.reportsManager.charts.sales.toBase64Image();
        const link = document.createElement('a');
        link.download = `graphique-ventes-${new Date().toISOString().split('T')[0]}.png`;
        link.href = url;
        link.click();
        window.reportsManager.showNotification('Graphique exporté', 'success');
    }
};
