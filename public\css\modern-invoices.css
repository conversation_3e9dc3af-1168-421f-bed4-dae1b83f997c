/* Modern Invoices CSS - GRADIS
   Palette de couleurs bleue pour l'interface de gestion des factures
*/

/* Variables globales */
:root {
    --primary-blue: #1E88E5;
    --dark-blue: #0D47A1;
    --light-blue: #BBDEFB;
    --light-blue-10: rgba(187, 222, 251, 0.1);
    --light-blue-30: rgba(187, 222, 251, 0.3);
    --blue-gradient: linear-gradient(135deg, #1E88E5, #0D47A1);
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
    --border-radius: 10px;
    --transition-speed: 0.3s;
}

/* Adaptations pour le layout principal */
.container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
    max-width: 100%;
}

/* En-tête et présentation */
.invoice-header {
    background: var(--blue-gradient);
    color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    width: auto;
    max-width: 100%;
}

.invoice-header:before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 180px;
    height: 180px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 100%;
    transform: translate(40%, -40%);
}

.invoice-header h1 {
    font-weight: 700;
    font-size: 1.8rem;
    margin-bottom: 5px;
}

.invoice-header p {
    opacity: 0.9;
    max-width: 600px;
}

/* Cartes des statistiques */
.stats-card {
    border-radius: var(--border-radius);
    background: white;
    padding: 20px;
    height: 100%;
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    position: relative;
    overflow: hidden;
    border-left: 4px solid var(--primary-blue);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.stats-card .icon {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 2.5rem;
    color: var(--light-blue);
    opacity: 0.5;
}

.stats-card .stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-top: 10px;
    color: var(--dark-blue);
}

.stats-card .stat-label {
    text-transform: uppercase;
    font-size: 0.85rem;
    font-weight: 600;
    color: #718096;
    letter-spacing: 0.5px;
}

/* Tableau des factures */
.invoices-table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.invoices-table thead {
    background: var(--blue-gradient);
    color: white;
}

.invoices-table th {
    font-weight: 600;
    padding: 15px;
    border: none;
}

.invoices-table td {
    padding: 15px;
    vertical-align: middle;
    border-color: #EDF2F7;
}

.invoices-table tr {
    transition: background-color var(--transition-speed);
}

.invoices-table tbody tr:hover {
    background-color: var(--light-blue-10);
}

/* Badges pour les statuts */
.badge-status {
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.75rem;
    display: inline-flex;
    align-items: center;
}

.badge-status i {
    margin-right: 4px;
    font-size: 0.7rem;
}

.badge-paid {
    background-color: rgba(72, 187, 120, 0.1);
    color: #38A169;
}

.badge-pending {
    background-color: rgba(237, 137, 54, 0.1);
    color: #DD6B20;
}

.badge-late {
    background-color: rgba(229, 62, 62, 0.1);
    color: #E53E3E;
}

/* Boutons */
.btn-invoice {
    border-radius: 6px;
    padding: 8px 15px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    transition: all var(--transition-speed);
    border: none;
}

.btn-invoice i {
    margin-right: 5px;
}

.btn-invoice-primary {
    background: var(--blue-gradient);
    color: white;
}

.btn-invoice-primary:hover {
    box-shadow: 0 4px 12px rgba(30, 136, 229, 0.3);
    transform: translateY(-2px);
    color: white;
}

.btn-invoice-outline {
    background: transparent;
    color: var(--primary-blue);
    border: 1px solid var(--primary-blue);
}

.btn-invoice-outline:hover {
    background: var(--light-blue-10);
}

.btn-icon {
    height: 36px;
    width: 36px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all var(--transition-speed);
    margin: 0 3px;
}

.btn-icon i {
    margin: 0;
    font-size: 1rem;
}

.btn-invoice-view {
    background-color: rgba(49, 151, 240, 0.1);
    color: #1E88E5;
}

.btn-invoice-download {
    background-color: rgba(72, 187, 120, 0.1);
    color: #38A169;
}

.btn-invoice-print {
    background-color: rgba(159, 122, 234, 0.1);
    color: #805AD5;
}

/* Filtres et recherche */
.filters-container {
    background: white;
    border-radius: var(--border-radius);
    padding: 15px 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-sm);
}

.search-input {
    border-radius: 6px;
    border: 1px solid #E2E8F0;
    padding: 10px 15px;
    padding-left: 38px;
    transition: all var(--transition-speed);
    background-color: #F7FAFC;
}

.search-input:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 2px rgba(30, 136, 229, 0.2);
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #718096;
}

.filter-dropdown select {
    border-radius: 6px;
    border: 1px solid #E2E8F0;
    padding: 10px 30px 10px 15px;
    background-color: #F7FAFC;
    appearance: none;
    -webkit-appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23718096' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
}

/* Pagination */
.invoices-pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

.invoices-pagination .page-link {
    border: none;
    color: #4A5568;
    margin: 0 2px;
    border-radius: 4px;
    transition: all var(--transition-speed);
}

.invoices-pagination .page-item.active .page-link {
    background-color: var(--primary-blue);
    color: white;
}

.invoices-pagination .page-item:not(.active) .page-link:hover {
    background-color: var(--light-blue-10);
    color: var(--primary-blue);
}

/* Adaptations responsives */
/* Adaptations responsives */
@media (max-width: 768px) {
    .invoice-header {
        text-align: center;
    }
    
    .invoice-header:before {
        display: none;
    }
    
    .filters-container .row {
        row-gap: 10px;
    }
    
    .stats-card {
        margin-bottom: 15px;
    }
    
    .invoices-table {
        font-size: 0.9rem;
    }
    
    .btn-icon {
        width: 32px;
        height: 32px;
    }
    
    /* Pour les appareils mobiles où la sidebar est masquée */
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
}

/* Animation pour les éléments de la page */
.fade-in-up {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Ajustements pour les éléments qui affichent le montant */
.currency {
    font-family: 'Roboto Mono', monospace;
    white-space: nowrap;
}

/* Classe pour appliquer un délai d'animation */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
