@extends('layouts.admin_minimal')

@section('title', 'Gestion des Utilisateurs')

@section('content')
<div class="container-fluid">
    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-4">
        <h1 class="h3 mb-3 mb-md-0">Gestion des utilisateurs</h1>
        <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Nouvel utilisateur
        </a>
    </div>

    <div class="card shadow">
        <div class="card-body p-0">
            <!-- Barre de recherche mobile -->
            <div class="d-md-none p-3">
                <input type="text" class="form-control" id="searchMobile" placeholder="Rechercher un utilisateur...">
            </div>

            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="ps-3">N°</th>
                            <th>Nom</th>
                            <th class="d-none d-md-table-cell">Email</th>
                            <th>Rôles</th>
                            <th class="text-center">Statut</th>
                            <th class="text-end pe-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="searchable">
                        @foreach($users as $index => $user)
                        <tr>
                            <td class="ps-3" data-label="N°">{{ $users->firstItem() + $index }}</td>
                            <td data-label="Nom">
                                <div>{{ $user->name }}</div>
                                <div class="text-muted small d-md-none">{{ $user->email }}</div>
                            </td>
                            <td class="d-none d-md-table-cell" data-label="Email">{{ $user->email }}</td>
                            <td data-label="Rôles">
                                <div class="d-flex flex-wrap gap-1">
                                    @foreach($user->roles as $role)
                                        @php
                                            $roleColors = [
                                                'admin' => 'bg-danger',
                                                'accountant' => 'bg-primary',
                                                'cement_manager' => 'bg-success',
                                                'iron_manager' => 'bg-info',
                                                'cashier' => 'bg-warning',
                                                'customer_service' => 'bg-secondary'
                                            ];
                                            $roleFrench = [
                                                'admin' => 'Administrateur',
                                                'accountant' => 'Comptable',
                                                'cement_manager' => 'Gestionnaire Ciment',
                                                'iron_manager' => 'Gestionnaire Fer',
                                                'cashier' => 'Caissier',
                                                'customer_service' => 'Service Client'
                                            ];
                                            $bgColor = $roleColors[$role->name] ?? 'bg-dark';
                                            $roleName = $roleFrench[$role->name] ?? $role->name;
                                            
                                            // Version courte pour mobile
                                            $shortName = [
                                                'Administrateur' => 'Admin',
                                                'Comptable' => 'Compt.',
                                                'Gestionnaire Ciment' => 'Gest. Cim.',
                                                'Gestionnaire Fer' => 'Gest. Fer',
                                                'Caissier' => 'Caiss.',
                                                'Service Client' => 'Serv. Cl.'
                                            ];
                                        @endphp
                                        <span class="badge {{ $bgColor }} d-none d-md-inline-block">{{ $roleName }}</span>
                                        <span class="badge {{ $bgColor }} d-inline-block d-md-none">{{ $shortName[$roleName] ?? $roleName }}</span>
                                    @endforeach
                                </div>
                            </td>
                            <td class="text-center" data-label="Statut">
                                @if($user->is_active)
                                    <span class="badge bg-success">Actif</span>
                                @else
                                    <span class="badge bg-danger">Inactif</span>
                                @endif
                            </td>
                            <td data-label="Actions">
                                <div class="d-flex justify-content-end gap-2 pe-3">
                                    <a href="{{ route('admin.users.edit', $user) }}" 
                                       class="btn btn-sm btn-primary" 
                                       title="Modifier"
                                       data-bs-toggle="tooltip">
                                        <i class="fas fa-edit"></i>
                                        <span class="d-none d-md-inline ms-1">Modifier</span>
                                    </a>
                                    
                                    <form action="{{ route('admin.users.toggle-active', $user) }}" 
                                          method="POST" 
                                          class="d-inline">
                                        @csrf
                                        <button type="submit" 
                                                class="btn btn-sm {{ $user->is_active ? 'btn-warning' : 'btn-success' }}"
                                                title="{{ $user->is_active ? 'Désactiver' : 'Activer' }}"
                                                data-bs-toggle="tooltip">
                                            <i class="fas {{ $user->is_active ? 'fa-user-slash' : 'fa-user-check' }}"></i>
                                            <span class="d-none d-md-inline ms-1">{{ $user->is_active ? 'Désactiver' : 'Activer' }}</span>
                                        </button>
                                    </form>

                                    <form action="{{ route('admin.users.destroy', $user) }}" 
                                          method="POST" 
                                          class="d-inline" 
                                          onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" 
                                                class="btn btn-sm btn-danger"
                                                title="Supprimer"
                                                data-bs-toggle="tooltip">
                                            <i class="fas fa-trash"></i>
                                            <span class="d-none d-md-inline ms-1">Supprimer</span>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-center justify-content-md-end p-3">
                {{ $users->links() }}
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light fixed-bottom">
    <div class="container text-center">
        <span class="text-muted"> {{ date('Y') }} GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>

<style>
.badge {
    font-size: 0.85em;
    padding: 0.5em 0.75em;
}

.table th {
    white-space: nowrap;
}

.table td {
    vertical-align: middle;
}

/* Styles responsives */
@media (max-width: 767.98px) {
    .card {
        border: none;
        box-shadow: none !important;
    }

    .card-body {
        padding: 0;
    }

    .table {
        font-size: 0.9rem;
    }

    .table thead {
        display: none;
    }

    .table tbody tr {
        display: block;
        border-bottom: 1px solid #dee2e6;
        padding: 0.5rem 0;
    }

    .table tbody td {
        display: block;
        text-align: left;
        padding: 0.5rem 1rem;
        border: none;
    }

    .table td:before {
        content: attr(data-label);
        float: left;
        font-weight: bold;
        margin-right: 1rem;
    }

    .table td:last-child {
        border-bottom: 0;
    }

    .btn-sm {
        padding: 0.4rem 0.8rem;
        font-size: 0.875rem;
    }

    .badge {
        font-size: 0.75em;
        padding: 0.35em 0.65em;
        margin: 0.1rem;
    }

    .d-flex.justify-content-end {
        justify-content: space-around !important;
    }

    /* Ajustements pour la pagination sur mobile */
    .pagination {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.25rem;
    }

    .page-link {
        padding: 0.375rem 0.75rem;
    }
}

/* Améliorations pour les tablettes */
@media (min-width: 768px) and (max-width: 991.98px) {
    .table {
        font-size: 0.95rem;
    }

    .btn-sm {
        padding: 0.3rem 0.6rem;
    }
}

/* Animation de survol pour les lignes du tableau */
.table-hover tbody tr:hover {
    transform: translateX(3px);
    transition: transform 0.2s ease;
}

/* Style pour la barre de recherche */
#searchMobile {
    border-radius: 20px;
    border: 1px solid #dee2e6;
    padding: 0.5rem 1rem;
    margin-bottom: 1rem;
}

/* Animation pour les badges */
.badge {
    transition: all 0.2s ease;
}

.badge:hover {
    transform: scale(1.1);
}
</style>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Fonction de recherche mobile
    const searchMobile = document.getElementById('searchMobile');
    if (searchMobile) {
        searchMobile.addEventListener('input', function(e) {
            const searchText = e.target.value.toLowerCase();
            const rows = document.querySelectorAll('.searchable tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchText) ? '' : 'none';
            });
        });
    }

    // Ajout des attributs data-label pour l'affichage mobile
    const rows = document.querySelectorAll('.table tbody tr');
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        cells.forEach((cell, index) => {
            let label = '';
            switch(index) {
                case 0: label = 'N°'; break;
                case 1: label = 'Nom'; break;
                case 2: label = 'Email'; break;
                case 3: label = 'Rôles'; break;
                case 4: label = 'Statut'; break;
                case 5: label = 'Actions'; break;
            }
            cell.setAttribute('data-label', label);
        });
    });
});
</script>
@endpush
@endsection
