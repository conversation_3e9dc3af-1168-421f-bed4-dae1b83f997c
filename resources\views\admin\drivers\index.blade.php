@extends('layouts.admin_minimal')

@section('title', 'Gestion des Chauffeurs')

@section('content')
<div class="container-fluid py-4">
    <!-- Header avec titre et actions -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-users text-primary me-2"></i>
                Gestion des Chauffeurs
            </h1>
            <p class="text-muted mb-0">Gérez votre équipe de chauffeurs et suivez leur disponibilité</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" id="toggleView" title="Changer la vue">
                <i class="fas fa-th-large" id="viewIcon"></i>
            </button>
            <a href="{{ route('admin.drivers.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Nouveau Chauffeur
            </a>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Chauffeurs Disponibles
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $drivers->where('status', 'available')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Indisponibles
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $drivers->where('status', 'unavailable')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-times fa-2x text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Permis Expirant
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $drivers->filter(function($driver) {
                                    return $driver->license_expiry && $driver->license_expiry->diffInDays(now()) <= 30;
                                })->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Chauffeurs
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $drivers->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres et recherche -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>
                Filtres et Recherche
            </h6>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="searchInput" class="form-label">Rechercher</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="searchInput" placeholder="Nom, email, téléphone...">
                    </div>
                </div>
                <div class="col-md-3">
                    <label for="statusFilter" class="form-label">Statut</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">Tous les statuts</option>
                        <option value="available">Disponible</option>
                        <option value="unavailable">Indisponible</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="licenseFilter" class="form-label">État du permis</label>
                    <select class="form-select" id="licenseFilter">
                        <option value="">Tous</option>
                        <option value="valid">Valide</option>
                        <option value="expiring">Expire bientôt</option>
                        <option value="expired">Expiré</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="button" class="btn btn-outline-secondary w-100" id="clearFilters">
                        <i class="fas fa-times me-1"></i> Effacer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Vue en cartes (par défaut) -->
    <div id="cardView" class="drivers-view">
        <div class="row" id="driversContainer">
            @forelse($drivers as $driver)
                @php
                    $licenseStatus = 'valid';
                    $licenseClass = 'success';
                    if ($driver->license_expiry) {
                        $daysUntilExpiry = $driver->license_expiry->diffInDays(now(), false);
                        if ($daysUntilExpiry < 0) {
                            $licenseStatus = 'expired';
                            $licenseClass = 'danger';
                        } elseif ($daysUntilExpiry <= 30) {
                            $licenseStatus = 'expiring';
                            $licenseClass = 'warning';
                        }
                    }
                @endphp
                <div class="col-xl-4 col-lg-6 col-md-6 mb-4 driver-card"
                     data-status="{{ $driver->status }}"
                     data-license="{{ $licenseStatus }}"
                     data-search="{{ strtolower($driver->first_name . ' ' . $driver->last_name . ' ' . $driver->email . ' ' . $driver->phone) }}">
                    <div class="card shadow-sm h-100 driver-item border-left-{{ $driver->status === 'available' ? 'success' : 'danger' }}">
                        <div class="card-header bg-transparent border-0 pb-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="d-flex align-items-center">
                                    <div class="driver-avatar me-3">
                                        <div class="avatar-circle bg-{{ $driver->status === 'available' ? 'success' : 'danger' }}">
                                            {{ strtoupper(substr($driver->first_name, 0, 1)) }}{{ strtoupper(substr($driver->last_name, 0, 1)) }}
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="card-title mb-1 font-weight-bold">{{ $driver->first_name }} {{ $driver->last_name }}</h6>
                                        <p class="text-muted small mb-0">{{ $driver->email }}</p>
                                    </div>
                                </div>
                                <span class="badge bg-{{ $driver->status === 'available' ? 'success' : 'danger' }} status-badge">
                                    <i class="fas fa-{{ $driver->status === 'available' ? 'user-check' : 'user-times' }} me-1"></i>
                                    {{ $driver->status === 'available' ? 'Disponible' : 'Indisponible' }}
                                </span>
                            </div>
                        </div>
                        <div class="card-body pt-2">
                            <div class="row text-center mb-3">
                                <div class="col-6">
                                    <div class="border-end">
                                        <div class="text-muted small">Téléphone</div>
                                        <div class="font-weight-bold small">{{ $driver->phone }}</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-muted small">N° Permis</div>
                                    <div class="font-weight-bold small">{{ $driver->license_number }}</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">Expiration permis</small>
                                    <span class="badge bg-{{ $licenseClass }} badge-sm">
                                        @if($driver->license_expiry)
                                            @if($licenseStatus === 'expired')
                                                <i class="fas fa-times-circle me-1"></i> Expiré
                                            @elseif($licenseStatus === 'expiring')
                                                <i class="fas fa-exclamation-triangle me-1"></i> Expire bientôt
                                            @else
                                                <i class="fas fa-check-circle me-1"></i> Valide
                                            @endif
                                        @else
                                            <i class="fas fa-question-circle me-1"></i> Non défini
                                        @endif
                                    </span>
                                </div>
                                <div class="small font-weight-bold">
                                    {{ $driver->license_expiry ? $driver->license_expiry->format('d/m/Y') : 'Non défini' }}
                                    @if($driver->license_expiry && $licenseStatus === 'expiring')
                                        <span class="text-warning">({{ $driver->license_expiry->diffInDays(now()) }} jours)</span>
                                    @endif
                                </div>
                            </div>

                            @if($driver->notes)
                                <div class="mb-3">
                                    <small class="text-muted">Notes</small>
                                    <div class="small">{{ Str::limit($driver->notes, 60) }}</div>
                                </div>
                            @endif
                        </div>
                        <div class="card-footer bg-transparent border-0 pt-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.drivers.edit', $driver->id) }}"
                                       class="btn btn-outline-warning btn-sm"
                                       title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button"
                                            class="btn btn-outline-danger btn-sm delete-driver"
                                            data-id="{{ $driver->id }}"
                                            data-name="{{ $driver->first_name }} {{ $driver->last_name }}"
                                            title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>
                                    ID: {{ $driver->id }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucun chauffeur enregistré</h5>
                            <p class="text-muted mb-4">Commencez par ajouter votre premier chauffeur à l'équipe.</p>
                            <a href="{{ route('admin.drivers.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> Ajouter un chauffeur
                            </a>
                        </div>
                    </div>
                </div>
            @endforelse
        </div>
    </div>

    <!-- Vue en tableau (cachée par défaut) -->
    <div id="tableView" class="drivers-view" style="display: none;">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-table me-2"></i>
                    Liste des Chauffeurs
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="driversTable">
                        <thead class="table-light">
                            <tr>
                                <th>Chauffeur</th>
                                <th>Contact</th>
                                <th>Permis</th>
                                <th>Expiration</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($drivers as $driver)
                                @php
                                    $licenseStatus = 'valid';
                                    $licenseClass = 'success';
                                    if ($driver->license_expiry) {
                                        $daysUntilExpiry = $driver->license_expiry->diffInDays(now(), false);
                                        if ($daysUntilExpiry < 0) {
                                            $licenseStatus = 'expired';
                                            $licenseClass = 'danger';
                                        } elseif ($daysUntilExpiry <= 30) {
                                            $licenseStatus = 'expiring';
                                            $licenseClass = 'warning';
                                        }
                                    }
                                @endphp
                                <tr class="driver-row"
                                    data-status="{{ $driver->status }}"
                                    data-license="{{ $licenseStatus }}"
                                    data-search="{{ strtolower($driver->first_name . ' ' . $driver->last_name . ' ' . $driver->email . ' ' . $driver->phone) }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle bg-{{ $driver->status === 'available' ? 'success' : 'danger' }} me-3">
                                                {{ strtoupper(substr($driver->first_name, 0, 1)) }}{{ strtoupper(substr($driver->last_name, 0, 1)) }}
                                            </div>
                                            <div>
                                                <div class="font-weight-bold">{{ $driver->first_name }} {{ $driver->last_name }}</div>
                                                <div class="small text-muted">{{ $driver->email }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="font-weight-bold">{{ $driver->phone }}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="font-weight-bold">{{ $driver->license_number }}</div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="font-weight-bold">
                                                {{ $driver->license_expiry ? $driver->license_expiry->format('d/m/Y') : 'Non défini' }}
                                            </div>
                                            @if($driver->license_expiry)
                                                <span class="badge bg-{{ $licenseClass }} badge-sm">
                                                    @if($licenseStatus === 'expired')
                                                        <i class="fas fa-times-circle me-1"></i> Expiré
                                                    @elseif($licenseStatus === 'expiring')
                                                        <i class="fas fa-exclamation-triangle me-1"></i> {{ $driver->license_expiry->diffInDays(now()) }}j
                                                    @else
                                                        <i class="fas fa-check-circle me-1"></i> Valide
                                                    @endif
                                                </span>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $driver->status === 'available' ? 'success' : 'danger' }}">
                                            <i class="fas fa-{{ $driver->status === 'available' ? 'user-check' : 'user-times' }} me-1"></i>
                                            {{ $driver->status === 'available' ? 'Disponible' : 'Indisponible' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('admin.drivers.edit', $driver->id) }}"
                                               class="btn btn-sm btn-outline-warning"
                                               title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger delete-driver"
                                                    data-id="{{ $driver->id }}"
                                                    data-name="{{ $driver->first_name }} {{ $driver->last_name }}"
                                                    title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression amélioré -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Confirmer la suppression
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-user-times fa-3x text-danger mb-3"></i>
                    <p>Êtes-vous sûr de vouloir supprimer le chauffeur <strong id="driverName"></strong> ?</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Cette action est irréversible !
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> Annuler
                </button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i> Supprimer définitivement
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light fixed-bottom">
    <div class="container text-center">
        <span class="text-muted"> {{ date('Y') }} GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>
@endsection

@push('styles')
<style>
    /* Styles généraux */
    .border-left-success {
        border-left: 4px solid #1cc88a !important;
    }
    .border-left-warning {
        border-left: 4px solid #f6c23e !important;
    }
    .border-left-danger {
        border-left: 4px solid #e74a3b !important;
    }
    .border-left-info {
        border-left: 4px solid #36b9cc !important;
    }

    /* Cartes de chauffeurs */
    .driver-item {
        transition: all 0.3s ease;
        border: 1px solid #e3e6f0;
    }
    .driver-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    /* Avatar circulaire */
    .avatar-circle {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 0.875rem;
        text-transform: uppercase;
    }

    .driver-avatar .avatar-circle {
        width: 50px;
        height: 50px;
        font-size: 1rem;
    }

    /* Badges de statut */
    .status-badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        border-radius: 0.5rem;
    }

    .badge-sm {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }

    /* Animations et transitions */
    .driver-card, .driver-row {
        transition: all 0.3s ease;
    }

    .driver-card.filtered-out, .driver-row.filtered-out {
        display: none !important;
    }

    /* Styles pour les filtres */
    .form-control:focus, .form-select:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }

    /* Boutons d'action */
    .btn-group .btn {
        margin-right: 2px;
    }
    .btn-group .btn:last-child {
        margin-right: 0;
    }

    /* Vue responsive */
    @media (max-width: 768px) {
        .driver-item .card-body .row {
            text-align: left !important;
        }
        .driver-item .card-body .border-end {
            border-right: none !important;
            margin-bottom: 1rem;
        }
        .avatar-circle {
            width: 40px;
            height: 40px;
            font-size: 0.8rem;
        }
    }

    /* Styles pour les statistiques */
    .card.border-left-success,
    .card.border-left-warning,
    .card.border-left-danger,
    .card.border-left-info {
        border-radius: 0.5rem;
    }

    /* Animation de chargement */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    /* Styles pour le toggle de vue */
    #toggleView {
        transition: all 0.3s ease;
    }
    #toggleView:hover {
        transform: scale(1.05);
    }

    /* Amélioration du tableau */
    .table th {
        background-color: #f8f9fc;
        border-color: #e3e6f0;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
    }

    .table td {
        vertical-align: middle;
        border-color: #e3e6f0;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(78, 115, 223, 0.05);
    }

    /* Message d'état vide */
    .empty-state {
        padding: 3rem 1rem;
    }

    /* Amélioration des badges */
    .badge {
        font-weight: 500;
        letter-spacing: 0.5px;
    }

    /* Modal amélioré */
    .modal-header.bg-danger {
        border-bottom: none;
    }

    /* Indicateurs de statut de permis */
    .license-status {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    /* Animations d'entrée */
    .card {
        animation: slideInUp 0.5s ease-out;
    }
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Styles pour les alertes de permis */
    .license-expiring {
        background: linear-gradient(135deg, rgba(246, 194, 62, 0.1) 0%, rgba(246, 194, 62, 0.05) 100%);
        border-left-color: #f6c23e !important;
    }

    .license-expired {
        background: linear-gradient(135deg, rgba(231, 74, 59, 0.1) 0%, rgba(231, 74, 59, 0.05) 100%);
        border-left-color: #e74a3b !important;
    }

    /* Amélioration des cartes d'information */
    .info-card {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .info-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    /* Styles pour les notes tronquées */
    [title] {
        cursor: help;
    }

    /* Amélioration des boutons */
    .btn {
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
    }

    /* Styles pour les icônes de statut */
    .status-icon {
        width: 20px;
        text-align: center;
    }

    /* Responsive pour les cartes */
    @media (max-width: 576px) {
        .driver-card {
            margin-bottom: 1rem;
        }

        .card-body {
            padding: 1rem;
        }

        .status-badge {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
        }
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const deleteForm = document.getElementById('deleteForm');
    let currentView = 'card'; // 'card' ou 'table'

    // Éléments DOM
    const cardView = document.getElementById('cardView');
    const tableView = document.getElementById('tableView');
    const toggleViewBtn = document.getElementById('toggleView');
    const viewIcon = document.getElementById('viewIcon');
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const licenseFilter = document.getElementById('licenseFilter');
    const clearFiltersBtn = document.getElementById('clearFilters');

    // Gestionnaire pour le changement de vue
    toggleViewBtn.addEventListener('click', function() {
        if (currentView === 'card') {
            // Passer à la vue tableau
            cardView.style.display = 'none';
            tableView.style.display = 'block';
            viewIcon.className = 'fas fa-th-large';
            currentView = 'table';
            this.title = 'Vue en cartes';
        } else {
            // Passer à la vue cartes
            cardView.style.display = 'block';
            tableView.style.display = 'none';
            viewIcon.className = 'fas fa-table';
            currentView = 'card';
            this.title = 'Vue en tableau';
        }

        // Réappliquer les filtres après le changement de vue
        applyFilters();
    });

    // Fonction de filtrage
    function applyFilters() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value;
        const licenseValue = licenseFilter.value;

        // Sélectionner les éléments selon la vue actuelle
        const items = currentView === 'card'
            ? document.querySelectorAll('.driver-card')
            : document.querySelectorAll('.driver-row');

        let visibleCount = 0;

        items.forEach(item => {
            const searchData = item.dataset.search;
            const statusData = item.dataset.status;
            const licenseData = item.dataset.license;

            let shouldShow = true;

            // Filtre de recherche
            if (searchTerm && !searchData.includes(searchTerm)) {
                shouldShow = false;
            }

            // Filtre de statut
            if (statusValue && statusData !== statusValue) {
                shouldShow = false;
            }

            // Filtre de permis
            if (licenseValue) {
                if (licenseValue === 'valid' && licenseData !== 'valid') {
                    shouldShow = false;
                } else if (licenseValue === 'expiring' && licenseData !== 'expiring') {
                    shouldShow = false;
                } else if (licenseValue === 'expired' && licenseData !== 'expired') {
                    shouldShow = false;
                }
            }

            // Appliquer la visibilité
            if (shouldShow) {
                item.classList.remove('filtered-out');
                visibleCount++;
            } else {
                item.classList.add('filtered-out');
            }
        });

        // Afficher un message si aucun résultat
        updateEmptyState(visibleCount === 0);
    }

    // Fonction pour afficher/masquer le message d'état vide
    function updateEmptyState(isEmpty) {
        let emptyStateElement = document.getElementById('emptyState');

        if (isEmpty) {
            if (!emptyStateElement) {
                emptyStateElement = document.createElement('div');
                emptyStateElement.id = 'emptyState';
                emptyStateElement.className = 'col-12';
                emptyStateElement.innerHTML = `
                    <div class="card shadow-sm">
                        <div class="card-body text-center py-5 empty-state">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucun chauffeur trouvé</h5>
                            <p class="text-muted mb-4">Aucun chauffeur ne correspond aux critères de recherche.</p>
                            <button type="button" class="btn btn-outline-primary" onclick="clearAllFilters()">
                                <i class="fas fa-times me-1"></i> Effacer les filtres
                            </button>
                        </div>
                    </div>
                `;

                if (currentView === 'card') {
                    document.getElementById('driversContainer').appendChild(emptyStateElement);
                } else {
                    // Pour la vue tableau, on peut ajouter une ligne
                    const tbody = document.querySelector('#driversTable tbody');
                    const emptyRow = document.createElement('tr');
                    emptyRow.id = 'emptyStateRow';
                    emptyRow.innerHTML = `
                        <td colspan="6" class="text-center py-5">
                            <i class="fas fa-search fa-2x text-muted mb-3"></i>
                            <div class="h6 text-muted">Aucun chauffeur trouvé</div>
                            <p class="text-muted mb-3">Aucun chauffeur ne correspond aux critères de recherche.</p>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="clearAllFilters()">
                                <i class="fas fa-times me-1"></i> Effacer les filtres
                            </button>
                        </td>
                    `;
                    tbody.appendChild(emptyRow);
                }
            }
        } else {
            if (emptyStateElement) {
                emptyStateElement.remove();
            }
            const emptyRow = document.getElementById('emptyStateRow');
            if (emptyRow) {
                emptyRow.remove();
            }
        }
    }

    // Fonction globale pour effacer tous les filtres
    window.clearAllFilters = function() {
        searchInput.value = '';
        statusFilter.value = '';
        licenseFilter.value = '';
        applyFilters();
    };

    // Gestionnaires d'événements pour les filtres
    searchInput.addEventListener('input', applyFilters);
    statusFilter.addEventListener('change', applyFilters);
    licenseFilter.addEventListener('change', applyFilters);
    clearFiltersBtn.addEventListener('click', window.clearAllFilters);

    // Gestionnaire pour les boutons de suppression
    document.addEventListener('click', function(e) {
        if (e.target.closest('.delete-driver')) {
            const button = e.target.closest('.delete-driver');
            const driverId = button.dataset.id;
            const driverName = button.dataset.name;

            document.getElementById('driverName').textContent = driverName;
            deleteForm.action = `/admin/drivers/${driverId}`;
            deleteModal.show();
        }
    });

    // Animation d'entrée pour les cartes
    function animateCards() {
        const cards = document.querySelectorAll('.driver-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            setTimeout(() => {
                card.style.transition = 'all 0.3s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // Fonction pour mettre en évidence les permis expirant
    function highlightExpiringLicenses() {
        const driverCards = document.querySelectorAll('.driver-card');
        driverCards.forEach(card => {
            const licenseStatus = card.dataset.license;
            if (licenseStatus === 'expiring') {
                card.classList.add('license-expiring');
            } else if (licenseStatus === 'expired') {
                card.classList.add('license-expired');
            }
        });
    }

    // Fonction pour afficher des notifications pour les permis expirant
    function checkExpiringLicenses() {
        const expiringCount = document.querySelectorAll('[data-license="expiring"]').length;
        const expiredCount = document.querySelectorAll('[data-license="expired"]').length;

        if (expiredCount > 0) {
            showNotification(`⚠️ ${expiredCount} permis de conduire ont expiré !`, 'danger');
        } else if (expiringCount > 0) {
            showNotification(`⚠️ ${expiringCount} permis de conduire expirent bientôt !`, 'warning');
        }
    }

    // Fonction pour afficher des notifications
    function showNotification(message, type = 'info') {
        // Créer une notification toast
        const toastContainer = document.querySelector('.toast-container') || createToastContainer();
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        toastContainer.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // Supprimer le toast après qu'il soit caché
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    // Créer le conteneur de toast s'il n'existe pas
    function createToastContainer() {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(container);
        return container;
    }

    // Initialisation
    animateCards();
    highlightExpiringLicenses();

    // Vérifier les permis expirant après un délai
    setTimeout(checkExpiringLicenses, 1000);
});
</script>
@endpush
