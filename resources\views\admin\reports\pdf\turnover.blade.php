<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Rapport du Chiffre d'affaires</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #4472C4;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #4472C4;
            font-size: 24px;
            margin: 0;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .summary {
            margin-bottom: 30px;
        }
        .summary-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .summary-item {
            display: table-cell;
            width: 33.33%;
            text-align: center;
            padding: 15px;
            background-color: #f8f9fc;
            border: 1px solid #e3e6f0;
        }
        .summary-item h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #5a5c69;
        }
        .summary-item .amount {
            font-size: 18px;
            font-weight: bold;
            color: #4472C4;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #4472C4;
            font-size: 16px;
            margin-bottom: 15px;
            border-bottom: 1px solid #e3e6f0;
            padding-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table th, table td {
            border: 1px solid #e3e6f0;
            padding: 8px;
            text-align: left;
        }
        table th {
            background-color: #4472C4;
            color: white;
            font-weight: bold;
        }
        table tr:nth-child(even) {
            background-color: #f8f9fc;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #e3e6f0;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>RAPPORT DU CHIFFRE D'AFFAIRES</h1>
        <p>Période: {{ \Carbon\Carbon::parse($start_date)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($end_date)->format('d/m/Y') }}</p>
        <p>Généré le {{ \Carbon\Carbon::now()->format('d/m/Y à H:i') }}</p>
    </div>

    <div class="summary">
        <div class="summary-grid">
            <div class="summary-item">
                <h3>Chiffre d'affaires des ventes</h3>
                <div class="amount">{{ number_format($sales_turnover, 0, ',', ' ') }} FCFA</div>
            </div>
            <div class="summary-item">
                <h3>Chiffre d'affaires des commandes</h3>
                <div class="amount">{{ number_format($orders_turnover, 0, ',', ' ') }} FCFA</div>
            </div>
            <div class="summary-item">
                <h3>TOTAL</h3>
                <div class="amount">{{ number_format($total_turnover, 0, ',', ' ') }} FCFA</div>
            </div>
        </div>
    </div>

    @if($monthly_data->count() > 0)
    <div class="section">
        <h2>Évolution mensuelle</h2>
        <table>
            <thead>
                <tr>
                    <th>Mois</th>
                    <th class="text-center">Nombre de ventes</th>
                    <th class="text-right">Chiffre d'affaires</th>
                    <th class="text-right">Moyenne par vente</th>
                </tr>
            </thead>
            <tbody>
                @foreach($monthly_data as $data)
                <tr>
                    <td>{{ \Carbon\Carbon::createFromDate($data->year, $data->month, 1)->format('F Y') }}</td>
                    <td class="text-center">{{ number_format($data->count) }}</td>
                    <td class="text-right">{{ number_format($data->total, 0, ',', ' ') }} FCFA</td>
                    <td class="text-right">{{ number_format($data->count > 0 ? $data->total / $data->count : 0, 0, ',', ' ') }} FCFA</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    @if($product_data->count() > 0)
    <div class="section">
        <h2>Top produits</h2>
        <table>
            <thead>
                <tr>
                    <th>Produit</th>
                    <th class="text-center">Nombre de ventes</th>
                    <th class="text-right">Chiffre d'affaires</th>
                </tr>
            </thead>
            <tbody>
                @foreach($product_data->take(10) as $product)
                <tr>
                    <td>{{ $product->product_name }}</td>
                    <td class="text-center">{{ number_format($product->count) }}</td>
                    <td class="text-right">{{ number_format($product->total, 0, ',', ' ') }} FCFA</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <div class="footer">
        <p>GRADIS - Système de gestion des approvisionnements et ventes</p>
        <p>Ce rapport a été généré automatiquement le {{ \Carbon\Carbon::now()->format('d/m/Y à H:i') }}</p>
    </div>
</body>
</html>
