@extends('layouts.admin_minimal')

@section('title', 'Rapport des Bénéfices')

@section('content')
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-coins text-success me-2"></i>
                Rapport des Bénéfices
            </h1>
            <p class="text-muted mb-0">Analyse des bénéfices du {{ \Carbon\Carbon::parse($start_date)->format('d/m/Y') }} au {{ \Carbon\Carbon::parse($end_date)->format('d/m/Y') }}</p>
        </div>
        <div>
            <a href="{{ route('admin.reports.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Retour
            </a>
        </div>
    </div>

    <!-- Formulaire de filtres -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>
                Filtres de période
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.reports.profit') }}" class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">Date de début</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ $start_date }}">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">Date de fin</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ $end_date }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i> Filtrer
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-1"></i> Exporter
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.reports.profit', array_merge(request()->query(), ['export' => 'excel'])) }}">
                                    <i class="fas fa-file-excel text-success me-2"></i> Excel
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.reports.profit', array_merge(request()->query(), ['export' => 'pdf'])) }}">
                                    <i class="fas fa-file-pdf text-danger me-2"></i> PDF
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Résumé des bénéfices -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Revenus
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($revenue, 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-up fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Coûts
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($costs, 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-down fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-{{ $profit >= 0 ? 'success' : 'danger' }} shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-{{ $profit >= 0 ? 'success' : 'danger' }} text-uppercase mb-1">
                                Bénéfice net
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($profit, 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-coins fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Marge bénéficiaire
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($profit_margin, 1) }}%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphique des bénéfices -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line me-2"></i>
                        Évolution mensuelle des bénéfices
                    </h6>
                </div>
                <div class="card-body">
                    @if($monthly_profit->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Mois</th>
                                        <th>Revenus</th>
                                        <th>Coûts</th>
                                        <th>Bénéfice</th>
                                        <th>Marge (%)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($monthly_profit as $data)
                                        @php
                                            $monthlyMargin = $data->revenue > 0 ? ($data->profit / $data->revenue) * 100 : 0;
                                        @endphp
                                        <tr>
                                            <td>{{ \Carbon\Carbon::createFromDate($data->year, $data->month, 1)->format('F Y') }}</td>
                                            <td>{{ number_format($data->revenue, 0, ',', ' ') }} FCFA</td>
                                            <td>{{ number_format($data->costs, 0, ',', ' ') }} FCFA</td>
                                            <td class="text-{{ $data->profit >= 0 ? 'success' : 'danger' }}">
                                                {{ number_format($data->profit, 0, ',', ' ') }} FCFA
                                            </td>
                                            <td class="text-{{ $monthlyMargin >= 0 ? 'success' : 'danger' }}">
                                                {{ number_format($monthlyMargin, 1) }}%
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Graphique visuel simple -->
                        <div class="mt-4">
                            <h6 class="text-primary mb-3">Visualisation des bénéfices mensuels</h6>
                            @foreach($monthly_profit as $data)
                                @php
                                    $maxProfit = $monthly_profit->max('profit');
                                    $minProfit = $monthly_profit->min('profit');
                                    $range = $maxProfit - $minProfit;
                                    $percentage = $range > 0 ? (($data->profit - $minProfit) / $range) * 100 : 50;
                                @endphp
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span class="text-sm font-weight-bold">
                                            {{ \Carbon\Carbon::createFromDate($data->year, $data->month, 1)->format('M Y') }}
                                        </span>
                                        <span class="text-sm text-{{ $data->profit >= 0 ? 'success' : 'danger' }}">
                                            {{ number_format($data->profit, 0, ',', ' ') }} FCFA
                                        </span>
                                    </div>
                                    <div class="progress progress-sm">
                                        <div class="progress-bar bg-{{ $data->profit >= 0 ? 'success' : 'danger' }}" 
                                             role="progressbar" style="width: {{ $percentage }}%">
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line fa-3x text-gray-300 mb-3"></i>
                            <p class="text-muted">Aucune donnée disponible pour cette période</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Analyse des performances -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-analytics me-2"></i>
                        Analyse des performances
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-success">Points positifs</h6>
                            <ul class="list-unstyled">
                                @if($profit > 0)
                                    <li><i class="fas fa-check text-success me-2"></i> Bénéfice positif sur la période</li>
                                @endif
                                @if($profit_margin > 10)
                                    <li><i class="fas fa-check text-success me-2"></i> Marge bénéficiaire satisfaisante ({{ number_format($profit_margin, 1) }}%)</li>
                                @endif
                                @if($revenue > $costs)
                                    <li><i class="fas fa-check text-success me-2"></i> Revenus supérieurs aux coûts</li>
                                @endif
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-warning">Points d'attention</h6>
                            <ul class="list-unstyled">
                                @if($profit <= 0)
                                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i> Bénéfice négatif ou nul</li>
                                @endif
                                @if($profit_margin < 10)
                                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i> Marge bénéficiaire faible</li>
                                @endif
                                @if($costs > $revenue * 0.8)
                                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i> Coûts élevés par rapport aux revenus</li>
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.progress-sm {
    height: 0.5rem;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
@endpush
