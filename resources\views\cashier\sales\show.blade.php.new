@extends('layouts.cashier')

@section('styles')
<style>
    /* Variables globales */
    :root {
        --primary-color: #2196F3; /* Bleu principal */
        --primary-light: #BBDEFB; /* Bleu clair */
        --primary-dark: #1976D2; /* Bleu foncé */
        --secondary-color: #4CAF50; /* Vert */
        --warning-color: #FF9800; /* Orange */
        --danger-color: #F44336; /* Rouge */
        --info-color: #00BCD4; /* Cyan */
        --light-color: #FAFAFA; /* Gris très clair */
        --dark-color: #263238; /* Bleu-gris foncé */
        --text-color: #37474F; /* Bleu-gris */
        --border-radius: 1rem;
        --card-radius: 1.25rem;
        --box-shadow: 0 6px 15px rgba(0, 0, 0, 0.05);
        --transition: all 0.25s ease;
    }
    
    /* Styles généraux */
    .sale-container {
        padding: 1.5rem 1.5rem 2.5rem;
        background-color: var(--light-color);
        min-height: calc(100vh - 70px);
    }
    
    /* En-tête moderne avec design épuré */
    .sale-header {
        background: white;
        color: var(--text-color);
        border-radius: var(--card-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        box-shadow: var(--box-shadow);
        border-left: 5px solid var(--primary-color);
    }
    
    /* Badges colorés pour les statuts */
    .badge.bg-success {
        background-color: var(--secondary-color) !important;
    }
    
    .badge.bg-warning {
        background-color: var(--warning-color) !important;
    }
    
    .badge.bg-danger {
        background-color: var(--danger-color) !important;
    }
    
    /* Barre de progression pour visualiser l'état des paiements */
    .payment-progress {
        height: 10px;
        background-color: #e9ecef;
        border-radius: 5px;
        overflow: hidden;
        margin-bottom: 10px;
    }
    
    .payment-progress .progress-bar {
        background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
        height: 100%;
        border-radius: 5px;
    }
    
    /* Cartes modernes avec animations subtiles */
    .sale-card {
        border: none;
        border-radius: var(--card-radius);
        box-shadow: var(--box-shadow);
        margin-bottom: 1.5rem;
        transition: var(--transition);
        overflow: hidden;
        background-color: white;
    }
</style>
@endsection

@section('content')
<div class="container-fluid sale-container">
    <!-- En-tête avec les actions -->
    <div class="sale-header d-flex flex-column flex-md-row justify-content-between align-items-md-center">
        <div class="sale-info">
            <div class="d-flex align-items-center mb-2">
                <h1 class="h3 mb-0 me-3">Détails de la Vente</h1>
                @if($sale->status)
                    @if($sale->status == 'paid')
                        <span class="badge bg-success">Payé</span>
                    @elseif($sale->status == 'pending')
                        <span class="badge bg-warning">En attente</span>
                    @elseif($sale->status == 'cancelled')
                        <span class="badge bg-danger">Annulé</span>
                    @else
                        <span class="badge bg-secondary">{{ $sale->status }}</span>
                    @endif
                @endif
            </div>
            
            <div class="mb-2">
                <span class="text-muted">Référence:</span>
                <span class="fw-bold">{{ $sale->reference }}</span>
            </div>
            
            <div class="mb-2">
                <span class="text-muted">Date:</span>
                <span>{{ $sale->created_at->format('d/m/Y à H:i') }}</span>
            </div>
            
            @if($sale->customer_name)
            <div class="mb-2">
                <span class="text-muted">Client:</span>
                <span>{{ $sale->customer_name }}</span>
            </div>
            @endif
            
            @if($sale->customer_phone)
            <div>
                <span class="text-muted">Téléphone:</span>
                <a href="tel:{{ $sale->customer_phone }}" class="text-decoration-none">{{ $sale->customer_phone }}</a>
            </div>
            @endif
        </div>
        
        <div class="sale-actions mt-3 mt-md-0">
            <div class="payment-summary p-3 rounded mb-3">
                <h6 class="mb-2 d-flex align-items-center"><i class="fas fa-chart-pie text-primary me-2"></i> Progression du paiement</h6>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>{{ number_format($sale->payment_progress ?? 0, 0) }}% payé</span>
                    <span class="fw-bold">{{ number_format($sale->amount_paid ?? 0, 0, ',', ' ') }} / {{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</span>
                </div>
                
                <div class="progress payment-progress">
                    <div class="progress-bar" role="progressbar" style="width: {{ $sale->payment_progress ?? 0 }}%" aria-valuenow="{{ $sale->payment_progress ?? 0 }}" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
            
            <div class="action-buttons d-flex flex-wrap gap-2">
                <a href="{{ route('cashier.sales.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Retour
                </a>
                
                <div class="dropdown ms-2">
                    <button class="btn btn-primary dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-cog me-1"></i> Actions
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                        <li>
                            <a class="dropdown-item" href="#" onclick="window.print()">
                                <i class="fas fa-print me-2"></i> Imprimer
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Navigation par onglets -->
    <div class="sale-tabs mb-4">
        <ul class="nav nav-pills nav-fill" id="saleTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab">
                    <i class="fas fa-info-circle me-2"></i> Détails
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="delivery-tab" data-bs-toggle="tab" data-bs-target="#delivery" type="button" role="tab">
                    <i class="fas fa-truck me-2"></i> Livraison
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment" type="button" role="tab">
                    <i class="fas fa-money-bill-wave me-2"></i> Paiement
                </button>
            </li>
        </ul>
    </div>
    
    <div class="tab-content" id="saleTabContent">
        <!-- Onglet Détails -->
        <div class="tab-pane fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
            <div class="row">
                <!-- Informations générales -->
                <div class="col-lg-8">
                    <div class="sale-card">
                        <div class="card-header">
                            <i class="fas fa-info-circle"></i> Informations générales
                        </div>
                        <div class="card-body">
                            <!-- Contenu des informations générales -->
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <!-- Validation administrative -->
                    @if($sale->discount_per_ton > 0 || $sale->price_modified)
                        <div class="sale-card">
                            <div class="card-header">
                                <i class="fas fa-check-circle"></i> Validation administrative
                            </div>
                            <div class="card-body">
                                <!-- Contenu de la validation administrative -->
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- Onglet Livraison -->
        <div class="tab-pane fade" id="delivery" role="tabpanel" aria-labelledby="delivery-tab">
            <div class="row">
                <!-- Contenu de l'onglet livraison -->
            </div>
        </div>
        
        <!-- Onglet Paiement -->
        <div class="tab-pane fade" id="payment" role="tabpanel" aria-labelledby="payment-tab">
            <div class="row">
                <!-- Contenu de l'onglet paiement -->
            </div>
        </div>
    </div>
</div>
@endsection
