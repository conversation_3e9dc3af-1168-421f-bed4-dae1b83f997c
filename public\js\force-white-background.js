// Script d'urgence pour forcer le fond blanc et supprimer le dégradé bleu
(function() {
    // Fonction pour forcer le fond blanc
    function forceWhiteBackground() {
        // Injecter une feuille de style avec priorité maximale
        const style = document.createElement('style');
        style.id = 'emergency-white-bg';
        style.innerHTML = `
            html, body, .main-card, .filters-section, .container-fluid, .card {
                background: white !important;
                background-color: white !important;
                background-image: none !important;
                background-attachment: unset !important;
                background-position: unset !important;
                background-repeat: unset !important;
                background-size: unset !important;
            }
            
            /* Préserver les styles des colonnes Type et Validation */
            .sales-table th.highlight-header,
            .header-cell.highlight-header {
                background: linear-gradient(135deg, #1976D2, #2196F3) !important;
                color: white !important;
            }
            
            .highlight-header .header-content span {
                color: white !important;
            }
            
            .highlight-header .header-icon {
                background: rgba(255, 255, 255, 0.2) !important;
                color: white !important;
            }
            
            .sales-table td.type-column,
            .sales-table td.validation-column,
            .type-column,
            .validation-column {
                background-color: rgba(25, 118, 210, 0.08) !important;
                position: relative !important;
                border-radius: 0.5rem !important;
            }
            
            .type-column::before,
            .validation-column::before {
                content: '' !important;
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
                background: linear-gradient(to bottom, rgba(25, 118, 210, 0.15), rgba(25, 118, 210, 0.05)) !important;
                pointer-events: none !important;
                border-radius: 0.5rem !important;
                z-index: 0 !important;
            }
            
            /* Adapter les textes au fond blanc */
            .main-card h1, .main-card h2, .main-card h3, .main-card h4, .main-card h5, .main-card h6 {
                color: #1976D2 !important;
            }
            
            .main-card p, .main-card .text-muted {
                color: #666 !important;
            }
            
            /* Désactiver toutes les animations */
            * {
                animation: none !important;
            }
        `;
        document.head.appendChild(style);
        
        // Appliquer directement les styles au body
        document.body.style.cssText = 'background: white !important; background-color: white !important; background-image: none !important;';
        
        // Trouver tous les éléments avec un dégradé bleu et le remplacer
        const allElements = document.querySelectorAll('*');
        allElements.forEach(el => {
            try {
                const computedStyle = window.getComputedStyle(el);
                const bgImage = computedStyle.backgroundImage;
                
                // Vérifier si l'élément a un dégradé bleu
                if (bgImage.includes('linear-gradient') && 
                    (bgImage.includes('rgb(30, 136, 229)') || 
                     bgImage.includes('rgb(13, 71, 161)') ||
                     bgImage.includes('#1E88E5') || 
                     bgImage.includes('#0D47A1'))) {
                    
                    // Forcer le fond blanc
                    el.style.cssText += 'background: white !important; background-image: none !important; background-color: white !important;';
                }
            } catch (e) {
                // Ignorer les erreurs
            }
        });
        
        // Cibler spécifiquement la carte principale
        const mainCard = document.querySelector('.main-card');
        if (mainCard) {
            mainCard.style.cssText = 'background: white !important; background-color: white !important; background-image: none !important; color: #333 !important;';
        }
        
        // Cibler la section des filtres
        const filtersSection = document.querySelector('.filters-section');
        if (filtersSection) {
            filtersSection.style.cssText = 'background: white !important; background-color: white !important; background-image: none !important; color: #333 !important;';
        }
        
        console.log('Force White Background: Emergency script executed');
    }
    
    // Exécuter immédiatement
    forceWhiteBackground();
    
    // Exécuter après le chargement du DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', forceWhiteBackground);
    }
    
    // Exécuter après le chargement complet de la page
    window.addEventListener('load', forceWhiteBackground);
    
    // Exécuter périodiquement
    setInterval(forceWhiteBackground, 300);
})();
