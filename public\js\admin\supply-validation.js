// Configuration de base pour les notifications Toast
const Toast = Swal.mixin({
    toast: true,
    position: 'top-end',
    showConfirmButton: false,
    timer: 3000,
    timerProgressBar: true,
    didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer)
        toast.addEventListener('mouseleave', Swal.resumeTimer)
    }
});

// Fonction pour afficher les messages de succès/erreur
function showMessage(type, message) {
    Toast.fire({
        icon: type,
        title: message
    });
}

// Configuration globale pour les requêtes AJAX
$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});

// Gestionnaire pour la validation des approvisionnements
$(document).on('click', '.validate-supply', function(e) {
    e.preventDefault();
    const button = $(this);
    const url = button.data('validate-url');
    const supplyId = button.data('supply-id');

    Swal.fire({
        title: 'Êtes-vous sûr?',
        text: "Voulez-vous valider cet approvisionnement ?",
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Oui, valider!',
        cancelButtonText: 'Annuler'
    }).then((result) => {
        if (result.isConfirmed) {
            // Désactiver le bouton pendant la validation
            button.prop('disabled', true);
            
            $.ajax({
                url: url,
                type: 'POST',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showMessage('success', response.success);
                        
                        // Mettre à jour l'interface
                        const row = button.closest('tr');
                        row.find('.status-badge')
                            .removeClass('bg-warning')
                            .addClass('bg-success')
                            .text('Validé');
                        
                        // Masquer les boutons d'action
                        button.closest('.btn-group').remove();
                        
                        // Recharger la page après un court délai
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        showMessage('error', 'Une erreur inattendue est survenue.');
                        button.prop('disabled', false);
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'Une erreur est survenue lors de la validation.';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }
                    showMessage('error', errorMessage);
                    button.prop('disabled', false);
                }
            });
        }
    });
});

// Gestionnaire pour le rejet des approvisionnements
$(document).on('click', '.reject-supply', function(e) {
    e.preventDefault();
    const button = $(this);
    const url = button.data('reject-url');
    const supplyId = button.data('supply-id');

    Swal.fire({
        title: 'Rejet de l\'approvisionnement',
        input: 'textarea',
        inputLabel: 'Raison du rejet',
        inputPlaceholder: 'Entrez la raison du rejet...',
        inputAttributes: {
            'aria-label': 'Raison du rejet'
        },
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Rejeter',
        cancelButtonText: 'Annuler',
        inputValidator: (value) => {
            if (!value) {
                return 'Vous devez entrer une raison pour le rejet!';
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Désactiver le bouton pendant le rejet
            button.prop('disabled', true);
            
            $.ajax({
                url: url,
                type: 'POST',
                data: {
                    rejection_reason: result.value
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showMessage('success', response.success);
                        
                        // Mettre à jour l'interface
                        const row = button.closest('tr');
                        row.find('.status-badge')
                            .removeClass('bg-warning')
                            .addClass('bg-danger')
                            .text('Rejeté');
                        
                        // Masquer les boutons d'action
                        button.closest('.btn-group').remove();
                        
                        // Recharger la page après un court délai
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        showMessage('error', 'Une erreur inattendue est survenue.');
                        button.prop('disabled', false);
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'Une erreur est survenue lors du rejet.';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }
                    showMessage('error', errorMessage);
                    button.prop('disabled', false);
                }
            });
        }
    });
});
