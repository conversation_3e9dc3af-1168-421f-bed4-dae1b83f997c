@extends('layouts.accountant')

@section('content')
<div class="container-fluid">
    <!-- En-tête de la page -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Gestion des véhicules</h1>
        <a href="{{ route('accountant.trucks.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Ajouter un véhicule
        </a>
    </div>

    <!-- Liste des véhicules -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Liste des véhicules</h6>
        </div>
        <div class="card-body">
            @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif

            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Immatriculation</th>
                            <th>Marque</th>
                            <th>Modèle</th>
                            <th>Capacité</th>
                            <th>Année</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($trucks as $truck)
                            <tr class="{{ $truck->status === 'maintenance' ? 'table-warning' : ($truck->status === 'busy' ? 'table-info' : '') }}">
                                <td class="fw-bold">{{ $truck->registration_number }}</td>
                                <td>{{ $truck->brand }}</td>
                                <td>{{ $truck->model }}</td>
                                <td class="text-end fw-bold {{ $truck->capacity_value >= 20 ? 'table-success' : '' }}">
                                    @if($truck->capacity_name)
                                        {{ $truck->capacity_name }} ({{ number_format($truck->capacity_value, 1) }} {{ $truck->capacity_unit }})
                                    @else
                                        Non définie
                                    @endif
                                </td>
                                <td class="text-center {{ (date('Y') - $truck->year) > 10 ? 'table-danger' : '' }}">
                                    {{ $truck->year }}
                                </td>
                                <td>
                                    @php
                                        $statusClass = match($truck->status) {
                                            'available' => 'success',
                                            'maintenance' => 'warning',
                                            'busy' => 'info',
                                            'assigned' => 'primary',
                                            default => 'secondary'
                                        };
                                        $statusLabel = match($truck->status) {
                                            'available' => 'Disponible',
                                            'maintenance' => 'En maintenance',
                                            'busy' => 'En mission',
                                            'assigned' => $truck->driver ? 'Affecté à ' . $truck->driver->full_name : 'Affecté',
                                            default => 'Statut inconnu'
                                        };
                                    @endphp
                                    <span class="badge bg-{{ $statusClass }} text-wrap">
                                        {{ $statusLabel }}
                                    </span>
                                    @if($truck->driver && $truck->status === 'assigned')
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>{{ $truck->driver->full_name }}
                                        </small>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('accountant.trucks.show', $truck) }}" 
                                       class="btn btn-info btn-sm"
                                       title="Voir les détails">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center">Aucun véhicule trouvé</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4">
                {{ $trucks->links() }}
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    .table td {
        vertical-align: middle;
    }
    .badge {
        font-size: 0.85rem;
        padding: 0.5em 0.75em;
    }
    .btn-group {
        gap: 0.25rem;
    }
</style>
@endpush
@endsection
