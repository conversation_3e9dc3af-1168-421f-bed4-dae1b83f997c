@extends('layouts.admin_minimal')

@section('title', 'Rapport des Top Clients')

@section('content')
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-users text-info me-2"></i>
                Rapport des Top Clients
            </h1>
            <p class="text-muted mb-0">Clients les plus actifs du {{ \Carbon\Carbon::parse($start_date)->format('d/m/Y') }} au {{ \Carbon\Carbon::parse($end_date)->format('d/m/Y') }}</p>
        </div>
        <div>
            <a href="{{ route('admin.reports.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Retour
            </a>
        </div>
    </div>

    <!-- Formulaire de filtres -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>
                Filtres de période
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.reports.top-customers') }}" class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">Date de début</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ $start_date }}">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">Date de fin</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ $end_date }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i> Filtrer
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-1"></i> Exporter
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.reports.top-customers', array_merge(request()->query(), ['export' => 'excel'])) }}">
                                    <i class="fas fa-file-excel text-success me-2"></i> Excel
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.reports.top-customers', array_merge(request()->query(), ['export' => 'pdf'])) }}">
                                    <i class="fas fa-file-pdf text-danger me-2"></i> PDF
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total clients actifs
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($total_customers) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Chiffre d'affaires total
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($top_customers->sum('total_spent'), 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Panier moyen
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($top_customers->avg('average_order'), 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des top clients -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-trophy me-2"></i>
                Top 50 des clients les plus actifs
            </h6>
        </div>
        <div class="card-body">
            @if($top_customers->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>Rang</th>
                                <th>Client</th>
                                <th>Contact</th>
                                <th>Nombre de commandes</th>
                                <th>Total dépensé</th>
                                <th>Panier moyen</th>
                                <th>Dernière commande</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($top_customers as $index => $customer)
                                <tr>
                                    <td>
                                        @if($index < 3)
                                            <span class="badge bg-{{ $index == 0 ? 'warning' : ($index == 1 ? 'secondary' : 'dark') }}">
                                                @if($index == 0)
                                                    <i class="fas fa-trophy me-1"></i>
                                                @elseif($index == 1)
                                                    <i class="fas fa-medal me-1"></i>
                                                @else
                                                    <i class="fas fa-award me-1"></i>
                                                @endif
                                                {{ $index + 1 }}
                                            </span>
                                        @else
                                            <span class="badge bg-light text-dark">{{ $index + 1 }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>{{ $customer->customer_name }}</strong>
                                    </td>
                                    <td>
                                        @if($customer->customer_phone)
                                            <i class="fas fa-phone text-primary me-1"></i>
                                            {{ $customer->customer_phone }}
                                        @endif
                                        @if($customer->customer_email)
                                            <br><i class="fas fa-envelope text-info me-1"></i>
                                            {{ $customer->customer_email }}
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ number_format($customer->total_orders) }}</span>
                                    </td>
                                    <td>
                                        <strong>{{ number_format($customer->total_spent, 0, ',', ' ') }} FCFA</strong>
                                    </td>
                                    <td>
                                        {{ number_format($customer->average_order, 0, ',', ' ') }} FCFA
                                    </td>
                                    <td>
                                        {{ \Carbon\Carbon::parse($customer->last_order_date)->format('d/m/Y') }}
                                        <br><small class="text-muted">
                                            {{ \Carbon\Carbon::parse($customer->last_order_date)->diffForHumans() }}
                                        </small>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Analyse des top clients -->
                <div class="mt-4">
                    <h6 class="text-primary mb-3">Analyse des performances clients</h6>
                    <div class="row">
                        @if($top_customers->count() >= 3)
                            @foreach($top_customers->take(3) as $index => $customer)
                                <div class="col-md-4 mb-3">
                                    <div class="card border-{{ $index == 0 ? 'warning' : ($index == 1 ? 'secondary' : 'dark') }}">
                                        <div class="card-body text-center">
                                            <h6 class="card-title">
                                                @if($index == 0)
                                                    <i class="fas fa-trophy text-warning me-1"></i> 1er
                                                @elseif($index == 1)
                                                    <i class="fas fa-medal text-secondary me-1"></i> 2ème
                                                @else
                                                    <i class="fas fa-award text-dark me-1"></i> 3ème
                                                @endif
                                            </h6>
                                            <h5 class="text-primary">{{ $customer->customer_name }}</h5>
                                            <p class="mb-1">
                                                <strong>{{ number_format($customer->total_spent, 0, ',', ' ') }} FCFA</strong>
                                            </p>
                                            <small class="text-muted">
                                                {{ $customer->total_orders }} commande(s)
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @endif
                    </div>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-gray-300 mb-3"></i>
                    <p class="text-muted">Aucun client trouvé pour la période sélectionnée</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.border-left-info { border-left: 0.25rem solid #36b9cc !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.border-left-secondary { border-left: 0.25rem solid #858796 !important; }
.border-left-dark { border-left: 0.25rem solid #5a5c69 !important; }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
@endpush
