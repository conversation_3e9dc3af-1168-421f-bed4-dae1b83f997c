/* 
 * VENTES À CRÉDIT - CAISSIER RESPONSIVE
 * Design professionnel et intuitif avec palette de couleurs bleue
 * Applique la même responsivité que le tableau de bord caissier
 */

:root {
    /* Palette de couleurs principale (tons de bleu) */
    --primary-color: #1E88E5;
    --primary-light: #64B5F6;
    --primary-dark: #0D47A1;
    --primary-gradient: linear-gradient(135deg, #1E88E5, #0D47A1);
    
    /* Couleurs secondaires */
    --success-color: #2E7D32;
    --success-light: #4CAF50;
    --warning-color: #FF8F00;
    --warning-light: #FFB74D;
    --danger-color: #C62828;
    --danger-light: #EF5350;
    
    /* Couleurs neutres */
    --background-color: #F5F7FA;
    --card-bg: #FFFFFF;
    --border-color: #E0E6ED;
    --text-primary: #2D3748;
    --text-secondary: #718096;
    --text-muted: #A0AEC0;
}

/* Styles généraux */
.container-fluid {
    padding: 1.5rem;
    max-width: 100%;
    overflow-x: hidden;
}

/* Compatibilité avec la sidebar responsive */
.dashboard-container {
    transition: all 0.3s ease-in-out;
}

/* Titre de la page */
.h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-dark);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
}

/* Carte principale */
.card {
    background: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.card-body {
    padding: 1.5rem;
}

/* Tableau moderne */
.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background-color: #F9FAFB;
    color: var(--text-secondary);
    font-weight: 600;
    padding: 0.875rem 1.25rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table tbody tr {
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--border-color);
}

.table tbody tr:last-child {
    border-bottom: none;
}

.table tbody tr:hover {
    background-color: #F9FAFB;
}

.table td {
    padding: 1rem 1.25rem;
    vertical-align: middle;
    color: var(--text-primary);
    font-size: 0.875rem;
}

/* Badges de statut */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.35rem 0.75rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
    gap: 0.35rem;
}

.bg-success {
    background-color: rgba(46, 125, 50, 0.1) !important;
    color: var(--success-color) !important;
}

.bg-info {
    background-color: rgba(30, 136, 229, 0.1) !important;
    color: var(--primary-color) !important;
}

.bg-warning {
    background-color: rgba(255, 143, 0, 0.1) !important;
    color: var(--warning-color) !important;
}

/* Boutons d'action */
.btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
    border-radius: 6px;
}

.btn-info {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-info:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: white;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.25rem;
}

.page-link {
    border-radius: 8px;
    color: var(--primary-color);
    border: 1px solid var(--border-color);
    padding: 0.5rem 0.75rem;
    transition: all 0.2s ease;
}

.page-link:hover {
    background-color: #F9FAFB;
    color: var(--primary-dark);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Responsive */
@media (max-width: 1200px) {
    .container-fluid {
        padding: 1.25rem;
    }
}

@media (max-width: 992px) {
    .h3 {
        font-size: 1.5rem;
    }
    
    .card-body {
        padding: 1.25rem;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .dashboard-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .dashboard-title {
        font-size: 1.25rem;
    }
    
    .dashboard-subtitle {
        font-size: 0.85rem;
    }
    
    .h3 {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        max-width: 100%;
        margin-bottom: 1rem;
    }
    
    .table {
        min-width: 800px; /* Assurer que le tableau a une largeur minimale pour être lisible */
    }
    
    .table thead th, .table td {
        padding: 0.75rem 1rem;
    }
    
    .pagination-container {
        margin-top: 1rem;
    }
    
    /* Compatibilité avec la sidebar cachée */
    body.sidebar-hidden-body .dashboard-container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0.75rem;
    }
    
    .h3 {
        font-size: 1.125rem;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 1rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
    
    /* Optimisations pour les très petits écrans */
    .stats-card {
        margin-bottom: 0.75rem;
    }
    
    .stats-card .card-body {
        padding: 0.5rem;
    }
    
    .stats-value {
        font-size: 1.25rem;
    }
    
    .stats-label {
        font-size: 0.75rem;
    }
    
    /* Compatibilité avec la sidebar */
    body.sidebar-hidden-body .main-content {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
}
