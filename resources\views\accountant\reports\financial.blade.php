<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Rapport Financier</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 20px;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .date {
            color: #7f8c8d;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }
        .stat-title {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .trend {
            font-size: 14px;
            margin-top: 5px;
        }
        .trend.positive {
            color: #27ae60;
        }
        .trend.negative {
            color: #c0392b;
        }
        .chart {
            margin-bottom: 30px;
            text-align: center;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            color: #2c3e50;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            color: #7f8c8d;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <img src="{{ asset('images/logo.png') }}" alt="Logo" class="logo">
        <h1>Rapport Financier</h1>
        <div class="date">Généré le {{ $generatedAt }}</div>
    </div>

    <div class="section">
        <h2 class="section-title">Aperçu des performances</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-title">Revenus</div>
                <div class="stat-value">{{ number_format($stats['revenue'], 0, ',', ' ') }} FCFA</div>
                <div class="trend {{ $stats['revenue_trend'] >= 0 ? 'positive' : 'negative' }}">
                    {{ $stats['revenue_trend'] >= 0 ? '+' : '' }}{{ number_format($stats['revenue_trend'], 1) }}%
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Commandes</div>
                <div class="stat-value">{{ $stats['orders'] }}</div>
                <div class="trend {{ $stats['orders_trend'] >= 0 ? 'positive' : 'negative' }}">
                    {{ $stats['orders_trend'] >= 0 ? '+' : '' }}{{ number_format($stats['orders_trend'], 1) }}%
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Clients</div>
                <div class="stat-value">{{ $stats['customers'] }}</div>
                <div class="trend {{ $stats['customers_trend'] >= 0 ? 'positive' : 'negative' }}">
                    {{ $stats['customers_trend'] >= 0 ? '+' : '' }}{{ number_format($stats['customers_trend'], 1) }}%
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Commandes en attente</div>
                <div class="stat-value">{{ $stats['pending_orders'] ?? 0 }}</div>
                <div class="trend {{ ($stats['pending_orders_trend'] ?? 0) >= 0 ? 'positive' : 'negative' }}">
                    {{ ($stats['pending_orders_trend'] ?? 0) >= 0 ? '+' : '' }}{{ number_format($stats['pending_orders_trend'] ?? 0, 1) }}%
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2 class="section-title">Distribution des commandes</h2>
        <table>
            <thead>
                <tr>
                    <th>Statut</th>
                    <th>Nombre</th>
                    <th>Pourcentage</th>
                </tr>
            </thead>
            <tbody>
                @php
                    $total = $orderStatusData['completed'] + $orderStatusData['pending'] + $orderStatusData['cancelled'];
                @endphp
                <tr>
                    <td>Terminées</td>
                    <td>{{ $orderStatusData['completed'] }}</td>
                    <td>{{ $total > 0 ? number_format(($orderStatusData['completed'] / $total) * 100, 1) : 0 }}%</td>
                </tr>
                <tr>
                    <td>En attente</td>
                    <td>{{ $orderStatusData['pending'] }}</td>
                    <td>{{ $total > 0 ? number_format(($orderStatusData['pending'] / $total) * 100, 1) : 0 }}%</td>
                </tr>
                <tr>
                    <td>Annulées</td>
                    <td>{{ $orderStatusData['cancelled'] }}</td>
                    <td>{{ $total > 0 ? number_format(($orderStatusData['cancelled'] / $total) * 100, 1) : 0 }}%</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2 class="section-title">Revenus mensuels</h2>
        <table>
            <thead>
                <tr>
                    <th>Mois</th>
                    <th>Revenus</th>
                </tr>
            </thead>
            <tbody>
                @php
                    $months = [
                        1 => 'Janvier', 2 => 'Février', 3 => 'Mars',
                        4 => 'Avril', 5 => 'Mai', 6 => 'Juin',
                        7 => 'Juillet', 8 => 'Août', 9 => 'Septembre',
                        10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
                    ];
                @endphp
                @foreach($months as $monthNum => $monthName)
                    <tr>
                        <td>{{ $monthName }}</td>
                        <td>{{ number_format($revenueData[$monthNum] ?? 0, 0, ',', ' ') }} FCFA</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="footer">
        <p>Ce rapport a été généré automatiquement par GRADIS.</p>
        <p>© {{ date('Y') }} GRADIS. Tous droits réservés.</p>
    </div>
</body>
</html>
