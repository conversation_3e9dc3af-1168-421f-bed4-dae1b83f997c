// Tableau de bord comptable moderne - JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Animation des compteurs
    animateCounters();
    
    // Initialisation des graphiques
    initCharts();
    
    // Animation des éléments au défilement
    initScrollAnimations();
    
    // Initialisation des tooltips
    initTooltips();
});

// Animation des compteurs avec effets modernes
function animateCounters() {
    const counters = document.querySelectorAll('.counter');
    
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 1500; // durée de l'animation en ms
        const step = target / (duration / 16); // 60fps
        
        let current = 0;
        const updateCounter = () => {
            current += step;
            if (current < target) {
                counter.innerText = Math.ceil(current).toLocaleString();
                requestAnimationFrame(updateCounter);
            } else {
                counter.innerText = target.toLocaleString();
            }
        };
        
        updateCounter();
    });
    
    // Animation des compteurs monétaires
    const monetaryCounters = document.querySelectorAll('.monetary-counter');
    
    monetaryCounters.forEach(counter => {
        const target = parseFloat(counter.getAttribute('data-target'));
        const duration = 1500; // durée de l'animation en ms
        const step = target / (duration / 16); // 60fps
        const currency = counter.getAttribute('data-currency') || 'XAF';
        
        let current = 0;
        const updateCounter = () => {
            current += step;
            if (current < target) {
                counter.innerText = Math.ceil(current).toLocaleString() + ' ' + currency;
                requestAnimationFrame(updateCounter);
            } else {
                counter.innerText = target.toLocaleString() + ' ' + currency;
            }
        };
        
        updateCounter();
    });
}

// Initialisation des graphiques avec Chart.js
function initCharts() {
    // Graphique des ventes mensuelles
    const salesChartEl = document.getElementById('salesChart');
    if (salesChartEl) {
        const ctx = salesChartEl.getContext('2d');
        
        // Récupération des données du graphique
        const labels = JSON.parse(salesChartEl.getAttribute('data-labels') || '[]');
        const data = JSON.parse(salesChartEl.getAttribute('data-values') || '[]');
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Ventes mensuelles',
                    data: data,
                    backgroundColor: 'rgba(30, 136, 229, 0.1)',
                    borderColor: '#1E88E5',
                    borderWidth: 2,
                    pointBackgroundColor: '#1E88E5',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#263238',
                        bodyColor: '#263238',
                        borderColor: 'rgba(0, 0, 0, 0.05)',
                        borderWidth: 1,
                        padding: 12,
                        boxPadding: 6,
                        usePointStyle: true,
                        callbacks: {
                            label: function(context) {
                                let value = context.raw;
                                return value.toLocaleString() + ' XAF';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#607D8B'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.03)'
                        },
                        ticks: {
                            color: '#607D8B',
                            callback: function(value) {
                                return value.toLocaleString() + ' XAF';
                            }
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });
    }
    
    // Graphique des statuts de paiement
    const paymentChartEl = document.getElementById('paymentChart');
    if (paymentChartEl) {
        const ctx = paymentChartEl.getContext('2d');
        
        // Récupération des données du graphique
        const paid = parseInt(paymentChartEl.getAttribute('data-paid') || '0');
        const partial = parseInt(paymentChartEl.getAttribute('data-partial') || '0');
        const unpaid = parseInt(paymentChartEl.getAttribute('data-unpaid') || '0');
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Payé', 'Partiel', 'Non payé'],
                datasets: [{
                    data: [paid, partial, unpaid],
                    backgroundColor: ['#4CAF50', '#FF9800', '#F44336'],
                    borderColor: '#fff',
                    borderWidth: 2,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#263238',
                        bodyColor: '#263238',
                        borderColor: 'rgba(0, 0, 0, 0.05)',
                        borderWidth: 1,
                        padding: 12,
                        boxPadding: 6,
                        usePointStyle: true
                    }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });
    }
    
    // Graphique des taux de recouvrement
    const recoveryChartEl = document.getElementById('recoveryChart');
    if (recoveryChartEl) {
        const ctx = recoveryChartEl.getContext('2d');
        
        // Récupération des données du graphique
        const labels = JSON.parse(recoveryChartEl.getAttribute('data-labels') || '[]');
        const data = JSON.parse(recoveryChartEl.getAttribute('data-values') || '[]');
        
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Taux de recouvrement',
                    data: data,
                    backgroundColor: 'rgba(30, 136, 229, 0.7)',
                    borderColor: '#1E88E5',
                    borderWidth: 1,
                    borderRadius: 4,
                    hoverBackgroundColor: '#1E88E5'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#263238',
                        bodyColor: '#263238',
                        borderColor: 'rgba(0, 0, 0, 0.05)',
                        borderWidth: 1,
                        padding: 12,
                        boxPadding: 6,
                        callbacks: {
                            label: function(context) {
                                let value = context.raw;
                                return value.toFixed(1) + '%';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#607D8B'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        max: 100,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.03)'
                        },
                        ticks: {
                            color: '#607D8B',
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });
    }
}

// Animation des éléments au défilement
function initScrollAnimations() {
    // Utilisation d'Intersection Observer pour déclencher les animations au défilement
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1
    });
    
    // Observer tous les éléments avec la classe 'animate-on-scroll'
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
}

// Initialisation des tooltips
function initTooltips() {
    const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(tooltip => {
        new bootstrap.Tooltip(tooltip);
    });
}

// Filtrage des données du tableau de bord
function filterDashboard(period) {
    // Rediriger vers la même page avec le paramètre de période
    window.location.href = window.location.pathname + '?period=' + period;
}

// Recherche dans le tableau
function searchTable() {
    const input = document.getElementById('tableSearch');
    const filter = input.value.toUpperCase();
    const table = document.querySelector('.table');
    const rows = table.getElementsByTagName('tr');
    
    for (let i = 1; i < rows.length; i++) {
        let found = false;
        const cells = rows[i].getElementsByTagName('td');
        
        for (let j = 0; j < cells.length; j++) {
            const cell = cells[j];
            if (cell) {
                const text = cell.textContent || cell.innerText;
                if (text.toUpperCase().indexOf(filter) > -1) {
                    found = true;
                    break;
                }
            }
        }
        
        if (found) {
            rows[i].style.display = '';
        } else {
            rows[i].style.display = 'none';
        }
    }
}
