/**
 * GRADIS - Dashboard Comptable Professionnel
 * Scripts JavaScript pour les graphiques et animations
 * 2025 MOMK-Solutions
 */

// Attendre que le document soit prêt
document.addEventListener('DOMContentLoaded', function() {
    // Initialisation des compteurs animés
    initCounters();
    
    // Initialisation des graphiques si Chart.js est disponible
    if (typeof Chart !== 'undefined') {
        initCharts();
    }
    
    // Initialisation des filtres de période
    initPeriodFilters();
    
    // Initialisation des animations séquentielles
    initSequentialAnimations();
    
    // Initialisation des tooltips et popovers (si Bootstrap est disponible)
    if (typeof bootstrap !== 'undefined') {
        initTooltipsAndPopovers();
    }
});

/**
 * Initialise les compteurs animés pour les statistiques
 */
function initCounters() {
    const counterElements = document.querySelectorAll('.stat-value, .invoice-status-value');
    
    counterElements.forEach(function(counter) {
        const targetValue = parseFloat(counter.textContent.replace(/[^\d.-]/g, ''));
        const duration = 1000; // Durée d'animation en ms
        const framesPerSecond = 60;
        const totalFrames = duration * framesPerSecond / 1000;
        const initialValue = 0;
        const increment = targetValue / totalFrames;
        
        let currentValue = initialValue;
        let currentFrame = 0;
        const originalText = counter.textContent;
        const hasThousandSeparator = originalText.includes(' ') || originalText.includes(',');
        const hasCurrency = originalText.includes('F');
        
        // Formater le nombre selon qu'il contient une devise, une décimale, etc.
        const formatValue = function(value) {
            let formattedValue = Math.round(value);
            
            if (hasThousandSeparator) {
                formattedValue = formattedValue.toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ");
            }
            
            if (hasCurrency) {
                formattedValue += ' F';
            }
            
            return formattedValue;
        };
        
        // Animation du compteur
        const animateCounter = function() {
            if (currentFrame < totalFrames) {
                currentValue += increment;
                counter.textContent = formatValue(currentValue);
                
                // Effet de pulse à un certain pourcentage
                if (currentFrame === Math.floor(totalFrames * 0.8)) {
                    counter.classList.add('counter-animation', 'animate');
                    setTimeout(function() {
                        counter.classList.remove('animate');
                    }, 300);
                }
                
                currentFrame++;
                requestAnimationFrame(animateCounter);
            } else {
                counter.textContent = originalText;
                counter.classList.remove('counter-animation');
            }
        };
        
        // Observer pour démarrer l'animation quand l'élément est visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounter();
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });
        
        observer.observe(counter);
    });
}

/**
 * Initialise les graphiques du dashboard avec Chart.js
 */
function initCharts() {
    // Récupération des données du contrôleur (formatées en JSON)
    const chartDataElement = document.getElementById('chartData');
    let chartData = {};
    
    try {
        if (chartDataElement) {
            chartData = JSON.parse(chartDataElement.value);
        }
    } catch (e) {
        console.error("Erreur lors de la récupération des données des graphiques:", e);
    }
    
    // Graphique principal - Ventes et Paiements
    const salesPaymentsChartElement = document.getElementById('salesPaymentsChart');
    if (salesPaymentsChartElement) {
        // Utilisation de données simulées si les données réelles ne sont pas disponibles
        const salesData = chartData?.sales?.monthlySales || generateDemoData(12, 500000, 1000000);
        const paymentsData = chartData?.sales?.monthlyPayments || generateDemoData(12, 400000, 900000);
        
        const months = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'];
        
        new Chart(salesPaymentsChartElement, {
            type: 'line',
            data: {
                labels: months,
                datasets: [{
                    label: 'Ventes',
                    data: salesData,
                    backgroundColor: 'rgba(30, 136, 229, 0.3)',
                    borderColor: 'rgba(30, 136, 229, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(30, 136, 229, 1)',
                    pointBorderColor: '#fff',
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Paiements',
                    data: paymentsData,
                    backgroundColor: 'rgba(76, 175, 80, 0.3)',
                    borderColor: 'rgba(76, 175, 80, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(76, 175, 80, 1)',
                    pointBorderColor: '#fff',
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            boxWidth: 12,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#333',
                        bodyColor: '#666',
                        borderColor: 'rgba(200, 200, 200, 0.5)',
                        borderWidth: 1,
                        padding: 10,
                        boxWidth: 10,
                        usePointStyle: true,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += new Intl.NumberFormat('fr-FR').format(context.parsed.y) + ' F';
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                if (value >= 1000000) {
                                    return (value / 1000000).toFixed(1) + ' M';
                                } else if (value >= 1000) {
                                    return (value / 1000).toFixed(0) + ' k';
                                }
                                return value;
                            }
                        }
                    }
                },
                animations: {
                    tension: {
                        duration: 1000,
                        easing: 'linear'
                    }
                }
            }
        });
    }
    
    // Graphique des statuts de paiement
    const paymentStatusChartElement = document.getElementById('paymentStatusChart');
    if (paymentStatusChartElement) {
        const paymentStatusData = chartData?.sales?.paymentStatus || {
            paid: 65,
            partial: 20,
            unpaid: 15
        };
        
        new Chart(paymentStatusChartElement, {
            type: 'doughnut',
            data: {
                labels: ['Payé', 'Partiel', 'En attente'],
                datasets: [{
                    data: [
                        paymentStatusData.paid || 0,
                        paymentStatusData.partial || 0,
                        paymentStatusData.unpaid || 0
                    ],
                    backgroundColor: [
                        'rgba(76, 175, 80, 0.8)',
                        'rgba(255, 152, 0, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(76, 175, 80, 1)',
                        'rgba(255, 152, 0, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 1,
                    hoverOffset: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            boxWidth: 15,
                            padding: 15
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                const total = context.dataset.data.reduce((acc, data) => acc + data, 0);
                                const percentage = Math.round((value * 100) / total) + '%';
                                return `${label}: ${percentage}`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true
                }
            }
        });
    }
    
    // Graphique des approvisionnements
    const suppliesChartElement = document.getElementById('suppliesChart');
    if (suppliesChartElement) {
        const suppliesData = chartData?.supplies || {
            ciment: 60,
            fer: 20,
            bois: 15,
            autres: 5
        };
        
        new Chart(suppliesChartElement, {
            type: 'polarArea',
            data: {
                labels: Object.keys(suppliesData).map(capitalizeFirstLetter),
                datasets: [{
                    data: Object.values(suppliesData),
                    backgroundColor: [
                        'rgba(30, 136, 229, 0.7)',
                        'rgba(156, 39, 176, 0.7)',
                        'rgba(255, 152, 0, 0.7)',
                        'rgba(76, 175, 80, 0.7)',
                        'rgba(244, 67, 54, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            boxWidth: 15,
                            padding: 10,
                            font: {
                                size: 11
                            }
                        }
                    }
                },
                scales: {
                    r: {
                        ticks: {
                            display: false
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true
                }
            }
        });
    }
}

/**
 * Initialise les filtres de période pour les graphiques
 */
function initPeriodFilters() {
    const periodFilters = document.querySelectorAll('.period-filter');
    
    periodFilters.forEach(function(filter) {
        filter.addEventListener('click', function() {
            // Retirer la classe active de tous les filtres
            periodFilters.forEach(f => f.classList.remove('active'));
            
            // Ajouter la classe active au filtre cliqué
            this.classList.add('active');
            
            // Récupérer la période sélectionnée
            const selectedPeriod = this.dataset.period;
            
            // Dans un cas réel, on mettrait à jour les graphiques avec les données correspondantes
            // Pour l'instant, nous simulons un changement avec une animation
            document.querySelectorAll('.chart-container').forEach(function(chart) {
                chart.classList.add('fade-out');
                
                setTimeout(function() {
                    chart.classList.remove('fade-out');
                    chart.classList.add('fade-in');
                    
                    // Réinitialiser l'animation après qu'elle soit terminée
                    setTimeout(function() {
                        chart.classList.remove('fade-in');
                    }, 500);
                }, 300);
            });
        });
    });
}

/**
 * Initialise les animations séquentielles pour les éléments du dashboard
 */
function initSequentialAnimations() {
    const sequentialContainers = document.querySelectorAll('.sequential-fade-in');
    
    sequentialContainers.forEach(function(container) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Ajouter une classe visible au conteneur
                    entry.target.classList.add('visible');
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });
        
        observer.observe(container);
    });
}

/**
 * Initialise les tooltips et popovers pour les éléments du dashboard
 */
function initTooltipsAndPopovers() {
    // Initialiser les tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialiser les popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

/**
 * Génère des données de démonstration pour les graphiques
 * @param {number} count Nombre de points de données
 * @param {number} min Valeur minimale
 * @param {number} max Valeur maximale
 * @returns {Array} Tableau de données aléatoires
 */
function generateDemoData(count, min, max) {
    const data = [];
    for (let i = 0; i < count; i++) {
        data.push(Math.floor(Math.random() * (max - min + 1)) + min);
    }
    return data;
}

/**
 * Met en majuscule la première lettre d'une chaîne
 * @param {string} string Chaîne à formater
 * @returns {string} Chaîne formatée
 */
function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}
