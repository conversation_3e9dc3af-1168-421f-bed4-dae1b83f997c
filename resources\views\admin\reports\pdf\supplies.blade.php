<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Rapport des Approvisionnements</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #858796;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #858796;
            font-size: 24px;
            margin: 0;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .summary {
            margin-bottom: 30px;
        }
        .summary-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .summary-item {
            display: table-cell;
            width: 20%;
            text-align: center;
            padding: 10px;
            background-color: #f8f9fc;
            border: 1px solid #e3e6f0;
        }
        .summary-item h3 {
            margin: 0 0 5px 0;
            font-size: 10px;
            color: #5a5c69;
        }
        .summary-item .amount {
            font-size: 12px;
            font-weight: bold;
            color: #858796;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #858796;
            font-size: 14px;
            margin-bottom: 15px;
            border-bottom: 1px solid #e3e6f0;
            padding-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 8px;
        }
        table th, table td {
            border: 1px solid #e3e6f0;
            padding: 4px;
            text-align: left;
        }
        table th {
            background-color: #858796;
            color: white;
            font-weight: bold;
        }
        table tr:nth-child(even) {
            background-color: #f8f9fc;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 7px;
            font-weight: bold;
        }
        .badge-success { background-color: #1cc88a; color: white; }
        .badge-warning { background-color: #f6c23e; color: white; }
        .badge-danger { background-color: #e74a3b; color: white; }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 8px;
            color: #666;
            border-top: 1px solid #e3e6f0;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>RAPPORT DES APPROVISIONNEMENTS</h1>
        <p>Période: {{ \Carbon\Carbon::parse($start_date)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($end_date)->format('d/m/Y') }}</p>
        <p>Généré le {{ \Carbon\Carbon::now()->format('d/m/Y à H:i') }}</p>
    </div>

    <div class="summary">
        <div class="summary-grid">
            <div class="summary-item">
                <h3>Total approvisionnements</h3>
                <div class="amount">{{ number_format($stats['total_supplies']) }}</div>
            </div>
            <div class="summary-item">
                <h3>Montant total</h3>
                <div class="amount">{{ number_format($stats['total_amount'], 0, ',', ' ') }} FCFA</div>
            </div>
            <div class="summary-item">
                <h3>Tonnage total</h3>
                <div class="amount">{{ number_format($stats['total_tonnage'], 2) }} T</div>
            </div>
            <div class="summary-item">
                <h3>Validés</h3>
                <div class="amount">{{ number_format($stats['validated_amount'], 0, ',', ' ') }} FCFA</div>
            </div>
            <div class="summary-item">
                <h3>En attente</h3>
                <div class="amount">{{ number_format($stats['pending_amount'], 0, ',', ' ') }} FCFA</div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>Liste des approvisionnements ({{ $supplies->count() }} résultats)</h2>
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Référence</th>
                    <th>Fournisseur</th>
                    <th>Catégorie</th>
                    <th>Tonnage</th>
                    <th>Montant</th>
                    <th>Statut</th>
                    <th>Date livraison</th>
                </tr>
            </thead>
            <tbody>
                @foreach($supplies->take(50) as $supply)
                <tr>
                    <td>{{ $supply->created_at->format('d/m/Y') }}</td>
                    <td>{{ $supply->reference }}</td>
                    <td>{{ $supply->supplier ? $supply->supplier->name : 'N/A' }}</td>
                    <td>{{ $supply->category ? $supply->category->name : 'N/A' }}</td>
                    <td class="text-right">{{ number_format($supply->total_tonnage, 2) }} T</td>
                    <td class="text-right">{{ number_format($supply->total_amount, 0, ',', ' ') }}</td>
                    <td class="text-center">
                        @switch($supply->status)
                            @case('validated')
                                <span class="badge badge-success">Validé</span>
                                @break
                            @case('pending')
                                <span class="badge badge-warning">En attente</span>
                                @break
                            @case('rejected')
                                <span class="badge badge-danger">Rejeté</span>
                                @break
                            @default
                                {{ $supply->status }}
                        @endswitch
                    </td>
                    <td>{{ $supply->expected_delivery_date ? $supply->expected_delivery_date->format('d/m/Y') : 'N/A' }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
        @if($supplies->count() > 50)
            <p><em>Note: Seuls les 50 premiers approvisionnements sont affichés dans ce rapport PDF.</em></p>
        @endif
    </div>

    <div class="footer">
        <p>GRADIS - Système de gestion des approvisionnements et ventes</p>
        <p>Ce rapport a été généré automatiquement le {{ \Carbon\Carbon::now()->format('d/m/Y à H:i') }}</p>
    </div>
</body>
</html>
