class ProductForm {
    constructor() {
        this.initializeElements();
        this.setupEventListeners();
        this.updateDisplay(); // Initial display update
    }

    initializeElements() {
        this.elements = {
            categorySelect: document.getElementById('category_id'),
            regionalPricing: document.getElementById('regional-pricing'),
            ironSpecifications: document.getElementById('iron-specifications'),
            unitSelect: document.getElementById('unit'),
            basePriceInput: document.getElementById('base_price'),
            progressBar: document.querySelector('.progress-bar'),
            form: document.getElementById('productForm')
        };

        // Vérifier que les éléments sont trouvés
        Object.entries(this.elements).forEach(([key, element]) => {
            if (!element) {
                console.warn(`Élément non trouvé: ${key}`);
            }
        });
    }

    setupEventListeners() {
        if (!this.elements.categorySelect) {
            console.error('Select de catégorie non trouvé');
            return;
        }

        // Écouteur pour le changement de catégorie
        this.elements.categorySelect.addEventListener('change', () => {
            console.log('Changement de catégorie détecté');
            this.updateDisplay();
        });
    }

    getCurrentCategory() {
        if (!this.elements.categorySelect) return null;

        const selectedOption = this.elements.categorySelect.options[this.elements.categorySelect.selectedIndex];
        if (!selectedOption || !selectedOption.value) {
            console.log('Aucune option sélectionnée');
            return null;
        }

        // Récupérer le texte de l'option sélectionnée
        const categoryText = selectedOption.textContent.trim().toLowerCase();
        console.log('Catégorie sélectionnée (texte brut):', selectedOption.textContent);
        console.log('Catégorie sélectionnée (après traitement):', categoryText);

        // Vérifier la catégorie
        if (categoryText.includes('ciment')) {
            console.log('Type détecté: Ciment');
            return 'ciment';
        } else if (categoryText.includes('fer')) {
            console.log('Type détecté: Fer');
            return 'fer';
        }

        console.log('Type non reconnu:', categoryText);
        return null;
    }

    updateDisplay() {
        const categoryName = this.getCurrentCategory();
        if (!categoryName) {
            console.log('Pas de catégorie valide sélectionnée');
            return;
        }

        const isCement = categoryName === 'ciment';
        const isIron = categoryName === 'fer';

        console.log('Mise à jour de l\'affichage');
        console.log('Est ciment:', isCement);
        console.log('Est fer:', isIron);

        // Afficher/masquer les sections avec vérification
        if (this.elements.regionalPricing) {
            console.log('Mise à jour section prix régionaux:', isCement ? 'visible' : 'caché');
            this.elements.regionalPricing.style.display = isCement ? 'block' : 'none';
        }

        if (this.elements.ironSpecifications) {
            console.log('Mise à jour section spécifications fer:', isIron ? 'visible' : 'caché');
            this.elements.ironSpecifications.style.display = isIron ? 'block' : 'none';
        }

        // Gérer l'affichage du prix de base
        if (this.elements.basePriceInput) {
            const basePriceContainer = this.elements.basePriceInput.closest('.col-md-4');
            if (basePriceContainer) {
                console.log('Mise à jour section prix de base:', isIron ? 'caché' : 'visible');
                basePriceContainer.style.display = isIron ? 'none' : 'block';
                this.elements.basePriceInput.required = !isIron;
            }
        }

        // Gérer la validation des champs du fer
        const diameterSelect = document.getElementById('diameter');
        const lengthInput = document.getElementById('length');
        const unitPriceInput = document.getElementById('unit_price');

        if (diameterSelect && lengthInput && unitPriceInput) {
            console.log('Mise à jour des champs de spécification du fer');
            diameterSelect.required = isIron;
            lengthInput.required = isIron;
            unitPriceInput.required = isIron;
        }
    }
}

// Attendre que le DOM soit chargé
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM chargé, initialisation du formulaire');
    window.productForm = new ProductForm();
});
