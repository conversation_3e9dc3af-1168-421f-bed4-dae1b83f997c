@extends('layouts.admin_minimal')

@section('title', 'Ajouter un Véhicule')

@section('content')
<div class="container-fluid py-4">
    <!-- Header avec breadcrumb -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-2">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.trucks.index') }}" class="text-decoration-none">
                            <i class="fas fa-truck me-1"></i> Véhicules
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Nouveau véhicule</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-plus-circle text-success me-2"></i>
                Ajouter un Nouveau Véhicule
            </h1>
            <p class="text-muted mb-0">Enregistrez un nouveau véhicule dans votre flotte</p>
        </div>
        <div>
            <a href="{{ route('admin.trucks.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Retour à la liste
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-xl-10 col-lg-12">
            <!-- Formulaire principal -->
            <form id="truckForm" action="{{ route('admin.trucks.store') }}" method="POST" class="needs-validation" novalidate>
                @csrf

                <!-- Section 1: Informations d'identification -->
                <div class="card shadow mb-4">
                    <div class="card-header bg-gradient-primary text-white">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-id-card me-2"></i>
                            Informations d'Identification
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="registration_number" class="form-label fw-bold">
                                        <i class="fas fa-fingerprint text-primary me-1"></i>
                                        N° d'Immatriculation <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-hashtag text-muted"></i>
                                        </span>
                                        <input type="text"
                                               class="form-control form-control-lg @error('registration_number') is-invalid @enderror"
                                               id="registration_number"
                                               name="registration_number"
                                               value="{{ old('registration_number') }}"
                                               placeholder="Ex: ABC-123-XY"
                                               required>
                                        <div class="valid-feedback">
                                            <i class="fas fa-check me-1"></i> Parfait !
                                        </div>
                                        @error('registration_number')
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-triangle me-1"></i> {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Numéro unique d'identification du véhicule
                                    </small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="truck_capacity_id" class="form-label fw-bold">
                                        <i class="fas fa-weight-hanging text-warning me-1"></i>
                                        Capacité de Charge <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-balance-scale text-muted"></i>
                                        </span>
                                        <select class="form-select form-select-lg @error('truck_capacity_id') is-invalid @enderror"
                                                id="truck_capacity_id"
                                                name="truck_capacity_id"
                                                required>
                                            <option value="">Choisir la capacité...</option>
                                            @foreach($capacities as $capacity)
                                                <option value="{{ $capacity->id }}"
                                                        data-tonnage="{{ $capacity->tonnage }}"
                                                        {{ old('truck_capacity_id') == $capacity->id ? 'selected' : '' }}>
                                                    {{ $capacity->description }} ({{ number_format($capacity->tonnage, 1) }}T)
                                                </option>
                                            @endforeach
                                        </select>
                                        <div class="valid-feedback">
                                            <i class="fas fa-check me-1"></i> Capacité sélectionnée !
                                        </div>
                                        @error('truck_capacity_id')
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-triangle me-1"></i> {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Capacité maximale de transport du véhicule
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section 2: Caractéristiques du véhicule -->
                <div class="card shadow mb-4">
                    <div class="card-header bg-gradient-info text-white">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-cogs me-2"></i>
                            Caractéristiques du Véhicule
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="brand" class="form-label fw-bold">
                                        <i class="fas fa-industry text-info me-1"></i>
                                        Marque <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-trademark text-muted"></i>
                                        </span>
                                        <input type="text"
                                               class="form-control form-control-lg @error('brand') is-invalid @enderror"
                                               id="brand"
                                               name="brand"
                                               value="{{ old('brand') }}"
                                               placeholder="Ex: Mercedes, Volvo, MAN..."
                                               list="brandSuggestions"
                                               required>
                                        <datalist id="brandSuggestions">
                                            <option value="Mercedes-Benz">
                                            <option value="Volvo">
                                            <option value="MAN">
                                            <option value="Scania">
                                            <option value="DAF">
                                            <option value="Iveco">
                                            <option value="Renault">
                                            <option value="Isuzu">
                                        </datalist>
                                        <div class="valid-feedback">
                                            <i class="fas fa-check me-1"></i> Marque validée !
                                        </div>
                                        @error('brand')
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-triangle me-1"></i> {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="model" class="form-label fw-bold">
                                        <i class="fas fa-car text-info me-1"></i>
                                        Modèle <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-tag text-muted"></i>
                                        </span>
                                        <input type="text"
                                               class="form-control form-control-lg @error('model') is-invalid @enderror"
                                               id="model"
                                               name="model"
                                               value="{{ old('model') }}"
                                               placeholder="Ex: Actros, FH, TGX..."
                                               required>
                                        <div class="valid-feedback">
                                            <i class="fas fa-check me-1"></i> Modèle validé !
                                        </div>
                                        @error('model')
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-triangle me-1"></i> {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="year" class="form-label fw-bold">
                                        <i class="fas fa-calendar-alt text-info me-1"></i>
                                        Année de Fabrication <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-calendar text-muted"></i>
                                        </span>
                                        <input type="number"
                                               class="form-control form-control-lg @error('year') is-invalid @enderror"
                                               id="year"
                                               name="year"
                                               value="{{ old('year', date('Y')) }}"
                                               min="1900"
                                               max="{{ date('Y') + 1 }}"
                                               placeholder="{{ date('Y') }}"
                                               required>
                                        <div class="valid-feedback">
                                            <i class="fas fa-check me-1"></i> Année validée !
                                        </div>
                                        @error('year')
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-triangle me-1"></i> {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Entre 1900 et {{ date('Y') + 1 }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section 3: Configuration et Notes -->
                <div class="card shadow mb-4">
                    <div class="card-header bg-gradient-success text-white">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-cog me-2"></i>
                            Configuration et Informations Complémentaires
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status" class="form-label fw-bold">
                                        <i class="fas fa-traffic-light text-success me-1"></i>
                                        Statut Initial <span class="text-danger">*</span>
                                    </label>
                                    <div class="status-options">
                                        <div class="form-check form-check-card mb-3">
                                            <input class="form-check-input" type="radio" name="status" id="status_available"
                                                   value="available" {{ old('status', 'available') == 'available' ? 'checked' : '' }} required>
                                            <label class="form-check-label status-card available" for="status_available">
                                                <div class="status-icon">
                                                    <i class="fas fa-check-circle"></i>
                                                </div>
                                                <div class="status-content">
                                                    <div class="status-title">Disponible</div>
                                                    <div class="status-description">Prêt pour les missions</div>
                                                </div>
                                            </label>
                                        </div>

                                        <div class="form-check form-check-card mb-3">
                                            <input class="form-check-input" type="radio" name="status" id="status_maintenance"
                                                   value="maintenance" {{ old('status') == 'maintenance' ? 'checked' : '' }}>
                                            <label class="form-check-label status-card maintenance" for="status_maintenance">
                                                <div class="status-icon">
                                                    <i class="fas fa-tools"></i>
                                                </div>
                                                <div class="status-content">
                                                    <div class="status-title">En Maintenance</div>
                                                    <div class="status-description">Réparation ou entretien</div>
                                                </div>
                                            </label>
                                        </div>

                                        <div class="form-check form-check-card mb-3">
                                            <input class="form-check-input" type="radio" name="status" id="status_busy"
                                                   value="busy" {{ old('status') == 'busy' ? 'checked' : '' }}>
                                            <label class="form-check-label status-card busy" for="status_busy">
                                                <div class="status-icon">
                                                    <i class="fas fa-shipping-fast"></i>
                                                </div>
                                                <div class="status-content">
                                                    <div class="status-title">Occupé</div>
                                                    <div class="status-description">En mission actuellement</div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    @error('status')
                                        <div class="invalid-feedback d-block">
                                            <i class="fas fa-exclamation-triangle me-1"></i> {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="notes" class="form-label fw-bold">
                                        <i class="fas fa-sticky-note text-warning me-1"></i>
                                        Notes et Observations
                                    </label>
                                    <textarea class="form-control form-control-lg @error('notes') is-invalid @enderror"
                                              id="notes"
                                              name="notes"
                                              rows="6"
                                              placeholder="Ajoutez des informations complémentaires sur le véhicule...&#10;&#10;Exemples :&#10;- État général du véhicule&#10;- Équipements spéciaux&#10;- Historique de maintenance&#10;- Restrictions d'utilisation">{{ old('notes') }}</textarea>
                                    <div class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Informations utiles pour la gestion du véhicule (optionnel)
                                    </div>
                                    @error('notes')
                                        <div class="invalid-feedback">
                                            <i class="fas fa-exclamation-triangle me-1"></i> {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section 4: Résumé et Actions -->
                <div class="card shadow mb-4">
                    <div class="card-header bg-gradient-secondary text-white">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-clipboard-check me-2"></i>
                            Validation et Enregistrement
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Résumé des informations -->
                        <div class="alert alert-info border-left-info" id="formSummary" style="display: none;">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-info-circle fa-lg me-2"></i>
                                <h6 class="mb-0">Résumé du véhicule</h6>
                            </div>
                            <div id="summaryContent"></div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <button type="button" class="btn btn-outline-secondary" id="previewBtn">
                                    <i class="fas fa-eye me-1"></i> Aperçu
                                </button>
                                <button type="button" class="btn btn-outline-info" id="resetBtn">
                                    <i class="fas fa-undo me-1"></i> Réinitialiser
                                </button>
                            </div>
                            <div>
                                <a href="{{ route('admin.trucks.index') }}" class="btn btn-secondary me-2">
                                    <i class="fas fa-times me-1"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-success btn-lg" id="submitBtn">
                                    <i class="fas fa-save me-1"></i>
                                    <span class="btn-text">Enregistrer le Véhicule</span>
                                    <span class="spinner-border spinner-border-sm ms-2" role="status" style="display: none;"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light fixed-bottom">
    <div class="container text-center">
        <span class="text-muted"> {{ date('Y') }} GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>
@endsection

@push('styles')
<style>
    /* Styles généraux */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .bg-gradient-info {
        background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
    }
    .bg-gradient-success {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    }
    .bg-gradient-secondary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    /* Breadcrumb personnalisé */
    .breadcrumb {
        background: transparent;
        padding: 0;
    }
    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: #6c757d;
        font-weight: bold;
    }

    /* Cartes de formulaire */
    .card {
        border: none;
        border-radius: 15px;
        overflow: hidden;
    }
    .card-header {
        border: none;
        padding: 1.5rem;
    }
    .card-body {
        padding: 2rem;
    }

    /* Groupes d'entrée améliorés */
    .input-group-text {
        border: 1px solid #e3e6f0;
        background-color: #f8f9fc;
    }
    .form-control, .form-select {
        border: 1px solid #e3e6f0;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    .form-control:focus, .form-select:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }
    .form-control-lg, .form-select-lg {
        padding: 1rem 1.25rem;
        font-size: 1.1rem;
    }

    /* Labels améliorés */
    .form-label {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.75rem;
    }

    /* Cartes de statut radio */
    .status-options {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }
    .form-check-card {
        margin-bottom: 0 !important;
    }
    .form-check-input {
        display: none;
    }
    .status-card {
        display: flex;
        align-items: center;
        padding: 1rem;
        border: 2px solid #e3e6f0;
        border-radius: 0.75rem;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }
    .status-card:hover {
        border-color: #4e73df;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    .form-check-input:checked + .status-card {
        border-color: #1cc88a;
        background: linear-gradient(135deg, rgba(28, 200, 138, 0.1) 0%, rgba(28, 200, 138, 0.05) 100%);
    }
    .form-check-input:checked + .status-card.available {
        border-color: #1cc88a;
        background: linear-gradient(135deg, rgba(28, 200, 138, 0.1) 0%, rgba(28, 200, 138, 0.05) 100%);
    }
    .form-check-input:checked + .status-card.maintenance {
        border-color: #f6c23e;
        background: linear-gradient(135deg, rgba(246, 194, 62, 0.1) 0%, rgba(246, 194, 62, 0.05) 100%);
    }
    .form-check-input:checked + .status-card.busy {
        border-color: #e74a3b;
        background: linear-gradient(135deg, rgba(231, 74, 59, 0.1) 0%, rgba(231, 74, 59, 0.05) 100%);
    }

    .status-icon {
        font-size: 1.5rem;
        margin-right: 1rem;
        width: 40px;
        text-align: center;
    }
    .status-card.available .status-icon {
        color: #1cc88a;
    }
    .status-card.maintenance .status-icon {
        color: #f6c23e;
    }
    .status-card.busy .status-icon {
        color: #e74a3b;
    }

    .status-title {
        font-weight: 600;
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }
    .status-description {
        font-size: 0.875rem;
        color: #6c757d;
    }

    /* Boutons améliorés */
    .btn {
        border-radius: 0.5rem;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
    }
    .btn-lg {
        padding: 1rem 2rem;
        font-size: 1.1rem;
    }
    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* Alertes personnalisées */
    .border-left-info {
        border-left: 4px solid #36b9cc !important;
    }

    /* Validation feedback amélioré */
    .valid-feedback, .invalid-feedback {
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }
    .valid-feedback {
        color: #1cc88a;
    }

    /* Animations */
    .card {
        animation: slideInUp 0.5s ease-out;
    }
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Responsive */
    @media (max-width: 768px) {
        .card-body {
            padding: 1.5rem;
        }
        .status-options {
            gap: 0.5rem;
        }
        .status-card {
            padding: 0.75rem;
        }
        .status-icon {
            font-size: 1.25rem;
            margin-right: 0.75rem;
            width: 30px;
        }
    }

    /* Indicateur de chargement */
    .btn .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }

    /* Textarea amélioré */
    textarea.form-control {
        resize: vertical;
        min-height: 120px;
    }

    /* Datalist styling */
    input[list] {
        position: relative;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('truckForm');
    const submitBtn = document.getElementById('submitBtn');
    const previewBtn = document.getElementById('previewBtn');
    const resetBtn = document.getElementById('resetBtn');
    const formSummary = document.getElementById('formSummary');
    const summaryContent = document.getElementById('summaryContent');

    // Validation en temps réel
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            validateField(this);
            updateFormSummary();
        });
        input.addEventListener('change', function() {
            validateField(this);
            updateFormSummary();
        });
    });

    // Fonction de validation d'un champ
    function validateField(field) {
        const value = field.value.trim();
        const isRequired = field.hasAttribute('required');
        let isValid = true;

        // Réinitialiser les classes
        field.classList.remove('is-valid', 'is-invalid');

        if (isRequired && !value) {
            isValid = false;
        } else if (value) {
            // Validations spécifiques
            switch (field.name) {
                case 'registration_number':
                    // Validation du format d'immatriculation (exemple simple)
                    const regPattern = /^[A-Z0-9\-]{3,15}$/i;
                    isValid = regPattern.test(value);
                    break;
                case 'year':
                    const year = parseInt(value);
                    const currentYear = new Date().getFullYear();
                    isValid = year >= 1900 && year <= currentYear + 1;
                    break;
                case 'brand':
                case 'model':
                    isValid = value.length >= 2;
                    break;
            }
        }

        // Appliquer les classes de validation
        if (value && isValid) {
            field.classList.add('is-valid');
        } else if (value && !isValid) {
            field.classList.add('is-invalid');
        }

        return isValid;
    }

    // Fonction pour mettre à jour le résumé
    function updateFormSummary() {
        const formData = new FormData(form);
        const registration = formData.get('registration_number');
        const brand = formData.get('brand');
        const model = formData.get('model');
        const year = formData.get('year');
        const status = formData.get('status');
        const capacitySelect = document.getElementById('truck_capacity_id');
        const capacityText = capacitySelect.options[capacitySelect.selectedIndex]?.text || '';

        if (registration || brand || model) {
            const statusLabels = {
                'available': '✅ Disponible',
                'maintenance': '🔧 En maintenance',
                'busy': '🚛 Occupé'
            };

            summaryContent.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>Véhicule:</strong> ${brand} ${model} (${year})<br>
                        <strong>Immatriculation:</strong> ${registration}<br>
                        <strong>Capacité:</strong> ${capacityText}
                    </div>
                    <div class="col-md-6">
                        <strong>Statut:</strong> ${statusLabels[status] || 'Non défini'}
                    </div>
                </div>
            `;
            formSummary.style.display = 'block';
        } else {
            formSummary.style.display = 'none';
        }
    }

    // Bouton aperçu
    previewBtn.addEventListener('click', function() {
        updateFormSummary();
        if (formSummary.style.display === 'none') {
            alert('Veuillez remplir au moins les informations de base pour voir l\'aperçu.');
        } else {
            formSummary.scrollIntoView({ behavior: 'smooth' });
        }
    });

    // Bouton réinitialiser
    resetBtn.addEventListener('click', function() {
        if (confirm('Êtes-vous sûr de vouloir réinitialiser le formulaire ? Toutes les données saisies seront perdues.')) {
            form.reset();
            inputs.forEach(input => {
                input.classList.remove('is-valid', 'is-invalid');
            });
            formSummary.style.display = 'none';
            // Remettre le statut par défaut
            document.getElementById('status_available').checked = true;
        }
    });

    // Soumission du formulaire
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validation complète
        let isFormValid = true;
        inputs.forEach(input => {
            if (!validateField(input)) {
                isFormValid = false;
            }
        });

        if (!isFormValid) {
            alert('Veuillez corriger les erreurs dans le formulaire avant de continuer.');
            return;
        }

        // Afficher l'indicateur de chargement
        const btnText = submitBtn.querySelector('.btn-text');
        const spinner = submitBtn.querySelector('.spinner-border');

        btnText.textContent = 'Enregistrement...';
        spinner.style.display = 'inline-block';
        submitBtn.disabled = true;

        // Soumettre le formulaire
        setTimeout(() => {
            form.submit();
        }, 500);
    });

    // Auto-complétion intelligente pour la marque
    const brandInput = document.getElementById('brand');
    const modelInput = document.getElementById('model');

    const brandModels = {
        'Mercedes-Benz': ['Actros', 'Arocs', 'Atego', 'Axor', 'Econic'],
        'Volvo': ['FH', 'FM', 'FE', 'FL', 'VNL'],
        'MAN': ['TGX', 'TGS', 'TGL', 'TGM'],
        'Scania': ['R-Series', 'S-Series', 'P-Series', 'G-Series'],
        'DAF': ['XF', 'CF', 'LF'],
        'Iveco': ['Stralis', 'Trakker', 'Eurocargo', 'Daily'],
        'Renault': ['T-Series', 'C-Series', 'K-Series', 'D-Series'],
        'Isuzu': ['NPR', 'NQR', 'FRR', 'FVR']
    };

    brandInput.addEventListener('change', function() {
        const selectedBrand = this.value;
        if (brandModels[selectedBrand]) {
            // Créer une datalist pour les modèles
            let modelDatalist = document.getElementById('modelSuggestions');
            if (!modelDatalist) {
                modelDatalist = document.createElement('datalist');
                modelDatalist.id = 'modelSuggestions';
                document.body.appendChild(modelDatalist);
            }

            modelDatalist.innerHTML = '';
            brandModels[selectedBrand].forEach(model => {
                const option = document.createElement('option');
                option.value = model;
                modelDatalist.appendChild(option);
            });

            modelInput.setAttribute('list', 'modelSuggestions');
        }
    });

    // Animation d'entrée pour les cartes
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Initialiser le résumé si des données existent déjà
    updateFormSummary();
});
</script>
@endpush
