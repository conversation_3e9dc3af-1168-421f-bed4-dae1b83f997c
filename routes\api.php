<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Models\Category;
use App\Models\Region;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\CityController;
use App\Models\Truck;
use App\Models\City;
use App\Http\Controllers\Api\TruckController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Routes publiques
Route::get('/vehicles', function () {
    try {
        $vehicles = Truck::with(['capacity', 'driver'])
            ->whereHas('driver') // Seulement les véhicules avec chauffeur
            ->whereHas('capacity') // Et avec une capacité définie
            ->get()
            ->map(function ($truck) {
                return [
                    'id' => $truck->id,
                    'registration_number' => $truck->registration_number,
                    'capacity' => [
                        'id' => $truck->capacity->id,
                        'tonnage' => $truck->capacity->tonnage,
                    ],
                    'driver' => [
                        'id' => $truck->driver->id,
                        'name' => $truck->driver->full_name,
                        'phone' => $truck->driver->phone_number
                    ]
                ];
            });

        return response()->json($vehicles);
    } catch (\Exception $e) {
        \Log::error('Erreur lors de la récupération des véhicules', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        return response()->json(
            ['error' => 'Erreur lors de la récupération des véhicules'],
            500
        );
    }
});

Route::get('/regions/{region}/cities', function ($region) {
    try {
        $cities = City::where('region_id', $region)
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        return response()->json($cities);
    } catch (\Exception $e) {
        \Log::error('Erreur lors de la récupération des villes', [
            'error' => $e->getMessage(),
            'region' => $region
        ]);
        return response()->json(
            ['error' => 'Erreur lors de la récupération des villes'],
            500
        );
    }
});

Route::get('/categories/{category}/products', function ($category) {
    try {
        $products = Category::findOrFail($category)->products;
        return response()->json($products);
    } catch (\Exception $e) {
        \Log::error('Erreur lors de la récupération des produits', [
            'error' => $e->getMessage(),
            'category' => $category
        ]);
        return response()->json(
            ['error' => 'Erreur lors de la récupération des produits'],
            500
        );
    }
});

// Routes protégées
Route::middleware(['auth'])->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // Route pour les villes
    Route::get('/cities/list', [CityController::class, 'list']);

    // Route pour les véhicules avec leurs chauffeurs
    Route::get('/vehicles/with-drivers', function () {
        try {
            $vehicles = Truck::with(['driver', 'capacity'])
                ->whereNotNull('driver_id')
                ->where('status', 'assigned')
                ->get()
                ->map(function ($truck) {
                    return [
                        'id' => $truck->id,
                        'registration' => $truck->registration_number,
                        'capacity' => $truck->capacity ? $truck->capacity->tonnage : null,
                        'driver' => $truck->driver ? [
                            'id' => $truck->driver->id,
                            'name' => $truck->driver->full_name,
                            'phone' => $truck->driver->phone_number
                        ] : null
                    ];
                });

            return response()->json($vehicles);
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la récupération des véhicules avec chauffeurs', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(
                ['error' => 'Erreur lors de la récupération des véhicules avec chauffeurs'],
                500
            );
        }
    });

    // Routes pour les camions
    Route::get('/trucks/available', [TruckController::class, 'getAvailableTrucks']);
});

Route::prefix('admin')->name('api.admin.')->middleware(['auth', 'admin'])->group(function () {
    Route::post('supplies/{supply}/validate', [App\Http\Controllers\Admin\SupplyController::class, 'validateSupply'])
        ->name('supplies.validate');
    Route::post('supplies/{supply}/reject', [App\Http\Controllers\Admin\SupplyController::class, 'rejectSupply'])
        ->name('supplies.reject');
});
