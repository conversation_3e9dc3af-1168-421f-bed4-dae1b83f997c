/**
 * Initialisation du graphique des approvisionnements
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialiser le graphique des approvisionnements s'il existe
    const suppliesChartElement = document.getElementById('suppliesChart');
    if (suppliesChartElement) {
        initializeSuppliesChart(suppliesChartElement);
    }
});

/**
 * Initialise le graphique des approvisionnements
 * @param {HTMLElement} chartElement - L'élément canvas du graphique
 */
function initializeSupplies<PERSON>hart(chartElement) {
    try {
        // Récupérer les données du graphique
        const suppliesData = JSON.parse(chartElement.dataset.supplies || '{"labels":[],"supplies":[],"tonnages":[]}');
        
        // Configurer le graphique
        const ctx = chartElement.getContext('2d');
        
        // Créer le graphique avec deux séries de données (nombre et tonnage)
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: suppliesData.labels || [],
                datasets: [
                    {
                        label: 'Nombre d\'approvisionnements',
                        data: suppliesData.supplies || [],
                        backgroundColor: 'rgba(54, 162, 235, 0.6)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1,
                        yAxisID: 'y',
                        order: 2
                    },
                    {
                        label: 'Tonnage (T)',
                        data: suppliesData.tonnages || [],
                        type: 'line',
                        fill: false,
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        pointBackgroundColor: 'rgba(255, 99, 132, 1)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgba(255, 99, 132, 1)',
                        tension: 0.4,
                        yAxisID: 'y1',
                        order: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Nombre d\'approvisionnements'
                        },
                        beginAtZero: true
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Tonnage (T)'
                        },
                        beginAtZero: true,
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    },
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: false
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                },
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    } catch (error) {
        console.error('Erreur lors de l\'initialisation du graphique des approvisionnements:', error);
    }
}

/**
 * Gestion des filtres de période pour le graphique des approvisionnements
 * À implémenter selon les besoins
 */
function handleSupplyPeriodFilter(period) {
    // À implémenter pour filtrer les données par période
    console.log('Filtrage des approvisionnements par période:', period);
    // Ici, vous pourriez faire une requête AJAX pour obtenir de nouvelles données
    // et mettre à jour le graphique
}
