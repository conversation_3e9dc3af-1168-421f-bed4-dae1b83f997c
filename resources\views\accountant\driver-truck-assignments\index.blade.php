@extends('layouts.accountant')

@section('title', 'Assignation des Véhicules')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Gestion des Assignations de Véhicules</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Chauffeur</th>
                                    <th>Véhicule Actuel</th>
                                    <th>Nouveau Véhicule</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($drivers as $driver)
                                <tr>
                                    <td>{{ $driver->first_name }} {{ $driver->last_name }}</td>
                                    <td>
                                        @if($driver->truck)
                                            {{ $driver->truck->registration_number }}
                                            ({{ $driver->truck->status }})
                                        @else
                                            Aucun
                                        @endif
                                    </td>
                                    <td>
                                        <select class="form-control truck-select" data-driver-id="{{ $driver->id }}">
                                            <option value="">Sélectionner un véhicule</option>
                                            @foreach($trucks as $truck)
                                                @if($truck->status === 'available' || $truck->driver_id === $driver->id)
                                                    <option value="{{ $truck->id }}" 
                                                        {{ $driver->truck_id == $truck->id ? 'selected' : '' }}>
                                                        {{ $truck->registration_number }} 
                                                        ({{ $truck->capacity ? $truck->capacity->name : 'Capacité non définie' }})
                                                        {{ $truck->status !== 'available' ? '(Actuellement assigné)' : '' }}
                                                    </option>
                                                @endif
                                            @endforeach
                                        </select>
                                    </td>
                                    <td>
                                        <button class="btn btn-primary btn-sm assign-truck" 
                                                data-driver-id="{{ $driver->id }}">
                                            Assigner
                                        </button>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const assignButtons = document.querySelectorAll('.assign-truck');
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    
    assignButtons.forEach(button => {
        button.addEventListener('click', function() {
            const driverId = this.dataset.driverId;
            const truckSelect = document.querySelector(`.truck-select[data-driver-id="${driverId}"]`);
            const truckId = truckSelect.value;

            if (!truckId) {
                alert('Veuillez sélectionner un véhicule.');
                return;
            }

            fetch('{{ route("accountant.driver-truck-assignments.update") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    driver_id: driverId,
                    truck_id: truckId
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erreur réseau');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert(data.message || 'Une erreur est survenue');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue lors de la réassignation du véhicule.');
            });
        });
    });
});
</script>
@endpush
