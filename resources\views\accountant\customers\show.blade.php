@extends('layouts.accountant')

@section('title', 'Détails du Client')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="dashboard-card slide-in-up">
                <div class="dashboard-card-header d-flex justify-content-between align-items-center">
                    <div class="dashboard-card-title">
                        <i class="fas fa-user me-2"></i>Détails du client
                    </div>
                    <div>
                        <a href="{{ route('accountant.customers.edit', $customer) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-1"></i> Modifier
                        </a>
                    </div>
                </div>
                <div class="dashboard-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="customer-info-card mb-4">
                                <h4 class="section-title">
                                    <i class="fas fa-info-circle me-2"></i>Informations personnelles
                                </h4>
                                <div class="table-responsive">
                                    <table class="table">
                                        <tr>
                                            <th width="40%">Nom complet</th>
                                            <td>{{ $customer->name }}</td>
                                        </tr>
                                        <tr>
                                            <th>Email</th>
                                            <td>{{ $customer->email }}</td>
                                        </tr>
                                        <tr>
                                            <th>Téléphone</th>
                                            <td>{{ $customer->phone }}</td>
                                        </tr>
                                        <tr>
                                            <th>Adresse</th>
                                            <td>{{ $customer->address ?? 'Non spécifiée' }}</td>
                                        </tr>
                                        <tr>
                                            <th>Date d'inscription</th>
                                            <td>{{ $customer->created_at->format('d/m/Y') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="customer-stats-card mb-4">
                                <h4 class="section-title">
                                    <i class="fas fa-chart-bar me-2"></i>Statistiques
                                </h4>
                                <div class="row">
                                    <div class="col-6 mb-3">
                                        <div class="stat-card bg-primary text-white">
                                            <div class="stat-icon">
                                                <i class="fas fa-shopping-cart"></i>
                                            </div>
                                            <div class="stat-details">
                                                <div class="stat-value">{{ $customer->sales->count() ?? 0 }}</div>
                                                <div class="stat-label">Ventes totales</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="stat-card bg-success text-white">
                                            <div class="stat-icon">
                                                <i class="fas fa-money-bill"></i>
                                            </div>
                                            <div class="stat-details">
                                                <div class="stat-value">{{ $customer->payments->count() ?? 0 }}</div>
                                                <div class="stat-label">Paiements</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Section des ventes récentes du client -->
                    <div class="recent-sales mt-4">
                        <h4 class="section-title">
                            <i class="fas fa-history me-2"></i>Ventes récentes
                        </h4>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Date</th>
                                        <th>Référence</th>
                                        <th>Montant</th>
                                        <th>Statut</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if(isset($customer->sales) && $customer->sales->count() > 0)
                                        @foreach($customer->sales->take(5) as $sale)
                                            <tr>
                                                <td>{{ $loop->iteration }}</td>
                                                <td>{{ $sale->created_at->format('d/m/Y') }}</td>
                                                <td>{{ $sale->reference }}</td>
                                                <td>{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</td>
                                                <td>
                                                    @if($sale->status == 'paid')
                                                        <span class="badge bg-success">Payée</span>
                                                    @elseif($sale->status == 'pending')
                                                        <span class="badge bg-warning">En attente</span>
                                                    @else
                                                        <span class="badge bg-danger">Annulée</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <a href="{{ route('accountant.sales.show', $sale->id) }}" class="btn btn-info btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="6" class="text-center">Aucune vente trouvée</td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <a href="{{ route('accountant.customers.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Retour à la liste
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .customer-info-card, .customer-stats-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        padding: 20px;
    }
    
    .section-title {
        color: #1E88E5;
        font-size: 1.1rem;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .stat-card {
        display: flex;
        align-items: center;
        border-radius: 8px;
        padding: 15px;
        height: 100%;
    }
    
    .stat-icon {
        font-size: 2rem;
        margin-right: 15px;
    }
    
    .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .recent-sales {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        padding: 20px;
    }
</style>
@endsection
