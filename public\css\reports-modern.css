/* Styles modernes pour les rapports financiers - GRADIS */

/* Variables CSS supplémentaires */
:root {
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --backdrop-blur: blur(20px);
    --neon-glow: 0 0 20px rgba(79, 70, 229, 0.5);
    --card-hover-scale: 1.02;
    --animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Effet glassmorphism pour les cartes */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Animations personnalisées */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(79, 70, 229, 0.5); }
    50% { box-shadow: 0 0 20px rgba(79, 70, 229, 0.8); }
}

@keyframes slideInFromLeft {
    0% { transform: translateX(-100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromRight {
    0% { transform: translateX(100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

/* Classes d'animation */
.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
}

.animate-slide-left {
    animation: slideInFromLeft 0.6s ease-out;
}

.animate-slide-right {
    animation: slideInFromRight 0.6s ease-out;
}

.animate-bounce-in {
    animation: bounceIn 0.6s var(--animation-bounce);
}

/* Styles pour les badges et indicateurs */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-badge.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Styles pour les tooltips modernes */
.tooltip-modern {
    position: relative;
    cursor: help;
}

.tooltip-modern::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-900);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius);
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.tooltip-modern::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--gray-900);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.tooltip-modern:hover::before,
.tooltip-modern:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Styles pour les graphiques en temps réel */
.realtime-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: var(--success);
    font-weight: 500;
}

.realtime-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    background: var(--success);
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

/* Styles pour les métriques avancées */
.metric-comparison {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.comparison-arrow {
    font-size: 0.75rem;
    padding: 0.25rem;
    border-radius: var(--radius);
}

.comparison-arrow.up {
    color: var(--success);
    background: rgba(16, 185, 129, 0.1);
}

.comparison-arrow.down {
    color: var(--error);
    background: rgba(239, 68, 68, 0.1);
}

/* Styles pour les filtres avancés */
.filter-group {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1.5rem;
}

.filter-select {
    padding: 0.5rem 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius);
    background: white;
    font-size: 0.875rem;
    transition: var(--transition);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Styles pour les notifications en temps réel */
.notification-bell {
    position: relative;
    cursor: pointer;
}

.notification-bell::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: var(--error);
    border-radius: 50%;
    animation: pulse 1s infinite;
}

/* Styles pour le mode sombre (optionnel) */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-50: #0f172a;
        --gray-100: #1e293b;
        --gray-200: #334155;
        --gray-300: #475569;
        --gray-400: #64748b;
        --gray-500: #94a3b8;
        --gray-600: #cbd5e1;
        --gray-700: #e2e8f0;
        --gray-800: #f1f5f9;
        --gray-900: #f8fafc;
    }
    
    body {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
    }
    
    .stats-card,
    .chart-card {
        background: rgba(30, 41, 59, 0.8);
        border-color: rgba(255, 255, 255, 0.1);
    }
}

/* Styles pour l'impression */
@media print {
    .dashboard-header,
    .header-actions,
    .btn-modern {
        display: none !important;
    }
    
    .stats-grid,
    .chart-card {
        break-inside: avoid;
    }
    
    body {
        background: white !important;
    }
}
