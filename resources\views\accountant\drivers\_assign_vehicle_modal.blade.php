<!-- Modal d'assignation de véhicule -->
<div class="modal fade" id="assignVehicleModal" tabindex="-1" role="dialog" aria-labelledby="assignVehicleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignVehicleModalLabel">
                    <i class="fas fa-truck mr-1"></i> Assignation de véhicule
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form class="assign-vehicle-form" method="POST">
                @csrf
                <div class="modal-body">
                    <input type="hidden" name="driver_id" value="{{ $driver->id }}">
                    @if($driver->truck)
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            Ce chauffeur est actuellement assigné au véhicule : 
                            <strong>
                                {{ $driver->truck->registration_number }}
                                ({{ $driver->truck->brand }} {{ $driver->truck->model }} - 
                                {{ $driver->truck->capacity ? $driver->truck->capacity->tonnage . 'T' : 'N/A' }})
                            </strong>
                        </div>
                        <p class="text-muted">
                            Pour assigner un nouveau véhicule, vous devez d'abord retirer l'assignation actuelle.
                        </p>
                        <button type="button" class="btn btn-warning mb-3" id="removeAssignment">
                            <i class="fas fa-unlink"></i> Retirer l'assignation actuelle
                        </button>
                    @endif
                    
                    <div class="form-group">
                        <label for="truck_id">Sélectionner un véhicule :</label>
                        <select class="form-control" 
                                name="truck_id" 
                                id="truck_id" 
                                required
                                {{ $driver->truck ? 'disabled' : '' }}>
                            <option value="">Sélectionnez un véhicule</option>
                            @foreach($availableTrucks as $truck)
                                <option value="{{ $truck->id }}">
                                    {{ $truck->registration_number }} 
                                    ({{ $truck->brand }} {{ $truck->model }} - 
                                    {{ $truck->capacity ? $truck->capacity->tonnage . 'T' : 'N/A' }})
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                    <button type="submit" class="btn btn-primary" {{ $driver->truck ? 'disabled' : '' }}>
                        <i class="fas fa-save"></i> Assigner
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
