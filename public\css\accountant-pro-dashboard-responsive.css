/**
 * GRADIS - Dashboard Comptable Professionnel
 * Styles responsifs pour l'interface
 * 2025 MOMK-Solutions
 */

/* Responsive Design - Mobile First */
@media (max-width: 576px) {
    .dashboard-header {
        padding: 1.5rem 1rem;
        margin-bottom: 1rem;
    }
    
    .dashboard-title {
        font-size: 1.5rem;
    }
    
    .dashboard-subtitle {
        font-size: 0.875rem;
    }
    
    .header-actions {
        flex-wrap: wrap;
    }
    
    .header-action-btn {
        font-size: 0.75rem;
        padding: 0.4rem 0.75rem;
    }
    
    .header-data-item {
        flex: 0 0 50%;
    }
    
    .header-data-value {
        font-size: 1.25rem;
    }
    
    .stat-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-value {
        font-size: 1.25rem;
    }
    
    .dashboard-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .period-filters {
        overflow-x: auto;
        padding-bottom: 0.5rem;
        width: 100%;
        justify-content: flex-start;
    }
    
    .period-filter {
        white-space: nowrap;
    }
    
    .chart-container {
        min-height: 200px;
    }
    
    .pro-table thead th {
        padding: 0.75rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .pro-table tbody td {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .status-badge {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .btn-action {
        width: 32px;
        height: 32px;
    }
    
    .info-widget-value {
        font-size: 1.125rem;
    }
    
    .quick-action-card {
        padding: 0.75rem;
    }
    
    .quick-action-icon {
        width: 36px;
        height: 36px;
        font-size: 1rem;
        margin-right: 0.75rem;
    }
    
    .quick-action-title {
        font-size: 0.875rem;
    }
    
    .quick-action-desc {
        font-size: 0.75rem;
    }
}

/* Tablette */
@media (min-width: 577px) and (max-width: 991px) {
    .stat-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .dashboard-header {
        padding: 1.75rem 1.25rem;
    }
    
    .dashboard-card-header {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 0.75rem;
        align-items: center;
    }
    
    .period-filters {
        flex-wrap: wrap;
    }
}

/* Écran moyen à grand */
@media (min-width: 992px) {
    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
    }
    
    .dashboard-header {
        padding: 2rem;
    }
    
    .stat-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
    }
}

/* Écran très large */
@media (min-width: 1400px) {
    .dashboard-container {
        max-width: 1600px;
    }
    
    .dashboard-card {
        margin-bottom: 2rem;
    }
}

/* Classes utilitaires responsives */
@media (max-width: 767px) {
    .hide-on-mobile {
        display: none !important;
    }
    
    .text-center-mobile {
        text-align: center !important;
    }
    
    .flex-column-mobile {
        flex-direction: column !important;
    }
    
    .w-100-mobile {
        width: 100% !important;
    }
    
    .mt-2-mobile {
        margin-top: 0.5rem !important;
    }
    
    .mb-2-mobile {
        margin-bottom: 0.5rem !important;
    }
    
    .p-2-mobile {
        padding: 0.5rem !important;
    }
}

/* Modifications spécifiques pour l'affichage des tableaux sur mobile */
@media (max-width: 767px) {
    .mobile-scroll-wrapper {
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    /* Tableau responsive avec cartes sur mobile */
    .pro-table.table-to-cards {
        display: block;
    }
    
    .pro-table.table-to-cards thead {
        display: none;
    }
    
    .pro-table.table-to-cards tbody {
        display: block;
    }
    
    .pro-table.table-to-cards tr {
        display: block;
        margin-bottom: 1rem;
        background: white;
        border-radius: var(--card-border-radius);
        box-shadow: var(--card-shadow);
        overflow: hidden;
        border-left: 3px solid var(--primary);
    }
    
    .pro-table.table-to-cards td {
        display: flex;
        padding: 0.5rem 1rem;
        text-align: right;
        border-bottom: 1px solid var(--gray-100);
    }
    
    .pro-table.table-to-cards td:last-child {
        border-bottom: none;
    }
    
    .pro-table.table-to-cards td::before {
        content: attr(data-label);
        width: 40%;
        text-align: left;
        font-weight: 600;
        color: var(--gray-700);
    }
    
    .pro-table.table-to-cards td .status-badge {
        margin-left: auto;
    }
    
    .pro-table.table-to-cards td .btn-action {
        margin-left: 0.25rem;
    }
}

/* Adaptation des graphiques pour les écrans de différentes tailles */
@media (max-width: 767px) {
    .chart-container {
        min-height: 180px;
    }
}

@media (min-width: 768px) and (max-width: 1199px) {
    .chart-container {
        min-height: 250px;
    }
}

@media (min-width: 1200px) {
    .chart-container {
        min-height: 300px;
    }
}

/* Flex Grid Responsif */
.flex-grid {
    display: flex;
    flex-wrap: wrap;
    margin: -0.75rem;
}

.flex-grid > * {
    padding: 0.75rem;
}

/* Dimensions des colonnes de la grille flex par défaut */
.flex-grid-col {
    width: 100%;
}

@media (min-width: 576px) {
    .flex-grid-col.col-sm-6 {
        width: 50%;
    }
    
    .flex-grid-col.col-sm-4 {
        width: 33.333333%;
    }
    
    .flex-grid-col.col-sm-3 {
        width: 25%;
    }
}

@media (min-width: 768px) {
    .flex-grid-col.col-md-6 {
        width: 50%;
    }
    
    .flex-grid-col.col-md-4 {
        width: 33.333333%;
    }
    
    .flex-grid-col.col-md-3 {
        width: 25%;
    }
    
    .flex-grid-col.col-md-2 {
        width: 16.666667%;
    }
}

@media (min-width: 992px) {
    .flex-grid-col.col-lg-6 {
        width: 50%;
    }
    
    .flex-grid-col.col-lg-4 {
        width: 33.333333%;
    }
    
    .flex-grid-col.col-lg-3 {
        width: 25%;
    }
    
    .flex-grid-col.col-lg-2 {
        width: 16.666667%;
    }
}

/* Adaptation spécifique des icônes pour les petits écrans */
@media (max-width: 576px) {
    .stat-icon, .info-widget-icon, .quick-action-icon {
        transform: scale(0.9);
    }
}

/* Meilleure visibilité des badges sur mobile */
@media (max-width: 576px) {
    .status-badge {
        width: 100%;
        justify-content: center;
    }
}
