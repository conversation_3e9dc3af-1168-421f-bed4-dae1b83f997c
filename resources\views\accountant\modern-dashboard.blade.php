@extends('layouts.accountant')

@section('title', 'Tableau de bord')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
@endpush

<style>
/* Styles directement intégrés pour une interface professionnelle */
body {
  background: #f8fafc;
  font-family: 'Inter', sans-serif;
  color: #1F2937;
}

.dashboard-container {
  padding: 1.5rem;
  max-width: 1600px;
  margin: 0 auto;
}

.activities-container {
  padding: 1.5rem;
  max-height: 450px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(67, 97, 238, 0.3) transparent;
}

.activities-container::-webkit-scrollbar {
  width: 6px;
}

.activities-container::-webkit-scrollbar-track {
  background: transparent;
}

.activities-container::-webkit-scrollbar-thumb {
  background-color: rgba(67, 97, 238, 0.3);
  border-radius: 20px;
  border: transparent;
}

/* Styles pour les cartes */
.activity-card {
  background: white;
  backdrop-filter: blur(15px);
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.04), 0 6px 12px rgba(0, 0, 0, 0.02);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  transform-style: preserve-3d;
  perspective: 1000px;
  margin-bottom: 1.5rem;
  height: 100%;
  border-bottom: 4px solid #7209b7;
}

.stat-card {
  background: white;
  backdrop-filter: blur(15px);
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.04), 0 6px 12px rgba(0, 0, 0, 0.02);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  transform-style: preserve-3d;
  perspective: 1000px;
  opacity: 0;
  border-bottom: 4px solid transparent;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  height: 100%;
}

/* Bordures colorées pour les différentes cartes */
.primary-card {
  border-bottom-color: #4361ee;
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.03) 0%, rgba(255, 255, 255, 1) 100%);
}

.info-card {
  border-bottom-color: #4cc9f0;
  background: linear-gradient(135deg, rgba(76, 201, 240, 0.03) 0%, rgba(255, 255, 255, 1) 100%);
}

.success-card {
  border-bottom-color: #06d6a0;
  background: linear-gradient(135deg, rgba(6, 214, 160, 0.03) 0%, rgba(255, 255, 255, 1) 100%);
}

.warning-card {
  border-bottom-color: #f9c74f;
  background: linear-gradient(135deg, rgba(249, 199, 79, 0.03) 0%, rgba(255, 255, 255, 1) 100%);
}

.danger-card {
  border-bottom-color: #ef476f;
  background: linear-gradient(135deg, rgba(239, 71, 111, 0.03) 0%, rgba(255, 255, 255, 1) 100%);
}

/* Effets de survol améliorés */
.chart-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(67, 97, 238, 0.1), 0 8px 15px rgba(67, 97, 238, 0.05);
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08), 0 8px 15px rgba(0, 0, 0, 0.04);
}

.primary-card:hover {
  box-shadow: 0 15px 35px rgba(67, 97, 238, 0.15), 0 8px 15px rgba(67, 97, 238, 0.08);
}

.success-card:hover {
  box-shadow: 0 15px 35px rgba(6, 214, 160, 0.15), 0 8px 15px rgba(6, 214, 160, 0.08);
}

.warning-card:hover {
  box-shadow: 0 15px 35px rgba(249, 199, 79, 0.15), 0 8px 15px rgba(249, 199, 79, 0.08);
}

.danger-card:hover {
  box-shadow: 0 15px 35px rgba(239, 71, 111, 0.15), 0 8px 15px rgba(239, 71, 111, 0.08);
}

/* Contenu des cartes */
.card-body {
  padding: 1.5rem;
  position: relative;
  z-index: 2;
}

.card-title {
  font-size: 1.05rem;
  font-weight: 600;
  color: #4B5563;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  letter-spacing: -0.2px;
}

.card-value {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 1rem 0;
  display: block;
  letter-spacing: -0.5px;
  position: relative;
  z-index: 2;
  display: inline-flex;
  align-items: baseline;
}

.unit {
  font-size: 1.2rem;
  font-weight: 600;
  margin-left: 0.3rem;
  opacity: 0.7;
  -webkit-text-fill-color: initial;
  color: #4B5563;
}

.primary-card .card-value {
  background: linear-gradient(45deg, #3a0ca3, #4361ee);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 5px 15px rgba(67, 97, 238, 0.15);
}

.info-card .card-value {
  background: linear-gradient(45deg, #00b4d8, #00b4d8);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.success-card .card-value {
  background: linear-gradient(45deg, #059669, #06d6a0);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 5px 15px rgba(6, 214, 160, 0.15);
}

.warning-card .card-value {
  background: linear-gradient(45deg, #d97706, #f9c74f);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 5px 15px rgba(249, 199, 79, 0.15);
}

.danger-card .card-value {
  background: linear-gradient(45deg, #be123c, #ef476f);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 5px 15px rgba(239, 71, 111, 0.15);
}

/* Icônes des cartes */
.card-icon {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  font-size: 2.5rem;
  opacity: 0.15;
  color: #4361ee;
  transition: all 0.5s ease;
}

.primary-card .card-icon {
  color: #4361ee;
}

.info-card .card-icon {
  color: #00b4d8;
}

.success-card .card-icon {
  color: #06d6a0;
}

.warning-card .card-icon {
  color: #f9c74f;
}

.danger-card .card-icon {
  color: #ef476f;
}

/* Styles pour les graphiques */
.chart-card {
  background: white;
  backdrop-filter: blur(15px);
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.04), 0 6px 12px rgba(0, 0, 0, 0.02);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  transform-style: preserve-3d;
  perspective: 1000px;
  border: none;
  margin-bottom: 1.5rem;
  border-bottom: 4px solid #4361ee;
}

.chart-container {
  width: 100%;
  height: 350px;
  position: relative;
  z-index: 2;
  padding: 1rem;
}

/* Contrôles du graphique */
.chart-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin: 0 1.75rem 1.25rem;
  padding-top: 1rem;
  position: relative;
  z-index: 5;
}

.chart-toggle-btn {
  background: #f8fafc;
  border: 1px solid rgba(67, 97, 238, 0.15);
  border-radius: 50px;
  padding: 0.6rem 1.2rem;
  font-size: 0.85rem;
  font-weight: 600;
  color: #4B5563;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  color: #1F2937;
}

.chart-toggle-btn.active {
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
  border: none;
  color: white;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
  position: relative;
  padding: 0.65rem 1.25rem;
}

.chart-toggle-btn.active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 8px;
  height: 8px;
  background: #4361ee;
  border-radius: 50%;
}

/* Styles pour l'en-tête du tableau de bord */
.dashboard-header {
  background: linear-gradient(120deg, #4361ee 0%, #3a0ca3 100%);
  border-radius: 16px;
  padding: 2.5rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(67, 97, 238, 0.2);
  color: white;
}

/* Styles pour les étiquettes avec dégradé */
.gradient-text {
  background: linear-gradient(45deg, #3a0ca3, #4361ee);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

/* Badge pill pour les étiquettes */
.badge-pill {
  background: linear-gradient(45deg, #4361ee, #4cc9f0);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.7rem;
  font-weight: 600;
  margin-left: 0.75rem;
  vertical-align: middle;
  box-shadow: 0 3px 10px rgba(67, 97, 238, 0.2);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
}

.header-title-section {
  max-width: 70%;
}

.header-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  color: white;
  display: inline-block;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.5px;
}

.year-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.3rem 1rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 700;
  margin-left: 1rem;
  vertical-align: middle;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  animation: pulse 2s infinite;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(5px);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

.header-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  max-width: 80%;
  margin-top: 0.75rem;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.header-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.8rem;
  box-shadow: 0 10px 25px rgba(67, 97, 238, 0.3);
}

.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.2) 0%, rgba(58, 12, 163, 0.2) 100%);
  opacity: 0.5;
}

.circle-1 {
  width: 150px;
  height: 150px;
  top: -50px;
  right: 10%;
  animation: float 8s ease-in-out infinite;
}

.circle-2 {
  width: 100px;
  height: 100px;
  bottom: -30px;
  right: 20%;
  animation: float 6s ease-in-out infinite 1s;
}

.circle-3 {
  width: 70px;
  height: 70px;
  bottom: 30px;
  left: 15%;
  animation: float 7s ease-in-out infinite 0.5s;
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

.decoration-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 15px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' fill='%234361ee'/%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.5' fill='%234361ee'/%3E%3Cpath d='M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z' fill='%234361ee'/%3E%3C/svg%3E") no-repeat;
  background-size: cover;
  opacity: 0.2;
}
</style>

@section('content')
<!-- Début du contenu principal -->

<div class="dashboard-container">
    <!-- En-tête du tableau de bord avec animation -->
    <div class="dashboard-header animate__animated animate__fadeIn">
        <div class="header-content">
            <div class="header-title-section">
                <h1 class="header-title">Tableau de bord <span class="year-badge">2025</span></h1>
                <p class="header-subtitle">Gestion financière et suivi des approvisionnements</p>
            </div>
            <div class="header-icon animate__animated animate__pulse animate__infinite">
                <i class="fas fa-chart-pie"></i>
            </div>
        </div>
        <!-- Éléments décoratifs pour l'en-tête -->
        <div class="header-decoration">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
            <div class="decoration-wave"></div>
        </div>
    </div>

    <!-- Stats Overview avec design moderne -->
    <div class="stats-container">
        <div class="row g-4 mb-4">
            <div class="col-xl-3 col-md-6" style="--animation-order: 0">
                <div class="stat-card primary-card">
                    <div class="card-body">
                        <div class="card-title"><i class="fas fa-coins me-2"></i>Total des commandes</div>
                        <div class="card-value">{{ $stats['total_cement_orders'] ?? ($stats['monthly_cement_orders'] ?? 0) }}</div>
                        <i class="fas fa-file-invoice card-icon"></i>
                    </div>
                    <div class="card-trend">
                        <span><i class="fas fa-calendar-alt"></i> Ce mois: {{ $stats['monthly_cement_orders'] ?? 0 }}</span>
                    </div>
                    <div class="card-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6" style="--animation-order: 1">
                <div class="stat-card info-card">
                    <div class="card-body">
                        <div class="card-title"><i class="fas fa-weight-hanging me-2"></i>Tonnage total</div>
                        <div class="card-value">{{ $stats['total_cement_tonnage'] ?? 0 }}<span class="unit">T</span></div>
                        <i class="fas fa-truck-loading card-icon"></i>
                    </div>
                    <div class="card-trend">
                        <span><i class="fas fa-clipboard-list"></i> {{ $stats['pending_orders'] ?? 0 }} commandes en attente</span>
                    </div>
                    <div class="card-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6" style="--animation-order: 2">
                <div class="stat-card success-card">
                    <div class="card-body">
                        <div class="card-title"><i class="fas fa-check-circle me-2"></i>Tonnage validé</div>
                        <div class="card-value">{{ $stats['validated_cement_tonnage'] ?? 0 }}<span class="unit">T</span></div>
                        <i class="fas fa-thumbs-up card-icon"></i>
                    </div>
                    <div class="card-trend">
                        <span><i class="fas fa-percentage"></i> 
                            @php
                                $totalTonnage = $stats['total_cement_tonnage'] ?? 0;
                                $validatedPercentage = $totalTonnage > 0 ? round(($stats['validated_cement_tonnage'] ?? 0) / $totalTonnage * 100) : 0;
                            @endphp
                            {{ $validatedPercentage }}% du tonnage total
                        </span>
                    </div>
                    <div class="card-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6" style="--animation-order: 3">
                <div class="stat-card danger-card">
                    <div class="card-body">
                        <div class="card-title"><i class="fas fa-times-circle me-2"></i>Tonnage rejeté</div>
                        <div class="card-value">{{ $stats['rejected_cement_tonnage'] ?? 0 }}<span class="unit">T</span></div>
                        <i class="fas fa-thumbs-down card-icon"></i>
                    </div>
                    <div class="card-trend">
                        <span><i class="fas fa-percentage"></i>
                            @php
                                $totalTonnage = $stats['total_cement_tonnage'] ?? 0;
                                $rejectedPercentage = $totalTonnage > 0 ? round(($stats['rejected_cement_tonnage'] ?? 0) / $totalTonnage * 100) : 0;
                            @endphp
                            {{ $rejectedPercentage }}% du tonnage total
                        </span>
                    </div>
                    <div class="card-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Deuxième rangée de cartes statistiques -->
        <div class="row g-4">
            <div class="col-xl-4 col-md-6" style="--animation-order: 4">
                <div class="stat-card primary-card">
                    <div class="card-body">
                        <div class="card-title"><i class="fas fa-coins me-2"></i>Ventes du mois</div>
                        <div class="card-value currency">0</div>
                        <i class="fas fa-shopping-cart card-icon"></i>
                    </div>
                    <div class="card-trend">
                        <span><i class="fas fa-chart-line"></i> +12.5% par rapport au mois dernier</span>
                    </div>
                    <div class="card-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-4 col-md-6" style="--animation-order: 5">
                <div class="stat-card warning-card">
                    <div class="card-body">
                        <div class="card-title"><i class="fas fa-user-check me-2"></i>Clients actifs</div>
                        <div class="card-value">0</div>
                        <i class="fas fa-users card-icon"></i>
                    </div>
                    <div class="card-trend">
                        <span><i class="fas fa-user-plus"></i> 5 nouveaux ce mois</span>
                    </div>
                    <div class="card-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-4 col-md-6" style="--animation-order: 6">
                <div class="stat-card danger-card">
                    <div class="card-body">
                        <div class="card-title"><i class="fas fa-chart-line me-2"></i>Chiffre d'affaires</div>
                        <div class="card-value currency">0</div>
                        <i class="fas fa-money-bill-wave card-icon"></i>
                    </div>
                    <div class="card-trend">
                        <span><i class="fas fa-chart-line"></i> +8.3% par rapport au mois dernier</span>
                    </div>
                    <div class="card-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section de débogage temporaire -->
    @if(isset($stats['debug_info']))
    <div class="row mt-4 mb-4">
        <div class="col-12">
            <div class="chart-card">
                <div class="card-header">
                    <h5 class="mb-0">Informations de débogage</h5>
                </div>
                <div class="p-3">
                    <h6>Statuts disponibles:</h6>
                    <ul>
                        @foreach($stats['debug_info']['statuses'] as $status)
                            <li>{{ $status == 'validated' ? 'Validé' : ($status == 'rejected' ? 'Rejeté' : $status) }}</li>
                        @endforeach
                    </ul>
                    
                    <h6>Commandes (10 premières):</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Référence</th>
                                    <th>Statut</th>
                                    <th>Tonnage</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach(array_slice($stats['debug_info']['all_supplies'], 0, 10) as $supply)
                                    <tr>
                                        <td>{{ $supply['id'] }}</td>
                                        <td>{{ $supply['reference'] }}</td>
                                        <td>
                                            @if($supply['status'] == 'validated')
                                                Validé
                                            @elseif($supply['status'] == 'rejected')
                                                Rejeté
                                            @elseif($supply['status'] == 'pending')
                                                En attente
                                            @else
                                                {{ $supply['status'] }}
                                            @endif
                                        </td>
                                        <td>{{ $supply['total_tonnage'] }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
    
    <!-- Graphiques et statistiques -->
    <div class="row mt-4 mb-4">
        <div class="col-12 col-xl-8 mb-4">
            <div class="chart-card">
                <div class="card-header">
                    <div class="card-title">
                        <div class="card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3>
                            <span class="gradient-text">Évolution des commandes</span>
                            <span class="badge-pill">Statistiques</span>
                        </h3>
                    </div>
                    <div class="card-actions">
                        <button class="action-button" title="Télécharger">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
                <div class="chart-controls">
                    <button class="chart-toggle-btn active" data-type="orders">
                        <i class="fas fa-shopping-cart"></i> Nombre de commandes
                    </button>
                    <button class="chart-toggle-btn" data-type="amount">
                        <i class="fas fa-money-bill-wave"></i> Montant total
                    </button>
                    <button class="chart-toggle-btn" data-type="tonnage">
                        <i class="fas fa-weight-hanging"></i> Tonnage
                    </button>
                </div>
                <div class="chart-container">
                    <canvas id="ordersChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Activités récentes -->
        <div class="col-12 col-xl-4 mb-4">
            <div class="activity-card">
                <div class="card-header">
                    <div class="card-title">
                        <div class="card-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3>
                            <span class="gradient-text">Activités récentes</span>
                            <span class="badge-pill">Notifications</span>
                        </h3>
                    </div>
                </div>
                <div class="activities-container" id="recent-activities">
                    @if(count($formattedOrders) > 0)
                        @foreach($formattedOrders as $order)
                            <div class="activity-item {{ $order['status'] == 'completed' ? 'success' : ($order['status'] == 'rejected' ? 'danger' : 'warning') }}">
                                <div class="activity-icon">
                                    <i class="fas fa-{{ $order['status'] == 'completed' ? 'check-circle' : ($order['status'] == 'rejected' ? 'times-circle' : 'exclamation-circle') }}"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">
                                        Bon #{{ $order['id'] }}
                                        <span class="badge-status {{ $order['status'] == 'completed' ? 'success' : ($order['status'] == 'rejected' ? 'danger' : 'warning') }}">
                                            {{ $order['status'] == 'completed' ? 'Validé' : ($order['status'] == 'rejected' ? 'Rejeté' : 'En attente') }}
                                        </span>
                                    </div>
                                    <div class="activity-details">
                                        <div class="activity-detail">
                                            <i class="fas fa-building"></i>
                                            <span>{{ $order['supplier_name'] }}</span>
                                        </div>
                                        <div class="activity-detail">
                                            <i class="fas fa-weight-hanging"></i>
                                            <span>{{ $order['tonnage'] }} tonnes</span>
                                        </div>
                                        <div class="activity-detail">
                                            <i class="far fa-calendar-alt"></i>
                                            <span>{{ $order['date'] }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <p class="text-muted">Aucune activité récente</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Initialisation au chargement de la page
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM chargé, initialisation des graphiques et animations...');
        try {
            // Animation des cartes statistiques avec délai progressif
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('animate__animated', 'animate__fadeInUp');
                    card.style.opacity = 1;
                }, 100 * index);
            });
            
            // Animation des cercles décoratifs
            const circles = document.querySelectorAll('.decoration-circle');
            circles.forEach((circle, index) => {
                setTimeout(() => {
                    circle.style.opacity = '0.8';
                    circle.style.transform = 'scale(1)';
                }, 200 * index);
            });
            
            // Initialisation du graphique
            initOrdersChart();
            
            // Gestion des boutons de contrôle du graphique
            const chartToggleBtns = document.querySelectorAll('.chart-toggle-btn');
            chartToggleBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // Mettre à jour les classes actives
                    chartToggleBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    const dataType = this.getAttribute('data-type');
                    let datasetIndex;
                    
                    // Déterminer l'index du dataset en fonction du type
                    switch(dataType) {
                        case 'orders':
                            datasetIndex = 0;
                            break;
                        case 'amount':
                            datasetIndex = 1;
                            break;
                        case 'tonnage':
                            datasetIndex = 2;
                            break;
                        default:
                            datasetIndex = 0;
                    }
                    
                    // Afficher le dataset correspondant et masquer les autres
                    if (window.ordersChart) {
                        window.ordersChart.data.datasets.forEach((dataset, i) => {
                            dataset.hidden = i !== datasetIndex;
                        });
                        
                        // Mettre à jour le graphique avec animation
                        window.ordersChart.update({
                            duration: 800,
                            easing: 'easeOutQuart'
                        });
                    }
                });
            });
            
            // Simulation d'activités récentes
            setTimeout(function() {
                const container = document.getElementById('recent-activities');
                if (!container) {
                    console.log('Conteneur d\'activités récentes non trouvé');
                    return;
                }
                
                const activities = [
                    { type: 'Nouveau bon', status: 'success', message: 'Bon #12345 créé pour Client XYZ', time_ago: 'Il y a 10 min' },
                    { type: 'Paiement', status: 'success', message: 'Paiement de 1,500,000 FCFA reçu', time_ago: 'Il y a 1h' },
                    { type: 'Alerte', status: 'warning', message: 'Stock de ciment faible (15%)', time_ago: 'Il y a 3h' },
                    { type: 'Erreur', status: 'danger', message: 'Échec de la validation du bon #12340', time_ago: 'Il y a 5h' }
                ];
                
                if (activities.length) {
                    let html = '';
                    activities.forEach((activity, index) => {
                        let statusClass = activity.status;
                        
                        html += `
                        <div class="activity-item ${statusClass}">
                            <div class="activity-icon">
                                <i class="fas fa-${activity.status === 'success' ? 'check-circle' : activity.status === 'danger' ? 'times-circle' : 'exclamation-circle'}"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">
                                    ${activity.type}
                                    <span class="badge-status ${statusClass}">${activity.status === 'success' ? 'Succès' : activity.status === 'danger' ? 'Erreur' : 'Alerte'}</span>
                                </div>
                                <div class="activity-details">
                                    <div class="activity-detail">
                                        <i class="fas fa-info-circle"></i>
                                        <span>${activity.message}</span>
                                    </div>
                                    <div class="activity-detail">
                                        <i class="far fa-clock"></i>
                                        <span>${activity.time_ago}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        `;
                    });
                    
                    container.innerHTML = html;
                } else {
                    container.innerHTML = '<div class="text-center py-4"><p class="text-muted">Aucune activité récente</p></div>';
                }
            }, 500);
        } catch (error) {
            console.error('Erreur lors de l\'initialisation:', error);
        }
    });

    // Initialisation du graphique
    function initOrdersChart() {
        console.log('Initialisation du graphique...');
        const canvas = document.getElementById('ordersChart');
        if (!canvas) {
            console.error('Élément canvas non trouvé');
            return;
        }
        
        const ctx = canvas.getContext('2d');
        if (!ctx) {
            console.error('Contexte 2D non disponible');
            return;
        }
        
        // Créer des dégradés colorés plus vifs pour le fond du graphique
        const gradient1 = ctx.createLinearGradient(0, 0, 0, 300);
        gradient1.addColorStop(0, 'rgba(67, 97, 238, 0.7)');
        gradient1.addColorStop(0.5, 'rgba(67, 97, 238, 0.3)');
        gradient1.addColorStop(1, 'rgba(67, 97, 238, 0)');
        
        const gradient2 = ctx.createLinearGradient(0, 0, 0, 300);
        gradient2.addColorStop(0, 'rgba(6, 214, 160, 0.6)');
        gradient2.addColorStop(0.5, 'rgba(6, 214, 160, 0.25)');
        gradient2.addColorStop(1, 'rgba(6, 214, 160, 0)');
        
        const gradient3 = ctx.createLinearGradient(0, 0, 0, 300);
        gradient3.addColorStop(0, 'rgba(239, 71, 111, 0.6)');
        gradient3.addColorStop(0.5, 'rgba(239, 71, 111, 0.25)');
        gradient3.addColorStop(1, 'rgba(239, 71, 111, 0)');
        
        // Créer des dégradés plus vifs pour les lignes du graphique
        const lineGradient1 = ctx.createLinearGradient(0, 0, 600, 0);
        lineGradient1.addColorStop(0, '#3a0ca3');
        lineGradient1.addColorStop(0.5, '#4361ee');
        lineGradient1.addColorStop(1, '#4cc9f0');
        
        const lineGradient2 = ctx.createLinearGradient(0, 0, 600, 0);
        lineGradient2.addColorStop(0, '#059669');
        lineGradient2.addColorStop(0.5, '#06d6a0');
        lineGradient2.addColorStop(1, '#90e0ef');
        
        const lineGradient3 = ctx.createLinearGradient(0, 0, 600, 0);
        lineGradient3.addColorStop(0, '#be123c');
        lineGradient3.addColorStop(0.5, '#ef476f');
        lineGradient3.addColorStop(1, '#ffc8dd');
        
        console.log('Configuration du graphique...');
        window.ordersChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: {!! json_encode($chartData['labels']) !!},
                datasets: [
                    {
                        label: 'Nombre de bons',
                        data: {!! json_encode($chartData['data']) !!},
                        backgroundColor: gradient1,
                        borderColor: lineGradient1,
                        borderWidth: 3,
                        pointBackgroundColor: '#ffffff',
                        pointBorderColor: '#4361ee',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 10,
                        pointHoverBackgroundColor: '#ffffff',
                        pointHoverBorderColor: '#4cc9f0',
                        pointHoverBorderWidth: 3,
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Montant total (FCFA)',
                        data: [1800000, 2500000, 2100000, 3200000, 2800000, 3800000, 4500000],
                        backgroundColor: gradient2,
                        borderColor: lineGradient2,
                        borderWidth: 2,
                        borderDash: [5, 5],
                        pointBackgroundColor: '#ffffff',
                        pointBorderColor: '#06d6a0',
                        pointBorderWidth: 2,
                        pointRadius: 5,
                        pointHoverRadius: 8,
                        pointHoverBackgroundColor: '#ffffff',
                        pointHoverBorderColor: '#90e0ef',
                        pointHoverBorderWidth: 3,
                        tension: 0.4,
                        fill: true,
                        hidden: true
                    },
                    {
                        label: 'Tonnage total',
                        data: [60, 95, 75, 125, 110, 150, 175],
                        backgroundColor: gradient3,
                        borderColor: lineGradient3,
                        borderWidth: 2,
                        borderDash: [3, 3],
                        pointBackgroundColor: '#ffffff',
                        pointBorderColor: '#ef476f',
                        pointBorderWidth: 2,
                        pointRadius: 5,
                        pointHoverRadius: 8,
                        pointHoverBackgroundColor: '#ffffff',
                        pointHoverBorderColor: '#ffc8dd',
                        pointHoverBorderWidth: 3,
                        tension: 0.4,
                        fill: true,
                        hidden: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1800,
                    easing: 'easeOutQuart'
                },
                hover: {
                    mode: 'nearest',
                    intersect: true,
                    animationDuration: 300
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        align: 'end',
                        labels: {
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 20,
                            font: {
                                family: 'Poppins',
                                size: 12,
                                weight: '600'
                            }
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(255, 255, 255, 0.98)',
                        titleColor: '#111827',
                        bodyColor: '#4B5563',
                        borderColor: 'rgba(67, 97, 238, 0.3)',
                        borderWidth: 2,
                        padding: 16,
                        boxPadding: 10,
                        cornerRadius: 8,
                        titleFont: {
                            family: 'Poppins',
                            size: 14,
                            weight: '600'
                        },
                        bodyFont: {
                            family: 'Inter',
                            size: 13
                        },
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.datasetIndex === 1) {
                                    label += new Intl.NumberFormat('fr-FR').format(context.parsed.y) + ' FCFA';
                                } else {
                                    label += context.parsed.y;
                                }
                                return label;
                            }
                        },
                        animation: {
                            duration: 400
                        }
                    }
                },
                interaction: {
                    mode: 'index',
                    intersect: false,
                    hover: {
                        animationDuration: 400
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false,
                            drawBorder: false
                        },
                        ticks: {
                            font: {
                                family: 'Inter',
                                size: 12
                            },
                            color: '#6B7280'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(243, 244, 246, 0.6)',
                            drawBorder: false,
                            borderDash: [5, 5]
                        },
                        beginAtZero: true,
                        ticks: {
                            font: {
                                family: 'Inter',
                                size: 12
                            },
                            color: '#6B7280',
                            padding: 10
                        }
                    }
                },
                elements: {
                    line: {
                        tension: 0.4
                    },
                    point: {
                        hoverRadius: 10,
                        hoverBorderWidth: 3
                    }
                }
            }
        });
        console.log('Graphique initialisé avec succès');
    }
</script>
