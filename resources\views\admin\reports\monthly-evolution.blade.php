@extends('layouts.admin_minimal')

@section('title', 'Évolution Mensuelle')

@section('content')
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-chart-line text-success me-2"></i>
                Évolution Mensuelle {{ $year }}
            </h1>
            <p class="text-muted mb-0">Analyse des tendances mensuelles pour l'année {{ $year }}</p>
        </div>
        <div>
            <a href="{{ route('admin.reports.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Retour
            </a>
        </div>
    </div>

    <!-- Formulaire de filtres -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>
                Sélection de l'année
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.reports.monthly-evolution') }}" class="row g-3">
                <div class="col-md-4">
                    <label for="year" class="form-label">Année</label>
                    <select class="form-control" id="year" name="year">
                        @for($y = date('Y'); $y >= date('Y') - 5; $y--)
                            <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                        @endfor
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i> Analyser
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-1"></i> Exporter
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.reports.monthly-evolution', array_merge(request()->query(), ['export' => 'excel'])) }}">
                                    <i class="fas fa-file-excel text-success me-2"></i> Excel
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.reports.monthly-evolution', array_merge(request()->query(), ['export' => 'pdf'])) }}">
                                    <i class="fas fa-file-pdf text-danger me-2"></i> PDF
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Résumé annuel -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Revenus {{ $year }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($total_revenue, 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-up fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Coûts {{ $year }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($total_costs, 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-down fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-{{ $total_profit >= 0 ? 'success' : 'danger' }} shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-{{ $total_profit >= 0 ? 'success' : 'danger' }} text-uppercase mb-1">
                                Bénéfice {{ $year }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($total_profit, 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-coins fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Marge moyenne
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $total_revenue > 0 ? number_format(($total_profit / $total_revenue) * 100, 1) : 0 }}%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Évolution mensuelle -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-chart-area me-2"></i>
                Évolution mensuelle détaillée
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th>Mois</th>
                            <th>Ventes</th>
                            <th>Approvisionnements</th>
                            <th>Revenus</th>
                            <th>Coûts</th>
                            <th>Bénéfice</th>
                            <th>Marge (%)</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($monthly_data as $data)
                            <tr>
                                <td>
                                    <strong>{{ $data['month_name'] }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ number_format($data['sales_count']) }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ number_format($data['supplies_count']) }}</span>
                                </td>
                                <td>
                                    {{ number_format($data['revenue'], 0, ',', ' ') }} FCFA
                                </td>
                                <td>
                                    {{ number_format($data['costs'], 0, ',', ' ') }} FCFA
                                </td>
                                <td class="text-{{ $data['profit'] >= 0 ? 'success' : 'danger' }}">
                                    <strong>{{ number_format($data['profit'], 0, ',', ' ') }} FCFA</strong>
                                </td>
                                <td class="text-{{ $data['profit_margin'] >= 0 ? 'success' : 'danger' }}">
                                    {{ number_format($data['profit_margin'], 1) }}%
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Graphiques interactifs -->
            <div class="mt-4">
                <div class="row">
                    <!-- Graphique principal : Évolution des revenus, coûts et bénéfices -->
                    <div class="col-lg-8">
                        <div class="card shadow-sm">
                            <div class="card-header bg-gradient-primary text-white">
                                <h6 class="m-0 font-weight-bold">
                                    <i class="fas fa-chart-line me-2"></i>
                                    Évolution Financière {{ $year }}
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="evolutionChart" style="height: 400px;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Graphique secondaire : Répartition mensuelle -->
                    <div class="col-lg-4">
                        <div class="card shadow-sm">
                            <div class="card-header bg-gradient-info text-white">
                                <h6 class="m-0 font-weight-bold">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    Répartition des Bénéfices
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="profitDistributionChart" style="height: 400px;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Graphique des volumes d'activité -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card shadow-sm">
                            <div class="card-header bg-gradient-success text-white">
                                <h6 class="m-0 font-weight-bold">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    Volume d'Activité Mensuelle
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="volumeChart" style="height: 350px;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Indicateurs de performance avec graphiques sparkline -->
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            Tendance Revenus
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            @php
                                                $revenueGrowth = 0;
                                                $lastMonthRevenue = $monthly_data->where('month', 11)->first()['revenue'] ?? 0;
                                                $currentMonthRevenue = $monthly_data->where('month', 12)->first()['revenue'] ?? 0;
                                                if ($lastMonthRevenue > 0) {
                                                    $revenueGrowth = (($currentMonthRevenue - $lastMonthRevenue) / $lastMonthRevenue) * 100;
                                                }
                                            @endphp
                                            {{ $revenueGrowth >= 0 ? '+' : '' }}{{ number_format($revenueGrowth, 1) }}%
                                        </div>
                                        <div id="revenueSparkline" style="height: 50px;"></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-arrow-up fa-2x text-{{ $revenueGrowth >= 0 ? 'success' : 'danger' }}"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            Marge Moyenne
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            {{ number_format($monthly_data->avg('profit_margin'), 1) }}%
                                        </div>
                                        <div id="marginSparkline" style="height: 50px;"></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-percentage fa-2x text-success"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            Meilleur Mois
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            @php
                                                $bestMonth = $monthly_data->sortByDesc('profit')->first();
                                            @endphp
                                            {{ $bestMonth['month_name'] ?? 'N/A' }}
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            {{ number_format($bestMonth['profit'] ?? 0, 0, ',', ' ') }} FCFA
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-trophy fa-2x text-warning"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            Total Transactions
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            {{ number_format($monthly_data->sum('sales_count') + $monthly_data->sum('supplies_count')) }}
                                        </div>
                                        <div id="transactionSparkline" style="height: 50px;"></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-exchange-alt fa-2x text-warning"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analyse des tendances -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-analytics me-2"></i>
                Analyse des tendances
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-success">Meilleurs mois</h6>
                    <ul class="list-unstyled">
                        @foreach($monthly_data->sortByDesc('profit')->take(3) as $data)
                            <li class="mb-2">
                                <i class="fas fa-trophy text-warning me-2"></i>
                                <strong>{{ $data['month_name'] }}</strong> : 
                                {{ number_format($data['profit'], 0, ',', ' ') }} FCFA
                                <small class="text-muted">({{ number_format($data['profit_margin'], 1) }}%)</small>
                            </li>
                        @endforeach
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="text-info">Statistiques générales</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-chart-line text-info me-2"></i>
                            <strong>Revenus moyens/mois :</strong> 
                            {{ number_format($monthly_data->avg('revenue'), 0, ',', ' ') }} FCFA
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-shopping-cart text-primary me-2"></i>
                            <strong>Ventes moyennes/mois :</strong> 
                            {{ number_format($monthly_data->avg('sales_count'), 0) }}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-truck-loading text-secondary me-2"></i>
                            <strong>Approvisionnements moyens/mois :</strong> 
                            {{ number_format($monthly_data->avg('supplies_count'), 0) }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-danger { border-left: 0.25rem solid #e74a3b !important; }
.border-left-info { border-left: 0.25rem solid #36b9cc !important; }
.progress-sm { height: 0.5rem; }

/* Styles pour les graphiques */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

/* Animation pour les graphiques */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.chart-container {
    animation: fadeInUp 0.8s ease-out;
}

/* Responsive design pour les graphiques */
@media (max-width: 768px) {
    #evolutionChart,
    #profitDistributionChart,
    #volumeChart {
        height: 300px !important;
    }

    .col-lg-8,
    .col-lg-4 {
        margin-bottom: 1rem;
    }
}

/* Amélioration des tooltips */
.apexcharts-tooltip {
    background: rgba(0, 0, 0, 0.8) !important;
    border: none !important;
    border-radius: 8px !important;
    color: white !important;
}

/* Style pour les cartes d'indicateurs */
.card.border-left-primary:hover,
.card.border-left-success:hover,
.card.border-left-info:hover,
.card.border-left-warning:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-5px);
}

/* Animation des icônes */
.fa-arrow-up,
.fa-percentage,
.fa-trophy,
.fa-exchange-alt {
    transition: transform 0.3s ease;
}

.card:hover .fa-arrow-up,
.card:hover .fa-percentage,
.card:hover .fa-trophy,
.card:hover .fa-exchange-alt {
    transform: scale(1.1);
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/apexcharts@3.44.0/dist/apexcharts.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Données pour les graphiques
    const monthlyData = @json($monthly_data);
    const months = monthlyData.map(item => item.month_name);
    const revenues = monthlyData.map(item => item.revenue);
    const costs = monthlyData.map(item => item.costs);
    const profits = monthlyData.map(item => item.profit);
    const salesCounts = monthlyData.map(item => item.sales_count);
    const suppliesCounts = monthlyData.map(item => item.supplies_count);
    const profitMargins = monthlyData.map(item => item.profit_margin);

    // 1. Graphique principal : Évolution financière
    const evolutionChart = new ApexCharts(document.querySelector("#evolutionChart"), {
        series: [{
            name: 'Revenus',
            type: 'column',
            data: revenues
        }, {
            name: 'Coûts',
            type: 'column',
            data: costs
        }, {
            name: 'Bénéfices',
            type: 'line',
            data: profits
        }],
        chart: {
            height: 400,
            type: 'line',
            toolbar: {
                show: true,
                tools: {
                    download: true,
                    selection: false,
                    zoom: false,
                    zoomin: false,
                    zoomout: false,
                    pan: false,
                    reset: false
                }
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        colors: ['#4e73df', '#e74a3b', '#1cc88a'],
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '55%',
                endingShape: 'rounded'
            },
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            show: true,
            width: [0, 0, 4],
            colors: ['transparent', 'transparent', '#1cc88a']
        },
        xaxis: {
            categories: months,
            labels: {
                style: {
                    fontSize: '12px'
                }
            }
        },
        yaxis: [{
            title: {
                text: 'Montant (FCFA)'
            },
            labels: {
                formatter: function (val) {
                    return new Intl.NumberFormat('fr-FR').format(val) + ' FCFA';
                }
            }
        }],
        fill: {
            opacity: [0.85, 0.85, 1]
        },
        tooltip: {
            shared: true,
            intersect: false,
            y: {
                formatter: function (val) {
                    return new Intl.NumberFormat('fr-FR').format(val) + ' FCFA';
                }
            }
        },
        legend: {
            position: 'top',
            horizontalAlign: 'left'
        }
    });
    evolutionChart.render();

    // 2. Graphique de répartition des bénéfices (Donut)
    // Préparer les données pour le graphique de répartition
    const positiveProfits = profits.filter(p => p > 0);
    const positiveProfitLabels = months.filter((month, index) => profits[index] > 0);

    // Vérifier s'il y a des bénéfices positifs à afficher
    if (positiveProfits.length > 0) {
        const profitDistributionChart = new ApexCharts(document.querySelector("#profitDistributionChart"), {
            series: positiveProfits,
            labels: positiveProfitLabels,
            chart: {
                type: 'donut',
                height: 400,
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 800
                }
            },
            colors: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796', '#5a5c69', '#6f42c1', '#e83e8c', '#fd7e14', '#20c997', '#6610f2'],
            plotOptions: {
                pie: {
                    donut: {
                        size: '70%',
                        labels: {
                            show: true,
                            total: {
                                show: true,
                                label: 'Total',
                                formatter: function (w) {
                                    const total = w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                                    return new Intl.NumberFormat('fr-FR').format(total) + ' FCFA';
                                }
                            }
                        }
                    }
                }
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return new Intl.NumberFormat('fr-FR').format(val) + ' FCFA';
                    }
                }
            },
            legend: {
                position: 'bottom',
                fontSize: '12px'
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        });
        profitDistributionChart.render();
    } else {
        // Afficher un message si aucun bénéfice positif
        document.querySelector("#profitDistributionChart").innerHTML = `
            <div class="d-flex flex-column align-items-center justify-content-center h-100 text-muted">
                <i class="fas fa-chart-pie fa-3x mb-3 opacity-50"></i>
                <h6 class="mb-2">Aucun bénéfice positif</h6>
                <p class="text-center small mb-0">
                    Aucun bénéfice positif n'a été enregistré pour l'année {{ $year }}.<br>
                    Le graphique sera affiché lorsque des bénéfices positifs seront disponibles.
                </p>
            </div>
        `;
    }

    // 3. Graphique des volumes d'activité
    const volumeChart = new ApexCharts(document.querySelector("#volumeChart"), {
        series: [{
            name: 'Ventes',
            data: salesCounts
        }, {
            name: 'Approvisionnements',
            data: suppliesCounts
        }],
        chart: {
            type: 'bar',
            height: 350,
            toolbar: {
                show: true
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        colors: ['#1cc88a', '#36b9cc'],
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '55%',
                endingShape: 'rounded',
                dataLabels: {
                    position: 'top'
                }
            },
        },
        dataLabels: {
            enabled: true,
            offsetY: -20,
            style: {
                fontSize: '12px',
                colors: ["#304758"]
            }
        },
        stroke: {
            show: true,
            width: 2,
            colors: ['transparent']
        },
        xaxis: {
            categories: months,
            labels: {
                style: {
                    fontSize: '12px'
                }
            }
        },
        yaxis: {
            title: {
                text: 'Nombre de transactions'
            }
        },
        fill: {
            opacity: 1
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val + " transaction(s)";
                }
            }
        },
        legend: {
            position: 'top',
            horizontalAlign: 'left'
        }
    });
    volumeChart.render();

    // 4. Graphiques Sparkline pour les indicateurs
    // Sparkline des revenus
    const revenueSparkline = new ApexCharts(document.querySelector("#revenueSparkline"), {
        series: [{
            data: revenues
        }],
        chart: {
            type: 'line',
            height: 50,
            sparkline: {
                enabled: true
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 600
            }
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        colors: ['#4e73df'],
        tooltip: {
            fixed: {
                enabled: false
            },
            x: {
                show: false
            },
            y: {
                title: {
                    formatter: function (seriesName) {
                        return '';
                    }
                },
                formatter: function (val) {
                    return new Intl.NumberFormat('fr-FR').format(val) + ' FCFA';
                }
            },
            marker: {
                show: false
            }
        }
    });
    revenueSparkline.render();

    // Sparkline des marges
    const marginSparkline = new ApexCharts(document.querySelector("#marginSparkline"), {
        series: [{
            data: profitMargins
        }],
        chart: {
            type: 'line',
            height: 50,
            sparkline: {
                enabled: true
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 600
            }
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        colors: ['#1cc88a'],
        tooltip: {
            fixed: {
                enabled: false
            },
            x: {
                show: false
            },
            y: {
                title: {
                    formatter: function (seriesName) {
                        return '';
                    }
                },
                formatter: function (val) {
                    return val.toFixed(1) + '%';
                }
            },
            marker: {
                show: false
            }
        }
    });
    marginSparkline.render();

    // Sparkline des transactions
    const transactionData = monthlyData.map(item => item.sales_count + item.supplies_count);
    const transactionSparkline = new ApexCharts(document.querySelector("#transactionSparkline"), {
        series: [{
            data: transactionData
        }],
        chart: {
            type: 'bar',
            height: 50,
            sparkline: {
                enabled: true
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 600
            }
        },
        plotOptions: {
            bar: {
                columnWidth: '80%'
            }
        },
        colors: ['#f6c23e'],
        tooltip: {
            fixed: {
                enabled: false
            },
            x: {
                show: false
            },
            y: {
                title: {
                    formatter: function (seriesName) {
                        return '';
                    }
                },
                formatter: function (val) {
                    return val + ' transaction(s)';
                }
            },
            marker: {
                show: false
            }
        }
    });
    transactionSparkline.render();

    // Animation d'entrée pour les cartes
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Effet hover sur les cartes d'indicateurs
    const indicatorCards = document.querySelectorAll('.border-left-primary, .border-left-success, .border-left-info, .border-left-warning');
    indicatorCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'transform 0.3s ease';
            this.style.boxShadow = '0 0.5rem 1rem rgba(0, 0, 0, 0.15)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15)';
        });
    });
});
</script>
@endpush
