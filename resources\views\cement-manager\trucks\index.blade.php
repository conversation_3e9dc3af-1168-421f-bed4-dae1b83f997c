@extends('layouts.cement-manager')

@section('title', 'Liste des Véhicules')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card mt-5">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title m-0">Liste des Véhicules</h3>
                    <div class="card-tools">
                        <a href="{{ route('cement-manager.trucks.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Ajouter un véhicule
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>N° d'Immatriculation</th>
                                    <th>Marque</th>
                                    <th>Modèle</th>
                                    <th>Année</th>
                                    <th>Capacité</th>
                                    <th>Statut</th>
                                    <th>Notes</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($trucks as $truck)
                                <tr>
                                    <td>{{ $truck->registration_number }}</td>
                                    <td>{{ $truck->brand }}</td>
                                    <td>{{ $truck->model }}</td>
                                    <td>{{ $truck->year }}</td>
                                    <td>{{ $truck->capacity ? number_format($truck->capacity->tonnage, 2) . ' T' : 'N/A' }}</td>
                                    <td>
                                        <span class="badge bg-{{ $truck->status === 'available' ? 'success' : ($truck->status === 'maintenance' ? 'warning' : 'danger') }}">
                                            {{ $truck->status === 'available' ? 'Disponible' : ($truck->status === 'maintenance' ? 'En maintenance' : 'Occupé') }}
                                        </span>
                                    </td>
                                    <td>{{ $truck->notes }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('cement-manager.trucks.show', $truck->id) }}" class="btn btn-sm btn-info" title="Voir les détails">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .table th, .table td {
        vertical-align: middle;
    }
    .btn-group {
        gap: 5px;
    }
</style>
@endpush
