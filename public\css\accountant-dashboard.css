/* Nouvelle feuille de style pour le tableau de bord comptable - Design ultra moderne 2025 */

:root {
  /* Palette de couleurs ultra modernes 2025 */
  --primary: #4361ee;
  --primary-rgb: 67, 97, 238;
  --primary-light: #3f8efc;
  --primary-dark: #3a0ca3;
  
  --secondary: #7209b7;
  --secondary-rgb: 114, 9, 183;
  
  --success: #06d6a0;
  --success-rgb: 6, 214, 160;
  
  --info: #00b4d8;
  --info-rgb: 0, 180, 216;
  
  --warning: #f9c74f;
  --warning-rgb: 249, 199, 79;
  
  --danger: #ef476f;
  --danger-rgb: 239, 71, 111;
  
  --dark: #111827;
  --dark-rgb: 17, 24, 39;
  
  --light: #F9FAFB;
  --light-rgb: 249, 250, 251;
  
  --white: #FFFFFF;
  --white-rgb: 255, 255, 255;
  
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;
  
  /* Variables d'interface 2025 */
  --body-bg: #f8f9fa;
  --card-border-radius: 16px;
  --btn-border-radius: 12px;
  --box-shadow-sm: 0 2px 10px rgba(0, 0, 0, 0.03);
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  --box-shadow-md: 0 15px 35px rgba(0, 0, 0, 0.1);
  --box-shadow-lg: 0 25px 50px rgba(0, 0, 0, 0.12);
  --box-shadow-hover: 0 30px 60px rgba(0, 0, 0, 0.15);
  
  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-xxl: 3rem;
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  
  /* Typographie */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-secondary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Styles de base */
body {
  font-family: var(--font-primary);
  background-color: var(--body-bg);
  color: var(--gray-700);
  line-height: 1.6;
  overflow-x: hidden;
  position: relative;
}

.dashboard-wrapper {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding-bottom: var(--space-xxl);
}

/* En-tête du dashboard */
.dashboard-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  padding: var(--space-xl) 0;
  margin-bottom: var(--space-xxl);
  color: white;
  box-shadow: var(--box-shadow-lg);
  position: relative;
  overflow: hidden;
  border-radius: 0 0 30px 30px;
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 70%);
  z-index: 1;
  animation: pulse-light 8s infinite alternate;
}

@keyframes pulse-light {
  0% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
  100% { opacity: 0.5; transform: scale(1); }
}

.header-bg-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.header-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  z-index: 0;
}

.header-circle.circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: 10%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: float-circle 15s infinite alternate ease-in-out;
}

.header-circle.circle-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: 15%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
  border: 1px solid rgba(255, 255, 255, 0.15);
  animation: float-circle 12s infinite alternate-reverse ease-in-out;
}

@keyframes float-circle {
  0% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
  100% { transform: translateY(0) rotate(0deg); }
}

.header-wave {
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 50px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23f8f9fa' fill-opacity='1' d='M0,192L48,202.7C96,213,192,235,288,229.3C384,224,480,192,576,181.3C672,171,768,181,864,197.3C960,213,1056,235,1152,229.3C1248,224,1344,192,1392,176L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
  background-size: cover;
  background-repeat: no-repeat;
  z-index: 2;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.header-title-wrapper {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.header-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s ease;
}

.header-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
  transform: rotate(45deg);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% { transform: translateX(-100%) rotate(45deg); }
  100% { transform: translateX(100%) rotate(45deg); }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15); }
  50% { transform: scale(1.05); box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2); }
  100% { transform: scale(1); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15); }
}

.title-content {
  position: relative;
}

.header-title {
  font-size: 2rem;
  font-weight: 800;
  margin: 0;
  line-height: 1.2;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  letter-spacing: -0.5px;
  background: linear-gradient(to right, #ffffff, #e0e0e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.badge-new {
  font-size: 0.7rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ff9a8b 0%, #ff6a88 55%, #ff99ac 100%);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  display: inline-block;
  position: relative;
  top: -5px;
  box-shadow: 0 3px 10px rgba(255, 106, 136, 0.4);
  text-shadow: none;
  -webkit-text-fill-color: white;
  animation: pulse-badge 2s infinite;
  transform-origin: center;
  letter-spacing: 0.5px;
}

@keyframes pulse-badge {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.header-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0.5rem 0 0 0;
  font-weight: 400;
  letter-spacing: 0.2px;
  position: relative;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.7));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.header-stats {
  display: flex;
  gap: var(--space-xl);
  margin-top: 1.5rem;
}

.header-stat {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  position: relative;
  padding: 0.75rem 1.25rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
}

.header-stat:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.header-stat::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  z-index: 0;
}

.header-stat-icon {
  width: 45px;
  height: 45px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.header-stat:hover .header-stat-icon {
  transform: scale(1.1) rotate(5deg);
  background: rgba(255, 255, 255, 0.25);
}

.header-stat-info {
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

.header-stat-value {
  font-weight: 700;
  font-size: 1.25rem;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  letter-spacing: -0.5px;
}

.header-stat:hover .header-stat-value {
  transform: scale(1.05);
}

.header-stat-label {
  font-size: 0.85rem;
  opacity: 0.9;
  font-weight: 500;
  letter-spacing: 0.2px;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: var(--space-md);
}

.period-selector-wrapper {
  position: relative;
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--btn-border-radius);
  overflow: hidden;
}

.period-selector {
  background: transparent;
  border: none;
  color: white;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  font-weight: 500;
  appearance: none;
  outline: none;
  cursor: pointer;
  font-size: 0.9rem;
}

.period-selector option {
  color: var(--gray-700);
  background: white;
}

.period-selector-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.15);
  border: none;
  color: white;
  padding: 0.75rem 1.25rem;
  border-radius: var(--btn-border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
}

.refresh-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

/* Header du tableau de bord */
.accountant-dashboard-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  padding: 2.5rem 0;
  margin-bottom: 2rem;
  color: white;
  border-radius: 0 0 2rem 2rem;
  box-shadow: var(--box-shadow-lg);
  position: relative;
  overflow: hidden;
  animation: gradientAnimation 15s ease infinite;
  background-size: 200% 200%;
}

@keyframes gradientAnimation {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.accountant-dashboard-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 30%;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' fill-opacity='0.1' d='M0,256L48,240C96,224,192,192,288,192C384,192,480,224,576,229.3C672,235,768,213,864,213.3C960,213,1056,235,1152,234.7C1248,235,1344,213,1392,202.7L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center bottom;
  background-size: cover;
  z-index: 1;
}

.accountant-dashboard-title {
  font-family: var(--font-secondary);
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;
}

.accountant-dashboard-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 1.5rem;
  max-width: 600px;
  position: relative;
  z-index: 2;
}

.header-controls {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  position: relative;
  z-index: 2;
  justify-content: flex-end;
}

.header-stat-icon {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  margin-right: 0.75rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.header-stat-info {
  display: flex;
  flex-direction: column;
}

.header-stat-value {
  font-weight: 700;
  font-size: 1.1rem;
  line-height: 1.2;
}

.header-stat-label {
  font-size: 0.8rem;
  opacity: 0.8;
}

.period-selector-container {
  position: relative;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  padding: 0 0.5rem;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.period-selector {
  background: transparent;
  border: none;
  color: white;
  padding: 0.75rem 2rem 0.75rem 1rem;
  font-weight: 500;
  appearance: none;
  outline: none;
  cursor: pointer;
  z-index: 2;
}

.period-selector option {
  color: var(--gray-700);
  background: white;
}

.period-selector-icon {
  position: absolute;
  right: 1rem;
  pointer-events: none;
  z-index: 1;
}

/* Section des statistiques */
.stats-section {
  margin-top: -60px;
  margin-bottom: var(--space-xxl);
  position: relative;
  z-index: 10;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-lg);
}

@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

.stat-card {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  padding: 1.75rem;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.6);
  z-index: 1;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: var(--primary);
  z-index: 2;
}

.stat-card.primary::before {
  background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
}

.stat-card.success::before {
  background: linear-gradient(90deg, var(--success) 0%, #34d399 100%);
}

.stat-card.danger::before {
  background: linear-gradient(90deg, var(--danger) 0%, #f87171 100%);
}

.stat-card.warning::before {
  background: linear-gradient(90deg, var(--warning) 0%, #fbbf24 100%);
}

.stat-card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

/* Effet de lueur autour de la carte */
.stat-card-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 0%, rgba(var(--primary-rgb), 0.15), transparent 70%);
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 0;
  pointer-events: none;
}

.stat-card:hover .stat-card-glow {
  opacity: 1;
}

.stat-card-glow.success {
  background: radial-gradient(circle at 50% 0%, rgba(var(--success-rgb), 0.15), transparent 70%);
}

.stat-card-glow.danger {
  background: radial-gradient(circle at 50% 0%, rgba(var(--danger-rgb), 0.15), transparent 70%);
}

.stat-card-glow.warning {
  background: radial-gradient(circle at 50% 0%, rgba(var(--warning-rgb), 0.15), transparent 70%);
}

/* Éléments décoratifs */
.stat-card-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(var(--primary-rgb), 0.03);
  border: 1px solid rgba(var(--primary-rgb), 0.05);
}

.decoration-circle.circle-1 {
  width: 120px;
  height: 120px;
  top: -60px;
  right: -30px;
  opacity: 0.5;
  transition: all 0.5s ease;
}

.decoration-circle.circle-2 {
  width: 80px;
  height: 80px;
  bottom: -30px;
  left: 20%;
  opacity: 0.3;
  transition: all 0.7s ease;
}

.stat-card:hover .decoration-circle.circle-1 {
  transform: scale(1.2) translateY(10px);
  opacity: 0.7;
}

.stat-card:hover .decoration-circle.circle-2 {
  transform: scale(1.3) translateX(10px);
  opacity: 0.5;
}

.decoration-circle.success {
  background: rgba(var(--success-rgb), 0.03);
  border: 1px solid rgba(var(--success-rgb), 0.05);
}

.decoration-circle.danger {
  background: rgba(var(--danger-rgb), 0.03);
  border: 1px solid rgba(var(--danger-rgb), 0.05);
}

.decoration-circle.warning {
  background: rgba(var(--warning-rgb), 0.03);
  border: 1px solid rgba(var(--warning-rgb), 0.05);
}

.stat-card-top {
  display: flex;
  gap: var(--space-md);
  align-items: center;
}

.stat-card-icon {
  width: 60px;
  height: 60px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.6rem;
  color: white;
  background: var(--primary);
  box-shadow: 0 8px 20px rgba(var(--primary-rgb), 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 2;
}

.stat-card-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
  transform: rotate(45deg);
  animation: shine 3s infinite;
}

.stat-card:hover .stat-card-icon {
  transform: scale(1.1) rotate(5deg);
}

.stat-card.primary .stat-card-icon {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
  box-shadow: 0 8px 20px rgba(var(--primary-rgb), 0.3);
}

.stat-card.success .stat-card-icon {
  background: linear-gradient(135deg, var(--success) 0%, #34d399 100%);
  box-shadow: 0 8px 20px rgba(var(--success-rgb), 0.3);
}

.stat-card.danger .stat-card-icon {
  background: linear-gradient(135deg, var(--danger) 0%, #f87171 100%);
  box-shadow: 0 8px 20px rgba(var(--danger-rgb), 0.3);
}

.stat-card.warning .stat-card-icon {
  background: linear-gradient(135deg, var(--warning) 0%, #fbbf24 100%);
  box-shadow: 0 8px 20px rgba(var(--warning-rgb), 0.3);
}

/* Icône d'arrière-plan */
.stat-card-bg-icon {
  position: absolute;
  bottom: -1rem;
  right: -1rem;
  font-size: 7rem;
  color: rgba(var(--primary-rgb), 0.05);
  transition: all 0.5s ease;
  transform: rotate(-5deg);
  opacity: 0.5;
  z-index: 0;
  pointer-events: none;
}

.stat-card:hover .stat-card-bg-icon {
  transform: scale(1.2) rotate(0deg);
  opacity: 0.7;
}

.stat-card-bg-icon.success {
  color: rgba(var(--success-rgb), 0.05);
}

.stat-card-bg-icon.danger {
  color: rgba(var(--danger-rgb), 0.05);
}

.stat-card-bg-icon.warning {
  color: rgba(var(--warning-rgb), 0.05);
}

.stat-card-info {
  flex: 1;
  position: relative;
  z-index: 2;
}

.stat-card-label {
  font-size: 0.8rem;
  font-weight: 700;
  color: var(--gray-600);
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  display: inline-block;
  transition: all 0.3s ease;
}

.stat-card:hover .stat-card-label {
  transform: translateX(5px);
  color: var(--gray-700);
}

.stat-card-value {
  font-size: 2.5rem;
  font-weight: 800;
  line-height: 1;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-600) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  position: relative;
  display: inline-block;
  transition: all 0.5s ease;
  letter-spacing: -1px;
}

.stat-card:hover .stat-card-value {
  transform: scale(1.05);
  letter-spacing: -0.5px;
}

.stat-card.primary .stat-card-value {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  text-shadow: 0 2px 10px rgba(var(--primary-rgb), 0.1);
}

.stat-card.success .stat-card-value {
  background: linear-gradient(135deg, #065f46 0%, var(--success) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  text-shadow: 0 2px 10px rgba(var(--success-rgb), 0.1);
}

.stat-card.danger .stat-card-value {
  background: linear-gradient(135deg, #991b1b 0%, var(--danger) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  text-shadow: 0 2px 10px rgba(var(--danger-rgb), 0.1);
}

.stat-card.warning .stat-card-value {
  background: linear-gradient(135deg, #92400e 0%, var(--warning) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  text-shadow: 0 2px 10px rgba(var(--warning-rgb), 0.1);
}

.stat-card-bottom {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.stat-card-trend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  font-weight: 600;
  padding: 0.6rem 1rem;
  border-radius: 12px;
  width: fit-content;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.stat-card:hover .stat-card-trend {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
}

.stat-card-trend i {
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.stat-card:hover .stat-card-trend i {
  transform: scale(1.2);
}

.stat-card-trend.primary {
  color: var(--primary);
  background: rgba(var(--primary-rgb), 0.1);
  border-color: rgba(var(--primary-rgb), 0.2);
}

.stat-card-trend.success {
  color: var(--success);
  background: rgba(var(--success-rgb), 0.1);
  border-color: rgba(var(--success-rgb), 0.2);
}

.stat-card-trend.danger {
  color: var(--danger);
  background: rgba(var(--danger-rgb), 0.1);
  border-color: rgba(var(--danger-rgb), 0.2);
}

.stat-card-trend.warning {
  color: var(--warning);
  background: rgba(var(--warning-rgb), 0.1);
  border-color: rgba(var(--warning-rgb), 0.2);
}

.stat-card-progress {
  height: 8px;
  background: rgba(var(--gray-200-rgb), 0.3);
  border-radius: 4px;
  overflow: hidden;
  margin-top: 1rem;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 2;
}

.stat-card-progress .progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-light) 0%, var(--primary) 100%);
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  transition: width 1.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  box-shadow: 0 1px 5px rgba(var(--primary-rgb), 0.3);
}

.progress-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
  animation: shimmer 2s infinite;
  z-index: 1;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.stat-card-progress .progress-bar.success {
  background: linear-gradient(90deg, #34d399 0%, var(--success) 100%);
  box-shadow: 0 1px 5px rgba(var(--success-rgb), 0.3);
}

.stat-card-progress .progress-bar.danger {
  background: linear-gradient(90deg, #f87171 0%, var(--danger) 100%);
  box-shadow: 0 1px 5px rgba(var(--danger-rgb), 0.3);
}

.progress-glow.danger {
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
}

.stat-card-progress .progress-bar.warning {
  background: linear-gradient(90deg, #fbbf24 0%, var(--warning) 100%);
  box-shadow: 0 1px 5px rgba(var(--warning-rgb), 0.3);
}

.progress-glow.warning {
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.stats-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.85);
}

.stats-card-icon {
  position: absolute;
  bottom: -1rem;
  right: -1rem;
  font-size: 5rem;
  color: rgba(var(--primary-rgb), 0.07);
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  filter: blur(1px);
  z-index: 0;
  transform: rotate(-5deg);
}

.stats-card.success .stats-card-icon {
  color: rgba(var(--success-rgb), 0.07);
}

.stats-card.danger .stats-card-icon {
  color: rgba(var(--danger-rgb), 0.07);
}

.stats-card.warning .stats-card-icon {
  color: rgba(var(--warning-rgb), 0.07);
}

.stats-card:hover .stats-card-icon {
  transform: scale(1.2) rotate(0deg);
  color: rgba(var(--primary-rgb), 0.12);
  filter: blur(0);
}

.stats-card.success:hover .stats-card-icon {
  color: rgba(var(--success-rgb), 0.12);
}

.stats-card.danger:hover .stats-card-icon {
  color: rgba(var(--danger-rgb), 0.12);
}

.stats-card.warning:hover .stats-card-icon {
  color: rgba(var(--warning-rgb), 0.12);
}

.stats-card-content {
  position: relative;
  z-index: 2;
  flex: 1;
}

.stats-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.stats-card-title {
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: var(--gray-600);
  margin-bottom: 0;
}

.stats-card-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: rgba(var(--primary-rgb), 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: var(--primary);
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(var(--primary-rgb), 0.2);
  transform: rotate(-5deg);
}

.stats-card-icon-wrapper.success {
  background: rgba(var(--success-rgb), 0.15);
  color: var(--success);
  box-shadow: 0 4px 10px rgba(var(--success-rgb), 0.2);
}

.stats-card-icon-wrapper.danger {
  background: rgba(var(--danger-rgb), 0.15);
  color: var(--danger);
  box-shadow: 0 4px 10px rgba(var(--danger-rgb), 0.2);
}

.stats-card-icon-wrapper.warning {
  background: rgba(var(--warning-rgb), 0.15);
  color: var(--warning);
  box-shadow: 0 4px 10px rgba(var(--warning-rgb), 0.2);
}

.stats-card-value {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  font-family: var(--font-secondary);
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  letter-spacing: -0.5px;
  line-height: 1.1;
  text-shadow: 0 2px 10px rgba(var(--primary-rgb), 0.2);
}

.stats-card.success .stats-card-value {
  background: linear-gradient(135deg, #047857 0%, #10b981 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  text-shadow: 0 2px 10px rgba(var(--success-rgb), 0.2);
}

.stats-card.danger .stats-card-value {
  background: linear-gradient(135deg, #b91c1c 0%, #ef4444 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  text-shadow: 0 2px 10px rgba(var(--danger-rgb), 0.2);
}

.stats-card.warning .stats-card-value {
  background: linear-gradient(135deg, #b45309 0%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  text-shadow: 0 2px 10px rgba(var(--warning-rgb), 0.2);
}

.stats-card-trend {
  margin-top: auto;
  background: rgba(var(--primary-rgb), 0.08);
  border-radius: 2rem;
  padding: 0.6rem 1.2rem;
  font-size: 0.9rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  align-self: flex-start;
  backdrop-filter: blur(4px);
  border: none;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.05);
  color: var(--primary-dark);
  transition: all 0.3s ease;
}

.stats-card-trend.success {
  background: rgba(var(--success-rgb), 0.08);
  color: var(--success);
}

.stats-card-trend.danger {
  background: rgba(var(--danger-rgb), 0.08);
  color: var(--danger);
}

.stats-card-trend.warning {
  background: rgba(var(--warning-rgb), 0.08);
  color: var(--warning);
}

.stats-card:hover .stats-card-trend {
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.08);
}

.stats-card-progress {
  margin-top: 1.25rem;
  height: 6px;
  background: rgba(var(--gray-200-rgb), 0.3);
  border-radius: 3px;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.stats-card-progress .progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-light) 0%, var(--primary) 100%);
  border-radius: 3px;
  transition: width 1.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  box-shadow: 0 1px 5px rgba(var(--primary-rgb), 0.3);
  position: relative;
  overflow: hidden;
}

.stats-card-progress .progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.stats-card-progress .progress-bar.success {
  background: linear-gradient(90deg, #34d399 0%, var(--success) 100%);
  box-shadow: 0 1px 5px rgba(var(--success-rgb), 0.3);
}

.stats-card-progress .progress-bar.danger {
  background: linear-gradient(90deg, #f87171 0%, var(--danger) 100%);
  box-shadow: 0 1px 5px rgba(var(--danger-rgb), 0.3);
}

.stats-card-progress .progress-bar.warning {
  background: linear-gradient(90deg, #fbbf24 0%, var(--warning) 100%);
  box-shadow: 0 1px 5px rgba(var(--warning-rgb), 0.3);
}

.stats-card-trend i {
  font-size: 0.8rem;
}

/* Variantes de couleurs pour les cartes de statistiques */
.stats-card.primary {
  border-top: 4px solid var(--primary);
}

.stats-card.primary .stats-card-value {
  color: var(--primary);
}

.stats-card.primary .stats-card-trend {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
}

.stats-card.success {
  border-top: 4px solid var(--success);
}

.stats-card.success .stats-card-value {
  color: var(--success);
}

.stats-card.success .stats-card-trend {
  background-color: rgba(var(--success-rgb), 0.1);
  color: var(--success);
}

.stats-card.danger {
  border-top: 4px solid var(--danger);
}

.stats-card.danger .stats-card-value {
  color: var(--danger);
}

.stats-card.danger .stats-card-trend {
  background-color: rgba(var(--danger-rgb), 0.1);
  color: var(--danger);
}

.stats-card.warning {
  border-top: 4px solid var(--warning);
}

.stats-card.warning .stats-card-value {
  color: var(--warning);
}

.stats-card.warning .stats-card-trend {
  background-color: rgba(var(--warning-rgb), 0.1);
  color: var(--warning);
}

/* Style pour les cartes (graphiques et tables) */
.dashboard-card {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border-radius: var(--card-border-radius);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  height: 100%;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: none;
  overflow: hidden;
  position: relative;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
  z-index: 2;
}

.dashboard-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.85);
}

.dashboard-card-header {
  padding: 1.5rem 1.75rem;
  border-bottom: 1px solid rgba(var(--gray-200-rgb), 0.5);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.5);
}

.dashboard-card-title {
  font-weight: 700;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--gray-800);
  margin: 0;
  position: relative;
}

.dashboard-card-title i {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  font-size: 1.1rem;
}

.card-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-card-action {
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
  border: none;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 3px 6px rgba(var(--primary-rgb), 0.1);
}

.btn-card-action:hover {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: white;
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 5px 15px rgba(var(--primary-rgb), 0.3);
}

.dashboard-card-body {
  padding: 1.75rem;
  position: relative;
}

.dashboard-card-body::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%234F46E5' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
  z-index: 0;
}

.dashboard-card-body > * {
  position: relative;
  z-index: 1;
}

/* Styles pour la section de contenu du dashboard */
.dashboard-content {
  margin-top: 2rem;
  margin-bottom: 3rem;
}

.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
}

@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
}

/* Styles pour la carte de graphique */
.stat-card {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-radius: 24px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08), 0 5px 15px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  border: none;
  transform-style: preserve-3d;
  perspective: 1000px;
  padding: 0;
}

.stat-card:hover {
  transform: translateY(-15px) rotateX(5deg);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.12), 0 10px 20px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.9);
}

.chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
  z-index: 2;
}

.chart-card .card-header {
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(var(--gray-200-rgb), 0.5);
}

.chart-card .card-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.chart-card .card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  box-shadow: 0 5px 15px rgba(var(--primary-rgb), 0.3);
}

.chart-card .card-title h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-800);
}

.chart-card .card-actions {
  display: flex;
  gap: 0.75rem;
}

.chart-card .action-button {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(var(--primary-rgb), 0.1);
  border: none;
  color: var(--primary);
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-card .action-button:hover {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(var(--primary-rgb), 0.3);
}

.chart-card .card-body {
  padding: 1.5rem;
}

.chart-container {
  width: 100%;
  height: 350px;
  position: relative;
}

.chart-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 0 1.5rem 1rem;
  padding-top: 0.5rem;
}

.chart-toggle-btn {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(var(--primary-rgb), 0.1);
  border-radius: 50px;
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--gray-600);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.chart-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  color: var(--gray-800);
}

.chart-toggle-btn.active {
  background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.15) 0%, rgba(var(--primary-rgb), 0.05) 100%);
  border: 1px solid rgba(var(--primary-rgb), 0.3);
  color: var(--primary);
  box-shadow: 0 4px 10px rgba(var(--primary-rgb), 0.1);
}

/* Styles pour la carte d'activités */
.activity-card {
  background: rgba(255, 255, 255, 0.85);
  background-image: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.1) 100%);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-radius: 24px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08), 0 5px 15px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.7);
  height: 100%;
  display: flex;
  flex-direction: column;
  transform-style: preserve-3d;
  perspective: 1000px;
}

.activity-card:hover {
  transform: translateY(-15px) rotateX(5deg);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.12), 0 10px 20px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.9);
}

.activity-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--secondary) 0%, var(--primary) 100%);
  z-index: 2;
}

.activity-card .card-header {
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(var(--gray-200-rgb), 0.5);
}

.activity-card .card-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.activity-card .card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--secondary) 0%, var(--primary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  box-shadow: 0 5px 15px rgba(var(--secondary-rgb), 0.3);
}

.activity-card .card-title h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-800);
}

.activity-card .card-body {
  padding: 1.5rem;
  flex: 1;
  overflow: hidden;
}

.activity-list {
  height: 350px;
  overflow-y: auto;
  padding-right: 0.5rem;
  position: relative;
}

.activity-list::-webkit-scrollbar {
  width: 6px;
}

.activity-list::-webkit-scrollbar-track {
  background: rgba(var(--gray-200-rgb), 0.3);
  border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb {
  background: rgba(var(--primary-rgb), 0.3);
  border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--primary-rgb), 0.5);
}

/* Style pour les activités récentes */
.activity-timeline {
  position: relative;
  padding-left: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.activity-timeline::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 2px;
  background: linear-gradient(to bottom, var(--primary), var(--secondary));
  opacity: 0.3;
}

.activity-item {
  position: relative;
  padding: 1.25rem 1rem;
  margin-bottom: 0.75rem;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  background: rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.activity-item:hover {
  transform: translateX(8px) translateY(-5px);
  background: rgba(255, 255, 255, 0.7);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.activity-item::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 1.25rem;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffffff 0%, #f3f4f6 100%);
  border: 2px solid var(--primary);
  z-index: 1;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.15), 0 4px 8px rgba(0, 0, 0, 0.1);
}

.activity-item:hover::before {
  transform: scale(1.3) translateY(-2px);
  box-shadow: 0 0 0 6px rgba(var(--primary-rgb), 0.2), 0 8px 16px rgba(0, 0, 0, 0.15);
  border-width: 3px;
}

.activity-item.success::before {
  border-color: var(--success);
  box-shadow: 0 0 0 4px rgba(var(--success-rgb), 0.1);
}

.activity-item.success:hover::before {
  box-shadow: 0 0 0 6px rgba(var(--success-rgb), 0.15);
}

.activity-item.warning::before {
  border-color: var(--warning);
  box-shadow: 0 0 0 4px rgba(var(--warning-rgb), 0.1);
}

.activity-item.warning:hover::before {
  box-shadow: 0 0 0 6px rgba(var(--warning-rgb), 0.15);
}

.activity-item.danger::before {
  border-color: var(--danger);
  box-shadow: 0 0 0 4px rgba(var(--danger-rgb), 0.1);
}

.activity-item.danger:hover::before {
  box-shadow: 0 0 0 6px rgba(var(--danger-rgb), 0.15);
}

.activity-title {
  font-weight: 700;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  color: var(--gray-800);
  padding-left: 2.5rem;
}

.activity-title a {
  color: var(--primary);
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1rem;
  position: relative;
}

.activity-title a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.badge-status {
  font-size: 0.7rem;
  font-weight: 700;
  padding: 0.35rem 0.8rem;
  border-radius: 50px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.badge-status.success {
  background: rgba(var(--success-rgb), 0.1);
  color: var(--success);
}

.badge-status.warning {
  background: rgba(var(--warning-rgb), 0.1);
  color: var(--warning);
}

.badge-status.danger {
  background: rgba(var(--danger-rgb), 0.1);
  color: var(--danger);
}

.activity-content {
  color: var(--gray-700);
  font-size: 0.9rem;
  padding-left: 2.5rem;
}

.activity-icon {
  position: absolute;
  left: 1rem;
  top: 1.25rem;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.15) 0%, rgba(var(--primary-rgb), 0.05) 100%);
  color: var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(var(--primary-rgb), 0.1);
  z-index: 2;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.activity-item:hover .activity-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
}

.activity-item.success .activity-icon {
  background: linear-gradient(135deg, rgba(var(--success-rgb), 0.15) 0%, rgba(var(--success-rgb), 0.05) 100%);
  color: var(--success);
  border: 1px solid rgba(var(--success-rgb), 0.1);
}

.activity-item.warning .activity-icon {
  background: linear-gradient(135deg, rgba(var(--warning-rgb), 0.15) 0%, rgba(var(--warning-rgb), 0.05) 100%);
  color: var(--warning);
  border: 1px solid rgba(var(--warning-rgb), 0.1);
}

.activity-item.danger .activity-icon {
  background: linear-gradient(135deg, rgba(var(--danger-rgb), 0.15) 0%, rgba(var(--danger-rgb), 0.05) 100%);
  color: var(--danger);
  border: 1px solid rgba(var(--danger-rgb), 0.1);
}

.activity-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 0.5rem;
}

.activity-detail {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--gray-600);
  background: rgba(255, 255, 255, 0.5);
  padding: 0.4rem 0.8rem;
  border-radius: 50px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.activity-detail:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.activity-detail i {
  color: var(--primary);
  font-size: 0.8rem;
}

/* Style pour l'indicateur de chargement */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--gray-500);
}

.spinner {
  font-size: 2rem;
  color: var(--primary);
  margin-bottom: 1rem;
}

.loading-indicator p {
  font-size: 0.9rem;
  margin: 0;
}

.activity-time {
  color: var(--gray-400);
  font-size: 0.8rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Badge pour les status */
.status-badge {
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 2rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.success {
  background-color: rgba(var(--success-rgb), 0.1);
  color: var(--success);
}

.status-badge.warning {
  background-color: rgba(var(--warning-rgb), 0.1);
  color: var(--warning);
}

.status-badge.danger {
  background-color: rgba(var(--danger-rgb), 0.1);
  color: var(--danger);
}

.status-badge.primary {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
}

/* Style pour les actions rapides */
.quick-action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 1.25rem;
  padding: 0.5rem;
}

.quick-action {
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border-radius: var(--btn-border-radius);
  padding: 1.5rem 1rem;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  text-decoration: none;
  color: var(--gray-700);
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: var(--box-shadow-sm);
  border: var(--glass-border);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.quick-action::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.1) 0%, rgba(var(--secondary-rgb), 0.1) 100%);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.4s ease;
}

.quick-action:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: var(--box-shadow-md);
  color: var(--primary-dark);
  border: 1px solid rgba(255, 255, 255, 0.6);
}

.quick-action:hover::before {
  opacity: 1;
}

.quick-action-icon {
  font-size: 1.75rem;
  margin-bottom: 1.25rem;
  color: var(--primary);
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  background: rgba(var(--primary-rgb), 0.1);
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 4px 8px rgba(var(--primary-rgb), 0.15);
}

.quick-action:hover .quick-action-icon {
  transform: scale(1.15) rotate(10deg);
  background: rgba(var(--primary-rgb), 0.2);
  box-shadow: 0 8px 16px rgba(var(--primary-rgb), 0.25);
}

.quick-action-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.quick-action-description {
  font-size: 0.85rem;
  color: var(--gray-500);
}

/* Boutons et contrôles */
.dashboard-btn {
  padding: 0.625rem 1.25rem;
  border-radius: var(--btn-border-radius);
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.dashboard-btn-primary {
  background: var(--primary);
  color: white;
}

.dashboard-btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(var(--primary-rgb), 0.3);
}

.dashboard-btn-outline {
  background: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
}

.dashboard-btn-outline:hover {
  background: var(--primary);
  color: white;
  transform: translateY(-2px);
}

.period-selector {
  padding: 0.625rem 2.5rem 0.625rem 1.25rem;
  border-radius: var(--btn-border-radius);
  background: white;
  border: 1px solid var(--gray-300);
  color: var(--gray-700);
  font-size: 0.9rem;
  font-weight: 500;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%236B7280' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.period-selector:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* Adaptations responsives */
@media (max-width: 768px) {
  .accountant-dashboard-header {
    padding: 2rem 0;
  }
  
  .accountant-dashboard-title {
    font-size: 1.75rem;
  }
  
  .stats-card-value {
    font-size: 1.75rem;
  }
  
  .dashboard-card-header {
    padding: 1rem;
  }
  
  .dashboard-card-body {
    padding: 1rem;
  }
  
  .quick-action-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.fadeInUp {
  animation: fadeInUp 0.5s ease forwards;
}

.delay-1 {
  animation-delay: 0.1s;
}

.delay-2 {
  animation-delay: 0.2s;
}

.delay-3 {
  animation-delay: 0.3s;
}

.delay-4 {
  animation-delay: 0.4s;
}
