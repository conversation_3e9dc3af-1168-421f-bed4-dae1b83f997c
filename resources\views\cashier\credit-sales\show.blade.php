@extends('layouts.cashier')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Détails de la Vente à Crédit #{{ $sale->id }}</h1>
        <div>
            <a href="{{ route('cashier.credit-sales.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Retour
            </a>
            <button onclick="window.print()" class="btn btn-primary ms-2">
                <i class="fas fa-print"></i> Imprimer
            </button>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Détails des produits</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Quantité</th>
                                    <th>Prix unitaire</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if($sale->detail && $sale->detail->product)
                                <tr>
                                    <td>{{ $sale->detail->product->name }}</td>
                                    <td>{{ $sale->detail->quantity }} {{ $sale->detail->product->unit }}</td>
                                    <td>{{ number_format($sale->detail->unit_price, 0, ',', ' ') }} FCFA</td>
                                    <td>{{ number_format($sale->amount, 0, ',', ' ') }} FCFA</td>
                                </tr>
                                @else
                                <tr>
                                    <td colspan="4" class="text-center">Aucun détail disponible</td>
                                </tr>
                                @endif
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="3" class="text-end"><strong>Total</strong></td>
                                    <td><strong>{{ number_format($sale->amount, 0, ',', ' ') }} FCFA</strong></td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="text-end"><strong>Montant payé</strong></td>
                                    <td><strong>{{ number_format($sale->paid_amount, 0, ',', ' ') }} FCFA</strong></td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="text-end"><strong>Reste à payer</strong></td>
                                    <td><strong>{{ number_format($sale->remaining_amount, 0, ',', ' ') }} FCFA</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Historique des paiements</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Montant</th>
                                    <th>Méthode</th>
                                    <th>Référence</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($sale->payments as $payment)
                                    <tr>
                                        <td>{{ $payment->payment_date->format('d/m/Y H:i') }}</td>
                                        <td>{{ number_format($payment->amount, 0, ',', ' ') }} FCFA</td>
                                        <td>
                                            @switch($payment->payment_method)
                                                @case('cash')
                                                    Espèces
                                                    @break
                                                @case('bank_transfer')
                                                    Virement bancaire
                                                    @break
                                                @case('check')
                                                    Chèque
                                                    @break
                                            @endswitch
                                        </td>
                                        <td>{{ $payment->reference_number ?? '-' }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center">Aucun paiement enregistré</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informations Client</h6>
                </div>
                <div class="card-body">
                    @if($sale->detail && $sale->detail->customer)
                        <p><strong>Nom:</strong> {{ $sale->detail->customer->name }}</p>
                        <p><strong>Ville:</strong> {{ $sale->detail->city ? $sale->detail->city->name : 'Non définie' }}</p>
                        <p><strong>Date de la vente:</strong> {{ $sale->created_at->format('d/m/Y H:i') }}</p>
                        <p><strong>Statut:</strong>
                            @if($sale->status === 'paid')
                                <span class="badge bg-success">Payé</span>
                            @elseif($sale->status === 'partially_paid')
                                <span class="badge bg-info">Partiellement payé</span>
                            @else
                                <span class="badge bg-warning">En attente</span>
                            @endif
                        </p>
                        @if($sale->due_date)
                            <p><strong>Date d'échéance:</strong> {{ $sale->due_date->format('d/m/Y') }}</p>
                        @endif
                    @else
                        <p class="text-center">Aucune information client disponible</p>
                    @endif
                </div>
            </div>

            @if($sale->status !== 'paid')
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Enregistrer un paiement</h6>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('cashier.credit-sales.record-payment', $sale) }}" method="POST">
                            @csrf
                            <div class="mb-3">
                                <label for="amount" class="form-label">Montant</label>
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control @error('amount') is-invalid @enderror" 
                                           id="amount" 
                                           name="amount" 
                                           step="0.01" 
                                           min="0" 
                                           max="{{ $sale->remaining_amount }}"
                                           value="{{ old('amount') }}"
                                           required>
                                    <span class="input-group-text">FCFA</span>
                                </div>
                                @error('amount')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="payment_method" class="form-label">Méthode de paiement</label>
                                <select class="form-select @error('payment_method') is-invalid @enderror" 
                                        id="payment_method" 
                                        name="payment_method" 
                                        required>
                                    <option value="">Sélectionner une méthode</option>
                                    <option value="cash" {{ old('payment_method') === 'cash' ? 'selected' : '' }}>Espèces</option>
                                    <option value="bank_transfer" {{ old('payment_method') === 'bank_transfer' ? 'selected' : '' }}>Virement bancaire</option>
                                    <option value="check" {{ old('payment_method') === 'check' ? 'selected' : '' }}>Chèque</option>
                                </select>
                                @error('payment_method')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3">{{ old('notes') }}</textarea>
                                @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Enregistrer le paiement
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.querySelector('select[name="payment_method"]').addEventListener('change', function() {
        const referenceField = document.getElementById('referenceNumberField');
        if (this.value === 'cash') {
            referenceField.style.display = 'none';
            referenceField.querySelector('input').removeAttribute('required');
        } else {
            referenceField.style.display = 'block';
            referenceField.querySelector('input').setAttribute('required', 'required');
        }
    });
</script>
@endpush

@push('styles')
<style>
    @media print {
        .btn, .navbar, .sidebar {
            display: none !important;
        }
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        .container-fluid {
            padding: 0 !important;
        }
    }
</style>
@endpush
@endsection
