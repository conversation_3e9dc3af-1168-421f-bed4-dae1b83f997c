<?php
// Script pour vérifier l'accessibilité du fichier CSS
$cssFile = 'css/modern-accountant-dashboard.css';
$fullPath = __DIR__ . '/' . $cssFile;

echo "<h1>Vérification du fichier CSS</h1>";

// Vérifier si le fichier existe physiquement
if (file_exists($fullPath)) {
    echo "<p style='color:green'>✓ Le fichier existe physiquement à l'emplacement: {$fullPath}</p>";
    
    // Afficher la taille du fichier
    $fileSize = filesize($fullPath);
    echo "<p>Taille du fichier: {$fileSize} octets</p>";
    
    // Vérifier les permissions
    $perms = substr(sprintf('%o', fileperms($fullPath)), -4);
    echo "<p>Permissions du fichier: {$perms}</p>";
    
    // Afficher les premiers caractères du fichier
    $content = file_get_contents($fullPath);
    $preview = substr($content, 0, 200) . '...';
    echo "<p>Aperçu du contenu:</p>";
    echo "<pre>{$preview}</pre>";
    
    // Vérifier l'URL
    $cssUrl = 'http://' . $_SERVER['HTTP_HOST'] . '/' . $cssFile;
    echo "<p>URL du fichier CSS: <a href='{$cssUrl}' target='_blank'>{$cssUrl}</a></p>";
    
} else {
    echo "<p style='color:red'>✗ Le fichier n'existe PAS à l'emplacement: {$fullPath}</p>";
    
    // Vérifier si le dossier css existe
    $cssDir = __DIR__ . '/css';
    if (is_dir($cssDir)) {
        echo "<p>Le dossier CSS existe.</p>";
        
        // Lister les fichiers dans le dossier css
        echo "<p>Fichiers dans le dossier CSS:</p>";
        echo "<ul>";
        foreach (scandir($cssDir) as $file) {
            if ($file != '.' && $file != '..') {
                echo "<li>{$file}</li>";
            }
        }
        echo "</ul>";
    } else {
        echo "<p style='color:red'>Le dossier CSS n'existe pas!</p>";
    }
}
?>

<h2>Test d'inclusion du CSS</h2>
<style>
.test-box {
    width: 200px;
    height: 200px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    margin: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}
</style>

<link rel="stylesheet" href="/css/modern-accountant-dashboard.css">

<div class="test-box">
    Si cette boîte a un dégradé bleu, des coins arrondis et une ombre, le CSS fonctionne.
</div>
