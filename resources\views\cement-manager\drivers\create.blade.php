@extends('layouts.cement-manager')

@section('title', 'Ajouter un Chauffeur')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card mt-5">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title m-0">Ajouter un Chauffeur</h3>
                    <div class="card-tools">
                        <a href="{{ route('cement-manager.drivers.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour à la liste
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form id="driverForm" class="needs-validation" novalidate>
                        @csrf
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="first_name" class="form-label">Prénom</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                                <div class="invalid-feedback" data-field="first_name"></div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="last_name" class="form-label">Nom</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                                <div class="invalid-feedback" data-field="last_name"></div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="phone_number" class="form-label">Téléphone</label>
                                <input type="tel" class="form-control" id="phone_number" name="phone_number" 
                                       pattern="[0-9+\s-]+" required>
                                <div class="invalid-feedback" data-field="phone_number"></div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="emergency_phone" class="form-label">Contact d'urgence</label>
                                <input type="tel" class="form-control" id="emergency_phone" name="emergency_phone" 
                                       pattern="[0-9+\s-]+" required>
                                <div class="invalid-feedback" data-field="emergency_phone"></div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="license_number" class="form-label">Numéro de Permis</label>
                                <input type="text" class="form-control" id="license_number" name="license_number" required>
                                <div class="invalid-feedback" data-field="license_number"></div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="license_expiry" class="form-label">Date d'expiration du Permis</label>
                                <input type="date" class="form-control" id="license_expiry" name="license_expiry" required>
                                <div class="invalid-feedback" data-field="license_expiry"></div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="is_available" class="form-label">Disponibilité</label>
                                <select class="form-select" id="is_available" name="is_available" required>
                                    <option value="1">Disponible</option>
                                    <option value="0">Non disponible</option>
                                </select>
                                <div class="invalid-feedback" data-field="is_available"></div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="address" class="form-label">Adresse</label>
                                <textarea class="form-control" id="address" name="address" rows="1" required></textarea>
                                <div class="invalid-feedback" data-field="address"></div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                <div class="invalid-feedback" data-field="notes"></div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast de notification -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="alertToast" class="toast align-items-center text-white bg-success" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .form-label {
        font-weight: 500;
    }
    textarea {
        resize: none;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('driverForm');
    const toast = new bootstrap.Toast(document.getElementById('alertToast'));

    // Set min date for license expiry
    const licenseExpiryInput = document.getElementById('license_expiry');
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    licenseExpiryInput.min = tomorrow.toISOString().split('T')[0];

    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Reset previous error states
        form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        form.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');

        const formData = new FormData(form);
        const data = {};
        formData.forEach((value, key) => {
            data[key] = value;
        });

        try {
            const response = await fetch('{{ route("cement-manager.drivers.store") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (!response.ok) {
                if (response.status === 422) {
                    // Validation errors
                    Object.keys(result.errors).forEach(field => {
                        const input = form.querySelector(`[name="${field}"]`);
                        const feedback = form.querySelector(`[data-field="${field}"]`);
                        if (input && feedback) {
                            input.classList.add('is-invalid');
                            feedback.textContent = result.errors[field][0];
                        }
                    });
                } else {
                    throw new Error(result.message || 'Une erreur est survenue');
                }
                return;
            }

            // Success
            const alertToast = document.getElementById('alertToast');
            alertToast.classList.remove('bg-danger');
            alertToast.classList.add('bg-success');
            alertToast.querySelector('.toast-body').textContent = result.message;
            toast.show();

            // Reset form
            form.reset();

            // Redirect after 2 seconds
            setTimeout(() => {
                window.location.href = '{{ route("cement-manager.drivers.index") }}';
            }, 2000);

        } catch (error) {
            console.error('Error:', error);
            const alertToast = document.getElementById('alertToast');
            alertToast.classList.remove('bg-success');
            alertToast.classList.add('bg-danger');
            alertToast.querySelector('.toast-body').textContent = error.message;
            toast.show();
        }
    });
});
</script>
@endpush
