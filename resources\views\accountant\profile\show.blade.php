@extends('layouts.accountant')

@php
use Illuminate\Support\Str;
@endphp

@section('title', 'Mon Profil')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <!-- <PERSON><PERSON><PERSON> gauche (navigation du profil) -->
        <div class="col-12 col-lg-3 mb-4">
            <div class="profile-sidebar">
                <div class="profile-sidebar-header">
                    <h5 class="profile-sidebar-title">Mon Compte</h5>
                    <p class="profile-sidebar-subtitle">Gérez vos informations personnelles</p>
                </div>
                
                <div class="profile-menu">
                    <a href="{{ route('accountant.profile.show') }}" class="profile-menu-item active">
                        <div class="profile-menu-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="profile-menu-content">
                            <span class="profile-menu-label">Informations personnelles</span>
                            <span class="profile-menu-description">Consultez vos informations</span>
                        </div>
                        <div class="profile-menu-indicator"></div>
                    </a>
                    
                    <a href="{{ route('accountant.profile.edit') }}" class="profile-menu-item">
                        <div class="profile-menu-icon">
                            <i class="fas fa-user-edit"></i>
                        </div>
                        <div class="profile-menu-content">
                            <span class="profile-menu-label">Modifier le profil</span>
                            <span class="profile-menu-description">Mettez à jour vos informations</span>
                        </div>
                    </a>
                    
                    <a href="{{ route('accountant.profile.edit') }}#password" class="profile-menu-item">
                        <div class="profile-menu-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="profile-menu-content">
                            <span class="profile-menu-label">Mot de passe</span>
                            <span class="profile-menu-description">Modifiez votre mot de passe</span>
                        </div>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Colonne de droite (informations de profil) -->
        <div class="col-12 col-lg-9">
            @if(session('success'))
                <div class="profile-alert success" id="success-alert">
                    <div class="profile-alert-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="profile-alert-content">
                        {{ session('success') }}
                    </div>
                    <button class="profile-alert-close" onclick="document.getElementById('success-alert').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            @endif
            
            <div class="profile-card">
                <div class="profile-card-header">
                    <h4 class="profile-card-title">
                        <i class="fas fa-user text-primary me-2"></i> Informations personnelles
                    </h4>
                    <p class="profile-card-subtitle">Consultez vos informations de profil</p>
                </div>

                <div class="profile-card-body">
                    <!-- Section avatar -->
                    <div class="profile-overview">
                        <div class="profile-avatar">
                            <img src="{{ $user->avatar ? (Str::startsWith($user->avatar, 'avatars/') ? asset('storage/' . $user->avatar) : asset($user->avatar)) : asset('images/default-avatar.png') }}" 
                                 alt="Photo de profil" 
                                 class="profile-avatar-image">
                        </div>
                        <div class="profile-overview-details">
                            <h3 class="profile-name">{{ $user->name }}</h3>
                            <p class="profile-role">Comptable</p>
                            <div class="profile-badges">
                                <span class="profile-badge">
                                    <i class="fas fa-calendar-alt"></i> Membre depuis {{ $user->created_at->format('d/m/Y') }}
                                </span>
                                <span class="profile-badge">
                                    <i class="fas fa-envelope"></i> {{ $user->email }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="profile-divider"></div>

                    <!-- Informations détaillées -->
                    <div class="profile-details">
                        <h5 class="profile-section-title">Détails du compte</h5>
                        
                        <div class="row g-4">
                            <div class="col-12 col-md-6">
                                <div class="profile-info-card">
                                    <div class="profile-info-icon">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="profile-info-content">
                                        <span class="profile-info-label">Nom complet</span>
                                        <span class="profile-info-value">{{ $user->name }}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12 col-md-6">
                                <div class="profile-info-card">
                                    <div class="profile-info-icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="profile-info-content">
                                        <span class="profile-info-label">Adresse email</span>
                                        <span class="profile-info-value">{{ $user->email }}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12 col-md-6">
                                <div class="profile-info-card">
                                    <div class="profile-info-icon">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    <div class="profile-info-content">
                                        <span class="profile-info-label">Numéro de téléphone</span>
                                        <span class="profile-info-value">{{ $user->phone ?: 'Non renseigné' }}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12 col-md-6">
                                <div class="profile-info-card">
                                    <div class="profile-info-icon">
                                        <i class="fas fa-briefcase"></i>
                                    </div>
                                    <div class="profile-info-content">
                                        <span class="profile-info-label">Poste / Fonction</span>
                                        <span class="profile-info-value">{{ $user->position ?: 'Non renseigné' }}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12 col-md-6">
                                <div class="profile-info-card">
                                    <div class="profile-info-icon">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <div class="profile-info-content">
                                        <span class="profile-info-label">Date d'inscription</span>
                                        <span class="profile-info-value">{{ $user->created_at->format('d/m/Y') }}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12 col-md-6">
                                <div class="profile-info-card">
                                    <div class="profile-info-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="profile-info-content">
                                        <span class="profile-info-label">Dernière connexion</span>
                                        <span class="profile-info-value">{{ $user->last_login_at ?? 'Aujourd\'hui' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="profile-divider"></div>

                    <!-- Boutons d'action -->
                    <div class="profile-actions">
                        <a href="{{ route('accountant.dashboard') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Retour au tableau de bord
                        </a>
                        <div>
                            <a href="{{ route('accountant.profile.edit') }}" class="btn btn-primary">
                                <i class="fas fa-edit me-1"></i> Modifier le profil
                            </a>
                            <a href="{{ route('accountant.profile.password') }}" class="btn btn-outline-primary ms-2">
                                <i class="fas fa-key me-1"></i> Changer le mot de passe
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* Styles pour la page de profil */
    .profile-sidebar {
        background: white;
        border-radius: 16px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        height: 100%;
    }
    
    .profile-sidebar-header {
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .profile-sidebar-title {
        font-weight: 700;
        color: #1E293B;
        margin-bottom: 0.5rem;
    }
    
    .profile-sidebar-subtitle {
        color: #64748B;
        font-size: 0.875rem;
        margin-bottom: 0;
    }
    
    .profile-menu {
        padding: 1rem 0;
    }
    
    .profile-menu-item {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        color: #475569;
        text-decoration: none;
        position: relative;
        transition: all 0.3s ease;
    }
    
    .profile-menu-item:hover {
        background-color: rgba(37, 99, 235, 0.05);
        color: #2563EB;
    }
    
    .profile-menu-item.active {
        background-color: rgba(37, 99, 235, 0.1);
        color: #2563EB;
        font-weight: 600;
    }
    
    .profile-menu-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background-color: rgba(37, 99, 235, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: #2563EB;
        transition: all 0.3s ease;
    }
    
    .profile-menu-item:hover .profile-menu-icon,
    .profile-menu-item.active .profile-menu-icon {
        background-color: #2563EB;
        color: white;
    }
    
    .profile-menu-content {
        flex: 1;
    }
    
    .profile-menu-label {
        display: block;
        font-weight: 500;
        margin-bottom: 0.25rem;
    }
    
    .profile-menu-description {
        display: block;
        font-size: 0.75rem;
        color: #64748B;
    }
    
    .profile-menu-indicator {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 24px;
        background-color: #2563EB;
        border-radius: 4px 0 0 4px;
    }
    
    /* Alerte de profil */
    .profile-alert {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        margin-bottom: 1.5rem;
        animation: slideDown 0.3s ease-out;
    }
    
    .profile-alert.success {
        background-color: rgba(16, 185, 129, 0.1);
        border-left: 4px solid #10B981;
    }
    
    .profile-alert-icon {
        margin-right: 1rem;
        font-size: 1.25rem;
    }
    
    .profile-alert.success .profile-alert-icon {
        color: #10B981;
    }
    
    .profile-alert-content {
        flex: 1;
        color: #1E293B;
    }
    
    .profile-alert-close {
        background: transparent;
        border: none;
        color: #94A3B8;
        cursor: pointer;
        transition: color 0.2s ease;
    }
    
    .profile-alert-close:hover {
        color: #475569;
    }
    
    @keyframes slideDown {
        from { transform: translateY(-20px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }
    
    /* Carte principale */
    .profile-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }
    
    .profile-card-header {
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .profile-card-title {
        font-weight: 700;
        color: #1E293B;
        margin-bottom: 0.5rem;
    }
    
    .profile-card-subtitle {
        color: #64748B;
        font-size: 0.875rem;
        margin-bottom: 0;
    }
    
    .profile-card-body {
        padding: 1.5rem;
    }
    
    /* Aperçu du profil */
    .profile-overview {
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
    }
    
    .profile-avatar {
        margin-right: 2rem;
        position: relative;
    }
    
    .profile-avatar-image {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border: 4px solid white;
    }
    
    .profile-overview-details {
        flex: 1;
    }
    
    .profile-name {
        font-size: 1.75rem;
        font-weight: 700;
        color: #1E293B;
        margin-bottom: 0.25rem;
    }
    
    .profile-role {
        font-size: 1rem;
        color: #2563EB;
        background-color: rgba(37, 99, 235, 0.1);
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        display: inline-block;
        margin-bottom: 1rem;
    }
    
    .profile-badges {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
    }
    
    .profile-badge {
        font-size: 0.875rem;
        color: #64748B;
        display: flex;
        align-items: center;
    }
    
    .profile-badge i {
        margin-right: 0.5rem;
        color: #94A3B8;
    }
    
    /* Séparateur */
    .profile-divider {
        height: 1px;
        background-color: rgba(0, 0, 0, 0.05);
        margin: 1.5rem 0;
    }
    
    /* Section de détails */
    .profile-section-title {
        font-weight: 600;
        color: #1E293B;
        margin-bottom: 1.25rem;
    }
    
    .profile-info-card {
        display: flex;
        align-items: center;
        background-color: #F8FAFC;
        border-radius: 12px;
        padding: 1rem 1.25rem;
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .profile-info-card:hover {
        background-color: #F1F5F9;
        transform: translateY(-2px);
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
    }
    
    .profile-info-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background-color: rgba(37, 99, 235, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: #2563EB;
    }
    
    .profile-info-content {
        flex: 1;
    }
    
    .profile-info-label {
        display: block;
        font-size: 0.75rem;
        color: #64748B;
        margin-bottom: 0.25rem;
    }
    
    .profile-info-value {
        display: block;
        font-weight: 500;
        color: #1E293B;
    }
    
    /* Boutons d'action */
    .profile-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .btn-outline-secondary {
        border-color: #E2E8F0;
        color: #64748B;
    }
    
    .btn-outline-secondary:hover {
        background-color: #F1F5F9;
        border-color: #CBD5E1;
        color: #475569;
    }
    
    .btn-primary {
        background-color: #2563EB;
        border-color: #2563EB;
    }
    
    .btn-primary:hover {
        background-color: #1D4ED8;
        border-color: #1D4ED8;
    }
    
    .btn-outline-primary {
        border-color: #2563EB;
        color: #2563EB;
    }
    
    .btn-outline-primary:hover {
        background-color: rgba(37, 99, 235, 0.1);
        color: #1D4ED8;
    }
    
    /* Animations */
    .pulse {
        animation: pulse 0.6s ease-in-out;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    .profile-info-card {
        opacity: 0;
        transform: translateY(20px);
    }
    
    .profile-info-card.animate-in {
        animation: fadeInUp 0.5s ease forwards;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .fade-out {
        animation: fadeOut 0.3s ease forwards;
    }
    
    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
    
    /* Styles responsives */
    @media (max-width: 992px) {
        .profile-sidebar {
            margin-bottom: 1.5rem;
        }
        
        .profile-overview {
            flex-direction: column;
            text-align: center;
        }
        
        .profile-avatar {
            margin-right: 0;
            margin-bottom: 1.5rem;
        }
        
        .profile-badges {
            justify-content: center;
        }
        
        .profile-actions {
            flex-direction: column;
            gap: 1rem;
        }
        
        .profile-actions a {
            width: 100%;
        }
        
        .profile-actions div {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            width: 100%;
        }
        
        .profile-actions div a {
            margin-left: 0 !important;
        }
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation des éléments au chargement
    const profileElements = document.querySelectorAll('.profile-info-card');
    profileElements.forEach((element, index) => {
        setTimeout(() => {
            element.classList.add('animate-in');
        }, 100 * index);
    });
    
    // Gérer les animations du menu latéral
    const menuItems = document.querySelectorAll('.profile-menu-item');
    menuItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.querySelector('.profile-menu-icon').classList.add('pulse');
        });
        
        item.addEventListener('mouseleave', function() {
            this.querySelector('.profile-menu-icon').classList.remove('pulse');
        });
    });
    
    // Fermeture automatique de l'alerte après 5 secondes
    const alert = document.getElementById('success-alert');
    if (alert) {
        setTimeout(() => {
            alert.classList.add('fade-out');
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    }
});
</script>
@endpush
