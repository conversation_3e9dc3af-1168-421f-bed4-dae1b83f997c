<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Rapport des Bénéfices</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #1CC88A;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #1CC88A;
            font-size: 24px;
            margin: 0;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .summary {
            margin-bottom: 30px;
        }
        .summary-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .summary-item {
            display: table-cell;
            width: 25%;
            text-align: center;
            padding: 15px;
            background-color: #f8f9fc;
            border: 1px solid #e3e6f0;
        }
        .summary-item h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #5a5c69;
        }
        .summary-item .amount {
            font-size: 18px;
            font-weight: bold;
        }
        .positive { color: #1CC88A; }
        .negative { color: #e74a3b; }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #1CC88A;
            font-size: 16px;
            margin-bottom: 15px;
            border-bottom: 1px solid #e3e6f0;
            padding-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table th, table td {
            border: 1px solid #e3e6f0;
            padding: 8px;
            text-align: left;
        }
        table th {
            background-color: #1CC88A;
            color: white;
            font-weight: bold;
        }
        table tr:nth-child(even) {
            background-color: #f8f9fc;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .analysis {
            background-color: #f8f9fc;
            padding: 15px;
            border-left: 4px solid #1CC88A;
            margin-bottom: 20px;
        }
        .analysis h3 {
            color: #1CC88A;
            margin-top: 0;
        }
        .analysis ul {
            margin: 0;
            padding-left: 20px;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #e3e6f0;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>RAPPORT DES BÉNÉFICES</h1>
        <p>Période: {{ \Carbon\Carbon::parse($start_date)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($end_date)->format('d/m/Y') }}</p>
        <p>Généré le {{ \Carbon\Carbon::now()->format('d/m/Y à H:i') }}</p>
    </div>

    <div class="summary">
        <div class="summary-grid">
            <div class="summary-item">
                <h3>Revenus</h3>
                <div class="amount positive">{{ number_format($revenue, 0, ',', ' ') }} FCFA</div>
            </div>
            <div class="summary-item">
                <h3>Coûts</h3>
                <div class="amount negative">{{ number_format($costs, 0, ',', ' ') }} FCFA</div>
            </div>
            <div class="summary-item">
                <h3>Bénéfice Net</h3>
                <div class="amount {{ $profit >= 0 ? 'positive' : 'negative' }}">{{ number_format($profit, 0, ',', ' ') }} FCFA</div>
            </div>
            <div class="summary-item">
                <h3>Marge Bénéficiaire</h3>
                <div class="amount {{ $profit_margin >= 0 ? 'positive' : 'negative' }}">{{ number_format($profit_margin, 1) }}%</div>
            </div>
        </div>
    </div>

    @if($monthly_profit->count() > 0)
    <div class="section">
        <h2>Évolution mensuelle des bénéfices</h2>
        <table>
            <thead>
                <tr>
                    <th>Mois</th>
                    <th class="text-right">Revenus</th>
                    <th class="text-right">Coûts</th>
                    <th class="text-right">Bénéfice</th>
                    <th class="text-center">Marge (%)</th>
                </tr>
            </thead>
            <tbody>
                @foreach($monthly_profit as $data)
                @php
                    $monthlyMargin = $data->revenue > 0 ? ($data->profit / $data->revenue) * 100 : 0;
                @endphp
                <tr>
                    <td>{{ \Carbon\Carbon::createFromDate($data->year, $data->month, 1)->format('F Y') }}</td>
                    <td class="text-right">{{ number_format($data->revenue, 0, ',', ' ') }} FCFA</td>
                    <td class="text-right">{{ number_format($data->costs, 0, ',', ' ') }} FCFA</td>
                    <td class="text-right {{ $data->profit >= 0 ? 'positive' : 'negative' }}">{{ number_format($data->profit, 0, ',', ' ') }} FCFA</td>
                    <td class="text-center {{ $monthlyMargin >= 0 ? 'positive' : 'negative' }}">{{ number_format($monthlyMargin, 1) }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <div class="section">
        <h2>Analyse des performances</h2>
        <div class="analysis">
            <h3>Points positifs</h3>
            <ul>
                @if($profit > 0)
                    <li>✓ Bénéfice positif sur la période</li>
                @endif
                @if($profit_margin > 10)
                    <li>✓ Marge bénéficiaire satisfaisante ({{ number_format($profit_margin, 1) }}%)</li>
                @endif
                @if($revenue > $costs)
                    <li>✓ Revenus supérieurs aux coûts</li>
                @endif
            </ul>

            <h3>Points d'attention</h3>
            <ul>
                @if($profit <= 0)
                    <li>⚠ Bénéfice négatif ou nul</li>
                @endif
                @if($profit_margin < 10)
                    <li>⚠ Marge bénéficiaire faible</li>
                @endif
                @if($costs > $revenue * 0.8)
                    <li>⚠ Coûts élevés par rapport aux revenus</li>
                @endif
            </ul>
        </div>
    </div>

    <div class="footer">
        <p>GRADIS - Système de gestion des approvisionnements et ventes</p>
        <p>Ce rapport a été généré automatiquement le {{ \Carbon\Carbon::now()->format('d/m/Y à H:i') }}</p>
    </div>
</body>
</html>
