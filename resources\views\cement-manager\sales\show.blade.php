@extends('layouts.cement_manager')

@section('title', '<PERSON><PERSON><PERSON> de la vente')

@push('styles')
<style>
    :root {
        --primary-color: #4f46e5; /* Indigo */
        --primary-light: #818cf8;
        --primary-dark: #4338ca;
        --secondary-color: #475569; /* Slate */
        --success-color: #059669; /* Emerald */
        --danger-color: #dc2626; /* Red */
        --warning-color: #d97706; /* Amber */
        --info-color: #0284c7; /* Sky */
        --light-color: #f8fafc; /* Slate 50 */
        --dark-color: #0f172a; /* Slate 900 */
        --gray-50: #f9fafb;
        --gray-100: #f3f4f6;
        --gray-200: #e5e7eb;
        --gray-300: #d1d5db;
        --gray-400: #9ca3af;
        --gray-500: #6b7280;
        --border-radius: 0.5rem;
        --border-radius-lg: 0.75rem;
        --border-radius-sm: 0.25rem;
        --box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --box-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --transition: all 0.3s ease;
    }
    
    body {
        background-color: var(--gray-100);
        font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        color: var(--dark-color);
        line-height: 1.6;
    }
    
    .sale-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: #fff;
        border-radius: var(--border-radius-lg);
        padding: 2.5rem;
        box-shadow: var(--box-shadow-md);
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .sale-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z' fill='%23ffffff' fill-opacity='0.08'/%3E%3C/svg%3E");
        opacity: 0.3;
        z-index: 0;
    }
    
    .sale-header .badge {
        font-size: 1rem;
        padding: 0.625rem 1.25rem;
        border-radius: 50rem;
        background: rgba(255, 255, 255, 0.15);
        color: #fff;
        font-weight: 700;
        backdrop-filter: blur(4px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        letter-spacing: 0.5px;
        position: relative;
        z-index: 1;
    }
    
    .sale-header .invoice-number {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }
    
    .detail-card {
        background: #fff;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow-sm);
        padding: 1.75rem;
        margin-bottom: 1.75rem;
        transition: var(--transition);
        border: 1px solid var(--gray-200);
        position: relative;
        overflow: hidden;
    }
    
    .detail-card:hover {
        box-shadow: var(--box-shadow);
        transform: translateY(-3px);
        border-color: var(--gray-300);
    }
    
    .detail-card::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-color);
        opacity: 0.7;
    }
    
    .section-title {
        color: var(--dark-color);
        font-weight: 700;
        margin-bottom: 1.5rem;
        font-size: 1.25rem;
        position: relative;
        padding-left: 0;
        display: flex;
        align-items: center;
    }
    
    .section-title i {
        color: var(--primary-color);
        margin-right: 0.75rem;
        font-size: 1.1rem;
        background: rgba(79, 70, 229, 0.1);
        padding: 0.5rem;
        border-radius: 0.5rem;
        box-shadow: var(--box-shadow-sm);
    }
    
    .info-group {
        margin-bottom: 1rem;
    }
    
    .info-label {
        font-size: 0.875rem;
        color: var(--secondary-color);
        margin-bottom: 0.25rem;
    }
    
    .info-value {
        font-weight: 600;
        color: var(--dark-color);
    }
    
    .status-badge {
        padding: 0.35rem 0.75rem;
        border-radius: 50rem;
        font-size: 0.75rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
    }
    
    .status-badge i {
        margin-right: 0.35rem;
    }
    
    .status-badge.completed {
        background-color: rgba(16, 185, 129, 0.1);
        color: var(--success-color);
    }
    
    .status-badge.pending {
        background-color: rgba(245, 158, 11, 0.1);
        color: var(--warning-color);
    }
    
    .status-badge.in-progress {
        background-color: rgba(14, 165, 233, 0.1);
        color: var(--info-color);
    }
    
    .remise-block h6 {
        color: #198754;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    .augmentation-block {
        background: #fff8e1;
        border-left: 5px solid #ff9800;
        border-radius: 0.5rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }
    .augmentation-block h6 {
        color: #e65100;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    .table-responsive {
        border-radius: 0.5rem;
        overflow: auto;
        background: #f8fafc;
        margin-bottom: 1.5rem;
    }
    .table {
        margin-bottom: 0;
    }
    .notes-block {
        background: #fff3cd;
        border-left: 5px solid #ffc107;
        border-radius: 0.5rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }
    .notes-block h6 {
        color: #856404;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    @media (max-width: 767.98px) {
        .sale-header, .detail-card {
            padding: 1.1rem 0.7rem 1rem 0.7rem;
        }
        .section-title {
            font-size: 1rem;
        }
        .remise-block, .augmentation-block, .notes-block {
            padding: 0.7rem 0.7rem;
        }
    }
    @media (max-width: 575.98px) {
        .sale-header h1 {
            font-size: 1.1rem;
        }
        .sale-header {
            padding: 1rem 0.5rem 0.7rem 0.5rem;
        }
        .detail-card {
            padding: 1rem 0.5rem 0.7rem 0.5rem;
        }
    }
</style>
@endpush

@section('content')
<div class="container-fluid py-4">
    <div class="sale-header mb-4 d-flex flex-column flex-md-row justify-content-between align-items-md-center">
        <div>
            <h1 class="h3 mb-2">Vente #{{ $sale->invoice_number }}
                @if($sale->delivery_status === 'completed')
                    <i class="fas fa-check-circle icon text-success"></i>
                @elseif($sale->delivery_status === 'in_progress')
                    <i class="fas fa-truck icon text-warning"></i>
                @else
                    <i class="fas fa-hourglass-half icon text-info"></i>
                @endif
            </h1>
        </div>
    </div>

    <!-- En-tête de la vente avec les informations principales -->
    <div class="sale-header mb-4">
        <div class="row align-items-center">
            <div class="col-md-7">
                <div class="invoice-number">
                    <i class="fas fa-file-invoice me-2"></i> Vente #{{ $sale->id }}
                </div>
                
                <!-- Libellé indiquant le type de vente -->
                <div class="mt-2 mb-3">
                    @if($sale->sale_type == 'discount')
                        <div class="px-3 py-2" style="background-color: rgba(14, 165, 233, 0.15); border-radius: 0.5rem; display: inline-block;">
                            <span style="color: var(--info-color); font-weight: 600;">
                                <i class="fas fa-arrow-down me-1"></i> Vente avec remise
                            </span>
                        </div>
                    @elseif($sale->sale_type == 'increased')
                        <div class="px-3 py-2" style="background-color: rgba(217, 119, 6, 0.15); border-radius: 0.5rem; display: inline-block;">
                            <span style="color: var(--warning-color); font-weight: 600;">
                                <i class="fas fa-arrow-up me-1"></i> Vente avec prix augmenté
                            </span>
                        </div>
                    @elseif($sale->sale_type == 'standard')
                        <div class="px-3 py-2" style="background-color: rgba(5, 150, 105, 0.15); border-radius: 0.5rem; display: inline-block;">
                            <span style="color: var(--success-color); font-weight: 600;">
                                <i class="fas fa-check-circle me-1"></i> Vente au prix standard
                            </span>
                        </div>
                    @endif
                </div>
                <div class="d-flex align-items-center mb-3">
                    <span class="status-badge {{ $sale->status == 'completed' ? 'completed' : 'pending' }}">
                        <i class="fas {{ $sale->status == 'completed' ? 'fa-check-circle' : 'fa-clock' }}"></i>
                        {{ $sale->status == 'completed' ? 'Complétée' : 'En attente' }}
                    </span>
                    
                    @if($sale->sale_type == 'discount')
                    <span class="ms-3 status-badge" style="background-color: rgba(14, 165, 233, 0.2); color: #fff;">
                        <i class="fas fa-arrow-down"></i> Remise appliquée
                    </span>
                    @elseif($sale->sale_type == 'increased')
                    <span class="ms-3 status-badge" style="background-color: rgba(217, 119, 6, 0.2); color: #fff;">
                        <i class="fas fa-arrow-up"></i> Prix augmenté
                    </span>
                    @elseif($sale->sale_type == 'standard')
                    <span class="ms-3 status-badge" style="background-color: rgba(5, 150, 105, 0.2); color: #fff;">
                        <i class="fas fa-check-circle"></i> Prix standard
                    </span>
                    @endif
                    
                    <span class="ms-3 text-white-50">{{ $sale->created_at->format('d/m/Y à H:i') }}</span>
                </div>
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="info-group">
                            <div class="info-label">Client</div>
                            <div class="info-value">{{ $sale->customer_name }}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-group">
                            <div class="info-label">Ville</div>
                            <div class="info-value">{{ $sale->city->name }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-5 text-md-end">
                <div class="badge mb-3 fs-5">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</div>
                <!-- Cartes d'informations -->
                <div class="row">
                    <!-- Détails du produit -->
                    <div class="col-md-6">
                        <div class="detail-card h-100">
                            <h5 class="section-title"><i class="fas fa-box me-2"></i>Détails du produit</h5>
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <div class="info-label">Produit</div>
                                        <div class="info-value">{{ $sale->supply->details->first()->product->name }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <div class="info-label">Quantité</div>
                                        <div class="info-value">{{ $sale->quantity }} tonnes</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <div class="info-label">Prix unitaire</div>
                                        <div class="info-value">{{ number_format($sale->unit_price, 0, ',', ' ') }} FCFA/tonne</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <div class="info-label">Montant total</div>
                                        <div class="info-value">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <div class="info-label">Date de vente</div>
                                        <div class="info-value">{{ $sale->created_at->format('d/m/Y') }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <div class="info-label">Statut</div>
                                        <div class="info-value">
                                            @if($sale->status == 'completed')
                                                <span class="status-badge completed"><i class="fas fa-check-circle"></i> Complétée</span>
                                            @else
                                                <span class="status-badge pending"><i class="fas fa-clock"></i> En attente</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informations du client -->
                    <div class="col-md-6">
                        <div class="detail-card h-100">
                            <h5 class="section-title"><i class="fas fa-user me-2"></i>Informations du client</h5>
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <div class="info-label">Nom du client</div>
                                        <div class="info-value">{{ $sale->customer_name }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <div class="info-label">Ville</div>
                                        <div class="info-value">{{ $sale->city->name }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <div class="info-label">Créée par</div>
                                        <div class="info-value">{{ $sale->createdBy->name }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <div class="info-label">Date de création</div>
                                        <div class="info-value">{{ $sale->created_at->format('d/m/Y à H:i') }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="detail-card">
        <div class="row">
            <div class="col-md-6 mb-3 mb-md-0">
                <div class="section-title"><i class="fas fa-user me-1"></i> Client</div>
                <ul class="info-list">
                    <li><strong>{{ $sale->customer_name }}</strong></li>
                    <li>{{ $sale->customer_phone }}</li>
                    <li>{{ $sale->customer_email }}</li>
                    <li>{{ $sale->customer_address }}</li>
                </ul>
            </div>
            <div class="col-md-6">
                <div class="section-title"><i class="fas fa-map-marker-alt me-1"></i> Livraison</div>
                <ul class="info-list">
                    <li>Ville : <strong>{{ $sale->city->name }}</strong></li>
                    <li>Date prévue : <strong>{{ $sale->delivery_date ? $sale->delivery_date->format('d/m/Y H:i') : 'Non défini' }}</strong></li>
                    <li>Nombre de voyages : <strong>{{ $sale->trips }}</strong></li>
                </ul>
            </div>
        </div>
        <hr>
        <div class="section-title"><i class="fas fa-cube me-1"></i> Produit</div>
        <div class="table-responsive">
            <table class="table table-bordered align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th colspan="4" class="text-center">
                            @if($sale->sale_type == 'discount')
                                <div style="color: var(--info-color); font-weight: 600;">
                                    <i class="fas fa-arrow-down me-1"></i> VENTE AVEC REMISE
                                </div>
                            @elseif($sale->sale_type == 'increased')
                                <div style="color: var(--warning-color); font-weight: 600;">
                                    <i class="fas fa-arrow-up me-1"></i> VENTE AVEC PRIX AUGMENTÉ
                                </div>
                            @elseif($sale->sale_type == 'standard')
                                <div style="color: var(--success-color); font-weight: 600;">
                                    <i class="fas fa-check-circle me-1"></i> VENTE AU PRIX STANDARD
                                </div>
                            @endif
                        </th>
                    </tr>
                    <tr>
                        <th>Produit</th>
                        <th>Quantité</th>
                        <th>Prix unitaire</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{{ $sale->supply->details->first()->product->name }}</td>
                        <td>{{ number_format($sale->quantity, 2, ',', ' ') }} T</td>
                        <td>
                            <span class="fw-bold">{{ number_format($sale->unit_price, 0, ',', ' ') }} FCFA</span>
                            @if($sale->sale_type == 'increased')
                                <div class="mt-2 p-2" style="background-color: rgba(217, 119, 6, 0.1); border-radius: 0.5rem; border-left: 3px solid var(--warning-color);">
                                    <div class="d-flex align-items-center mb-1" style="color: var(--warning-color);">
                                        <i class="fas fa-arrow-up me-2"></i>
                                        <span class="fw-bold">Prix augmenté</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <small>Prix standard:</small>
                                        <small class="fw-bold">{{ number_format($sale->original_price, 0, ',', ' ') }} FCFA</small>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <small>Augmentation:</small>
                                        <small class="fw-bold" style="color: var(--danger-color);">+{{ number_format($sale->unit_price - $sale->original_price, 0, ',', ' ') }} FCFA</small>
                                    </div>
                                </div>
                            @elseif($sale->sale_type == 'discount')
                                <div class="mt-2 p-2" style="background-color: rgba(14, 165, 233, 0.1); border-radius: 0.5rem; border-left: 3px solid var(--info-color);">
                                    <div class="d-flex align-items-center mb-1" style="color: var(--info-color);">
                                        <i class="fas fa-arrow-down me-2"></i>
                                        <span class="fw-bold">Remise appliquée</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <small>Prix standard:</small>
                                        <small class="fw-bold">{{ number_format($sale->original_price ?? ($sale->unit_price + $sale->discount_per_ton), 0, ',', ' ') }} FCFA</small>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <small>Remise:</small>
                                        <small class="fw-bold" style="color: var(--info-color);">-{{ number_format($sale->discount_per_ton, 0, ',', ' ') }} FCFA</small>
                                    </div>
                                </div>
                            @elseif($sale->sale_type == 'standard')
                                <div class="mt-2 p-2" style="background-color: rgba(5, 150, 105, 0.1); border-radius: 0.5rem; border-left: 3px solid var(--success-color);">
                                    <div class="d-flex align-items-center mb-1" style="color: var(--success-color);">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <span class="fw-bold">Prix standard</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <small>Prix catalogue:</small>
                                        <small class="fw-bold">{{ number_format($sale->unit_price, 0, ',', ' ') }} FCFA</small>
                                    </div>
                                </div>
                            @endif
                        </td>
                        <td>{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</td>
                    </tr>
                </tbody>
            </table>
        </div>

    </div>
    
    <!-- Informations détaillées sur le produit et le stock -->
    <div class="detail-card mt-4">
        <h5 class="section-title">
            <i class="fas fa-info-circle me-2" style="color: var(--primary-color); background: rgba(79, 70, 229, 0.1);"></i>
            <span>Informations détaillées du produit</span>
        </h5>
        <div class="row">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="fas fa-box me-2"></i> Détails du produit</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            @php
                                // Charger les informations complètes de l'approvisionnement
                                $supply = \App\Models\Supply::with([
                                    'cities.city', 
                                    'cities.vehicle.capacity', 
                                    'cities.driver',
                                    'details.product'
                                ])->find($sale->supply_id);
                                
                                // Récupérer les informations de la ville et du véhicule spécifiques à cette vente
                                $supplyCity = null;
                                if ($supply) {
                                    // Essayer de trouver la correspondance exacte par véhicule et ville
                                    $supplyCity = $supply->cities->where('vehicle_id', $sale->vehicle_id)
                                                               ->where('city_id', $sale->city_id)
                                                               ->first();
                                    
                                    // Si on ne trouve pas, essayer par véhicule uniquement
                                    if (!$supplyCity) {
                                        $supplyCity = $supply->cities->where('vehicle_id', $sale->vehicle_id)->first();
                                    }
                                    
                                    // Si on ne trouve toujours pas, essayer par ville uniquement
                                    if (!$supplyCity) {
                                        $supplyCity = $supply->cities->where('city_id', $sale->city_id)->first();
                                    }
                                }
                                
                                // Si on a trouvé les informations de l'approvisionnement, extraire les détails
                                if ($supplyCity) {
                                    $totalQuantity = $supplyCity->quantity;
                                    $remainingQuantity = $supplyCity->remaining_quantity ?? $supplyCity->quantity;
                                    $usedQuantity = $totalQuantity - $remainingQuantity;
                                    $vehicleCapacity = $supplyCity->vehicle && $supplyCity->vehicle->capacity ? $supplyCity->vehicle->capacity->capacity : 0;
                                } else {
                                    // Valeurs par défaut si on ne trouve pas les informations
                                    $totalQuantity = 0;
                                    $remainingQuantity = 0;
                                    $usedQuantity = 0;
                                    $vehicleCapacity = $sale->vehicle && $sale->vehicle->capacity ? $sale->vehicle->capacity->capacity : 0;
                                }
                            @endphp
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-weight me-2 text-primary"></i> Quantité totale :</span>
                                <span class="fw-bold">{{ number_format($totalQuantity, 2, ',', ' ') }} T</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-shopping-cart me-2 text-success"></i> Quantité vendue :</span>
                                <span class="fw-bold">{{ number_format($usedQuantity, 2, ',', ' ') }} T</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-warehouse me-2 text-info"></i> Quantité restante :</span>
                                <span class="fw-bold">{{ number_format($remainingQuantity, 2, ',', ' ') }} T</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-tag me-2 text-danger"></i> Prix de vente par tonne :</span>
                                <span class="fw-bold">{{ number_format($sale->unit_price, 0, ',', ' ') }} FCFA</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0"><i class="fas fa-truck me-2"></i> Informations du transport</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-truck me-2 text-secondary"></i> Véhicule assigné :</span>
                                <span class="fw-bold">{{ $supplyCity && $supplyCity->vehicle ? $supplyCity->vehicle->registration_number : ($sale->vehicle ? $sale->vehicle->registration_number : 'Non assigné') }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-balance-scale me-2 text-warning"></i> Capacité :</span>
                                <span class="fw-bold">{{ number_format($supplyCity && $supplyCity->vehicle && $supplyCity->vehicle->capacity ? $supplyCity->vehicle->capacity->capacity : $vehicleCapacity, 2, ',', ' ') }} T</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-user me-2 text-info"></i> Chauffeur :</span>
                                <span class="fw-bold">{{ $supplyCity && $supplyCity->driver ? $supplyCity->driver->first_name . ' ' . $supplyCity->driver->last_name : ($sale->driver ? $sale->driver->first_name . ' ' . $sale->driver->last_name : 'Non assigné') }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-phone me-2 text-success"></i> Téléphone chauffeur :</span>
                                <span class="fw-bold">{{ $supplyCity && $supplyCity->driver ? $supplyCity->driver->phone : ($sale->driver ? $sale->driver->phone : 'N/A') }}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Section pour afficher la raison du rejet si la vente a été rejetée -->
    @if($sale->admin_validation_status === 'rejected' && $sale->admin_note)
    <div class="detail-card mt-4 border-danger">
        <div class="section-title text-danger"><i class="fas fa-exclamation-triangle"></i> Vente rejetée</div>
        <div class="alert alert-danger">
            <h5 class="alert-heading">Raison du rejet :</h5>
            <p>{{ $sale->admin_note }}</p>
            <hr>
            <p class="mb-0">La quantité de {{ number_format($sale->quantity, 2, ',', ' ') }} tonnes a été retournée au stock.</p>
        </div>
    </div>
    @endif

    <!-- Section récapitulative des modifications de prix -->
    @if(($sale->discount_per_ton ?? 0) > 0 || ($sale->price_modified && $sale->original_price))
    <div class="detail-card mt-4">
        <h5 class="section-title">
            @if(($sale->discount_per_ton ?? 0) > 0)
                <i class="fas fa-arrow-down me-2" style="color: var(--info-color); background: rgba(14, 165, 233, 0.15);"></i>
                <span style="color: var(--info-color);">Récapitulatif de la remise de prix</span>
            @else
                <i class="fas fa-arrow-up me-2" style="color: var(--warning-color); background: rgba(217, 119, 6, 0.15);"></i>
                <span style="color: var(--warning-color);">Récapitulatif de l'augmentation de prix</span>
            @endif
        </h5>
        
        <div class="row">
            @if(($sale->discount_per_ton ?? 0) > 0)
            <div class="col-md-6 mb-4">
                <!-- Carte de remise moderne (bleu) -->
                <div class="price-modification-card discount">
                    <!-- En-tête bleu avec icône info -->
                    <div class="price-modification-header">
                        <i class="fas fa-arrow-down"></i>
                        <h6 class="mb-0">Informations de remise</h6>
                    </div>
                    
                    <!-- Liste des informations avec badges -->
                    <div class="price-modification-body">
                        <div class="price-item">
                            <span>Remise par tonne</span>
                            <span class="price-badge" style="background-color: rgba(14, 165, 233, 0.15); color: var(--info-color);">{{ number_format($sale->discount_per_ton, 0, ',', ' ') }} FCFA</span>
                        </div>
                        <div class="price-item">
                            <span>Remise totale</span>
                            <span class="price-badge" style="background-color: rgba(14, 165, 233, 0.15); color: var(--info-color);">{{ number_format($sale->discount_total ?? ($sale->discount_per_ton * $sale->quantity), 0, ',', ' ') }} FCFA</span>
                        </div>
                        <div class="price-item">
                            <span>Montant avant remise</span>
                            <span class="price-badge" style="background-color: rgba(71, 85, 105, 0.1); color: var(--secondary-color);">{{ number_format($sale->total_before_discount ?? (($sale->unit_price + $sale->discount_per_ton) * $sale->quantity), 0, ',', ' ') }} FCFA</span>
                        </div>
                    </div>
                </div>
            </div>
            @endif
            
            @if($sale->price_modified && $sale->original_price)
            <div class="col-md-6 mb-4">
                <!-- Carte d'augmentation de prix moderne (orange/rouge) -->
                <div class="price-modification-card increase">
                    <!-- En-tête orange avec icône info -->
                    <div class="price-modification-header">
                        <i class="fas fa-arrow-up"></i>
                        <h6 class="mb-0">Informations d'augmentation de prix</h6>
                    </div>
                    
                    <!-- Liste des informations avec badges -->
                    <div class="price-modification-body">
                        <div class="price-item">
                            <span>Prix standard par tonne</span>
                            <span class="price-badge" style="background-color: rgba(71, 85, 105, 0.1); color: var(--secondary-color);">{{ number_format($sale->original_price, 0, ',', ' ') }} FCFA</span>
                        </div>
                        <div class="price-item">
                            <span>Augmentation par tonne</span>
                            <span class="price-badge" style="background-color: rgba(220, 38, 38, 0.15); color: var(--danger-color);">{{ number_format($sale->unit_price - $sale->original_price, 0, ',', ' ') }} FCFA</span>
                        </div>
                        <div class="price-item">
                            <span>Prix final par tonne</span>
                            <span class="price-badge" style="background-color: rgba(217, 119, 6, 0.15); color: var(--warning-color);">{{ number_format($sale->unit_price, 0, ',', ' ') }} FCFA</span>
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
    @endif
    
    @if($sale->notes)
    <div class="detail-card mt-4">
        <h5 class="mb-3"><i class="fas fa-sticky-note me-2"></i> Notes</h5>
        <p class="mb-0">{{ $sale->notes }}</p>
    </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
function printInvoice() {
    window.print();
}
</script>
@endpush
