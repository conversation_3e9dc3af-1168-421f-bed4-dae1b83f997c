/* Cartes statistiques avec effets 3D et animations */
.stats-section {
    margin-bottom: 2.5rem;
    position: relative;
    z-index: 10;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    position: relative;
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.39, 0.575, 0.565, 1);
    z-index: 1;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.03);
    backdrop-filter: blur(10px);
    transform-style: preserve-3d;
}

.stat-card:hover {
    transform: translateY(-7px);
    box-shadow: var(--shadow-lg);
    border-color: rgba(30, 136, 229, 0.1);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-primary);
    z-index: 1;
}

.stat-card.primary::before { background: var(--gradient-primary); }
.stat-card.success::before { background: var(--gradient-success); }
.stat-card.warning::before { background: var(--gradient-warning); }
.stat-card.danger::before { background: var(--gradient-danger); }

.stat-card-glow {
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(30, 136, 229, 0.15) 0%, rgba(30, 136, 229, 0) 70%);
    border-radius: 50%;
    z-index: -1;
}

.stat-card-glow.success { background: radial-gradient(circle, rgba(76, 175, 80, 0.15) 0%, rgba(76, 175, 80, 0) 70%); }
.stat-card-glow.warning { background: radial-gradient(circle, rgba(255, 152, 0, 0.15) 0%, rgba(255, 152, 0, 0) 70%); }
.stat-card-glow.danger { background: radial-gradient(circle, rgba(244, 67, 54, 0.15) 0%, rgba(244, 67, 54, 0) 70%); }

.stat-card-top {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.stat-card-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 4px 10px rgba(30, 136, 229, 0.3);
}

.stat-card.primary .stat-card-icon { background: var(--gradient-primary); box-shadow: 0 4px 10px rgba(30, 136, 229, 0.3); }
.stat-card.success .stat-card-icon { background: var(--gradient-success); box-shadow: 0 4px 10px rgba(76, 175, 80, 0.3); }
.stat-card.warning .stat-card-icon { background: var(--gradient-warning); box-shadow: 0 4px 10px rgba(255, 152, 0, 0.3); }
.stat-card.danger .stat-card-icon { background: var(--gradient-danger); box-shadow: 0 4px 10px rgba(244, 67, 54, 0.3); }

.stat-card-trend {
    font-size: 0.85rem;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.stat-card-trend.up { background: rgba(76, 175, 80, 0.1); color: var(--success-color); }
.stat-card-trend.down { background: rgba(244, 67, 54, 0.1); color: var(--danger-color); }

.stat-card-title {
    font-size: 0.9rem;
    color: var(--gray-color);
    margin-bottom: 0.5rem;
}

.stat-card-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.stat-card-subtitle {
    font-size: 0.85rem;
    color: var(--gray-color);
}

/* Cartes du tableau de bord avec animations */
.dashboard-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: var(--transition-normal);
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.dashboard-card:hover {
    box-shadow: var(--shadow-lg);
}

.dashboard-card-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(248, 249, 250, 0.5);
}

.dashboard-card-title {
    font-weight: 600;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    color: var(--dark-color);
}

.dashboard-card-title i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.dashboard-card-body {
    padding: 1.5rem;
}

/* Graphiques et visualisations */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 1rem;
}

.chart-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: var(--transition-normal);
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.chart-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-3px);
}

.chart-card-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(248, 249, 250, 0.5);
}

.chart-card-title {
    font-weight: 600;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    color: var(--dark-color);
}

.chart-card-title i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.chart-card-body {
    padding: 1.5rem;
}

.chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: var(--gray-color);
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 3px;
}

.legend-color.primary { background: var(--primary-color); }
.legend-color.success { background: var(--success-color); }
.legend-color.warning { background: var(--warning-color); }
.legend-color.danger { background: var(--danger-color); }

/* Animations */
.fadeInUp {
    animation: fadeInUp 0.6s both;
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }
.delay-5 { animation-delay: 0.5s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 20px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}
