@extends('layouts.accountant')

@section('title', 'Modifier l\'approvisionnement')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Modifier l'approvisionnement</h1>
        <a href="{{ route('accountant.supplies.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> Retour
        </a>
    </div>

    @if(session('error'))
        <div class="alert alert-danger">
            {{ session('error') }}
        </div>
    @endif

    <div class="card shadow mb-4">
        <div class="card-body">
            <form action="{{ route('accountant.supplies.update', $supply) }}" method="POST" id="editSupplyForm">
                @csrf
                @method('PUT')

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="categorySelect" class="form-label">Catégorie</label>
                            <select class="form-select" id="categorySelect" required>
                                <option value="">Sélectionner une catégorie</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" 
                                            {{ $supply->product->category_id == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label for="productSelect" class="form-label">Produit</label>
                            <select class="form-select" id="productSelect" name="product_id" required>
                                <option value="">Sélectionner un produit</option>
                                <!-- Les produits seront chargés dynamiquement -->
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="regionSelect" class="form-label">Région</label>
                            <select class="form-select" id="regionSelect" name="region_id" required>
                                <option value="">Sélectionner une région</option>
                                @foreach($regions as $region)
                                    <option value="{{ $region->id }}" 
                                            {{ $supply->region_id == $region->id ? 'selected' : '' }}>
                                        {{ $region->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="cities-container">
                    @foreach($supply->cities as $city)
                        <div class="city-entry card mb-3">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="form-label">Ville</label>
                                            <select class="form-select city-select" name="cities[{{ $loop->index }}][id]" required>
                                                <option value="">Sélectionner une ville</option>
                                                <!-- Les villes seront chargées dynamiquement -->
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="form-label">Camion</label>
                                            <select class="form-select truck-select" name="cities[{{ $loop->index }}][truck_id]" required>
                                                <option value="">Sélectionner un camion</option>
                                                <!-- Les camions seront chargés dynamiquement -->
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="form-label">Quantité (T)</label>
                                            <input type="number" 
                                                   class="form-control quantity-input" 
                                                   name="cities[{{ $loop->index }}][quantity]" 
                                                   value="{{ $city->pivot->quantity }}"
                                                   step="0.01" 
                                                   min="0" 
                                                   required>
                                        </div>
                                    </div>

                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="form-label">Voyages</label>
                                            <input type="number" 
                                                   class="form-control trips-input" 
                                                   name="cities[{{ $loop->index }}][trips]" 
                                                   value="{{ $city->pivot->trips }}"
                                                   min="1" 
                                                   required>
                                        </div>
                                    </div>

                                    <div class="col-md-2 d-flex align-items-end">
                                        <button type="button" class="btn btn-danger remove-city">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="mb-3">
                    <button type="button" class="btn btn-success" id="addCity">
                        <i class="fas fa-plus-circle me-1"></i> Ajouter une ville
                    </button>
                </div>

                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Enregistrer les modifications
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Template pour une nouvelle entrée de ville -->
<template id="cityEntryTemplate">
    <div class="city-entry card mb-3">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label">Ville</label>
                        <select class="form-select city-select" name="cities[INDEX][id]" required>
                            <option value="">Sélectionner une ville</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label">Camion</label>
                        <select class="form-select truck-select" name="cities[INDEX][truck_id]" required>
                            <option value="">Sélectionner un camion</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="form-group">
                        <label class="form-label">Quantité (T)</label>
                        <input type="number" 
                               class="form-control quantity-input" 
                               name="cities[INDEX][quantity]" 
                               step="0.01" 
                               min="0" 
                               required>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="form-group">
                        <label class="form-label">Voyages</label>
                        <input type="number" 
                               class="form-control trips-input" 
                               name="cities[INDEX][trips]" 
                               min="1" 
                               required>
                    </div>
                </div>

                <div class="col-md-2 d-flex align-items-end">
                    <button type="button" class="btn btn-danger remove-city">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

@endsection

@push('scripts')
<script>
class SupplyManager {
    constructor() {
        this.token = document.querySelector('meta[name="csrf-token"]').content;
        this.form = document.getElementById('editSupplyForm');
        this.categorySelect = document.getElementById('categorySelect');
        this.productSelect = document.getElementById('productSelect');
        this.regionSelect = document.getElementById('regionSelect');
        this.citiesContainer = document.querySelector('.cities-container');
        this.addCityButton = document.getElementById('addCity');
        this.cityTemplate = document.getElementById('cityEntryTemplate');
        this.cityIndex = {{ $supply->cities->count() }};
        this.vehicles = new Map();

        this.initializeEventListeners();
        this.loadInitialData();
    }

    async fetchWithAuth(url) {
        const response = await fetch(`${window.location.origin}/accountant${url}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': this.token,
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
        }

        return response;
    }

    async loadInitialData() {
        try {
            // Charger les produits de la catégorie actuelle
            if (this.categorySelect.value) {
                await this.loadProducts(this.categorySelect.value);
                if (this.productSelect) {
                    this.productSelect.value = '{{ $supply->product_id }}';
                }
            }

            // Charger les villes de la région actuelle
            if (this.regionSelect.value) {
                await this.loadCities(this.regionSelect.value);
                const citySelects = document.querySelectorAll('.city-select');
                const cityData = @json($supply->cities->pluck('id'));
                citySelects.forEach((select, index) => {
                    if (cityData[index]) {
                        select.value = cityData[index];
                    }
                });
            }

            // Charger les camions
            await this.loadAvailableTrucks();
            const truckSelects = document.querySelectorAll('.truck-select');
            const truckData = @json($supply->cities->pluck('pivot.truck_id'));
            truckSelects.forEach((select, index) => {
                if (truckData[index]) {
                    select.value = truckData[index];
                }
            });
        } catch (error) {
            console.error('Erreur lors du chargement des données initiales:', error);
            toastr.error('Erreur lors du chargement des données initiales');
        }
    }

    initializeEventListeners() {
        this.categorySelect.addEventListener('change', () => this.loadProducts(this.categorySelect.value));
        this.regionSelect.addEventListener('change', () => this.loadCities(this.regionSelect.value));
        this.addCityButton.addEventListener('click', () => this.addCityEntry());

        this.citiesContainer.addEventListener('click', (e) => {
            if (e.target.closest('.remove-city')) {
                e.target.closest('.city-entry').remove();
                this.updateCityIndices();
            }
        });

        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
    }

    async loadProducts(categoryId) {
        try {
            const response = await this.fetchWithAuth(`/supplies/products/category/${categoryId}`);
            const data = await response.json();
            
            if (!this.productSelect) {
                console.error('Select des produits non trouvé');
                return;
            }

            this.productSelect.innerHTML = '<option value="">Sélectionner un produit</option>';
            
            if (data.success && Array.isArray(data.products)) {
                data.products.forEach(product => {
                    const option = document.createElement('option');
                    option.value = product.id;
                    option.textContent = product.name;
                    this.productSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Erreur lors du chargement des produits:', error);
            toastr.error('Erreur lors du chargement des produits');
        }
    }

    async loadCities(regionId) {
        try {
            const response = await this.fetchWithAuth(`/supplies/cities/region/${regionId}`);
            const data = await response.json();
            
            const citySelects = document.querySelectorAll('.city-select');
            if (!citySelects.length) {
                console.error('Aucun select de ville trouvé');
                return;
            }

            citySelects.forEach(select => {
                const currentValue = select.value;
                select.innerHTML = '<option value="">Sélectionner une ville</option>';
                
                if (data.success && Array.isArray(data.cities)) {
                    data.cities.forEach(city => {
                        const option = document.createElement('option');
                        option.value = city.id;
                        option.textContent = city.name;
                        select.appendChild(option);
                    });
                }
                
                if (currentValue) {
                    select.value = currentValue;
                }
            });
        } catch (error) {
            console.error('Erreur lors du chargement des villes:', error);
            toastr.error('Erreur lors du chargement des villes');
        }
    }

    async loadAvailableTrucks() {
        try {
            const response = await this.fetchWithAuth('/supplies/trucks/list');
            const data = await response.json();
            
            const truckSelects = document.querySelectorAll('.truck-select');
            if (!truckSelects.length) {
                console.error('Aucun select de camion trouvé');
                return;
            }

            truckSelects.forEach(select => {
                const currentValue = select.value;
                select.innerHTML = '<option value="">Sélectionner un camion</option>';

                if (Array.isArray(data)) {
                    data.forEach(truck => {
                        if (truck.is_available) {
                            const option = document.createElement('option');
                            option.value = truck.id;
                            option.textContent = `${truck.registration_number} - ${truck.capacity?.weight || 0} ${truck.capacity?.unit || ''} - ${truck.driver?.name || 'Pas de chauffeur'}`;
                            select.appendChild(option);
                            this.vehicles.set(truck.id, truck);
                        }
                    });
                }

                if (currentValue) {
                    select.value = currentValue;
                }
            });
        } catch (error) {
            console.error('Erreur lors du chargement des camions:', error);
            toastr.error('Erreur lors du chargement des camions');
        }
    }

    addCityEntry() {
        const template = this.cityTemplate.content.cloneNode(true);
        const newEntry = template.querySelector('.city-entry');
        
        // Mettre à jour les indices
        const inputs = newEntry.querySelectorAll('[name*="[INDEX]"]');
        inputs.forEach(input => {
            input.name = input.name.replace('INDEX', this.cityIndex);
        });

        this.cityIndex++;
        this.citiesContainer.appendChild(newEntry);

        // Charger les données pour le nouvel élément
        if (this.regionSelect.value) {
            this.loadCities(this.regionSelect.value);
        }
        this.loadAvailableTrucks();
    }

    updateCityIndices() {
        const cityEntries = this.citiesContainer.querySelectorAll('.city-entry');
        cityEntries.forEach((entry, index) => {
            const inputs = entry.querySelectorAll('[name*="cities["]');
            inputs.forEach(input => {
                input.name = input.name.replace(/cities\[\d+\]/, `cities[${index}]`);
            });
        });
        this.cityIndex = cityEntries.length;
    }

    handleSubmit(e) {
        e.preventDefault();
        
        const cityEntries = this.citiesContainer.querySelectorAll('.city-entry');
        if (!cityEntries.length) {
            toastr.error('Veuillez ajouter au moins une ville');
            return;
        }

        this.form.submit();
    }
}

document.addEventListener('DOMContentLoaded', () => {
    window.supplyManager = new SupplyManager();
});
</script>
@endpush
