@extends('layouts.admin_minimal')

@section('title', 'Gestion des Véhicules')

@section('content')
<div class="container-fluid py-4">
    <!-- Header avec titre et actions -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-truck text-primary me-2"></i>
                Gestion des Véhicules
            </h1>
            <p class="text-muted mb-0">Gérez votre flotte de véhicules et suivez leur statut</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" id="toggleView" title="Changer la vue">
                <i class="fas fa-th-large" id="viewIcon"></i>
            </button>
            <a href="{{ route('admin.trucks.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Nouveau Véhicule
            </a>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Véhicules Disponibles
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $trucks->where('status', 'available')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                En Maintenance
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $trucks->where('status', 'maintenance')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tools fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Occupés
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $trucks->where('status', 'busy')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shipping-fast fa-2x text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Véhicules
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $trucks->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-truck fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres et recherche -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>
                Filtres et Recherche
            </h6>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="searchInput" class="form-label">Rechercher</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="searchInput" placeholder="Immatriculation, marque, modèle...">
                    </div>
                </div>
                <div class="col-md-3">
                    <label for="statusFilter" class="form-label">Statut</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">Tous les statuts</option>
                        <option value="available">Disponible</option>
                        <option value="maintenance">En maintenance</option>
                        <option value="busy">Occupé</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="brandFilter" class="form-label">Marque</label>
                    <select class="form-select" id="brandFilter">
                        <option value="">Toutes les marques</option>
                        @foreach($trucks->pluck('brand')->unique()->sort() as $brand)
                            <option value="{{ $brand }}">{{ $brand }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="button" class="btn btn-outline-secondary w-100" id="clearFilters">
                        <i class="fas fa-times me-1"></i> Effacer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Vue en cartes (par défaut) -->
    <div id="cardView" class="trucks-view">
        <div class="row" id="trucksContainer">
            @forelse($trucks as $truck)
                <div class="col-xl-4 col-lg-6 col-md-6 mb-4 truck-card"
                     data-status="{{ $truck->status }}"
                     data-brand="{{ $truck->brand }}"
                     data-search="{{ strtolower($truck->registration_number . ' ' . $truck->brand . ' ' . $truck->model) }}">
                    <div class="card shadow-sm h-100 truck-item border-left-{{ $truck->status === 'available' ? 'success' : ($truck->status === 'maintenance' ? 'warning' : 'danger') }}">
                        <div class="card-header bg-transparent border-0 pb-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="card-title mb-1 font-weight-bold">{{ $truck->registration_number }}</h6>
                                    <p class="text-muted small mb-0">{{ $truck->brand }} {{ $truck->model }}</p>
                                </div>
                                <span class="badge bg-{{ $truck->status === 'available' ? 'success' : ($truck->status === 'maintenance' ? 'warning' : 'danger') }} status-badge">
                                    <i class="fas fa-{{ $truck->status === 'available' ? 'check-circle' : ($truck->status === 'maintenance' ? 'tools' : 'shipping-fast') }} me-1"></i>
                                    {{ $truck->status === 'available' ? 'Disponible' : ($truck->status === 'maintenance' ? 'Maintenance' : 'Occupé') }}
                                </span>
                            </div>
                        </div>
                        <div class="card-body pt-2">
                            <div class="row text-center mb-3">
                                <div class="col-6">
                                    <div class="border-end">
                                        <div class="text-muted small">Année</div>
                                        <div class="font-weight-bold">{{ $truck->year }}</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-muted small">Capacité</div>
                                    <div class="font-weight-bold">
                                        @if($truck->capacity)
                                            {{ number_format($truck->capacity->tonnage, 1) }}T
                                        @else
                                            <span class="text-muted">N/A</span>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            @if($truck->capacity)
                                <div class="mb-3">
                                    <small class="text-muted">Type de capacité</small>
                                    <div class="small font-weight-bold">{{ $truck->capacity->description }}</div>
                                </div>
                            @endif

                            @if($truck->notes)
                                <div class="mb-3">
                                    <small class="text-muted">Notes</small>
                                    <div class="small">{{ Str::limit($truck->notes, 60) }}</div>
                                </div>
                            @endif
                        </div>
                        <div class="card-footer bg-transparent border-0 pt-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.trucks.edit', $truck->id) }}"
                                       class="btn btn-outline-warning btn-sm"
                                       title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button"
                                            class="btn btn-outline-danger btn-sm delete-truck"
                                            data-id="{{ $truck->id }}"
                                            data-registration="{{ $truck->registration_number }}"
                                            title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-truck me-1"></i>
                                    ID: {{ $truck->id }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucun véhicule enregistré</h5>
                            <p class="text-muted mb-4">Commencez par ajouter votre premier véhicule à la flotte.</p>
                            <a href="{{ route('admin.trucks.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> Ajouter un véhicule
                            </a>
                        </div>
                    </div>
                </div>
            @endforelse
        </div>
    </div>

    <!-- Vue en tableau (cachée par défaut) -->
    <div id="tableView" class="trucks-view" style="display: none;">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-table me-2"></i>
                    Liste des Véhicules
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="trucksTable">
                        <thead class="table-light">
                            <tr>
                                <th>Immatriculation</th>
                                <th>Véhicule</th>
                                <th>Année</th>
                                <th>Capacité</th>
                                <th>Statut</th>
                                <th>Notes</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($trucks as $truck)
                            <tr class="truck-row"
                                data-status="{{ $truck->status }}"
                                data-brand="{{ $truck->brand }}"
                                data-search="{{ strtolower($truck->registration_number . ' ' . $truck->brand . ' ' . $truck->model) }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="status-indicator bg-{{ $truck->status === 'available' ? 'success' : ($truck->status === 'maintenance' ? 'warning' : 'danger') }}"></div>
                                        <strong>{{ $truck->registration_number }}</strong>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="font-weight-bold">{{ $truck->brand }} {{ $truck->model }}</div>
                                    </div>
                                </td>
                                <td>{{ $truck->year }}</td>
                                <td>
                                    @if($truck->capacity)
                                        <div>
                                            <strong>{{ number_format($truck->capacity->tonnage, 1) }}T</strong>
                                            <div class="small text-muted">{{ $truck->capacity->description }}</div>
                                        </div>
                                    @else
                                        <span class="text-muted">Non définie</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-{{ $truck->status === 'available' ? 'success' : ($truck->status === 'maintenance' ? 'warning' : 'danger') }}">
                                        <i class="fas fa-{{ $truck->status === 'available' ? 'check-circle' : ($truck->status === 'maintenance' ? 'tools' : 'shipping-fast') }} me-1"></i>
                                        {{ $truck->status === 'available' ? 'Disponible' : ($truck->status === 'maintenance' ? 'Maintenance' : 'Occupé') }}
                                    </span>
                                </td>
                                <td>
                                    @if($truck->notes)
                                        <span title="{{ $truck->notes }}">{{ Str::limit($truck->notes, 30) }}</span>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ route('admin.trucks.edit', $truck->id) }}"
                                           class="btn btn-sm btn-outline-warning"
                                           title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-danger delete-truck"
                                                data-id="{{ $truck->id }}"
                                                data-registration="{{ $truck->registration_number }}"
                                                title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light fixed-bottom">
    <div class="container text-center">
        <span class="text-muted"> {{ date('Y') }} GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer le véhicule <span id="truckRegistration"></span> ?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Supprimer</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* Styles généraux */
    .border-left-success {
        border-left: 4px solid #1cc88a !important;
    }
    .border-left-warning {
        border-left: 4px solid #f6c23e !important;
    }
    .border-left-danger {
        border-left: 4px solid #e74a3b !important;
    }
    .border-left-info {
        border-left: 4px solid #36b9cc !important;
    }

    /* Cartes de véhicules */
    .truck-item {
        transition: all 0.3s ease;
        border: 1px solid #e3e6f0;
    }
    .truck-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        border-radius: 0.5rem;
    }

    /* Indicateur de statut pour le tableau */
    .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
        display: inline-block;
    }

    /* Animations et transitions */
    .truck-card, .truck-row {
        transition: all 0.3s ease;
    }

    .truck-card.filtered-out, .truck-row.filtered-out {
        display: none !important;
    }

    /* Styles pour les filtres */
    .form-control:focus, .form-select:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }

    /* Boutons d'action */
    .btn-group .btn {
        margin-right: 2px;
    }
    .btn-group .btn:last-child {
        margin-right: 0;
    }

    /* Vue responsive */
    @media (max-width: 768px) {
        .truck-item .card-body .row {
            text-align: left !important;
        }
        .truck-item .card-body .border-end {
            border-right: none !important;
            margin-bottom: 1rem;
        }
    }

    /* Styles pour les statistiques */
    .card.border-left-success,
    .card.border-left-warning,
    .card.border-left-danger,
    .card.border-left-info {
        border-radius: 0.5rem;
    }

    /* Animation de chargement */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    /* Styles pour le toggle de vue */
    #toggleView {
        transition: all 0.3s ease;
    }
    #toggleView:hover {
        transform: scale(1.05);
    }

    /* Amélioration du tableau */
    .table th {
        background-color: #f8f9fc;
        border-color: #e3e6f0;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
    }

    .table td {
        vertical-align: middle;
        border-color: #e3e6f0;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(78, 115, 223, 0.05);
    }

    /* Message d'état vide */
    .empty-state {
        padding: 3rem 1rem;
    }

    /* Amélioration des badges */
    .badge {
        font-weight: 500;
        letter-spacing: 0.5px;
    }

    /* Styles pour les notes tronquées */
    [title] {
        cursor: help;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    let truckIdToDelete = null;
    let currentView = 'card'; // 'card' ou 'table'

    // Éléments DOM
    const cardView = document.getElementById('cardView');
    const tableView = document.getElementById('tableView');
    const toggleViewBtn = document.getElementById('toggleView');
    const viewIcon = document.getElementById('viewIcon');
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const brandFilter = document.getElementById('brandFilter');
    const clearFiltersBtn = document.getElementById('clearFilters');

    // Gestionnaire pour le changement de vue
    toggleViewBtn.addEventListener('click', function() {
        if (currentView === 'card') {
            // Passer à la vue tableau
            cardView.style.display = 'none';
            tableView.style.display = 'block';
            viewIcon.className = 'fas fa-th-large';
            currentView = 'table';
            this.title = 'Vue en cartes';
        } else {
            // Passer à la vue cartes
            cardView.style.display = 'block';
            tableView.style.display = 'none';
            viewIcon.className = 'fas fa-table';
            currentView = 'card';
            this.title = 'Vue en tableau';
        }

        // Réappliquer les filtres après le changement de vue
        applyFilters();
    });

    // Fonction de filtrage
    function applyFilters() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value;
        const brandValue = brandFilter.value;

        // Sélectionner les éléments selon la vue actuelle
        const items = currentView === 'card'
            ? document.querySelectorAll('.truck-card')
            : document.querySelectorAll('.truck-row');

        let visibleCount = 0;

        items.forEach(item => {
            const searchData = item.dataset.search;
            const statusData = item.dataset.status;
            const brandData = item.dataset.brand;

            let shouldShow = true;

            // Filtre de recherche
            if (searchTerm && !searchData.includes(searchTerm)) {
                shouldShow = false;
            }

            // Filtre de statut
            if (statusValue && statusData !== statusValue) {
                shouldShow = false;
            }

            // Filtre de marque
            if (brandValue && brandData !== brandValue) {
                shouldShow = false;
            }

            // Appliquer la visibilité
            if (shouldShow) {
                item.classList.remove('filtered-out');
                visibleCount++;
            } else {
                item.classList.add('filtered-out');
            }
        });

        // Afficher un message si aucun résultat
        updateEmptyState(visibleCount === 0);
    }

    // Fonction pour afficher/masquer le message d'état vide
    function updateEmptyState(isEmpty) {
        let emptyStateElement = document.getElementById('emptyState');

        if (isEmpty) {
            if (!emptyStateElement) {
                emptyStateElement = document.createElement('div');
                emptyStateElement.id = 'emptyState';
                emptyStateElement.className = 'col-12';
                emptyStateElement.innerHTML = `
                    <div class="card shadow-sm">
                        <div class="card-body text-center py-5 empty-state">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucun véhicule trouvé</h5>
                            <p class="text-muted mb-4">Aucun véhicule ne correspond aux critères de recherche.</p>
                            <button type="button" class="btn btn-outline-primary" onclick="clearAllFilters()">
                                <i class="fas fa-times me-1"></i> Effacer les filtres
                            </button>
                        </div>
                    </div>
                `;

                if (currentView === 'card') {
                    document.getElementById('trucksContainer').appendChild(emptyStateElement);
                } else {
                    // Pour la vue tableau, on peut ajouter une ligne
                    const tbody = document.querySelector('#trucksTable tbody');
                    const emptyRow = document.createElement('tr');
                    emptyRow.id = 'emptyStateRow';
                    emptyRow.innerHTML = `
                        <td colspan="7" class="text-center py-5">
                            <i class="fas fa-search fa-2x text-muted mb-3"></i>
                            <div class="h6 text-muted">Aucun véhicule trouvé</div>
                            <p class="text-muted mb-3">Aucun véhicule ne correspond aux critères de recherche.</p>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="clearAllFilters()">
                                <i class="fas fa-times me-1"></i> Effacer les filtres
                            </button>
                        </td>
                    `;
                    tbody.appendChild(emptyRow);
                }
            }
        } else {
            if (emptyStateElement) {
                emptyStateElement.remove();
            }
            const emptyRow = document.getElementById('emptyStateRow');
            if (emptyRow) {
                emptyRow.remove();
            }
        }
    }

    // Fonction globale pour effacer tous les filtres
    window.clearAllFilters = function() {
        searchInput.value = '';
        statusFilter.value = '';
        brandFilter.value = '';
        applyFilters();
    };

    // Gestionnaires d'événements pour les filtres
    searchInput.addEventListener('input', applyFilters);
    statusFilter.addEventListener('change', applyFilters);
    brandFilter.addEventListener('change', applyFilters);
    clearFiltersBtn.addEventListener('click', window.clearAllFilters);

    // Gestionnaire pour le bouton de suppression
    document.addEventListener('click', function(e) {
        if (e.target.closest('.delete-truck')) {
            const button = e.target.closest('.delete-truck');
            const truckId = button.dataset.id;
            const registration = button.dataset.registration;
            truckIdToDelete = truckId;

            document.getElementById('truckRegistration').textContent = registration;
            deleteModal.show();
        }
    });

    // Gestionnaire pour la confirmation de suppression
    document.getElementById('confirmDelete').addEventListener('click', async function() {
        if (!truckIdToDelete) return;

        // Ajouter un indicateur de chargement
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Suppression...';
        this.disabled = true;

        try {
            const response = await fetch(`/admin/trucks/${truckIdToDelete}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                }
            });

            const result = await response.json();

            if (response.ok) {
                // Recharger la page après la suppression
                window.location.reload();
            } else {
                alert(result.message || 'Une erreur est survenue');
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Une erreur est survenue lors de la suppression');
        } finally {
            // Restaurer le bouton
            this.innerHTML = 'Supprimer';
            this.disabled = false;
            deleteModal.hide();
        }
    });

    // Animation d'entrée pour les cartes
    function animateCards() {
        const cards = document.querySelectorAll('.truck-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            setTimeout(() => {
                card.style.transition = 'all 0.3s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // Lancer l'animation au chargement
    animateCards();
});
</script>
@endpush
