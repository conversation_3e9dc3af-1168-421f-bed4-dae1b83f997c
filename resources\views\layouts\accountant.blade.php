@php
    use Illuminate\Support\Str;
@endphp
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="app-url" content="{{ url('/') }}">
    <title>@yield('title') - {{ config('app.name', 'Grad<PERSON>') }}</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    @stack('styles')

    <!-- Custom CSS -->
    <style>
        :root {
            /* Palette de couleurs bleue comme préféré par l'utilisateur */
            --primary-color: #1E88E5;
            --primary-light: #64B5F6;
            --primary-dark: #0D47A1;
            --secondary-color: #475569;
            --success-color: #2E7D32;
            --warning-color: #F57C00;
            --danger-color: #D32F2F;
            --light-color: #ECEFF1;
            --dark-color: #263238;
            
            /* Dégradé bleu pour le fond */
            --gradient-primary: linear-gradient(to right bottom, #1E88E5, #0D47A1);
        }
        
        /* Wrapper pour le contenu principal */
        body {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            padding-bottom: 60px;
            background-color: #f8fafc;
            font-family: 'Poppins', sans-serif;
        }
        
        /* Style pour la section des statistiques financieres en haut */
        .dashboard-header {
            background: var(--gradient-primary);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
            color: white;
            box-shadow: 0 5px 15px rgba(13, 71, 161, 0.2);
        }
        
        .dashboard-header-content {
            position: relative;
            z-index: 2;
        }
        
        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .dashboard-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 1.5rem;
        }
        
        .header-data-item {
            background-color: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 12px;
            margin-bottom: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .header-data-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .header-data-value {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .header-data-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        #wrapper {
            display: flex;
            flex: 1;
        }

        /* Sidebar moderne */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, #1E293B 0%, #0F172A 100%);
            overflow-y: auto;
            transition: all 0.3s ease;
            z-index: 1030;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            background-color: rgba(0, 0, 0, 0.2);
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            gap: 1rem;
        }
        
        .sidebar-logo {
            height: 40px;
            width: auto;
        }
        
        .sidebar-title {
            color: white;
            margin: 0;
            font-weight: 600;
            letter-spacing: 1px;
        }
        
        /* Profil utilisateur dans la sidebar */
        .sidebar-user {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .sidebar-user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .sidebar-user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .sidebar-user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .sidebar-avatar-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
            font-size: 1.2rem;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        .sidebar-user-details {
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .sidebar-user-name {
            color: #fff;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
            font-weight: 600;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .sidebar-user-role {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.75rem;
            background-color: rgba(255, 255, 255, 0.1);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            display: inline-block;
            margin-top: 0.25rem;
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar.collapsed .nav-text,
        .sidebar.collapsed .sidebar-header img {
            display: none;
        }

        /* Content Wrapper */
        #content-wrapper {
            flex: 1;
            margin-left: 280px; /* Même largeur que la sidebar */
            min-width: 0;
            transition: margin-left 0.3s ease;
        }

        #content {
            padding: 20px;
        }


        

        
        /* Navigation */
        .nav-item {
            margin-bottom: 0.5rem;
        }

                /* Menu de la sidebar */
        .sidebar-menu-header {
            color: rgba(255, 255, 255, 0.4);
            font-size: 0.75rem;
            font-weight: 600;
            padding: 1.5rem 1.5rem 0.75rem;
            letter-spacing: 1px;
        }
        
        .sidebar-nav {
            padding: 0 0.75rem;
            margin-bottom: 1.5rem;
        }
        
        .nav-link {
            color: rgba(255, 255, 255, 0.7);
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 0.5rem;
            position: relative;
            margin-bottom: 0.25rem;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .nav-link.active {
            background: rgba(37, 99, 235, 0.2);
            color: white;
        }

        .nav-link i {
            width: 24px;
            margin-right: 12px;
            text-align: center;
            font-size: 1rem;
        }
        
        .nav-text {
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .active-indicator {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background-color: var(--primary-color);
            border-radius: 4px;
        }
        
        /* Pied de page de la sidebar */
        .sidebar-footer {
            margin-top: auto;
            padding: 1rem;
            text-align: center;
            color: rgba(255, 255, 255, 0.3);
            font-size: 0.75rem;
            border-top: 1px solid rgba(255, 255, 255, 0.05);
        }

        /* Cards */
        .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            margin-bottom: 1.5rem;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 1rem;
        }

        /* Tables */
        .table {
            margin-bottom: 0;
        }

        .table th {
            border-top: none;
            background-color: var(--light-color);
        }

        /* Buttons */
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
        }

        /* Styles pour le modal */
        .modal-content {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            border-top-left-radius: 1rem;
            border-top-right-radius: 1rem;
            padding: 1.5rem;
        }

        .modal-body {
            padding: 2rem;
        }

        .modal-footer {
            border-bottom-left-radius: 1rem;
            border-bottom-right-radius: 1rem;
            padding: 1.5rem;
        }

        .modal .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            transition: all 0.2s ease;
        }

        .modal .btn-primary:hover {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
            transform: translateY(-1px);
        }


        
        .nav-link {
            color: var(--secondary-color);
            padding: 0.5rem;
            display: flex;
            align-items: center;
            font-size: 1.25rem;
            position: relative;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }
        
        .nav-link:hover {
            color: var(--primary-color);
            background-color: rgba(37, 99, 235, 0.1);
        }
        
        .user-dropdown .nav-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1rem;
            position: relative;
        }
        
        .avatar-container {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--primary-color);
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 2px solid rgba(255, 255, 255, 0.2);
            flex-shrink: 0;
            position: relative;
            z-index: 1030;
        }
        
        .avatar-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .avatar-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.25rem;
        }
        
        .avatar-placeholder-lg {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
            font-size: 1.5rem;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        .dropdown-user-details {
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-avatar-dropdown {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        
        .user-avatar-dropdown img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .user-info-dropdown {
            display: flex;
            flex-direction: column;
        }
        
        .user-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .user-email {
            font-size: 0.875rem;
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
        }
        
        .user-role {
            align-self: flex-start;
        }
        
        /* Notifications */
        .notifications-dropdown {
            width: 320px;
            padding: 0;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: none;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .dropdown-header {
            background-color: #f8f9fa;
            padding: 0.75rem 1rem;
            font-weight: 600;
        }
        
        .notification-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            transition: background-color 0.2s ease;
        }
        
        .notification-item:hover {
            background-color: rgba(37, 99, 235, 0.05);
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .notification-content {
            flex: 1;
        }
        
        .notification-text {
            margin-bottom: 0.25rem;
            font-weight: 500;
        }
        
        .notification-time {
            font-size: 0.75rem;
            color: var(--secondary-color);
            margin: 0;
        }

        /* Form Controls */
        .form-control {
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            transition: all 0.2s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
        }

        /* Select */
        .select-wrapper {
            position: relative;
        }

        .select-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none;
        }

        .select-icon i {
            color: #64748b;
            transition: transform 0.2s ease;
        }

        select.form-control {
            appearance: none;
            padding-right: 2.5rem;
        }

        select:focus + .select-icon i {
            transform: rotate(180deg);
            color: #4f46e5;
        }

        /* Alerts */
        .alert {
            border: none;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: #dcfce7;
            color: #166534;
        }

        .alert-danger {
            background-color: #fee2e2;
            color: #991b1b;
        }

        /* Badges */
        .badge {
            padding: 0.35rem 0.65rem;
            border-radius: 9999px;
            font-weight: 500;
        }

        .badge-primary {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .badge-success {
            background-color: #dcfce7;
            color: #166534;
        }

        /* Hover Effects */
        .btn-light-hover {
            background: #f1f5f9;
            color: #475569;
            border: none;
        }

        .btn-light-hover:hover {
            background: #e2e8f0;
            color: #1e293b;
        }

        /* Bouton flottant pour mobile */
        .mobile-sidebar-toggle {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1050;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .mobile-sidebar-toggle:hover {
            background-color: #1d4ed8;
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }

        .mobile-sidebar-toggle:active {
            transform: scale(0.95);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            #content-wrapper {
                margin-left: 0;
            }

            .sticky-footer {
                left: 0; /* Footer prend toute la largeur en mobile */
                width: 100%;
                height: 50px;
                font-size: 0.875rem;
            }

            body {
                padding-bottom: 50px;
            }

            /* Overlay pour fermer la sidebar */
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1020;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            /* Quand la sidebar est active */
            .sidebar-active .sidebar {
                transform: translateX(0);
                z-index: 1050;
            }

            .sidebar-active .sidebar-overlay {
                opacity: 1;
                visibility: visible;
            }

            .sidebar.collapsed + #content-wrapper {
                margin-left: 70px;
            }



            .sidebar-active .sticky-footer {
                left: 0; /* Footer reste en pleine largeur même avec sidebar active */
            }
        }

        /* Scrollbar personnalisée */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* Footer */
        .sticky-footer {
            position: fixed;
            bottom: 0;
            right: 0;
            left: 250px; /* Ajout de left pour le positionnement */
            width: auto; /* Remplacer width fixe par auto */
            height: 60px;
            background-color: #fff;
            border-top: 1px solid #e3e6f0;
            z-index: 1030;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <div id="wrapper">
        <!-- Sidebar moderne -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <img src="{{ asset('assets/images/logo_mini_gradis.png') }}" alt="Logo" class="sidebar-logo">
                <h5 class="sidebar-title">GRADIS</h5>
            </div>
            
            <!-- Profil utilisateur dans la sidebar -->
            <div class="sidebar-user">
                <div class="sidebar-user-avatar">
                    @if(Auth::user()->avatar)
                        <img src="{{ Str::startsWith(Auth::user()->avatar, 'avatars/') ? asset('storage/' . Auth::user()->avatar) : asset(Auth::user()->avatar) }}" alt="Avatar" class="sidebar-avatar-img">
                    @else
                        <div class="sidebar-avatar-placeholder">
                            {{ substr(Auth::user()->name, 0, 1) }}
                        </div>
                    @endif
                </div>
                <div class="sidebar-user-details">
                    <h6 class="sidebar-user-name">{{ Auth::user()->name }}</h6>
                    <span class="sidebar-user-role">Comptable</span>
                </div>
            </div>
            
            <!-- Menu principal -->
            <div class="sidebar-menu-header">MENU PRINCIPAL</div>
            <ul class="nav flex-column sidebar-nav">
                <li class="nav-item">
                    <a href="{{ route('accountant.dashboard') }}" class="nav-link {{ request()->routeIs('accountant.dashboard') ? 'active' : '' }}">
                        <i class="fas fa-home"></i>
                        <span class="nav-text">Tableau de bord</span>
                        @if(request()->routeIs('accountant.dashboard'))
                            <span class="active-indicator"></span>
                        @endif
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('accountant.supplies.index') }}" class="nav-link {{ request()->routeIs('accountant.supplies.*') ? 'active' : '' }}">
                        <i class="fas fa-box"></i>
                        <span class="nav-text">Approvisionnements</span>
                        @if(request()->routeIs('accountant.supplies.*'))
                            <span class="active-indicator"></span>
                        @endif
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('accountant.suppliers.index') }}" class="nav-link {{ request()->routeIs('accountant.suppliers.*') ? 'active' : '' }}">
                        <i class="fas fa-industry"></i>
                        <span class="nav-text">Fournisseurs</span>
                        @if(request()->routeIs('accountant.suppliers.*'))
                            <span class="active-indicator"></span>
                        @endif
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('accountant.drivers.index') }}" class="nav-link {{ request()->routeIs('accountant.drivers.*') ? 'active' : '' }}">
                        <i class="fas fa-truck"></i>
                        <span class="nav-text">Chauffeurs</span>
                        @if(request()->routeIs('accountant.drivers.*'))
                            <span class="active-indicator"></span>
                        @endif
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('accountant.recoveries.index') }}" class="nav-link {{ request()->routeIs('accountant.recoveries.*') ? 'active' : '' }}">
                        <i class="fas fa-hand-holding-usd"></i>
                        <span class="nav-text">Recouvrements</span>
                        @if(request()->routeIs('accountant.recoveries.*'))
                            <span class="active-indicator"></span>
                        @endif
                    </a>
                </li>
            </ul>
            
            <!-- Paramètres -->
            <div class="sidebar-menu-header">PARAMÈTRES</div>
            <ul class="nav flex-column sidebar-nav">
                <li class="nav-item">
                    <a href="{{ route('accountant.profile.edit') }}" class="nav-link {{ request()->routeIs('accountant.profile.*') ? 'active' : '' }}">
                        <i class="fas fa-user-cog"></i>
                        <span class="nav-text">Mon profil</span>
                        @if(request()->routeIs('accountant.profile.*'))
                            <span class="active-indicator"></span>
                        @endif
                    </a>
                </li>
                <li class="nav-item">
                    <form method="POST" action="{{ route('logout') }}" id="sidebar-logout-form">
                        @csrf
                        <a href="#" onclick="event.preventDefault(); document.getElementById('sidebar-logout-form').submit();" class="nav-link">
                            <i class="fas fa-sign-out-alt"></i>
                            <span class="nav-text">Déconnexion</span>
                        </a>
                    </form>
                </li>
            </ul>
            
            <!-- Version -->
            <div class="sidebar-footer">
                <span class="version">v1.0.0</span>
            </div>
        </nav>

        <!-- Bouton flottant pour ouvrir la sidebar sur mobile -->
        <button id="mobileSidebarToggle" class="mobile-sidebar-toggle d-md-none">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Overlay pour fermer la sidebar sur mobile -->
        <div id="sidebarOverlay" class="sidebar-overlay d-md-none"></div>

        <!-- Content Wrapper -->
        <div id="content-wrapper">
            <!-- Main Content -->
            <div id="content">


                <!-- Main Content Area -->
                @yield('content')
            </div>

            <!-- Footer -->
            <footer class="sticky-footer">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span> 2025 GRADIS. Tous droits réservés. Développé par <a href="#" class="text-primary text-decoration-none">MOMK-Solutions</a></span>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Bootstrap 5 Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile Sidebar Toggle
            const mobileSidebarToggle = document.getElementById('mobileSidebarToggle');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const sidebar = document.getElementById('sidebar');

            // Fonction pour ouvrir la sidebar
            function openSidebar() {
                document.body.classList.add('sidebar-active');
            }

            // Fonction pour fermer la sidebar
            function closeSidebar() {
                document.body.classList.remove('sidebar-active');
            }

            // Bouton mobile pour ouvrir la sidebar
            if (mobileSidebarToggle) {
                mobileSidebarToggle.addEventListener('click', function() {
                    openSidebar();
                });
            }

            // Overlay pour fermer la sidebar
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function() {
                    closeSidebar();
                });
            }

            // Fermer la sidebar avec la touche Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && document.body.classList.contains('sidebar-active')) {
                    closeSidebar();
                }
            });

            // Setup CSRF token for AJAX requests
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
        });
    </script>

    @stack('scripts')
</body>
</html>
