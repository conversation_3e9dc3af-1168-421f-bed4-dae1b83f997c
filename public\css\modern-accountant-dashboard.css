/* Styles modernes pour le tableau de bord comptable - Version améliorée */
:root {
    --primary-color: #1E88E5;
    --primary-dark: #0D47A1;
    --primary-light: #64B5F6;
    --secondary-color: #0288D1;
    --accent-color: #00BCD4;
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --danger-color: #F44336;
    --info-color: #2196F3;
    --dark-color: #263238;
    --light-color: #ECEFF1;
    --white-color: #FFFFFF;
    --gray-color: #607D8B;
    --gray-light: #B0BEC5;
    --gray-dark: #455A64;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.2);
    --gradient-primary: linear-gradient(135deg, #1E88E5 0%, #0D47A1 100%);
    --gradient-secondary: linear-gradient(135deg, #0288D1 0%, #01579B 100%);
    --gradient-success: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
    --gradient-warning: linear-gradient(135deg, #FF9800 0%, #E65100 100%);
    --gradient-danger: linear-gradient(135deg, #F44336 0%, #B71C1C 100%);
    --gradient-info: linear-gradient(135deg, #2196F3 0%, #0D47A1 100%);
    --gradient-dark: linear-gradient(135deg, #455A64 0%, #263238 100%);
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 1rem;
    --border-radius-xl: 1.5rem;
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
}

/* Styles généraux */
body {
    font-family: 'Inter', 'Poppins', sans-serif;
    background-color: #f8f9fa;
    color: var(--dark-color);
}

.dashboard-wrapper {
    position: relative;
    overflow-x: hidden;
}

/* En-tête avec dégradé bleu et éléments 3D - Amélioré */
.dashboard-header {
    position: relative;
    background: var(--gradient-primary);
    padding: 3rem 0;
    margin-bottom: 2rem;
    color: var(--white-color);
    overflow: hidden;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    transition: all 0.5s cubic-bezier(0.39, 0.575, 0.565, 1);
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.3;
}

.header-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.header-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
    width: 300px;
    height: 300px;
    top: -150px;
    right: -100px;
}

.circle-2 {
    width: 200px;
    height: 200px;
    bottom: -100px;
    left: 10%;
    background: rgba(255, 255, 255, 0.05);
}

.circle-3 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 20%;
    background: rgba(255, 255, 255, 0.07);
}

.dashboard-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 600px;
    margin-bottom: 1.5rem;
}

.dashboard-header .date-display {
    font-size: 1.1rem;
    opacity: 0.8;
    margin-bottom: 1rem;
}

.dashboard-header .btn-dashboard {
    padding: 0.6rem 1.5rem;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: var(--transition-normal);
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.dashboard-header .btn-dashboard:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.dashboard-header .btn-dashboard i {
    margin-right: 0.5rem;
}

/* Cartes statistiques avec effets 3D et animations - Améliorées */
.stats-section {
    margin-bottom: 2.5rem;
    position: relative;
    z-index: 10;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    position: relative;
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.39, 0.575, 0.565, 1);
    z-index: 1;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.03);
    backdrop-filter: blur(10px);
}

.stat-card:hover {
    transform: translateY(-7px);
    box-shadow: var(--shadow-lg);
    border-color: rgba(30, 136, 229, 0.1);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-primary);
    z-index: 1;
}

.stat-card.primary::before { background: var(--gradient-primary); }
.stat-card.success::before { background: var(--gradient-success); }
.stat-card.info::before { background: var(--gradient-info); }
.stat-card.warning::before { background: var(--gradient-warning); }
.stat-card.danger::before { background: var(--gradient-danger); }

.stat-card-glow {
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(30, 136, 229, 0.15) 0%, rgba(30, 136, 229, 0) 70%);
    border-radius: 50%;
    z-index: -1;
}

.stat-card-glow.success { background: radial-gradient(circle, rgba(76, 175, 80, 0.15) 0%, rgba(76, 175, 80, 0) 70%); }
.stat-card-glow.info { background: radial-gradient(circle, rgba(33, 150, 243, 0.15) 0%, rgba(33, 150, 243, 0) 70%); }
.stat-card-glow.warning { background: radial-gradient(circle, rgba(255, 152, 0, 0.15) 0%, rgba(255, 152, 0, 0) 70%); }
.stat-card-glow.danger { background: radial-gradient(circle, rgba(244, 67, 54, 0.15) 0%, rgba(244, 67, 54, 0) 70%); }

.stat-card-top {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.stat-card-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 4px 10px rgba(30, 136, 229, 0.3);
}

.stat-card.primary .stat-card-icon { background: var(--gradient-primary); box-shadow: 0 4px 10px rgba(30, 136, 229, 0.3); }
.stat-card.success .stat-card-icon { background: var(--gradient-success); box-shadow: 0 4px 10px rgba(76, 175, 80, 0.3); }
.stat-card.info .stat-card-icon { background: var(--gradient-info); box-shadow: 0 4px 10px rgba(33, 150, 243, 0.3); }
.stat-card.warning .stat-card-icon { background: var(--gradient-warning); box-shadow: 0 4px 10px rgba(255, 152, 0, 0.3); }
.stat-card.danger .stat-card-icon { background: var(--gradient-danger); box-shadow: 0 4px 10px rgba(244, 67, 54, 0.3); }

.stat-card-trend {
    font-size: 0.85rem;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.stat-card-trend.up { background: rgba(76, 175, 80, 0.1); color: var(--success-color); }
.stat-card-trend.down { background: rgba(244, 67, 54, 0.1); color: var(--danger-color); }

.stat-card-title {
    font-size: 0.9rem;
    color: var(--gray-color);
    margin-bottom: 0.5rem;
}

.stat-card-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.stat-card-subtitle {
    font-size: 0.85rem;
    color: var(--gray-color);
}

/* Cartes du tableau de bord avec animations */
.dashboard-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: var(--transition-normal);
}

.dashboard-card:hover {
    box-shadow: var(--shadow-lg);
}

.dashboard-card-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-card-title {
    font-weight: 600;
    margin-bottom: 0;
    display: flex;
    align-items: center;
}

.dashboard-card-title i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.dashboard-card-body {
    padding: 1.5rem;
}

/* Statistiques de recouvrement */
.stat-summary {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.25rem;
    box-shadow: var(--shadow-sm);
    height: 100%;
    position: relative;
    overflow: hidden;
    transition: var(--transition-normal);
}

.stat-summary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.stat-summary::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
}

.stat-summary.primary::after { background: var(--gradient-primary); }
.stat-summary.success::after { background: var(--gradient-success); }
.stat-summary.warning::after { background: var(--gradient-warning); }
.stat-summary.danger::after { background: var(--gradient-danger); }

.stat-icon {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin-bottom: 1rem;
    background: rgba(30, 136, 229, 0.1);
    color: var(--primary-color);
}

.stat-summary.primary .stat-icon { background: rgba(30, 136, 229, 0.1); color: var(--primary-color); }
.stat-summary.success .stat-icon { background: rgba(76, 175, 80, 0.1); color: var(--success-color); }
.stat-summary.warning .stat-icon { background: rgba(255, 152, 0, 0.1); color: var(--warning-color); }
.stat-summary.danger .stat-icon { background: rgba(244, 67, 54, 0.1); color: var(--danger-color); }

.stat-title {
    font-size: 0.85rem;
    color: var(--gray-color);
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0;
}

/* Tableau avec design moderne */
.table {
    width: 100%;
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: 1rem;
    border-top: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    color: var(--gray-color);
    background-color: rgba(0, 0, 0, 0.02);
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-top: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table tr:last-child td {
    border-bottom: none;
}

.table tr:hover td {
    background-color: rgba(0, 0, 0, 0.01);
}

/* Badges et étiquettes */
.badge {
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 500;
    border-radius: 50rem;
}

.badge-paid {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.badge-partial {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

.badge-unpaid {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
}

/* Barre de progression */
.progress {
    height: 0.5rem;
    border-radius: 50rem;
    background-color: rgba(0, 0, 0, 0.05);
    margin-bottom: 0;
}

.progress-bar {
    border-radius: 50rem;
}

.progress-bar-paid {
    background: var(--gradient-success);
}

.progress-bar-partial {
    background: var(--gradient-warning);
}

.progress-bar-unpaid {
    background: var(--gradient-danger);
}

/* Actions rapides */
.quick-actions {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

.quick-action {
    display: flex;
    align-items: center;
    padding: 1.25rem;
    border-radius: var(--border-radius-lg);
    background: white;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    text-decoration: none;
    color: var(--dark-color);
}

.quick-action:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
    text-decoration: none;
    color: var(--dark-color);
}

.action-icon {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin-right: 1rem;
    background: rgba(30, 136, 229, 0.1);
    color: var(--primary-color);
}

.quick-action.primary .action-icon { background: rgba(30, 136, 229, 0.1); color: var(--primary-color); }
.quick-action.success .action-icon { background: rgba(76, 175, 80, 0.1); color: var(--success-color); }
.quick-action.info .action-icon { background: rgba(33, 150, 243, 0.1); color: var(--info-color); }
.quick-action.warning .action-icon { background: rgba(255, 152, 0, 0.1); color: var(--warning-color); }
.quick-action.danger .action-icon { background: rgba(244, 67, 54, 0.1); color: var(--danger-color); }

.action-details h6 {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.action-details p {
    font-size: 0.85rem;
    color: var(--gray-color);
    margin-bottom: 0;
}

/* Graphiques et cartes de données - Améliorés */
.chart-card, .table-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.39, 0.575, 0.565, 1);
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.chart-card:hover, .table-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-3px);
    border-color: rgba(30, 136, 229, 0.1);
}

.chart-card .card-header, .table-card .card-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.chart-card .card-header::after, .table-card .card-header::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(30, 136, 229, 0.05) 100%);
    z-index: 1;
    pointer-events: none;
}

.chart-card .card-title, .table-card .card-title {
    font-weight: 600;
    margin-bottom: 0;
    display: flex;
    align-items: center;
}

.chart-card .card-title i, .table-card .card-title i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.chart-card .card-body, .table-card .card-body {
    padding: 1.5rem;
}

.chart-container {
    position: relative;
    height: 300px;
}

/* Activités récentes */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    position: relative;
    padding-left: 2rem;
    padding-bottom: 1rem;
}

.activity-item::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 0;
    width: 2px;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.05);
}

.activity-item::after {
    content: '';
    position: absolute;
    left: 4px;
    top: 8px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--primary-color);
    z-index: 1;
}

.activity-item.success::after { background-color: var(--success-color); }
.activity-item.warning::after { background-color: var(--warning-color); }
.activity-item.danger::after { background-color: var(--danger-color); }
.activity-item.info::after { background-color: var(--info-color); }

.activity-item:last-child {
    padding-bottom: 0;
}

.activity-item:last-child::before {
    height: 20px;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--gray-color);
    margin-bottom: 0.25rem;
}

.activity-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.activity-description {
    font-size: 0.85rem;
    color: var(--gray-color);
    margin-bottom: 0;
}

/* Animations améliorées */
.fadeIn {
    animation: fadeIn 0.6s cubic-bezier(0.39, 0.575, 0.565, 1) forwards;
}

.fadeInUp {
    animation: fadeInUp 0.6s cubic-bezier(0.39, 0.575, 0.565, 1) forwards;
}

.fadeInRight {
    animation: fadeInRight 0.6s cubic-bezier(0.39, 0.575, 0.565, 1) forwards;
}

.fadeInLeft {
    animation: fadeInLeft 0.6s cubic-bezier(0.39, 0.575, 0.565, 1) forwards;
}

.fadeInDown {
    animation: fadeInDown 0.6s cubic-bezier(0.39, 0.575, 0.565, 1) forwards;
}

.zoomIn {
    animation: zoomIn 0.6s cubic-bezier(0.39, 0.575, 0.565, 1) forwards;
}

.delay-1 {
    animation-delay: 0.1s;
}

.delay-2 {
    animation-delay: 0.2s;
}

.delay-3 {
    animation-delay: 0.3s;
}

.delay-4 {
    animation-delay: 0.4s;
}

.delay-5 {
    animation-delay: 0.5s;
}

.delay-6 {
    animation-delay: 0.6s;
}

.delay-7 {
    animation-delay: 0.7s;
}

.delay-8 {
    animation-delay: 0.8s;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 30px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translate3d(30px, 0, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translate3d(-30px, 0, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translate3d(0, -30px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale3d(0.9, 0.9, 0.9);
    }
    to {
        opacity: 1;
        transform: scale3d(1, 1, 1);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.animate-pulse {
    animation: pulse 2s infinite ease-in-out;
}

.animate-float {
    animation: float 6s infinite ease-in-out;
}

/* Notifications - Améliorées */
.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: white;
    border-radius: var(--border-radius-md);
    padding: 1rem;
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    max-width: 350px;
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.39, 0.575, 0.565, 1);
    border-left: 4px solid var(--primary-color);
    backdrop-filter: blur(10px);
}

.notification.success { border-left-color: var(--success-color); }
.notification.warning { border-left-color: var(--warning-color); }
.notification.danger { border-left-color: var(--danger-color); }

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

/* Responsive - Amélioré */
@media (max-width: 992px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 2rem 0;
        border-radius: var(--border-radius-md);
    }
    
    .dashboard-header h1 {
        font-size: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .stat-card-value {
        font-size: 1.75rem;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .dashboard-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .period-filter {
        width: 100%;
        overflow-x: auto;
        white-space: nowrap;
        padding-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .dashboard-header h1 {
        font-size: 1.75rem;
    }
    
    .dashboard-header p {
        font-size: 1rem;
    }
    
    .stat-card-value {
        font-size: 1.5rem;
    }
    
    .chart-container {
        height: 200px;
    }
    
    .table th, .table td {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
    
    .activity-item {
        padding-left: 1.5rem;
    }
}
