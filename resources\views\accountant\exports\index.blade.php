@extends('layouts.accountant')

@section('title', 'Exportation de données')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="dashboard-card slide-in-up">
                <div class="dashboard-card-header">
                    <div class="dashboard-card-title">
                        <i class="fas fa-file-export me-2"></i>Exportation de données
                    </div>
                </div>
                <div class="dashboard-card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-1"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer"></button>
                        </div>
                    @endif

                    <div class="row">
                        <!-- Exportation des ventes -->
                        <div class="col-lg-6 col-md-12 mb-4">
                            <div class="export-card">
                                <div class="export-card-header">
                                    <div class="d-flex align-items-center">
                                        <div class="export-icon bg-primary">
                                            <i class="fas fa-shopping-cart"></i>
                                        </div>
                                        <h4>Ventes</h4>
                                    </div>
                                </div>
                                <div class="export-card-body">
                                    <form action="{{ route('accountant.exports.sales') }}" method="POST" id="export-sales-form">
                                        @csrf
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="sales-start-date">Date de début</label>
                                                    <input type="date" id="sales-start-date" name="start_date" class="form-control">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="sales-end-date">Date de fin</label>
                                                    <input type="date" id="sales-end-date" name="end_date" class="form-control">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="sales-status">Statut</label>
                                                    <select id="sales-status" name="status" class="form-select">
                                                        <option value="">Tous les statuts</option>
                                                        <option value="paid">Payé</option>
                                                        <option value="pending">En attente</option>
                                                        <option value="canceled">Annulé</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-download me-2"></i>Exporter au format CSV
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Exportation des approvisionnements -->
                        <div class="col-lg-6 col-md-12 mb-4">
                            <div class="export-card">
                                <div class="export-card-header">
                                    <div class="d-flex align-items-center">
                                        <div class="export-icon bg-success">
                                            <i class="fas fa-truck"></i>
                                        </div>
                                        <h4>Approvisionnements</h4>
                                    </div>
                                </div>
                                <div class="export-card-body">
                                    <form action="{{ route('accountant.exports.supplies') }}" method="POST" id="export-supplies-form">
                                        @csrf
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="supplies-start-date">Date de début</label>
                                                    <input type="date" id="supplies-start-date" name="start_date" class="form-control">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="supplies-end-date">Date de fin</label>
                                                    <input type="date" id="supplies-end-date" name="end_date" class="form-control">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="supplies-status">Statut</label>
                                                    <select id="supplies-status" name="status" class="form-select">
                                                        <option value="">Tous les statuts</option>
                                                        <option value="completed">Complété</option>
                                                        <option value="pending">En attente</option>
                                                        <option value="rejected">Rejeté</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-download me-2"></i>Exporter au format CSV
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Exportation des paiements -->
                        <div class="col-lg-6 col-md-12 mb-4">
                            <div class="export-card">
                                <div class="export-card-header">
                                    <div class="d-flex align-items-center">
                                        <div class="export-icon bg-info">
                                            <i class="fas fa-money-bill"></i>
                                        </div>
                                        <h4>Paiements</h4>
                                    </div>
                                </div>
                                <div class="export-card-body">
                                    <form action="{{ route('accountant.exports.payments') }}" method="POST" id="export-payments-form">
                                        @csrf
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="payments-start-date">Date de début</label>
                                                    <input type="date" id="payments-start-date" name="start_date" class="form-control">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="payments-end-date">Date de fin</label>
                                                    <input type="date" id="payments-end-date" name="end_date" class="form-control">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="payment-method">Méthode de paiement</label>
                                                    <select id="payment-method" name="payment_method" class="form-select">
                                                        <option value="">Toutes les méthodes</option>
                                                        <option value="cash">Espèces</option>
                                                        <option value="card">Carte bancaire</option>
                                                        <option value="transfer">Virement</option>
                                                        <option value="mobile_money">Mobile Money</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-info">
                                            <i class="fas fa-download me-2"></i>Exporter au format CSV
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Exportation des clients -->
                        <div class="col-lg-6 col-md-12 mb-4">
                            <div class="export-card">
                                <div class="export-card-header">
                                    <div class="d-flex align-items-center">
                                        <div class="export-icon bg-warning">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <h4>Clients</h4>
                                    </div>
                                </div>
                                <div class="export-card-body">
                                    <form action="{{ route('accountant.exports.customers') }}" method="POST" id="export-customers-form">
                                        @csrf
                                        <p>Exportez la liste complète de tous les clients.</p>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-download me-2"></i>Exporter au format CSV
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .export-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        margin-bottom: 20px;
        transition: transform 0.3s, box-shadow 0.3s;
    }
    
    .export-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .export-card-header {
        padding: 15px 20px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .export-card-body {
        padding: 20px;
    }
    
    .export-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 1.5rem;
    }
    
    .export-card-header h4 {
        margin: 0;
        color: #1E88E5;
        font-size: 1.2rem;
        font-weight: 600;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-group label {
        font-weight: 500;
        margin-bottom: 5px;
        display: block;
    }
    
    .btn {
        border-radius: 4px;
        font-weight: 500;
    }
    
    /* Animation */
    .slide-in-up {
        animation: slide-in-up 0.5s ease forwards;
    }
    
    @keyframes slide-in-up {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
@endsection
