/* 
 * VENTES DIRECTES - CAISSIER RESPONSIVE
 * Design professionnel et intuitif avec palette de couleurs bleue
 * Applique la même responsivité que le tableau de bord caissier
 */

:root {
    /* Palette de couleurs principale (tons de bleu) */
    --primary-color: #1E88E5;
    --primary-light: #64B5F6;
    --primary-dark: #0D47A1;
    --primary-gradient: linear-gradient(135deg, #1E88E5, #0D47A1);
    
    /* Couleurs secondaires */
    --success-color: #2E7D32;
    --success-light: #4CAF50;
    --warning-color: #FF8F00;
    --warning-light: #FFB74D;
    --danger-color: #C62828;
    --danger-light: #EF5350;
    
    /* Couleurs neutres */
    --background-color: #F5F7FA;
    --card-bg: #FFFFFF;
    --border-color: #E0E6ED;
    --text-primary: #2D3748;
    --text-secondary: #718096;
    --text-muted: #A0AEC0;
}

/* Styles généraux */
.container-fluid {
    padding: 1.5rem;
    max-width: 100%;
    overflow-x: hidden;
    transition: all 0.3s ease-in-out;
}

/* Compatibilité avec la sidebar responsive */
.dashboard-container {
    transition: all 0.3s ease-in-out;
}

/* Titre de la page */
.page-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-dark);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
}

/* En-tête de page */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.page-actions {
    display: flex;
    gap: 0.75rem;
}

/* Cartes statistiques */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background: var(--card-bg);
    border-radius: 0.75rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.stat-card-header {
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-card-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card-icon.sales {
    background: var(--primary-gradient);
}

.stat-card-icon.payments {
    background: linear-gradient(135deg, var(--success-color), var(--success-light));
}

.stat-card-icon.pending {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-light));
}

.stat-card-icon.customers {
    background: linear-gradient(135deg, #6B46C1, #9F7AEA);
}

.stat-card-content {
    padding: 0.5rem 1rem 1.25rem;
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Tableau moderne */
.modern-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.modern-table thead th {
    background-color: var(--background-color);
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.modern-table tbody td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
    vertical-align: middle;
}

.modern-table tbody tr:last-child td {
    border-bottom: none;
}

.modern-table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.01);
}

/* Badges de statut */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
}

.badge-success {
    background-color: rgba(46, 125, 50, 0.1);
    color: var(--success-color);
}

.badge-warning {
    background-color: rgba(255, 143, 0, 0.1);
    color: var(--warning-color);
}

.badge-danger {
    background-color: rgba(198, 40, 40, 0.1);
    color: var(--danger-color);
}

.badge-info {
    background-color: rgba(30, 136, 229, 0.1);
    color: var(--primary-color);
}

/* Boutons d'action */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-button {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    background-color: transparent;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.action-button:hover {
    background-color: var(--background-color);
    color: var(--primary-color);
}

.action-button.view:hover {
    color: var(--primary-color);
}

.action-button.edit:hover {
    color: var(--warning-color);
}

.action-button.delete:hover {
    color: var(--danger-color);
}

/* Pagination */
.pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 0.25rem;
}

.page-item .page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
    padding: 0 0.5rem;
    border-radius: 6px;
    color: var(--text-secondary);
    background-color: transparent;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.page-item .page-link:hover {
    background-color: var(--background-color);
    color: var(--primary-color);
    border-color: var(--primary-light);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.page-item.disabled .page-link {
    color: var(--text-muted);
    pointer-events: none;
    background-color: transparent;
}

/* Optimisations pour les petits écrans */
@media (max-width: 768px) {
    /* Ajuster le contenu pour les tablettes */
    .container-fluid {
        padding: 1rem;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .page-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .page-title {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        max-width: 100%;
        margin-bottom: 1rem;
    }
    
    .modern-table {
        min-width: 800px; /* Assurer que le tableau a une largeur minimale pour être lisible */
    }
    
    .modern-table thead th, .modern-table td {
        padding: 0.75rem 1rem;
    }
    
    .pagination-container {
        margin-top: 1rem;
    }
    
    /* Compatibilité avec la sidebar cachée */
    body.sidebar-hidden-body .dashboard-container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0.75rem;
    }
    
    .page-title {
        font-size: 1.125rem;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 1rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
    
    /* Optimisations pour les très petits écrans */
    .stat-card {
        margin-bottom: 0.75rem;
    }
    
    .stat-card .card-body {
        padding: 0.5rem;
    }
    
    .stat-value {
        font-size: 1.25rem;
    }
    
    .stat-label {
        font-size: 0.75rem;
    }
    
    /* Compatibilité avec la sidebar */
    body.sidebar-hidden-body .main-content {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
}
