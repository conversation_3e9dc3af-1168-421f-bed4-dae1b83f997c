<?xml version="1.0" encoding="UTF-8"?>
<svg width="400px" height="300px" viewBox="0 0 400 300" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Dashboard Illustration</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#1E88E5" offset="0%"></stop>
            <stop stop-color="#0D47A1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-2">
            <stop stop-color="#64B5F6" offset="0%"></stop>
            <stop stop-color="#1E88E5" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Dashboard" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Charts">
            <rect id="Background" fill="#FFFFFF" opacity="0.1" x="0" y="0" width="400" height="300" rx="8"></rect>
            
            <!-- Bar Chart -->
            <g id="BarChart" transform="translate(50, 50)">
                <rect fill="#64B5F6" x="0" y="100" width="20" height="80" rx="4"></rect>
                <rect fill="#1E88E5" x="30" y="60" width="20" height="120" rx="4"></rect>
                <rect fill="#0D47A1" x="60" y="40" width="20" height="140" rx="4"></rect>
                <rect fill="#64B5F6" x="90" y="70" width="20" height="110" rx="4"></rect>
                <rect fill="#1E88E5" x="120" y="20" width="20" height="160" rx="4"></rect>
                <rect fill="#0D47A1" x="150" y="50" width="20" height="130" rx="4"></rect>
            </g>
            
            <!-- Pie Chart -->
            <g id="PieChart" transform="translate(270, 100)">
                <path d="M0,0 L0,-60 A60,60 0 0,1 52,30 z" fill="#1E88E5"></path>
                <path d="M0,0 L52,30 A60,60 0 0,1 -30,52 z" fill="#64B5F6"></path>
                <path d="M0,0 L-30,52 A60,60 0 0,1 -52,-30 z" fill="#0D47A1"></path>
                <path d="M0,0 L-52,-30 A60,60 0 0,1 0,-60 z" fill="#0288D1"></path>
                <circle cx="0" cy="0" r="20" fill="white"></circle>
            </g>
            
            <!-- Line Chart -->
            <g id="LineChart" transform="translate(50, 220)">
                <polyline points="0,0 30,-20 60,-40 90,-10 120,-50 150,-30 180,-60 210,-40" 
                          stroke="#1E88E5" stroke-width="3" fill="none"></polyline>
                <circle cx="0" cy="0" r="4" fill="#1E88E5"></circle>
                <circle cx="30" cy="-20" r="4" fill="#1E88E5"></circle>
                <circle cx="60" cy="-40" r="4" fill="#1E88E5"></circle>
                <circle cx="90" cy="-10" r="4" fill="#1E88E5"></circle>
                <circle cx="120" cy="-50" r="4" fill="#1E88E5"></circle>
                <circle cx="150" cy="-30" r="4" fill="#1E88E5"></circle>
                <circle cx="180" cy="-60" r="4" fill="#1E88E5"></circle>
                <circle cx="210" cy="-40" r="4" fill="#1E88E5"></circle>
            </g>
            
            <!-- Decorative Elements -->
            <circle cx="350" cy="50" r="20" fill="url(#linearGradient-1)" opacity="0.6"></circle>
            <circle cx="320" cy="30" r="10" fill="url(#linearGradient-2)" opacity="0.4"></circle>
            <circle cx="40" cy="30" r="15" fill="url(#linearGradient-2)" opacity="0.3"></circle>
            <circle cx="20" cy="270" r="12" fill="url(#linearGradient-1)" opacity="0.5"></circle>
            <circle cx="380" cy="260" r="8" fill="url(#linearGradient-2)" opacity="0.4"></circle>
        </g>
    </g>
</svg>
