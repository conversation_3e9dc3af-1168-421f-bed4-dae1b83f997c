export class ProductFormState {
    constructor() {
        this.state = {
            category: null,
            name: '',
            basePrice: 0,
            unit: '',
            stock: 0,
            regionalPrices: new Map(),
            ironSpecifications: {
                diameter: null,
                length: 12,
                unitPrice: 0,
                calculations: {
                    unitsPerTon: 0,
                    weightPerUnit: 0,
                    tonPrice: 0
                }
            },
            validation: {
                errors: new Map(),
                isValid: false
            }
        };

        this.listeners = new Set();
    }

    // Méthodes de mise à jour de l'état
    updateCategory(categoryId, categoryConfig) {
        this.state.category = categoryId;
        this.resetFormForCategory(categoryConfig);
        this.notifyListeners();
    }

    updateRegionalPrice(region, city, price) {
        if (!this.state.regionalPrices.has(region)) {
            this.state.regionalPrices.set(region, new Map());
        }
        this.state.regionalPrices.get(region).set(city, price);
        this.validateForm();
        this.notifyListeners();
    }

    updateIronSpecifications(specs) {
        this.state.ironSpecifications = {
            ...this.state.ironSpecifications,
            ...specs
        };
        this.calculateIronMetrics();
        this.validateForm();
        this.notifyListeners();
    }

    // Méthodes de calcul
    calculateIronMetrics() {
        const { diameter, length, unitPrice } = this.state.ironSpecifications;
        if (!diameter || !length || !unitPrice) return;

        const spec = this.getCurrentDiameterSpec();
        if (!spec) return;

        const weightPerUnit = spec.weightPerMeter * length;
        const unitsPerTon = Math.round(1000 / weightPerUnit);
        const tonPrice = unitPrice * unitsPerTon;

        this.state.ironSpecifications.calculations = {
            unitsPerTon,
            weightPerUnit: Number(weightPerUnit.toFixed(3)),
            tonPrice: Math.round(tonPrice)
        };
    }

    // Méthodes de validation
    validateForm() {
        const errors = new Map();
        const category = this.getCurrentCategory();

        if (!category) {
            errors.set('category', 'Veuillez sélectionner une catégorie');
            this.state.validation = { errors, isValid: false };
            return;
        }

        if (!this.state.name.trim()) {
            errors.set('name', 'Le nom du produit est requis');
        }

        if (category.features.requiresBasePrice && (!this.state.basePrice || this.state.basePrice <= 0)) {
            errors.set('basePrice', 'Le prix de base doit être supérieur à 0');
        }

        if (category.validation?.requireAtLeastOnePrice) {
            let hasPrice = false;
            this.state.regionalPrices.forEach(cities => {
                cities.forEach(price => {
                    if (price > 0) hasPrice = true;
                });
            });
            if (!hasPrice) {
                errors.set('regionalPrices', 'Au moins un prix régional doit être défini');
            }
        }

        if (category.features.requiresSpecifications) {
            const { diameter, length, unitPrice } = this.state.ironSpecifications;
            if (!diameter) errors.set('diameter', 'Le diamètre est requis');
            if (!length || length <= 0) errors.set('length', 'La longueur doit être supérieure à 0');
            if (!unitPrice || unitPrice <= 0) errors.set('unitPrice', 'Le prix unitaire doit être supérieur à 0');
        }

        this.state.validation = {
            errors,
            isValid: errors.size === 0
        };
    }

    // Méthodes utilitaires
    resetFormForCategory(categoryConfig) {
        this.state.regionalPrices.clear();
        this.state.ironSpecifications = {
            diameter: null,
            length: categoryConfig.specifications?.defaultLength || 12,
            unitPrice: 0,
            calculations: {
                unitsPerTon: 0,
                weightPerUnit: 0,
                tonPrice: 0
            }
        };

        // Définir l'unité par défaut
        const defaultUnit = categoryConfig.units.find(u => u.default);
        if (defaultUnit) {
            this.state.unit = defaultUnit.value;
        }

        this.validateForm();
    }

    getCurrentCategory() {
        return this.state.category;
    }

    getCurrentDiameterSpec() {
        const { diameter } = this.state.ironSpecifications;
        const category = this.getCurrentCategory();
        return category?.specifications?.diameters.find(d => d.value === diameter);
    }

    // Gestion des abonnements
    subscribe(listener) {
        this.listeners.add(listener);
        return () => this.listeners.delete(listener);
    }

    notifyListeners() {
        this.listeners.forEach(listener => listener(this.state));
    }
}
