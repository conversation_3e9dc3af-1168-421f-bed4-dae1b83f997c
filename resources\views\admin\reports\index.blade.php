@extends('layouts.admin_minimal')

@section('title', 'Rapports & Analyses')

@section('content')
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-chart-bar text-primary me-2"></i>
                Rapports & Analyses
            </h1>
            <p class="text-muted mb-0">Tableau de bord des analyses et rapports financiers</p>
        </div>
    </div>

    <!-- Cartes de navigation rapide -->
    <div class="row mb-4">
        <!-- Chiffre d'affaires -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Chiffre d'affaires
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format(\App\Models\Sale::where('payment_status', 'paid')->whereMonth('created_at', now()->month)->sum('total_amount'), 0, ',', ' ') }} FCFA
                            </div>
                            <div class="text-xs text-muted">Ce mois</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('admin.reports.turnover') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-chart-line me-1"></i> Voir le rapport
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bénéfices -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Bénéfices
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                @php
                                    $revenue = \App\Models\Sale::where('payment_status', 'paid')->whereMonth('created_at', now()->month)->sum('total_amount');
                                    $costs = \App\Models\Supply::where('status', 'validated')->whereMonth('created_at', now()->month)->sum('total_amount');
                                    $profit = $revenue - $costs;
                                @endphp
                                {{ number_format($profit, 0, ',', ' ') }} FCFA
                            </div>
                            <div class="text-xs text-muted">Ce mois</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-coins fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('admin.reports.profit') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-chart-area me-1"></i> Voir le rapport
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ventes -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Ventes
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ \App\Models\Sale::whereMonth('created_at', now()->month)->count() }}
                            </div>
                            <div class="text-xs text-muted">Ce mois</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('admin.reports.sales') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-list me-1"></i> Voir le rapport
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Paiements -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Paiements
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format(\App\Models\Payment::where('status', 'completed')->whereMonth('payment_date', now()->month)->sum('amount'), 0, ',', ' ') }} FCFA
                            </div>
                            <div class="text-xs text-muted">Ce mois</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('admin.reports.payments') }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-money-check me-1"></i> Voir le rapport
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liens vers tous les rapports -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar me-2"></i>
                        Tous les rapports disponibles
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Rapports financiers -->
                        <div class="col-md-6 mb-4">
                            <h5 class="text-primary">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                Rapports financiers
                            </h5>
                            <div class="list-group">
                                <a href="{{ route('admin.reports.turnover') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">
                                            <i class="fas fa-dollar-sign text-primary me-2"></i>
                                            Chiffre d'affaires
                                        </h6>
                                        <small class="text-muted">Excel, PDF</small>
                                    </div>
                                    <p class="mb-1">Analyse du chiffre d'affaires par période avec détails par produit et évolution mensuelle.</p>
                                </a>
                                <a href="{{ route('admin.reports.profit') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">
                                            <i class="fas fa-coins text-success me-2"></i>
                                            Bénéfices
                                        </h6>
                                        <small class="text-muted">Excel, PDF</small>
                                    </div>
                                    <p class="mb-1">Calcul des bénéfices avec analyse des revenus et coûts par période.</p>
                                </a>
                                <a href="{{ route('admin.reports.payments') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">
                                            <i class="fas fa-credit-card text-warning me-2"></i>
                                            Paiements
                                        </h6>
                                        <small class="text-muted">Excel, PDF</small>
                                    </div>
                                    <p class="mb-1">Suivi des paiements par méthode et statut avec détails des transactions.</p>
                                </a>
                            </div>
                        </div>

                        <!-- Rapports opérationnels -->
                        <div class="col-md-6 mb-4">
                            <h5 class="text-info">
                                <i class="fas fa-cogs me-2"></i>
                                Rapports opérationnels
                            </h5>
                            <div class="list-group">
                                <a href="{{ route('admin.reports.sales') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">
                                            <i class="fas fa-shopping-cart text-info me-2"></i>
                                            Ventes détaillées
                                        </h6>
                                        <small class="text-muted">Excel, PDF</small>
                                    </div>
                                    <p class="mb-1">Liste détaillée des ventes avec filtres par statut et période.</p>
                                </a>
                                <a href="{{ route('admin.reports.supplies') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">
                                            <i class="fas fa-truck-loading text-secondary me-2"></i>
                                            Approvisionnements
                                        </h6>
                                        <small class="text-muted">Excel, PDF</small>
                                    </div>
                                    <p class="mb-1">Suivi des approvisionnements par fournisseur et statut de validation.</p>
                                </a>
                                <a href="{{ route('admin.reports.low-stock') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">
                                            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                            Stocks faibles
                                        </h6>
                                        <small class="text-muted">Excel, PDF</small>
                                    </div>
                                    <p class="mb-1">Produits avec un stock faible nécessitant un réapprovisionnement.</p>
                                </a>
                                <a href="{{ route('admin.reports.top-customers') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">
                                            <i class="fas fa-users text-info me-2"></i>
                                            Top clients
                                        </h6>
                                        <small class="text-muted">Excel, PDF</small>
                                    </div>
                                    <p class="mb-1">Clients les plus actifs et leurs habitudes d'achat.</p>
                                </a>
                                <a href="{{ route('admin.reports.monthly-evolution') }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">
                                            <i class="fas fa-chart-line text-success me-2"></i>
                                            Évolution mensuelle
                                        </h6>
                                        <small class="text-muted">Excel, PDF</small>
                                    </div>
                                    <p class="mb-1">Évolution des ventes, coûts et bénéfices mois par mois.</p>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.list-group-item-action:hover {
    background-color: #f8f9fc;
}
</style>
@endpush
