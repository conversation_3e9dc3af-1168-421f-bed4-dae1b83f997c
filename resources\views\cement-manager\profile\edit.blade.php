@extends('layouts.cement_manager')

@section('title', 'Modifier mon profil')

@section('content')
<div class="profile-edit-header mb-4">
    <div class="container-fluid">
        <div class="profile-edit-cover">
            <div class="profile-edit-title">
                <h3>Modifier mon profil</h3>
                <p>Mettez à jour vos informations personnelles</p>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-2"></i>
                        <div>{{ session('success') }}</div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif
            
            <div class="card profile-edit-card">
                <div class="card-body p-0">
                    <div class="row g-0">
                        <!-- Sidebar avec avatar -->
                        <div class="col-lg-3 profile-edit-sidebar">
                            <div class="profile-edit-sidebar-content">
                                <div class="avatar-upload-wrapper">
                                    <div class="avatar-upload-container" id="currentAvatarContainer">
                                        @if($user->avatar && file_exists(public_path($user->avatar)))
                                            <img src="{{ asset($user->avatar) }}" alt="Avatar" class="current-avatar" id="currentAvatar">
                                        @else
                                            <div class="avatar-placeholder" id="avatarPlaceholder">
                                                {{ substr($user->name, 0, 1) }}
                                            </div>
                                        @endif
                                    </div>
                                    <div class="avatar-preview-container d-none" id="previewContainer">
                                        <img src="#" alt="Aperçu" id="avatarPreview" class="avatar-preview">
                                    </div>
                                    
                                    <div class="avatar-upload-actions mt-3">
                                        <label for="avatar" class="btn btn-primary btn-sm rounded-pill w-100">
                                            <i class="fas fa-camera me-2"></i> Changer l'avatar
                                        </label>
                                        <p class="avatar-upload-help">JPG, PNG ou GIF. Max 2MB.</p>
                                    </div>
                                </div>
                                
                                <div class="profile-edit-nav">
                                    <a href="{{ route('cement-manager.profile.show') }}" class="profile-edit-nav-link">
                                        <i class="fas fa-user me-2"></i> Voir mon profil
                                    </a>
                                    <a href="{{ route('cement-manager.profile.password') }}" class="profile-edit-nav-link">
                                        <i class="fas fa-key me-2"></i> Changer mot de passe
                                    </a>
                                    <a href="{{ route('cement-manager.dashboard') }}" class="profile-edit-nav-link">
                                        <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Formulaire d'édition -->
                        <div class="col-lg-9 profile-edit-form-container">
                            <form action="{{ route('cement-manager.profile.update') }}" method="POST" enctype="multipart/form-data" class="profile-edit-form">
                                @csrf
                                @method('PUT')
                                <input type="file" name="avatar" id="avatar" class="d-none" accept="image/*">
                                
                                <div class="profile-form-section">
                                    <h5 class="profile-form-section-title">
                                        <i class="fas fa-user-circle text-primary me-2"></i> Informations personnelles
                                    </h5>
                                    
                                    <div class="row g-4">
                                        <div class="col-md-6">
                                            <div class="form-floating">
                                                <input type="text" class="form-control @error('first_name') is-invalid @enderror" 
                                                    id="first_name" name="first_name" value="{{ old('first_name', $user->first_name) }}" 
                                                    placeholder="Prénom" required>
                                                <label for="first_name">Prénom</label>
                                                @error('first_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="form-floating">
                                                <input type="text" class="form-control @error('last_name') is-invalid @enderror" 
                                                    id="last_name" name="last_name" value="{{ old('last_name', $user->last_name) }}" 
                                                    placeholder="Nom" required>
                                                <label for="last_name">Nom</label>
                                                @error('last_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="profile-form-section">
                                    <h5 class="profile-form-section-title">
                                        <i class="fas fa-address-card text-primary me-2"></i> Coordonnées
                                    </h5>
                                    
                                    <div class="row g-4">
                                        <div class="col-md-6">
                                            <div class="form-floating">
                                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                                    id="email" name="email" value="{{ old('email', $user->email) }}" 
                                                    placeholder="Email" required>
                                                <label for="email">Email</label>
                                                @error('email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="form-floating">
                                                <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                                    id="phone" name="phone" value="{{ old('phone', $user->phone) }}" 
                                                    placeholder="Téléphone">
                                                <label for="phone">Téléphone</label>
                                                @error('phone')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        
                                        <!-- Le champ position a été temporairement retiré -->
                                    </div>
                                </div>
                                
                                <div class="profile-form-actions">
                                    <a href="{{ route('cement-manager.profile.show') }}" class="btn btn-outline-secondary rounded-pill px-4">
                                        <i class="fas fa-times me-2"></i> Annuler
                                    </a>
                                    <button type="submit" class="btn btn-primary rounded-pill px-4">
                                        <i class="fas fa-save me-2"></i> Enregistrer
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* En-tête de la page d'édition */
    .profile-edit-header {
        position: relative;
    }
    
    .profile-edit-cover {
        height: 150px;
        background: linear-gradient(135deg, #2563eb, #1e40af);
        border-radius: 0.5rem;
        position: relative;
        display: flex;
        align-items: center;
        padding: 0 2rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    .profile-edit-title {
        color: white;
    }
    
    .profile-edit-title h3 {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .profile-edit-title p {
        opacity: 0.8;
        margin-bottom: 0;
    }
    
    /* Carte principale */
    .profile-edit-card {
        border: none;
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
        margin-bottom: 2rem;
    }
    
    /* Sidebar avec avatar */
    .profile-edit-sidebar {
        background-color: #f8f9fa;
        padding: 2rem;
        border-right: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .profile-edit-sidebar-content {
        display: flex;
        flex-direction: column;
        height: 100%;
    }
    
    .avatar-upload-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 2rem;
    }
    
    .avatar-upload-container {
        width: 150px;
        height: 150px;
        position: relative;
        margin-bottom: 1rem;
    }
    
    .current-avatar, .avatar-preview {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
        border: 5px solid white;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .avatar-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #2563eb;
        color: white;
        font-size: 3.5rem;
        font-weight: 600;
        border-radius: 50%;
        border: 5px solid white;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .avatar-upload-help {
        font-size: 0.75rem;
        color: #6c757d;
        text-align: center;
        margin-top: 0.5rem;
    }
    
    .profile-edit-nav {
        display: flex;
        flex-direction: column;
        margin-top: auto;
    }
    
    .profile-edit-nav-link {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        color: #4b5563;
        text-decoration: none;
        border-radius: 0.5rem;
        transition: all 0.2s ease;
        margin-bottom: 0.5rem;
    }
    
    .profile-edit-nav-link:hover {
        background-color: rgba(37, 99, 235, 0.1);
        color: #2563eb;
    }
    
    /* Formulaire d'édition */
    .profile-edit-form-container {
        padding: 2rem;
    }
    
    .profile-edit-form {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    
    .profile-form-section {
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .profile-form-section:last-of-type {
        border-bottom: none;
    }
    
    .profile-form-section-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        color: #1f2937;
    }
    
    .form-floating {
        margin-bottom: 0.5rem;
    }
    
    .form-floating > .form-control {
        padding: 1rem 0.75rem;
    }
    
    .form-floating > label {
        padding: 1rem 0.75rem;
    }
    
    .profile-form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        margin-top: auto;
        padding-top: 1rem;
    }
    
    /* Responsive */
    @media (max-width: 992px) {
        .profile-edit-sidebar {
            border-right: none;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding-bottom: 2rem;
        }
        
        .profile-edit-nav {
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 1.5rem;
        }
        
        .profile-edit-nav-link {
            padding: 0.5rem 1rem;
            margin-bottom: 0;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const avatarInput = document.getElementById('avatar');
        const currentAvatarContainer = document.getElementById('currentAvatarContainer');
        const previewContainer = document.getElementById('previewContainer');
        const avatarPreview = document.getElementById('avatarPreview');
        
        avatarInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                const file = e.target.files[0];
                const fileReader = new FileReader();
                
                fileReader.onload = function(e) {
                    avatarPreview.src = e.target.result;
                    currentAvatarContainer.classList.add('d-none');
                    previewContainer.classList.remove('d-none');
                };
                
                fileReader.readAsDataURL(file);
            }
        });
    });
</script>
@endpush
