@extends($layout ?? 'layouts.admin')

@section('title', 'Liste des approvisionnements')

@section('content')
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Liste des approvisionnements</h1>

    @if(session('message'))
        <div class="alert alert-success">
            {{ session('message') }}
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger">
            {{ session('error') }}
        </div>
    @endif

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Référence</th>
                            <th>Fournisseur</th>
                            <th>Montant</th>
                            <th>Statut</th>
                            <th>Créé par</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($supplies as $supply)
                            <tr>
                                <td>{{ $supply->created_at->format('d/m/Y H:i') }}</td>
                                <td>{{ $supply->reference }}</td>
                                <td>{{ $supply->supplier->name }}</td>
                                <td>{{ number_format($supply->total_amount, 0, ',', ' ') }} FCFA</td>
                                <td>
                                    <span class="badge bg-{{ $supply->status == 'pending' ? 'warning' : ($supply->status == 'validated' ? 'success' : 'danger') }}">
                                        {{ $supply->status == 'pending' ? 'En attente' : ($supply->status == 'validated' ? 'Validé' : 'Rejeté') }}
                                    </span>
                                </td>
                                <td>{{ $supply->creator->name }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ route('admin.supplies.show', $supply) }}" 
                                           class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        
                                        @if($supply->status == 'pending')
                                            <a href="{{ route('admin.supplies.validate', $supply) }}" 
                                               class="btn btn-sm btn-success">
                                                <i class="fas fa-check"></i>
                                            </a>

                                            <a href="{{ route('admin.supplies.reject', $supply) }}" 
                                               class="btn btn-sm btn-danger">
                                                <i class="fas fa-times"></i>
                                            </a>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <div class="mt-3">
                {{ $supplies->links() }}
            </div>
        </div>
    </div>
</div>
@endsection
