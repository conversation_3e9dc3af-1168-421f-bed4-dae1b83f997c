@extends('layouts.cement-manager')

@section('title', 'Détails du Chauffeur')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title m-0">Détails du Chauffeur</h3>
                    <div class="card-tools">
                        <a href="{{ route('cement-manager.drivers.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour à la liste
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <h5 class="text-muted mb-2">Informations Personnelles</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th class="w-25">Nom Complet</th>
                                            <td>{{ $driver->first_name }} {{ $driver->last_name }}</td>
                                        </tr>
                                        <tr>
                                            <th>Téléphone</th>
                                            <td>{{ $driver->phone_number }}</td>
                                        </tr>
                                        <tr>
                                            <th>Contact d'urgence</th>
                                            <td>{{ $driver->emergency_phone }}</td>
                                        </tr>
                                        <tr>
                                            <th>Adresse</th>
                                            <td>{{ $driver->address }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-4">
                                <h5 class="text-muted mb-2">Informations Professionnelles</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th class="w-25">N° Permis</th>
                                            <td>{{ $driver->license_number }}</td>
                                        </tr>
                                        <tr>
                                            <th>Expiration Permis</th>
                                            <td>{{ $driver->license_expiry->format('d/m/Y') }}</td>
                                        </tr>
                                        <tr>
                                            <th>Disponibilité</th>
                                            <td>
                                                @if($driver->status === 'available')
                                                    <span class="badge bg-success">Disponible</span>
                                                @else
                                                    <span class="badge bg-danger">Non disponible</span>
                                                @endif
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($driver->notes)
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="text-muted mb-2">Notes</h5>
                            <div class="card bg-light">
                                <div class="card-body">
                                    {{ $driver->notes }}
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    @if($driver->trips->count() > 0)
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="text-muted mb-2">Historique des Voyages</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>Date de début</th>
                                            <th>Date de fin</th>
                                            <th>Camion</th>
                                            <th>Détails</th>
                                            <th>Statut</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($driver->trips as $trip)
                                            <tr>
                                                <td>{{ $trip->started_at ? $trip->started_at->format('d/m/Y H:i') : 'N/A' }}</td>
                                                <td>{{ $trip->completed_at ? $trip->completed_at->format('d/m/Y H:i') : 'N/A' }}</td>
                                                <td>{{ $trip->truck ? $trip->truck->registration_number : 'N/A' }}</td>
                                                <td>
                                                    @foreach($trip->tripAssignments as $assignment)
                                                        <div>
                                                            Commande #{{ $assignment->cement_order_id }} - 
                                                            {{ $assignment->cementOrderDetail ? $assignment->cementOrderDetail->tonnage . ' T' : 'N/A' }}
                                                        </div>
                                                    @endforeach
                                                </td>
                                                <td>
                                                    @switch($trip->status)
                                                        @case('pending')
                                                            <span class="badge bg-warning">En attente</span>
                                                            @break
                                                        @case('in_progress')
                                                            <span class="badge bg-info">En cours</span>
                                                            @break
                                                        @case('completed')
                                                            <span class="badge bg-success">Terminé</span>
                                                            @break
                                                        @case('cancelled')
                                                            <span class="badge bg-danger">Annulé</span>
                                                            @break
                                                        @default
                                                            <span class="badge bg-secondary">{{ $trip->status }}</span>
                                                    @endswitch
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .badge {
        font-size: 0.9em;
        padding: 0.4em 0.6em;
    }
    .table th {
        background-color: #f8f9fa;
    }
</style>
@endpush
