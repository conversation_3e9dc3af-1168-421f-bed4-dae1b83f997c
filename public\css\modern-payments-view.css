/* Fichier CSS moderne pour la vue des paiements */
:root {
    --primary-color: #1E88E5;
    --primary-light: #64B5F6;
    --primary-dark: #0D47A1;
    --secondary-color: #E3F2FD;
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --danger-color: #F44336;
    --info-color: #00BCD4;
    --text-color: #37474F;
    --text-light: #78909C;
    --border-color: #E0E0E0;
    --background-light: #F5F7FA;
    --shadow-color: rgba(13, 71, 161, 0.1);
    --gradient-blue: linear-gradient(135deg, #1E88E5, #0D47A1);
}

/* Styles généraux */
body {
    color: var(--text-color);
}

.page-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.page-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-dark);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.page-title i {
    color: var(--primary-color);
    font-size: 1.5rem;
}

/* Statistiques des paiements */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.25rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1.25rem;
    box-shadow: 0 4px 12px var(--shadow-color);
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px var(--shadow-color);
}

.stat-title {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-dark);
    margin-bottom: 0.75rem;
}

.stat-trend {
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.35rem;
}

.stat-trend.positive {
    color: var(--success-color);
}

.stat-trend.negative {
    color: var(--danger-color);
}

.stat-icon {
    position: absolute;
    top: 1.25rem;
    right: 1.25rem;
    font-size: 1.5rem;
    color: var(--primary-light);
    opacity: 0.7;
}

/* Filtres */
.filter-section {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 12px var(--shadow-color);
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
}

.filter-group {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
}

.filter-label {
    font-weight: 600;
    color: var(--text-color);
    margin-right: 0.5rem;
}

.filter-button {
    background: var(--background-light);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 50px;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-button i {
    font-size: 0.9rem;
}

.filter-button:hover {
    background: var(--secondary-color);
    border-color: var(--primary-light);
}

.filter-button.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.search-box {
    flex: 1;
    min-width: 250px;
    position: relative;
}

.search-box input {
    width: 100%;
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    border-radius: 50px;
    border: 1px solid var(--border-color);
    background: var(--background-light);
    transition: all 0.3s ease;
}

.search-box input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 136, 229, 0.2);
    outline: none;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

/* Tableau des paiements */
.card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 12px var(--shadow-color);
    border: none;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.card-header {
    background: white;
    border-bottom: 1px solid var(--border-color);
    padding: 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-dark);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-title i {
    color: var(--primary-color);
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.modern-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.modern-table th {
    background: var(--background-light);
    color: var(--text-color);
    font-weight: 600;
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.modern-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.modern-table tr:last-child td {
    border-bottom: none;
}

.modern-table tr:hover {
    background-color: var(--secondary-color);
}

/* Badges de statut */
.payment-method-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.35rem;
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
}

.payment-method-badge.cash {
    background-color: rgba(76, 175, 80, 0.15);
    color: var(--success-color);
}

.payment-method-badge.bank_transfer {
    background-color: rgba(0, 188, 212, 0.15);
    color: var(--info-color);
}

.payment-method-badge.check {
    background-color: rgba(255, 152, 0, 0.15);
    color: var(--warning-color);
}

.payment-method-badge.mobile_money {
    background-color: rgba(156, 39, 176, 0.15);
    color: #9C27B0;
}

/* Actions */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-button {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all 0.3s ease;
}

.action-button.view {
    background: var(--primary-color);
}

.action-button.print {
    background: var(--info-color);
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-color);
}

/* Animation pour les nouvelles entrées */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modern-table tbody tr {
    animation: fadeIn 0.5s ease forwards;
}

.modern-table tbody tr:nth-child(1) { animation-delay: 0.05s; }
.modern-table tbody tr:nth-child(2) { animation-delay: 0.1s; }
.modern-table tbody tr:nth-child(3) { animation-delay: 0.15s; }
.modern-table tbody tr:nth-child(4) { animation-delay: 0.2s; }
.modern-table tbody tr:nth-child(5) { animation-delay: 0.25s; }
.modern-table tbody tr:nth-child(6) { animation-delay: 0.3s; }
.modern-table tbody tr:nth-child(7) { animation-delay: 0.35s; }
.modern-table tbody tr:nth-child(8) { animation-delay: 0.4s; }
.modern-table tbody tr:nth-child(9) { animation-delay: 0.45s; }
.modern-table tbody tr:nth-child(10) { animation-delay: 0.5s; }

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: flex-end;
}

.pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 0.25rem;
}

.page-item .page-link {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    color: var(--text-color);
    background: var(--background-light);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.page-item.active .page-link {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.page-item .page-link:hover {
    background: var(--secondary-color);
    border-color: var(--primary-light);
}

/* Responsive */
@media (max-width: 992px) {
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: 1fr;
    }
    
    .filter-section {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .search-box {
        width: 100%;
    }
}

/* État vide */
.empty-state {
    padding: 3rem 1rem;
    text-align: center;
}

.empty-state i {
    font-size: 3rem;
    color: var(--text-light);
    margin-bottom: 1rem;
}

.empty-state h4 {
    font-size: 1.25rem;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: var(--text-light);
    max-width: 500px;
    margin: 0 auto 1.5rem;
}

/* Indicateur de montant */
.amount-indicator {
    font-weight: 700;
    color: var(--primary-dark);
}

/* Tooltip personnalisé */
.tooltip-container {
    position: relative;
    display: inline-block;
}

.tooltip-content {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-color);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 10;
}

.tooltip-container:hover .tooltip-content {
    opacity: 1;
    visibility: visible;
    bottom: calc(100% + 10px);
}

.tooltip-content::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: var(--text-color) transparent transparent transparent;
}

/* Effet de carte */
.card-effect {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-effect:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px var(--shadow-color);
}

/* Animations */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Bouton d'exportation */
.export-button {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.export-button:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-color);
}
