/* Styles pour les cartes colorées et les graphiques attrayants */

/* Styles généraux pour les cartes */
.stat-card {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-radius: 24px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08), 0 5px 15px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  transform-style: preserve-3d;
  perspective: 1000px;
  padding: 0;
  margin-bottom: 1.5rem;
  height: 100%;
  border: none;
}

/* Bordures colorées pour les différentes cartes */
.primary-card {
  border-top: 5px solid #4361ee;
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.success-card {
  border-top: 5px solid #06d6a0;
  background: linear-gradient(135deg, rgba(6, 214, 160, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.warning-card {
  border-top: 5px solid #f9c74f;
  background: linear-gradient(135deg, rgba(249, 199, 79, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.danger-card {
  border-top: 5px solid #ef476f;
  background: linear-gradient(135deg, rgba(239, 71, 111, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
}

/* Effets de survol améliorés */
.stat-card:hover {
  transform: translateY(-15px) rotateX(5deg);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.12), 0 10px 20px rgba(0, 0, 0, 0.05);
}

.primary-card:hover {
  box-shadow: 0 20px 50px rgba(67, 97, 238, 0.15), 0 10px 20px rgba(67, 97, 238, 0.1);
}

.success-card:hover {
  box-shadow: 0 20px 50px rgba(6, 214, 160, 0.15), 0 10px 20px rgba(6, 214, 160, 0.1);
}

.warning-card:hover {
  box-shadow: 0 20px 50px rgba(249, 199, 79, 0.15), 0 10px 20px rgba(249, 199, 79, 0.1);
}

.danger-card:hover {
  box-shadow: 0 20px 50px rgba(239, 71, 111, 0.15), 0 10px 20px rgba(239, 71, 111, 0.1);
}

/* Contenu des cartes */
.card-body {
  padding: 1.5rem;
  position: relative;
  z-index: 2;
}

.card-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.75rem;
}

.card-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, var(--gray-900), var(--primary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  /* La propriété text-fill-color n'est pas standard, nous utilisons uniquement -webkit-text-fill-color */
}

.primary-card .card-value {
  background: linear-gradient(45deg, #3a0ca3, #4361ee);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  /* La propriété text-fill-color n'est pas standard, nous utilisons uniquement -webkit-text-fill-color */
}

.success-card .card-value {
  background: linear-gradient(45deg, #059669, #06d6a0);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  /* La propriété text-fill-color n'est pas standard, nous utilisons uniquement -webkit-text-fill-color */
}

.warning-card .card-value {
  background: linear-gradient(45deg, #d97706, #f9c74f);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  /* La propriété text-fill-color n'est pas standard, nous utilisons uniquement -webkit-text-fill-color */
}

.danger-card .card-value {
  background: linear-gradient(45deg, #be123c, #ef476f);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  /* La propriété text-fill-color n'est pas standard, nous utilisons uniquement -webkit-text-fill-color */
}

/* Icônes des cartes */
.card-icon {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  font-size: 2.5rem;
  opacity: 0.15;
  color: var(--primary);
  transition: all 0.5s ease;
}

.primary-card .card-icon {
  color: #4361ee;
}

.success-card .card-icon {
  color: #06d6a0;
}

.warning-card .card-icon {
  color: #f9c74f;
}

.danger-card .card-icon {
  color: #ef476f;
}

.stat-card:hover .card-icon {
  transform: scale(1.2) rotate(15deg);
  opacity: 0.25;
}

/* Tendance en bas de carte */
.card-trend {
  padding: 0.75rem 1.5rem;
  background: rgba(0, 0, 0, 0.03);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  font-size: 0.85rem;
  color: var(--gray-600);
}

.primary-card .card-trend {
  background: rgba(67, 97, 238, 0.05);
  border-top: 1px solid rgba(67, 97, 238, 0.1);
}

.success-card .card-trend {
  background: rgba(6, 214, 160, 0.05);
  border-top: 1px solid rgba(6, 214, 160, 0.1);
}

.warning-card .card-trend {
  background: rgba(249, 199, 79, 0.05);
  border-top: 1px solid rgba(249, 199, 79, 0.1);
}

.danger-card .card-trend {
  background: rgba(239, 71, 111, 0.05);
  border-top: 1px solid rgba(239, 71, 111, 0.1);
}

.card-trend i {
  margin-right: 0.5rem;
}

/* Éléments décoratifs */
.card-decoration {
  position: absolute;
  bottom: 0;
  right: 0;
  opacity: 0.1;
  z-index: 1;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: var(--primary);
  opacity: 0.15;
  transition: all 0.5s ease;
}

.primary-card .decoration-circle {
  background: #4361ee;
}

.success-card .decoration-circle {
  background: #06d6a0;
}

.warning-card .decoration-circle {
  background: #f9c74f;
}

.danger-card .decoration-circle {
  background: #ef476f;
}

.decoration-circle:nth-child(1) {
  width: 100px;
  height: 100px;
  bottom: -50px;
  right: -50px;
}

.decoration-circle:nth-child(2) {
  width: 60px;
  height: 60px;
  bottom: 20px;
  right: 20px;
}

.stat-card:hover .decoration-circle:nth-child(1) {
  transform: scale(1.2) translateY(-10px);
}

.stat-card:hover .decoration-circle:nth-child(2) {
  transform: scale(1.3) translateX(-10px);
}

/* Styles pour les graphiques */
.chart-card {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-radius: 24px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08), 0 5px 15px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  transform-style: preserve-3d;
  perspective: 1000px;
  border: none;
  margin-bottom: 1.5rem;
  border-left: 5px solid #4361ee;
}

.chart-card:hover {
  transform: translateY(-15px) rotateX(5deg);
  box-shadow: 0 20px 50px rgba(67, 97, 238, 0.15), 0 10px 20px rgba(67, 97, 238, 0.1);
}

.chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
  z-index: 1;
  pointer-events: none;
}

.chart-container {
  width: 100%;
  height: 350px;
  position: relative;
  z-index: 2;
  padding: 1rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.5);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: rgba(67, 97, 238, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4361ee;
  font-size: 1.25rem;
}

.card-title h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-800);
  margin: 0;
}

.card-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-600);
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-button:hover {
  background: #4361ee;
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
}

/* Contrôles du graphique */
.chart-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 0 1.5rem 1rem;
  padding-top: 0.75rem;
}

.chart-toggle-btn {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(67, 97, 238, 0.1);
  border-radius: 50px;
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--gray-600);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.chart-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  color: var(--gray-800);
}

.chart-toggle-btn.active {
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
  border: none;
  color: white;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
}

/* Activités récentes */
.activity-card {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-radius: 24px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08), 0 5px 15px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  transform-style: preserve-3d;
  perspective: 1000px;
  margin-bottom: 1.5rem;
  height: 100%;
  border-right: 5px solid #7209b7;
}

.activity-card:hover {
  transform: translateY(-15px) rotateX(5deg);
  box-shadow: 0 20px 50px rgba(114, 9, 183, 0.15), 0 10px 20px rgba(114, 9, 183, 0.1);
}

.activities-container {
  padding: 1.25rem;
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  position: relative;
  padding: 1.25rem 1rem 1.25rem 3rem;
  margin-bottom: 1rem;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  background: rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
}

.activity-item:hover {
  transform: translateX(8px) translateY(-5px);
  background: rgba(255, 255, 255, 0.7);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
}

.activity-item.success {
  border-left: 3px solid #06d6a0;
}

.activity-item.warning {
  border-left: 3px solid #f9c74f;
}

.activity-item.danger {
  border-left: 3px solid #ef476f;
}

.activity-item.primary {
  border-left: 3px solid #4361ee;
}

.activity-icon {
  position: absolute;
  left: 1rem;
  top: 1.25rem;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(67, 97, 238, 0.1);
  color: #4361ee;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  transition: all 0.4s ease;
}

.activity-item.success .activity-icon {
  background: rgba(6, 214, 160, 0.1);
  color: #06d6a0;
}

.activity-item.warning .activity-icon {
  background: rgba(249, 199, 79, 0.1);
  color: #f9c74f;
}

.activity-item.danger .activity-icon {
  background: rgba(239, 71, 111, 0.1);
  color: #ef476f;
}

.activity-item:hover .activity-icon {
  transform: scale(1.1) rotate(5deg);
}

.activity-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--gray-800);
}

.badge-status {
  font-size: 0.7rem;
  font-weight: 700;
  padding: 0.35rem 0.8rem;
  border-radius: 50px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: rgba(67, 97, 238, 0.1);
  color: #4361ee;
}

.badge-status.success {
  background: rgba(6, 214, 160, 0.1);
  color: #06d6a0;
}

.badge-status.warning {
  background: rgba(249, 199, 79, 0.1);
  color: #f9c74f;
}

.badge-status.danger {
  background: rgba(239, 71, 111, 0.1);
  color: #ef476f;
}

.activity-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.activity-detail {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--gray-600);
}

.activity-detail i {
  color: var(--gray-500);
  font-size: 0.8rem;
}

/* Animations pour les éléments */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card, .chart-card, .activity-card {
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
}

.stat-card:nth-child(1) {
  animation-delay: 0.1s;
}

.stat-card:nth-child(2) {
  animation-delay: 0.2s;
}

.stat-card:nth-child(3) {
  animation-delay: 0.3s;
}

.stat-card:nth-child(4) {
  animation-delay: 0.4s;
}

.chart-card {
  animation-delay: 0.5s;
}

.activity-card {
  animation-delay: 0.6s;
}

/* Styles pour les graphiques */
canvas {
  border-radius: 12px;
  transition: all 0.5s ease;
}

canvas:hover {
  transform: scale(1.02);
}
