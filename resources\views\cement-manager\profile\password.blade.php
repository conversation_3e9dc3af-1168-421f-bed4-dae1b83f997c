@extends('layouts.cement_manager')

@section('title', 'Changer mon mot de passe')

@section('content')
<div class="password-header mb-4">
    <div class="container-fluid">
        <div class="password-cover">
            <div class="password-title">
                <h3>Changer mon mot de passe</h3>
                <p>Sécurisez votre compte avec un mot de passe fort</p>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-2"></i>
                        <div>{{ session('success') }}</div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif
            
            <div class="card password-card">
                <div class="card-body p-0">
                    <div class="row g-0">
                        <!-- Sidebar avec informations -->
                        <div class="col-lg-4 password-sidebar">
                            <div class="password-sidebar-content">
                                <div class="password-security-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                
                                <h4 class="password-sidebar-title">Sécurité du compte</h4>
                                <p class="password-sidebar-text">Un mot de passe fort est essentiel pour protéger votre compte. Suivez ces conseils pour créer un mot de passe sécurisé :</p>
                                
                                <div class="password-tips">
                                    <div class="password-tip-item">
                                        <div class="password-tip-icon">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="password-tip-text">Au moins 8 caractères</div>
                                    </div>
                                    <div class="password-tip-item">
                                        <div class="password-tip-icon">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="password-tip-text">Mélangez majuscules et minuscules</div>
                                    </div>
                                    <div class="password-tip-item">
                                        <div class="password-tip-icon">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="password-tip-text">Incluez des chiffres</div>
                                    </div>
                                    <div class="password-tip-item">
                                        <div class="password-tip-icon">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="password-tip-text">Ajoutez des caractères spéciaux</div>
                                    </div>
                                </div>
                                
                                <div class="password-nav">
                                    <a href="{{ route('cement-manager.profile.show') }}" class="password-nav-link">
                                        <i class="fas fa-user me-2"></i> Voir mon profil
                                    </a>
                                    <a href="{{ route('cement-manager.profile.edit') }}" class="password-nav-link">
                                        <i class="fas fa-edit me-2"></i> Modifier mon profil
                                    </a>
                                    <a href="{{ route('cement-manager.dashboard') }}" class="password-nav-link">
                                        <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Formulaire de mot de passe -->
                        <div class="col-lg-8 password-form-container">
                            <form action="{{ route('cement-manager.profile.password.update') }}" method="POST" class="password-form">
                                @csrf
                                @method('PUT')
                                
                                <div class="password-form-section">
                                    <h5 class="password-form-section-title">
                                        <i class="fas fa-lock text-primary me-2"></i> Changer votre mot de passe
                                    </h5>
                                    
                                    <div class="mb-4">
                                        <label for="current_password" class="form-label">Mot de passe actuel</label>
                                        <div class="password-input-group">
                                            <div class="password-input-icon">
                                                <i class="fas fa-key"></i>
                                            </div>
                                            <input type="password" class="form-control @error('current_password') is-invalid @enderror" 
                                                id="current_password" name="current_password" required>
                                            <button class="password-toggle toggle-password" type="button" data-target="current_password">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        @error('current_password')
                                            <div class="invalid-feedback d-block mt-1">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="password" class="form-label">Nouveau mot de passe</label>
                                        <div class="password-input-group">
                                            <div class="password-input-icon">
                                                <i class="fas fa-lock"></i>
                                            </div>
                                            <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                                id="password" name="password" required>
                                            <button class="password-toggle toggle-password" type="button" data-target="password">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        @error('password')
                                            <div class="invalid-feedback d-block mt-1">{{ $message }}</div>
                                        @enderror
                                        
                                        <div class="password-strength mt-3" id="passwordStrength">
                                            <div class="password-strength-label mb-1 d-flex justify-content-between">
                                                <span>Force du mot de passe</span>
                                                <span id="passwordStrengthText" class="password-strength-text">Très faible</span>
                                            </div>
                                            <div class="password-strength-meter">
                                                <div class="password-strength-segment" data-strength="25"></div>
                                                <div class="password-strength-segment" data-strength="50"></div>
                                                <div class="password-strength-segment" data-strength="75"></div>
                                                <div class="password-strength-segment" data-strength="100"></div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="password_confirmation" class="form-label">Confirmer le nouveau mot de passe</label>
                                        <div class="password-input-group">
                                            <div class="password-input-icon">
                                                <i class="fas fa-check-circle"></i>
                                            </div>
                                            <input type="password" class="form-control @error('password_confirmation') is-invalid @enderror" 
                                                id="password_confirmation" name="password_confirmation" required>
                                            <button class="password-toggle toggle-password" type="button" data-target="password_confirmation">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        @error('password_confirmation')
                                            <div class="invalid-feedback d-block mt-1">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="password-form-actions">
                                    <a href="{{ route('cement-manager.profile.show') }}" class="btn btn-outline-secondary rounded-pill px-4">
                                        <i class="fas fa-times me-2"></i> Annuler
                                    </a>
                                    <button type="submit" class="btn btn-primary rounded-pill px-4">
                                        <i class="fas fa-key me-2"></i> Mettre à jour
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* En-tête de la page de mot de passe */
    .password-header {
        position: relative;
    }
    
    .password-cover {
        height: 150px;
        background: linear-gradient(135deg, #2563eb, #1e40af);
        border-radius: 0.5rem;
        position: relative;
        display: flex;
        align-items: center;
        padding: 0 2rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    .password-title {
        color: white;
    }
    
    .password-title h3 {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .password-title p {
        opacity: 0.8;
        margin-bottom: 0;
    }
    
    /* Carte principale */
    .password-card {
        border: none;
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
        margin-bottom: 2rem;
    }
    
    /* Sidebar avec conseils */
    .password-sidebar {
        background-color: #f8f9fa;
        padding: 2rem;
        border-right: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .password-sidebar-content {
        display: flex;
        flex-direction: column;
        height: 100%;
    }
    
    .password-security-icon {
        width: 80px;
        height: 80px;
        background-color: rgba(37, 99, 235, 0.1);
        color: #2563eb;
        font-size: 2.5rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
    }
    
    .password-sidebar-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
        text-align: center;
        color: #1f2937;
    }
    
    .password-sidebar-text {
        color: #4b5563;
        margin-bottom: 1.5rem;
        text-align: center;
        font-size: 0.95rem;
    }
    
    .password-tips {
        margin-bottom: 2rem;
    }
    
    .password-tip-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.75rem;
    }
    
    .password-tip-icon {
        width: 24px;
        height: 24px;
        background-color: rgba(22, 163, 74, 0.1);
        color: #16a34a;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
        flex-shrink: 0;
        font-size: 0.75rem;
    }
    
    .password-tip-text {
        font-size: 0.9rem;
        color: #4b5563;
    }
    
    .password-nav {
        display: flex;
        flex-direction: column;
        margin-top: auto;
    }
    
    .password-nav-link {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        color: #4b5563;
        text-decoration: none;
        border-radius: 0.5rem;
        transition: all 0.2s ease;
        margin-bottom: 0.5rem;
    }
    
    .password-nav-link:hover {
        background-color: rgba(37, 99, 235, 0.1);
        color: #2563eb;
    }
    
    /* Formulaire de mot de passe */
    .password-form-container {
        padding: 2rem;
    }
    
    .password-form {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    
    .password-form-section {
        margin-bottom: 2rem;
    }
    
    .password-form-section-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        color: #1f2937;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .password-input-group {
        position: relative;
        display: flex;
        align-items: center;
    }
    
    .password-input-icon {
        position: absolute;
        left: 1rem;
        color: #6b7280;
    }
    
    .password-input-group .form-control {
        padding-left: 2.75rem;
        padding-right: 2.75rem;
        height: 48px;
        border-radius: 0.5rem;
    }
    
    .password-toggle {
        position: absolute;
        right: 1rem;
        background: none;
        border: none;
        color: #6b7280;
        cursor: pointer;
        padding: 0;
    }
    
    /* Indicateur de force du mot de passe */
    .password-strength {
        margin-top: 1rem;
    }
    
    .password-strength-text {
        font-weight: 500;
    }
    
    .password-strength-text.very-weak {
        color: #dc2626;
    }
    
    .password-strength-text.weak {
        color: #f59e0b;
    }
    
    .password-strength-text.medium {
        color: #0ea5e9;
    }
    
    .password-strength-text.strong {
        color: #16a34a;
    }
    
    .password-strength-meter {
        display: flex;
        gap: 4px;
        margin-top: 0.5rem;
    }
    
    .password-strength-segment {
        height: 8px;
        flex: 1;
        background-color: #e5e7eb;
        border-radius: 4px;
        transition: background-color 0.3s ease;
    }
    
    .password-strength-segment.active {
        background-color: #dc2626;
    }
    
    .password-strength-segment.active[data-strength="25"] {
        background-color: #dc2626;
    }
    
    .password-strength-segment.active[data-strength="50"] {
        background-color: #f59e0b;
    }
    
    .password-strength-segment.active[data-strength="75"] {
        background-color: #0ea5e9;
    }
    
    .password-strength-segment.active[data-strength="100"] {
        background-color: #16a34a;
    }
    
    .password-form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        margin-top: auto;
        padding-top: 1rem;
    }
    
    /* Responsive */
    @media (max-width: 992px) {
        .password-sidebar {
            border-right: none;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding-bottom: 2rem;
        }
        
        .password-nav {
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 1.5rem;
        }
        
        .password-nav-link {
            padding: 0.5rem 1rem;
            margin-bottom: 0;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Afficher/masquer le mot de passe
        document.querySelectorAll('.toggle-password').forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const input = document.getElementById(targetId);
                
                if (input.type === 'password') {
                    input.type = 'text';
                    this.innerHTML = '<i class="fas fa-eye-slash"></i>';
                } else {
                    input.type = 'password';
                    this.innerHTML = '<i class="fas fa-eye"></i>';
                }
            });
        });
        
        // Vérification de la force du mot de passe
        const passwordInput = document.getElementById('password');
        const strengthText = document.getElementById('passwordStrengthText');
        const strengthSegments = document.querySelectorAll('.password-strength-segment');
        
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            let strength = 0;
            
            // Réinitialiser les segments
            strengthSegments.forEach(segment => {
                segment.classList.remove('active');
            });
            
            if (password.length > 0) {
                // Longueur
                if (password.length >= 8) {
                    strength += 25;
                }
                
                // Lettres majuscules et minuscules
                if (password.match(/[a-z]/) && password.match(/[A-Z]/)) {
                    strength += 25;
                }
                
                // Chiffres
                if (password.match(/\d/)) {
                    strength += 25;
                }
                
                // Caractères spéciaux
                if (password.match(/[^a-zA-Z\d]/)) {
                    strength += 25;
                }
                
                // Mettre à jour les segments actifs
                let activeSegments = 0;
                if (strength >= 25) activeSegments = 1;
                if (strength >= 50) activeSegments = 2;
                if (strength >= 75) activeSegments = 3;
                if (strength >= 100) activeSegments = 4;
                
                for (let i = 0; i < activeSegments; i++) {
                    strengthSegments[i].classList.add('active');
                }
                
                // Définir le texte et la classe
                strengthText.classList.remove('very-weak', 'weak', 'medium', 'strong');
                
                if (strength <= 25) {
                    strengthText.textContent = 'Très faible';
                    strengthText.classList.add('very-weak');
                } else if (strength <= 50) {
                    strengthText.textContent = 'Faible';
                    strengthText.classList.add('weak');
                } else if (strength <= 75) {
                    strengthText.textContent = 'Moyen';
                    strengthText.classList.add('medium');
                } else {
                    strengthText.textContent = 'Fort';
                    strengthText.classList.add('strong');
                }
            } else {
                strengthText.textContent = 'Très faible';
                strengthText.classList.add('very-weak');
            }
        });
    });
</script>
@endpush
