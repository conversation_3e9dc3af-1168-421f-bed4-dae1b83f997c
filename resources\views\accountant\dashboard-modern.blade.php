@extends('layouts.accountant')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="{{ asset('css/accountant-dashboard-modern-2025-main.css') }}?v={{ time() }}">
@endpush

@section('title', 'Tableau de Bord Moderne')

@section('content')
<div class="dashboard-wrapper dashboard-content" style="padding-top: 80px;">
    <!-- En-tête avec dégradé bleu et éléments 3D -->
    <div class="dashboard-header">
        <div class="header-bg-elements">
            <div class="header-circle circle-1"></div>
            <div class="header-circle circle-2"></div>
            <div class="header-circle circle-3"></div>
        </div>
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="date-display mb-2">
                        <i class="fas fa-calendar-alt me-2"></i>{{ now()->format('l, d F Y') }}
                    </div>
                    <h1 class="mb-2">Tableau de bord comptable</h1>
                    <p>Bienvenue dans votre espace de gestion financière. Consultez les statistiques et suivez les recouvrements en temps réel.</p>
                    <div class="d-flex gap-3 mt-4">
                        <a href="{{ route('accountant.reports.index') }}" class="btn btn-dashboard">
                            <i class="fas fa-chart-bar"></i> Rapports
                        </a>
                        <a href="{{ route('accountant.recoveries.index') }}" class="btn btn-dashboard">
                            <i class="fas fa-hand-holding-usd"></i> Recouvrements
                        </a>
                    </div>
                </div>
                <div class="col-lg-6 d-none d-lg-block">
                    <div class="text-end">
                        <img src="{{ asset('images/dashboard-illustration.svg') }}" alt="Dashboard" class="img-fluid" style="max-height: 200px; opacity: 0.8;">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cartes statistiques avec effets 3D et animations -->
    <div class="stats-section">
        <div class="container">
            <h3 class="section-title fadeInUp"><i class="fas fa-chart-bar me-2"></i>Statistiques des ventes</h3>
            <div class="stats-grid">
                <!-- Carte 1: Total des ventes -->
                <div class="stat-card primary fadeInUp" style="animation-delay: 0.1s;">
                    <div class="stat-card-glow"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-card-trend up">
                            <i class="fas fa-arrow-up"></i> 12%
                        </div>
                    </div>
                    <div class="stat-card-title">Total des ventes</div>
                    <div class="stat-card-value counter" data-target="{{ $totalSales ?? 0 }}">{{ $totalSales ?? 0 }}</div>
                    <div class="stat-card-subtitle">Depuis le début</div>
                </div>

                <!-- Carte 2: Chiffre d'affaires -->
                <div class="stat-card success fadeInUp" style="animation-delay: 0.2s;">
                    <div class="stat-card-glow success"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-card-trend up">
                            <i class="fas fa-arrow-up"></i> 8%
                        </div>
                    </div>
                    <div class="stat-card-title">Chiffre d'affaires</div>
                    <div class="stat-card-value counter" data-target="{{ $totalRevenue ?? 0 }}">{{ number_format($totalRevenue ?? 0) }} F</div>
                    <div class="stat-card-subtitle">Ce mois</div>
                </div>

                <!-- Carte 3: Paiements reçus -->
                <div class="stat-card info fadeInUp" style="animation-delay: 0.3s;">
                    <div class="stat-card-glow info"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <div class="stat-card-trend up">
                            <i class="fas fa-arrow-up"></i> 15%
                        </div>
                    </div>
                    <div class="stat-card-title">Paiements reçus</div>
                    <div class="stat-card-value counter" data-target="{{ $totalPayments ?? 0 }}">{{ number_format($totalPayments ?? 0) }} F</div>
                    <div class="stat-card-subtitle">Ce mois</div>
                </div>

                <!-- Carte 4: Paiements en attente -->
                <div class="stat-card warning fadeInUp" style="animation-delay: 0.4s;">
                    <div class="stat-card-glow warning"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-card-trend down">
                            <i class="fas fa-arrow-down"></i> 5%
                        </div>
                    </div>
                    <div class="stat-card-title">Paiements en attente</div>
                    <div class="stat-card-value counter" data-target="{{ $pendingPayments ?? 0 }}">{{ number_format($pendingPayments ?? 0) }} F</div>
                    <div class="stat-card-subtitle">À recouvrer</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Section des statistiques d'approvisionnement -->
    <div class="stats-section supply-stats-section">
        <div class="container">
            <h3 class="section-title fadeInUp"><i class="fas fa-truck me-2"></i>Statistiques des approvisionnements</h3>
            <div class="stats-grid">
                <!-- Carte 1: Total des approvisionnements -->
                <div class="stat-card info fadeInUp" style="animation-delay: 0.1s;">
                    <div class="stat-card-glow info"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="stat-card-trend neutral">
                            <i class="fas fa-boxes"></i>
                        </div>
                    </div>
                    <div class="stat-card-title">Total approvisionnements</div>
                    <div class="stat-card-value counter" data-target="{{ $totalSupplies ?? 0 }}">{{ $totalSupplies ?? 0 }}</div>
                    <div class="stat-card-subtitle">Montant: {{ number_format($totalSupplyAmount ?? 0, 0, ',', ' ') }} FCFA</div>
                </div>
                
                <!-- Carte 2: Mes approvisionnements -->
                <div class="stat-card primary fadeInUp" style="animation-delay: 0.15s;">
                    <div class="stat-card-glow primary"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-card-trend neutral">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <div class="stat-card-title">Mes approvisionnements</div>
                    <div class="stat-card-value counter" data-target="{{ $mySupplies ?? 0 }}">{{ $mySupplies ?? 0 }}</div>
                    <div class="stat-card-subtitle">{{ $totalSupplies > 0 ? round(($mySupplies / $totalSupplies) * 100) : 0 }}% du total</div>
                </div>

                <!-- Carte 3: Approvisionnements validés -->
                <div class="stat-card success fadeInUp" style="animation-delay: 0.2s;">
                    <div class="stat-card-glow success"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-card-trend up">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                    <div class="stat-card-title">Approvisionnements validés</div>
                    <div class="stat-card-value counter" data-target="{{ $validatedSupplies ?? 0 }}">{{ $validatedSupplies ?? 0 }}</div>
                    <div class="stat-card-subtitle">Tonnage: {{ number_format($totalSupplyTonnage ?? 0, 1, ',', ' ') }} T</div>
                </div>

                <!-- Carte 4: Approvisionnements en attente -->
                <div class="stat-card warning fadeInUp" style="animation-delay: 0.3s;">
                    <div class="stat-card-glow warning"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-card-trend neutral">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <div class="stat-card-title">En attente de validation</div>
                    <div class="stat-card-value counter" data-target="{{ $pendingSupplies ?? 0 }}">{{ $pendingSupplies ?? 0 }}</div>
                    <div class="stat-card-subtitle">{{ $totalSupplies > 0 ? round(($pendingSupplies / $totalSupplies) * 100) : 0 }}% du total</div>
                </div>

                <!-- Carte 5: Approvisionnements rejetés -->
                <div class="stat-card danger fadeInUp" style="animation-delay: 0.4s;">
                    <div class="stat-card-glow danger"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-card-trend down">
                            <i class="fas fa-times"></i>
                        </div>
                    </div>
                    <div class="stat-card-title">Approvisionnements rejetés</div>
                    <div class="stat-card-value counter" data-target="{{ $rejectedSupplies ?? 0 }}">{{ $rejectedSupplies ?? 0 }}</div>
                    <div class="stat-card-subtitle">{{ $totalSupplies > 0 ? round(($rejectedSupplies / $totalSupplies) * 100) : 0 }}% du total</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section de suivi des recouvrements -->
    <div class="container mb-4">
        <div class="dashboard-card fadeInUp" style="animation-delay: 0.5s;">
            <div class="dashboard-card-header">
                <h5 class="dashboard-card-title">
                    <i class="fas fa-chart-line me-2"></i>Suivi des recouvrements
                </h5>
                <div class="d-flex align-items-center">
                    <div class="period-filters me-3">
                        <div class="period-filter" data-period="today">Aujourd'hui</div>
                        <div class="period-filter" data-period="week">Cette semaine</div>
                        <div class="period-filter" data-period="month">Ce mois</div>
                        <div class="period-filter" data-period="quarter">Ce trimestre</div>
                        <div class="period-filter" data-period="year">Cette année</div>
                        <div class="period-filter" data-period="all">Toutes</div>
                    </div>
                    <a href="{{ route('accountant.recoveries.index') }}" class="btn btn-sm btn-primary">
                        Voir tout <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
            <div class="dashboard-card-body">
                <div class="row g-4 mb-4">
                    <div class="col-md-3">
                        <div class="stat-summary primary">
                            <div class="stat-icon"><i class="fas fa-file-invoice"></i></div>
                            <div class="stat-title">Total des factures</div>
                            <div class="stat-value">{{ number_format($totalInvoices ?? 0) }}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-summary success">
                            <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                            <div class="stat-title">Factures payées</div>
                            <div class="stat-value">{{ number_format($paidInvoices ?? 0) }}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-summary warning">
                            <div class="stat-icon"><i class="fas fa-clock"></i></div>
                            <div class="stat-title">Paiements partiels</div>
                            <div class="stat-value">{{ number_format($partialInvoices ?? 0) }}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-summary danger">
                            <div class="stat-icon"><i class="fas fa-exclamation-circle"></i></div>
                            <div class="stat-title">Factures impayées</div>
                            <div class="stat-value">{{ number_format($unpaidInvoices ?? 0) }}</div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead>
                            <tr>
                                <th>Facture</th>
                                <th>Client</th>
                                <th>Montant</th>
                                <th>Date</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if(isset($recentSales) && count($recentSales) > 0)
                                @foreach($recentSales as $sale)
                                    <tr>
                                        <td>
                                            <span class="fw-medium">#{{ $sale->invoice_number ?? 'SALE-'.$sale->id }}</span>
                                        </td>
                                        <td>{{ $sale->customer->name ?? 'Client' }}</td>
                                        <td>{{ number_format($sale->total_amount) }} F</td>
                                        <td>{{ $sale->created_at->format('d/m/Y') }}</td>
                                        <td>
                                            @if($sale->payment_status == 'paid')
                                                <span class="badge badge-paid">Payé</span>
                                            @elseif($sale->payment_status == 'partial')
                                                <span class="badge badge-partial">Partiel</span>
                                            @else
                                                <span class="badge badge-unpaid">Impayé</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="d-flex gap-2">
                                                <a href="{{ route('accountant.recoveries.show', $sale->id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if($sale->payment_status != 'paid')
                                                    <a href="{{ route('accountant.payments.create', ['sale_id' => $sale->id]) }}" class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-plus"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">Aucune vente récente à afficher</p>
                                        </div>
                                    </td>
                                </tr>
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Section graphiques et actions rapides -->
    <div class="container mb-4">
        <div class="row g-4">
            <!-- Graphique des ventes mensuelles -->
            <div class="col-lg-8">
                <div class="chart-card fadeInUp" style="animation-delay: 0.6s;">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-chart-line"></i> Évolution des ventes
                        </h5>
                        <div class="period-filter">
                            <button class="period-btn active" data-period="month">Mois</button>
                            <button class="period-btn" data-period="quarter">Trimestre</button>
                            <button class="period-btn" data-period="year">Année</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="salesChart" data-sales="{{ json_encode($monthlySales ?? []) }}"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Graphique de répartition des paiements -->
            <div class="col-lg-4">
                <div class="chart-card fadeInRight" style="animation-delay: 0.7s;">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-chart-pie"></i> Statut des paiements
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="height: 250px;">
                            <canvas id="paymentsChart" data-payments="{{ json_encode($paymentStats ?? []) }}"></canvas>
                        </div>
                        <div class="mt-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span><i class="fas fa-circle text-success me-2"></i> Payé</span>
                                <span>{{ $paymentStats['paid'] ?? 0 }} ventes</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span><i class="fas fa-circle text-warning me-2"></i> Partiel</span>
                                <span>{{ $paymentStats['partial'] ?? 0 }} ventes</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span><i class="fas fa-circle text-danger me-2"></i> Impayé</span>
                                <span>{{ $paymentStats['unpaid'] ?? 0 }} ventes</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section graphique des approvisionnements -->
    <div class="container mb-4">
        <div class="row g-4">
            <!-- Graphique des approvisionnements mensuels -->
            <div class="col-lg-8">
                <div class="chart-card fadeInUp" style="animation-delay: 0.6s;">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-truck"></i> Évolution des approvisionnements
                        </h5>
                        <div class="period-filter">
                            <button class="period-btn active" data-period="month">Mois</button>
                            <button class="period-btn" data-period="quarter">Trimestre</button>
                            <button class="period-btn" data-period="year">Année</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="suppliesChart" data-supplies="{{ json_encode($supplyChartData ?? []) }}"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Carte de tonnage total -->
            <div class="col-lg-4">
                <div class="chart-card fadeInRight" style="animation-delay: 0.7s;">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-weight"></i> Tonnage par statut
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="tonnage-stats">
                            <div class="tonnage-item">
                                <div class="tonnage-icon bg-success">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div class="tonnage-info">
                                    <h4>{{ number_format($totalSupplyTonnage ?? 0, 1, ',', ' ') }} T</h4>
                                    <p>Tonnage total</p>
                                </div>
                            </div>
                            <div class="progress tonnage-progress mt-3 mb-4">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="status-dot bg-success me-2"></span>
                                    <span>Validés</span>
                                </div>
                                <span class="fw-medium">{{ $validatedSupplies ?? 0 }} approvisionnements</span>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="status-dot bg-warning me-2"></span>
                                    <span>En attente</span>
                                </div>
                                <span class="fw-medium">{{ $pendingSupplies ?? 0 }} approvisionnements</span>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <span class="status-dot bg-danger me-2"></span>
                                    <span>Rejetés</span>
                                </div>
                                <span class="fw-medium">{{ $rejectedSupplies ?? 0 }} approvisionnements</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section actions rapides et activités récentes -->
    <div class="container mb-4">
        <div class="row g-4">
            <!-- Actions rapides -->
            <div class="col-lg-4">
                <div class="dashboard-card fadeInUp" style="animation-delay: 0.8s;">
                    <div class="dashboard-card-header">
                        <h5 class="dashboard-card-title">
                            <i class="fas fa-bolt me-2"></i>Actions rapides
                        </h5>
                    </div>
                    <div class="dashboard-card-body">
                        <div class="quick-actions">
                            <a href="{{ route('accountant.reports.index') }}" class="quick-action primary">
                                <div class="action-icon">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <div class="action-details">
                                    <h6>Rapports financiers</h6>
                                    <p>Consultez tous les rapports financiers</p>
                                </div>
                            </a>
                            <a href="{{ route('accountant.recoveries.index') }}" class="quick-action success">
                                <div class="action-icon">
                                    <i class="fas fa-hand-holding-usd"></i>
                                </div>
                                <div class="action-details">
                                    <h6>Recouvrements</h6>
                                    <p>Gérez les recouvrements des ventes</p>
                                </div>
                            </a>
                            <a href="{{ route('accountant.payments.create') }}" class="quick-action info">
                                <div class="action-icon">
                                    <i class="fas fa-plus-circle"></i>
                                </div>
                                <div class="action-details">
                                    <h6>Nouveau paiement</h6>
                                    <p>Enregistrer un nouveau paiement</p>
                                </div>
                            </a>
                            <a href="{{ route('accountant.reports.sales') }}" class="quick-action warning">
                                <div class="action-icon">
                                    <i class="fas fa-file-invoice-dollar"></i>
                                </div>
                                <div class="action-details">
                                    <h6>Rapport des ventes</h6>
                                    <p>Consultez le rapport détaillé des ventes</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activités récentes -->
            <div class="col-lg-8">
                <div class="dashboard-card fadeInUp" style="animation-delay: 0.9s;">
                    <div class="dashboard-card-header">
                        <h5 class="dashboard-card-title">
                            <i class="fas fa-history me-2"></i>Activités récentes
                        </h5>
                        <div class="period-filters">
                            <div class="period-filter" data-period="today">Aujourd'hui</div>
                            <div class="period-filter" data-period="week">Cette semaine</div>
                            <div class="period-filter" data-period="month">Ce mois</div>
                            <div class="period-filter" data-period="all">Toutes</div>
                        </div>
                    </div>
                    <div class="dashboard-card-body">
                        <div class="activity-list" id="recentActivities">
                            @if(isset($recentActivities) && count($recentActivities) > 0)
                                @foreach($recentActivities as $activity)
                                    <div class="activity-item {{ $activity->type ?? 'primary' }}">
                                        <div class="activity-time">{{ $activity->created_at->diffForHumans() }}</div>
                                        <div class="activity-title">
                                            {{ $activity->title ?? 'Activité' }}
                                            <span class="badge badge-{{ $activity->badge ?? 'primary' }}">{{ $activity->badge_text ?? 'Info' }}</span>
                                        </div>
                                        <div class="activity-description">{{ $activity->description ?? 'Description de l\'activité' }}</div>
                                    </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <p class="text-muted">Aucune activité récente</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{ asset('js/accountant-dashboard-modern-2025.js') }}?v={{ time() }}"></script>
<script src="{{ asset('js/accountant-supply-chart.js') }}?v={{ time() }}"></script>
<script src="{{ asset('js/accountant/my-supplies-stats.js') }}?v={{ time() }}"></script>
@endpush
