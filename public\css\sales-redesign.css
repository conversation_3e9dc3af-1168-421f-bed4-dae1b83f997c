/* Refonte complète du tableau de bord des ventes - 2025 */

/* ====== VARIABLES GLOBALES ====== */
:root {
    /* Palette de couleurs principale */
    --primary: #2563eb;
    --primary-light: #3b82f6;
    --primary-dark: #1d4ed8;
    --primary-bg: rgba(37, 99, 235, 0.08);
    
    /* Couleurs secondaires */
    --secondary: #8b5cf6;
    --secondary-light: #a78bfa;
    --secondary-dark: #7c3aed;
    
    /* Couleurs d'accentuation */
    --accent-1: #0ea5e9;  /* Bleu ciel */
    --accent-2: #10b981;  /* Vert */
    --accent-3: #f59e0b;  /* Orange */
    --accent-4: #ef4444;  /* Rouge */
    
    /* Couleurs neutres */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Couleurs fonctionnelles */
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #0ea5e9;
    
    /* Couleurs pour les types de ventes */
    --direct-sale: var(--primary);
    --discount-sale: #ec4899;
    --price-increase: #f97316;
    
    /* Couleurs pour les statuts */
    --approved: var(--success);
    --pending: var(--warning);
    --rejected: var(--danger);
    
    /* Typographie */
    --font-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'JetBrains Mono', 'SF Mono', 'Fira Code', Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
    
    /* Espacements */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    
    /* Ombres */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Arrondis */
    --radius-sm: 0.125rem;
    --radius: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition: 200ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 300ms cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* ====== STYLES DE BASE ====== */
body {
    font-family: var(--font-sans);
    background-color: var(--gray-50) !important;
    color: var(--gray-800);
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-image: none !important;
}

.container-fluid {
    padding: var(--space-6);
    max-width: 1920px;
    margin: 0 auto;
}

/* ====== ANIMATIONS ====== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes shimmer {
    0% { background-position: -1000px 0; }
    100% { background-position: 1000px 0; }
}

/* ====== LAYOUT PRINCIPAL ====== */
.dashboard-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-6);
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
}

.page-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.page-title i {
    color: var(--primary);
    font-size: 1.5rem;
}

.page-subtitle {
    color: var(--gray-500);
    font-size: 1rem;
    margin-top: var(--space-2);
}

/* ====== CARTES ET CONTENEURS ====== */
.card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: none;
    overflow: hidden;
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-100);
    padding: var(--space-5) var(--space-6);
}

.card-body {
    padding: var(--space-6);
}

.card-footer {
    background: var(--white);
    border-top: 1px solid var(--gray-100);
    padding: var(--space-4) var(--space-6);
}

/* ====== STATISTIQUES ====== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-5);
    margin-bottom: var(--space-6);
}

.stat-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    height: 100%;
    border-top: 4px solid transparent;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-card-sales {
    border-top-color: var(--primary);
}

.stat-card-revenue {
    border-top-color: var(--accent-2);
}

.stat-card-discount {
    border-top-color: var(--accent-3);
}

.stat-card-customers {
    border-top-color: var(--secondary);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-4);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.stat-card-sales .stat-icon {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
}

.stat-card-revenue .stat-icon {
    background: linear-gradient(135deg, var(--accent-2), #0d9488);
}

.stat-card-discount .stat-icon {
    background: linear-gradient(135deg, var(--accent-3), #d97706);
}

.stat-card-customers .stat-icon {
    background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
}

.stat-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-500);
    margin-bottom: var(--space-2);
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: 0.875rem;
    font-weight: 500;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    width: fit-content;
}

.stat-trend.positive {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--accent-2);
}

.stat-trend.negative {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--accent-4);
}

.stat-progress {
    height: 6px;
    background-color: var(--gray-100);
    border-radius: var(--radius-full);
    margin-top: auto;
    overflow: hidden;
}

.stat-progress-bar {
    height: 100%;
    border-radius: var(--radius-full);
    transition: width 1s ease-in-out;
}

.stat-card-sales .stat-progress-bar {
    background: linear-gradient(to right, var(--primary-light), var(--primary));
}

.stat-card-revenue .stat-progress-bar {
    background: linear-gradient(to right, #34d399, var(--accent-2));
}

.stat-card-discount .stat-progress-bar {
    background: linear-gradient(to right, #fbbf24, var(--accent-3));
}

.stat-card-customers .stat-progress-bar {
    background: linear-gradient(to right, var(--secondary-light), var(--secondary));
}

/* ====== FILTRES ====== */
.filters-container {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-5);
    box-shadow: var(--shadow);
    margin-bottom: var(--space-6);
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
}

.filters-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.filters-title i {
    color: var(--primary);
}

.filter-pills {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
}

.filter-pill {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 500;
    background-color: var(--gray-100);
    color: var(--gray-700);
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid transparent;
}

.filter-pill:hover {
    background-color: var(--gray-200);
    transform: translateY(-2px);
}

.filter-pill.active {
    background-color: var(--primary-bg);
    color: var(--primary);
    border-color: var(--primary-light);
    font-weight: 600;
}

.filter-pill i {
    font-size: 0.75rem;
}

.search-container {
    position: relative;
}

.search-input {
    width: 100%;
    padding: var(--space-3) var(--space-3) var(--space-3) var(--space-10);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    font-size: 0.875rem;
    transition: var(--transition);
}

.search-input:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.search-icon {
    position: absolute;
    left: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    font-size: 1rem;
}

/* ====== TABLEAU DES VENTES ====== */
.table-container {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.table-header {
    padding: var(--space-5) var(--space-6);
    border-bottom: 1px solid var(--gray-100);
}

.table-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.table-title i {
    color: var(--primary);
}

.table-wrapper {
    overflow-x: auto;
    padding: var(--space-2);
}

.sales-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 var(--space-2);
}

.sales-table thead th {
    padding: var(--space-4);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--gray-500);
    background-color: var(--gray-50);
    border: none;
    text-align: left;
}

.sales-table thead th:first-child {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
    padding-left: var(--space-6);
}

.sales-table thead th:last-child {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
    padding-right: var(--space-6);
}

.sales-table th.highlight-header {
    background-color: var(--primary-bg);
    color: var(--primary);
}

.sales-table tbody tr {
    transition: var(--transition);
}

.sales-table tbody tr:hover {
    transform: translateY(-2px);
}

.sales-table tbody td {
    padding: var(--space-4);
    font-size: 0.875rem;
    color: var(--gray-700);
    background-color: var(--gray-50);
    border: none;
    vertical-align: middle;
}

.sales-table tbody td:first-child {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
    padding-left: var(--space-6);
}

.sales-table tbody td:last-child {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
    padding-right: var(--space-6);
}

.sales-table td.type-column,
.sales-table td.validation-column {
    background-color: var(--primary-bg);
}

.status-indicator {
    width: 4px !important;
    min-width: 4px;
    height: 100%;
    border-radius: var(--radius-full);
}

.status-approved {
    background-color: var(--success);
}

.status-pending {
    background-color: var(--warning);
}

.status-rejected {
    background-color: var(--danger);
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
}

.badge-direct-sale {
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--primary);
}

.badge-discount-sale {
    background-color: rgba(236, 72, 153, 0.1);
    color: #ec4899;
}

.badge-price-increase {
    background-color: rgba(249, 115, 22, 0.1);
    color: #f97316;
}

.badge-approved {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.badge-pending {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.badge-rejected {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

/* Boutons d'action */
.actions-cell {
    display: flex;
    gap: var(--space-2);
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 0.875rem;
    transition: var(--transition-bounce);
    box-shadow: var(--shadow);
}

.action-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-md);
}

.action-btn-view {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
}

.action-btn-edit {
    background: linear-gradient(135deg, var(--accent-2), #0d9488);
}

.action-btn-delete {
    background: linear-gradient(135deg, var(--accent-4), #b91c1c);
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: var(--space-4) var(--space-6);
    border-top: 1px solid var(--gray-100);
}

.pagination {
    display: flex;
    gap: var(--space-1);
}

.page-item .page-link {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--gray-700);
    border: 1px solid var(--gray-200);
    transition: var(--transition);
}

.page-item .page-link:hover {
    background-color: var(--gray-100);
    color: var(--gray-900);
}

.page-item.active .page-link {
    background-color: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

.page-item.disabled .page-link {
    color: var(--gray-400);
    background-color: var(--gray-50);
    cursor: not-allowed;
}

/* ====== LÉGENDE ====== */
.legend-container {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-5);
    box-shadow: var(--shadow);
    margin-bottom: var(--space-6);
}

.legend-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
}

.legend-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.legend-title i {
    color: var(--primary);
}

.legend-section {
    margin-bottom: var(--space-4);
}

.legend-section-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--space-3);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.legend-section-title i {
    color: var(--primary);
}

.legend-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-3);
}

.legend-item {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--space-3);
    border-left: 3px solid transparent;
}

.legend-item-direct-sale {
    border-left-color: var(--direct-sale);
}

.legend-item-discount-sale {
    border-left-color: var(--discount-sale);
}

.legend-item-price-increase {
    border-left-color: var(--price-increase);
}

.legend-item-approved {
    border-left-color: var(--approved);
}

.legend-item-pending {
    border-left-color: var(--pending);
}

.legend-item-rejected {
    border-left-color: var(--rejected);
}

.legend-item-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-800);
    margin-bottom: var(--space-1);
}

.legend-item-desc {
    font-size: 0.75rem;
    color: var(--gray-500);
}

/* ====== RESPONSIVE ====== */
@media (max-width: 992px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .container-fluid {
        padding: var(--space-4);
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-pills {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: var(--space-2);
    }
    
    .legend-items {
        grid-template-columns: 1fr;
    }
}

/* ====== DARK MODE (optionnel) ====== */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1f2937;
        --gray-50: #111827;
        --gray-100: #1f2937;
        --gray-200: #374151;
        --gray-300: #4b5563;
        --gray-400: #6b7280;
        --gray-500: #9ca3af;
        --gray-600: #d1d5db;
        --gray-700: #e5e7eb;
        --gray-800: #f3f4f6;
        --gray-900: #f9fafb;
    }
}

/* ====== UTILITAIRES ====== */
.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.text-primary {
    color: var(--primary) !important;
}

.text-success {
    color: var(--success) !important;
}

.text-warning {
    color: var(--warning) !important;
}

.text-danger {
    color: var(--danger) !important;
}

.bg-primary {
    background-color: var(--primary) !important;
}

.bg-success {
    background-color: var(--success) !important;
}

.bg-warning {
    background-color: var(--warning) !important;
}

.bg-danger {
    background-color: var(--danger) !important;
}

.fw-medium {
    font-weight: 500 !important;
}

.fw-semibold {
    font-weight: 600 !important;
}

.fw-bold {
    font-weight: 700 !important;
}

.d-flex {
    display: flex !important;
}

.align-items-center {
    align-items: center !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.gap-1 {
    gap: var(--space-1) !important;
}

.gap-2 {
    gap: var(--space-2) !important;
}

.gap-3 {
    gap: var(--space-3) !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mb-1 {
    margin-bottom: var(--space-1) !important;
}

.mb-2 {
    margin-bottom: var(--space-2) !important;
}

.mb-3 {
    margin-bottom: var(--space-3) !important;
}

.mb-4 {
    margin-bottom: var(--space-4) !important;
}

.mt-1 {
    margin-top: var(--space-1) !important;
}

.mt-2 {
    margin-top: var(--space-2) !important;
}

.mt-3 {
    margin-top: var(--space-3) !important;
}

.mt-4 {
    margin-top: var(--space-4) !important;
}

.ms-auto {
    margin-left: auto !important;
}

.me-auto {
    margin-right: auto !important;
}

.p-0 {
    padding: 0 !important;
}

.rounded {
    border-radius: var(--radius) !important;
}

.rounded-lg {
    border-radius: var(--radius-lg) !important;
}

.rounded-xl {
    border-radius: var(--radius-xl) !important;
}

.rounded-full {
    border-radius: var(--radius-full) !important;
}

.shadow {
    box-shadow: var(--shadow) !important;
}

.shadow-md {
    box-shadow: var(--shadow-md) !important;
}

.shadow-lg {
    box-shadow: var(--shadow-lg) !important;
}

.w-100 {
    width: 100% !important;
}

.h-100 {
    height: 100% !important;
}

.position-relative {
    position: relative !important;
}

.position-absolute {
    position: absolute !important;
}

.overflow-hidden {
    overflow: hidden !important;
}

.small {
    font-size: 0.875rem !important;
}

.smaller {
    font-size: 0.75rem !important;
}

.text-muted {
    color: var(--gray-500) !important;
}

/* Animations */
.animate-fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}

.animate-scale-in {
    animation: scaleIn 0.3s ease-out forwards;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-shimmer {
    background: linear-gradient(90deg, var(--gray-100) 0%, var(--gray-200) 50%, var(--gray-100) 100%);
    background-size: 1000px 100%;
    animation: shimmer 2s infinite linear;
}
