/* CSS isolé pour la page d'édition des produits - Évite les conflits */
/* Spécificité élevée pour écraser Bootstrap et autres frameworks */

/* Protection contre les conflits CSS globaux */
body .beautiful-edit-page {
    /* Isolation des styles pour éviter les conflits avec Bootstrap et autres frameworks */
    position: relative !important;
    z-index: 1 !important;
    /* Reset sélectif pour éviter les interférences */
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;
    background: white !important;
    background-color: white !important;
    background-image: none !important;
    min-height: 100vh !important;
    padding: 0 !important;
    margin: 0 !important;
    /* Forcer l'affichage par-dessus les autres éléments */
    width: 100% !important;
    display: block !important;
}

body .beautiful-edit-page * {
    box-sizing: border-box !important;
}

/* FORCE LE FOND BLANC - PRIORITÉ MAXIMALE */
html, body,
.beautiful-edit-page,
body .beautiful-edit-page,
.beautiful-edit-page .container,
.beautiful-edit-page .container-fluid,
.beautiful-edit-page .main-content,
.beautiful-edit-page .content-wrapper,
* {
    background: white !important;
    background-color: white !important;
    background-image: none !important;
}

/* Suppression spécifique des gradients violets/bleus */
[style*="linear-gradient"],
[style*="gradient"],
[style*="#667eea"],
[style*="#764ba2"],
[class*="beautiful-edit-page"],
body[class*="beautiful-edit-page"] {
    background: white !important;
    background-color: white !important;
    background-image: none !important;
    background-attachment: initial !important;
    background-position: initial !important;
    background-repeat: initial !important;
    background-size: initial !important;
}

/* Variables CSS pour le nouveau design - Scope limité à la page d'édition */
body .beautiful-edit-page {
    --primary-color: #6366f1;
    --primary-light: #a5b4fc;
    --primary-dark: #4338ca;
    --secondary-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --border-radius: 16px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Header flottant moderne */
body .beautiful-edit-page .floating-header {
    position: relative !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    border-radius: var(--border-radius) !important;
    margin: 2rem 2rem 0 2rem !important;
    padding: 2rem !important;
    box-shadow: var(--shadow-xl) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    animation: slideInFromTop 0.8s ease-out !important;
}

/* Cartes flottantes */
body .beautiful-edit-page .floating-card {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    border-radius: var(--border-radius) !important;
    margin: 2rem !important;
    box-shadow: var(--shadow-xl) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    overflow: hidden !important;
    animation: slideInFromBottom 0.8s ease-out !important;
}

/* Animations */
@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Boutons interactifs */
.beautiful-edit-page .btn-interactive {
    padding: 1rem 2rem !important;
    border-radius: 12px !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border: none !important;
    position: relative !important;
    overflow: hidden !important;
    box-shadow: var(--shadow-md) !important;
}

.beautiful-edit-page .btn-interactive:hover {
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-lg) !important;
}

.beautiful-edit-page .btn-interactive:active {
    transform: translateY(0) !important;
    box-shadow: var(--shadow-sm) !important;
}

/* Gradients pour les boutons */
.beautiful-edit-page .btn-primary-gradient {
    background: var(--gradient-primary) !important;
    color: white !important;
}

.beautiful-edit-page .btn-success-gradient {
    background: var(--gradient-success) !important;
    color: white !important;
}

.beautiful-edit-page .btn-info-gradient {
    background: var(--gradient-info) !important;
    color: white !important;
}

.beautiful-edit-page .btn-warning-gradient {
    background: var(--gradient-warning) !important;
    color: white !important;
}

/* Formulaires modernes */
.beautiful-edit-page .form-control-modern {
    border: 2px solid rgba(99, 102, 241, 0.1) !important;
    border-radius: 12px !important;
    padding: 1rem 1.5rem !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px) !important;
}

.beautiful-edit-page .form-control-modern:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25) !important;
    background: white !important;
}

/* Labels modernes */
.beautiful-edit-page .form-label-modern {
    font-weight: 600 !important;
    color: var(--dark-color) !important;
    margin-bottom: 0.75rem !important;
    font-size: 0.95rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

/* Cards modernes */
.beautiful-edit-page .card-header-gradient {
    background: var(--gradient-primary) !important;
    color: white !important;
    padding: 1.5rem 2rem !important;
    border: none !important;
    border-radius: 0 !important;
}

.beautiful-edit-page .card-body-modern {
    padding: 2rem !important;
    background: rgba(255, 255, 255, 0.95) !important;
}

.beautiful-edit-page .card-title-modern {
    margin: 0 !important;
    font-size: 1.25rem !important;
    font-weight: 700 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
}

.beautiful-edit-page .card-title-icon {
    width: 40px !important;
    height: 40px !important;
    background: rgba(255, 255, 255, 0.2) !important;
    border-radius: 10px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.1rem !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .beautiful-edit-page .floating-header {
        margin: 1rem !important;
        padding: 1.5rem !important;
    }

    .beautiful-edit-page .floating-card {
        margin: 1rem !important;
    }

    .beautiful-edit-page .card-body-modern {
        padding: 1.5rem !important;
    }

    .beautiful-edit-page .btn-interactive {
        padding: 0.875rem 1.5rem !important;
        font-size: 0.8rem !important;
    }
}

@media (max-width: 480px) {
    .beautiful-edit-page {
        padding: 0.5rem !important;
    }

    .beautiful-edit-page .floating-header {
        margin: 0.5rem !important;
        padding: 1rem !important;
    }

    .beautiful-edit-page .floating-card {
        margin: 0.5rem !important;
    }
}

/* Protection contre les conflits avec les styles existants */
.beautiful-edit-page .form-control {
    /* Réinitialiser les styles Bootstrap qui pourraient interférer */
    background-color: white !important;
    border: 1px solid #ced4da !important;
    border-radius: 0.375rem !important;
}

.beautiful-edit-page .btn {
    /* Réinitialiser les styles Bootstrap pour les boutons */
    display: inline-block !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
    text-align: center !important;
    text-decoration: none !important;
    vertical-align: middle !important;
    cursor: pointer !important;
    user-select: none !important;
    border: 1px solid transparent !important;
    padding: 0.375rem 0.75rem !important;
    font-size: 1rem !important;
    border-radius: 0.375rem !important;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
}

/* Styles spécifiques pour les boutons "Ajouter une ville" - Priorité maximale */
body .beautiful-edit-page .add-city-btn,
body .beautiful-edit-page .add-city-btn-modern,
body .beautiful-edit-page button[data-region-id],
body .beautiful-edit-page button[data-region-name],
body .beautiful-edit-page .region-card-modern .card-body button,
.beautiful-edit-page .add-city-btn,
.beautiful-edit-page .add-city-btn-modern,
.beautiful-edit-page button[data-region-id],
.beautiful-edit-page button[data-region-name] {
    background: #007bff !important;
    background-image: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    border: none !important;
    color: white !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3) !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    cursor: pointer !important;
}

body .beautiful-edit-page .add-city-btn:hover,
body .beautiful-edit-page .add-city-btn-modern:hover,
body .beautiful-edit-page button[data-region-id]:hover,
body .beautiful-edit-page button[data-region-name]:hover,
body .beautiful-edit-page .region-card-modern .card-body button:hover,
.beautiful-edit-page .add-city-btn:hover,
.beautiful-edit-page .add-city-btn-modern:hover,
.beautiful-edit-page button[data-region-id]:hover,
.beautiful-edit-page button[data-region-name]:hover {
    background: #0056b3 !important;
    background-image: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4) !important;
}
