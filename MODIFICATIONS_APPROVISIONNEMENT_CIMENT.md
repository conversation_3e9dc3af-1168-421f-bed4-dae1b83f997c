# Modifications du système d'approvisionnement pour la catégorie Ciment

## Résumé des modifications

Ce document détaille les modifications apportées au système d'approvisionnement pour gérer spécifiquement la catégorie "Ciment" avec un champ de sélection de fichier pour la facture, remplaçant le champ "Date de livraison prévue".

## Modifications apportées

### 1. Base de données

**Fichier**: `database/migrations/2025_07_26_000000_add_invoice_file_to_supplies_table.php`
- Ajout du champ `invoice_file` (nullable) à la table `supplies`
- Stockage du chemin vers le fichier de facture

**Fichier**: `app/Models/Supply.php`
- Ajout de `invoice_file` dans le tableau `$fillable`

### 2. Interface utilisateur

**Fichier**: `resources/views/accountant/supplies/create.blade.php`

#### Modifications HTML :
- Ajout de `enctype="multipart/form-data"` au formulaire pour supporter l'upload de fichiers
- Ajout d'un conteneur `#invoiceFileField` pour le champ de sélection de fichier
- Modification du conteneur `#deliveryDateField` pour le rendre conditionnel
- Champ de fichier accepte : JPG, PNG, PDF (max 5MB)

#### Modifications JavaScript :
- Nouvelle fonction `toggleFieldsForCiment(isCiment)` pour gérer l'affichage conditionnel
- Logique pour basculer entre les champs selon la catégorie sélectionnée
- Gestion des attributs `required` de manière dynamique

### 3. Logique métier

**Fichier**: `app/Http/Controllers/Accountant/AccountantSupplyController.php`

#### Validation conditionnelle :
- Détection automatique de la catégorie "Ciment" via `stripos($category->name, 'ciment')`
- Pour le ciment : `invoice_file` requis, `expected_delivery_date` optionnel
- Pour les autres catégories : `expected_delivery_date` requis, `invoice_file` ignoré

#### Gestion des fichiers :
- Stockage sécurisé dans `public/uploads/avatars/documents/`
- Nomenclature : `timestamp_reference.extension`
- Validation : formats JPG, PNG, PDF, taille max 5MB

## Comportement du système

### Catégorie "Ciment" sélectionnée :
1. Le champ "Date de livraison prévue" disparaît
2. Le champ "Facture d'approvisionnement" apparaît
3. La facture devient obligatoire
4. La date de livraison est automatiquement définie à la date d'approvisionnement

### Autres catégories sélectionnées :
1. Le champ "Facture d'approvisionnement" disparaît
2. Le champ "Date de livraison prévue" apparaît
3. La date de livraison devient obligatoire
4. Aucun fichier n'est requis

## Sécurité et validation

### Validation côté serveur :
- Vérification du type MIME
- Limitation de taille (5MB)
- Formats autorisés : JPEG, JPG, PNG, PDF
- Validation conditionnelle selon la catégorie

### Stockage sécurisé :
- Fichiers stockés dans `public/uploads/avatars/documents/`
- Noms de fichiers uniques avec timestamp
- Accès direct via URL publique

## Tests recommandés

1. **Test catégorie Ciment** :
   - Sélectionner "Ciment" → vérifier que le champ fichier apparaît
   - Essayer de soumettre sans fichier → vérifier l'erreur
   - Uploader un fichier valide → vérifier la sauvegarde

2. **Test autres catégories** :
   - Sélectionner une autre catégorie → vérifier que le champ date apparaît
   - Essayer de soumettre sans date → vérifier l'erreur
   - Remplir la date → vérifier la sauvegarde

3. **Test de basculement** :
   - Changer de catégorie → vérifier que les champs basculent correctement
   - Vérifier que les attributs `required` sont mis à jour

## Compatibilité

- ✅ Compatible avec le système existant
- ✅ Pas d'impact sur les approvisionnements existants
- ✅ Logique conditionnelle transparente pour l'utilisateur
- ✅ Validation robuste côté client et serveur

## Migration des fichiers existants

**Fichier de migration :** `migrate-invoice-files.php`

Ce script migre automatiquement les fichiers de factures existants :
- **Source :** `storage/app/public/invoices/`
- **Destination :** `public/uploads/avatars/documents/`
- **Actions :** Copie les fichiers et met à jour les chemins dans la base de données

### Utilisation :
1. Accéder à `http://votre-domaine/migrate-invoice-files.php`
2. Le script affiche un rapport détaillé de la migration
3. Vérifier que les factures s'affichent correctement
4. Supprimer les anciens fichiers une fois la migration confirmée

## Maintenance

Pour ajouter d'autres catégories avec des comportements spéciaux :
1. Modifier la condition dans `AccountantSupplyController.php` ligne 333
2. Ajouter la logique dans `toggleFieldsForCiment()` dans la vue
3. Adapter les règles de validation selon les besoins
