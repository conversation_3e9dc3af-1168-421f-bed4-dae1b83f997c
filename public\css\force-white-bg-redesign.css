/* Force le fond blanc pour la vue redessinée - Priorité maximale */

html, body, body::before, body::after,
.main-panel, .main-panel::before, .main-panel::after,
.content-wrapper, .content-wrapper::before, .content-wrapper::after,
.container-fluid, .container-fluid::before, .container-fluid::after,
.card, .card::before, .card::after,
.main-card, .main-card::before, .main-card::after {
    background: white !important;
    background-color: white !important;
    background-image: none !important;
    background-attachment: unset !important;
    background-position: unset !important;
    background-repeat: unset !important;
    background-size: unset !important;
    animation: none !important;
}

/* Supprimer tous les dégradés bleus */
[style*="linear-gradient"],
[style*="radial-gradient"],
[class*="bg-gradient"],
[class*="bg-primary"],
[class*="bg-blue"] {
    background: white !important;
    background-image: none !important;
    background-color: white !important;
}

/* Supprimer les animations de fond */
@keyframes cardGlow {
    0%, 50%, 100% {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
        background: white !important;
    }
}

/* Conserver le fond bleu uniquement pour les colonnes Type et Validation */
.sales-table td.type-column,
.sales-table td.validation-column {
    background-color: rgba(37, 99, 235, 0.08) !important;
}

/* Styles supplémentaires pour s'assurer que le fond reste blanc */
/* EXCEPTION: La sidebar garde son style sombre */
.sidebar:not(.modern-sidebar), .sidebar-wrapper:not(.modern-sidebar), .sidebar:not(.modern-sidebar)::before, .sidebar:not(.modern-sidebar)::after {
    background: white !important;
}

.navbar, .navbar-wrapper, .navbar::before, .navbar::after {
    background: white !important;
}

/* Assurer que les textes sont visibles sur fond blanc */
body, p, h1, h2, h3, h4, h5, h6, .text-white {
    color: #333 !important;
}

/* Supprimer les ombres bleues */
.shadow-primary, .shadow-blue {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
}

/* Supprimer les bordures bleues */
.border-primary, .border-blue {
    border-color: #e5e7eb !important;
}

/* Conserver les couleurs des badges et boutons */
.badge, .btn {
    color: inherit !important;
}
