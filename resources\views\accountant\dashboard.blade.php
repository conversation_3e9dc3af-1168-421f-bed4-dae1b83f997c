@extends('layouts.accountant')

@push('styles')
<!-- Styles modernes pour le tableau de bord comptable -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="{{ asset('css/accountant-dashboard-modern-2025-main.css') }}?v={{ time() }}">
<link rel="stylesheet" href="{{ asset('css/accountant-dashboard-additions.css') }}?v={{ time() }}">
<style>
    :root {
    --gradient-danger: linear-gradient(135deg, #EF4444 0%, #F87171 100%);
    --gradient-light: linear-gradient(135deg, #F9FAFB 0%, #FFFFFF 100%);
    --gradient-dark: linear-gradient(135deg, #111827 0%, #374151 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.2) 100%);
    
    /* Effets de profondeur et d'ombre élégants */
    --card-border-radius: 1rem;
    --border-radius: 0.5rem;
    --border-radius-lg: 1.5rem;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --box-shadow-hover: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Transitions et animations modernes */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    
    /* Espacement et typographie */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Variables pour l'effet "glassmorphism" */
    --glass-bg: rgba(255, 255, 255, 0.15);
    --glass-border: rgba(255, 255, 255, 0.25);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

body {
    background-color: var(--body-bg);
    background-image: url('https://images.unsplash.com/photo-1557683316-973673baf926?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
    color: var(--dark);
    position: relative;
    min-height: 100vh;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.03) 0%, rgba(168, 85, 247, 0.03) 100%);
    z-index: -1;
}

.container-fluid {
    padding: var(--spacing-xl);
    max-width: 1600px;
    margin: 0 auto;
}

/* En-tête avec effet glassmorphism moderne */
.dashboard-header {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: var(--spacing-2xl) 0;
    margin-bottom: var(--spacing-2xl);
    color: var(--dark);
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
}

@keyframes gradientAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.dashboard-header::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 120px;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='white' fill-opacity='0.15' d='M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,112C672,96,768,96,864,112C960,128,1056,160,1152,160C1248,160,1344,128,1392,112L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
    background-size: cover;
    background-position: center;
    animation: waveAnimation 10s linear infinite alternate;
}

@keyframes waveAnimation {
    0% { transform: translateX(-5%) scale(1.05); }
    100% { transform: translateX(5%) scale(1.1); }
}

.dashboard-header::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 60%);
    animation: rotateGlow 20s linear infinite;
    z-index: 0;
}

.dashboard-header .container-fluid {
    position: relative;
    z-index: 1;
}

.header-content {
    padding: var(--spacing-md);
}

.dashboard-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-md);
    color: var(--dark);
    font-weight: 500;
}

.date-info, .user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: rgba(255, 255, 255, 0.15);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 50px;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.date-info:hover, .user-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.header-controls {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding: var(--spacing-md);
}

.control-card {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: var(--card-border-radius);
    padding: var(--spacing-md);
    min-width: 200px;
}

.control-title {
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--dark);
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.control-buttons {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-xs);
}

.btn-action {
    background: rgba(255, 255, 255, 0.15);
    color: var(--dark);
    border: none;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 50px;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    font-size: 0.9rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    text-decoration: none;
}

.btn-action:hover {
    background: rgba(var(--primary-rgb), 0.15);
    color: var(--primary);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.btn-action.primary {
    background: rgba(var(--primary-rgb), 0.15);
    color: var(--primary);
}

.btn-action.primary:hover {
    background: var(--primary);
    color: white;
}

.dashboard-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.75rem;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    letter-spacing: 1px;
    position: relative;
    display: inline-block;
    animation: fadeInDown 1s both;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translate3d(0, -20px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

.dashboard-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    margin-right: 1rem;
    background: rgba(255, 255, 255, 0.2);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

.dashboard-header h1:hover i {
    transform: rotate(15deg) scale(1.1);
    background: rgba(255, 255, 255, 0.3);
}

.dashboard-header select {
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
    font-weight: 600;
    transition: var(--transition);
    -webkit-appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='white' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: calc(100% - 1rem) center;
    padding-right: 2.5rem;
}

.dashboard-header select:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.25);
    background-color: rgba(255, 255, 255, 0.3);
}

.dashboard-header .btn-primary {
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
    font-weight: 600;
    transition: var(--transition);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-header .btn-primary:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.dashboard-header .btn-primary:hover::before {
    transform: translateX(100%) rotate(45deg);
}

.dashboard-header .btn-primary:focus {
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.4), 0 6px 20px rgba(0, 0, 0, 0.15);
    outline: none;
}

.dashboard-header .btn-primary i {
    transition: var(--transition);
}

.dashboard-header .btn-primary:hover i {
    transform: rotate(180deg);
}

/* Styles pour les cartes de statistiques */
.stats-card {
    border-radius: var(--card-border-radius);
    background: rgba(255, 255, 255, 0.9);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    height: 100%;
    border: none;
    backdrop-filter: blur(10px);
    transform-style: preserve-3d;
}

.stats-card:hover {
    transform: translateY(-10px) rotateX(5deg);
    box-shadow: 0 1rem 3rem rgba(var(--primary-rgb), 0.3);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 100%;
    background: var(--gradient-primary);
    background-size: 200% 200%;
    animation: gradientAnimation 5s ease infinite;
}

.stats-card.primary-card::before { background: var(--gradient-primary); }
.stats-card.success-card::before { background: var(--gradient-success); }
.stats-card.info-card::before { background: var(--gradient-info); }
.stats-card.warning-card::before { background: var(--gradient-warning); }
.stats-card.danger-card::before { background: var(--gradient-danger); }

.stats-card .card-body {
    padding: 1.75rem;
    position: relative;
    z-index: 1;
    background: linear-gradient(120deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.4) 100%);
}

.stats-card .card-icon {
    position: absolute;
    top: 50%;
    right: 1.5rem;
    transform: translateY(-50%);
    font-size: 3.5rem;
    opacity: 0.15;
    color: var(--dark);
    transition: var(--transition);
    filter: drop-shadow(0 0 5px rgba(var(--primary-rgb), 0.3));
}

.stats-card:hover .card-icon {
    transform: translateY(-50%) scale(1.2) rotate(15deg);
    opacity: 0.25;
    animation: pulseIcon 2s infinite;
}

@keyframes pulseIcon {
    0% { transform: translateY(-50%) scale(1.2) rotate(15deg); }
    50% { transform: translateY(-50%) scale(1.3) rotate(10deg); }
    100% { transform: translateY(-50%) scale(1.2) rotate(15deg); }
}

.stats-card .card-title {
    text-transform: uppercase;
    font-size: 0.85rem;
    font-weight: 800;
    margin-bottom: 0.75rem;
    letter-spacing: 0.1em;
    text-shadow: 0 1px 2px rgba(var(--dark-rgb), 0.1);
}

.stats-card .card-value {
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: 0;
    color: var(--dark);
    text-shadow: 0 2px 4px rgba(var(--dark-rgb), 0.1);
    position: relative;
    display: inline-block;
}

.stats-card .card-value::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 3px;
    background: currentColor;
    opacity: 0.5;
    border-radius: 3px;
}

.stats-card .card-value small {
    font-size: 1rem;
    opacity: 0.7;
    font-weight: 600;
    margin-left: 3px;
}

.stats-card.primary-card .card-title { color: var(--primary); }
.stats-card.success-card .card-title { color: var(--success); }
.stats-card.info-card .card-title { color: var(--info); }

/* Styles pour les cartes avec animation au survol */
.stats-card::after {
    content: '';
    position: absolute;
    top: -100%;
    right: -100%;
    width: 250%;
    height: 250%;
    background: radial-gradient(circle, rgba(255,255,255,0.5) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transform: scale(0.5);
    transition: var(--transition);
    z-index: 0;
}

.stats-card:hover::after {
    opacity: 1;
    transform: scale(1);
    animation: rotateGradient 5s linear infinite;
}

@keyframes rotateGradient {
    0% { transform: scale(1) rotate(0deg); }
    100% { transform: scale(1) rotate(360deg); }
}

.container-fluid {
    padding: 1.5rem;
}

/* Actions rapides avec effet glassmorphism */
.quick-action {
    padding: var(--spacing-xl);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: var(--card-border-radius);
    box-shadow: var(--glass-shadow);
    transition: var(--transition-bounce);
    position: relative;
    overflow: hidden;
    z-index: 1;
    transform-style: preserve-3d;
    perspective: 1000px;
}

.quick-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: 300% 300%;
    opacity: 0;
    z-index: -1;
    transition: all 0.5s cubic-bezier(0.43, 0.195, 0.02, 1);
    animation: gradientAnimation 5s ease infinite;
}

.quick-action:hover {
    transform: translateY(-10px) scale(1.03) rotateX(10deg);
    box-shadow: 0 1.5rem 4rem rgba(var(--primary-rgb), 0.3);
    color: white;
}

.quick-action:hover::before {
    opacity: 1;
}

.quick-action .action-icon {
    width: 5rem;
    height: 5rem;
    border-radius: 50%;
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    margin-bottom: 1.5rem;
    transition: var(--transition);
    position: relative;
    box-shadow: 0 10px 20px rgba(var(--primary-rgb), 0.15);
}

.quick-action .action-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8), transparent 70%);
    opacity: 0.7;
    z-index: 1;
}

.quick-action .action-icon::after {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.5);
    opacity: 0;
    transform: scale(1.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.quick-action:hover .action-icon {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    transform: scale(1.15) rotate(15deg);
    animation: floatIcon 3s ease-in-out infinite;
}

@keyframes floatIcon {
    0% { transform: scale(1.15) rotate(15deg) translateY(0); }
    50% { transform: scale(1.15) rotate(10deg) translateY(-10px); }
    100% { transform: scale(1.15) rotate(15deg) translateY(0); }
}

.quick-action:hover .action-icon::after {
    opacity: 1;
    transform: scale(1);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

.quick-action .action-title {
    font-weight: 800;
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
    transition: var(--transition);
    text-shadow: 0 2px 4px rgba(var(--dark-rgb), 0.1);
    position: relative;
    display: inline-block;
}

.quick-action .action-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 2px;
    background: white;
    transform: translateX(-50%);
    transition: width 0.3s ease;
}

.quick-action:hover .action-title::after {
    width: 80%;
}

.quick-action .action-description {
    font-size: 0.9rem;
    opacity: 0.7;
    transition: var(--transition);
    max-width: 90%;
    margin: 0 auto;
}

.quick-action:hover .action-title,
.quick-action:hover .action-description {
    color: white;
    transform: translateZ(20px);
}

/* Variantes de couleurs pour les actions rapides */
.quick-action.primary::before { background: var(--gradient-primary); }
.quick-action.success::before { background: var(--gradient-success); }
.quick-action.info::before { background: var(--gradient-info); }
.quick-action.warning::before { background: var(--gradient-warning); }
.quick-action.danger::before { background: var(--gradient-danger); }

.quick-action.primary .action-icon { background-color: rgba(var(--primary-rgb), 0.15); color: var(--primary); }
.quick-action.success .action-icon { background-color: rgba(var(--success-rgb), 0.15); color: var(--success); }
.quick-action.info .action-icon { background-color: rgba(var(--info-rgb), 0.15); color: var(--info); }
.quick-action.warning .action-icon { background-color: rgba(var(--warning-rgb), 0.15); color: var(--warning); }
.quick-action.danger .action-icon { background-color: rgba(var(--danger-rgb), 0.15); color: var(--danger); }

/* Cartes de statistiques avec effet glassmorphism moderne */
.stats-card {
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-md);
    border-radius: var(--card-border-radius);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    height: 100%;
    position: relative;
    overflow: hidden;
    transition: var(--transition-bounce);
    transform-style: preserve-3d;
    perspective: 1000px;
    z-index: 1;
    display: flex;
    flex-direction: column;
}

.stats-card .card-body {
    flex: 1;
    position: relative;
    z-index: 2;
    padding-bottom: var(--spacing-md);
}

.stats-card .card-trend {
    font-size: 0.85rem;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 50px;
    background: rgba(var(--primary-rgb), 0.08);
    color: var(--primary);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    margin-top: auto;
    transition: var(--transition);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    align-self: flex-start;
    margin-left: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    transform: translateZ(5px);
}

.stats-card .card-trend i {
    margin-right: var(--spacing-xs);
    font-size: 0.8rem;
}

.stats-card .card-trend.success {
    background: rgba(var(--success-rgb), 0.08);
    color: var(--success);
}

.stats-card .card-trend.warning {
    background: rgba(var(--warning-rgb), 0.08);
    color: var(--warning);
}

.stats-card .card-trend.danger {
    background: rgba(var(--danger-rgb), 0.08);
    color: var(--danger);
}

.stats-card:hover .card-trend {
    transform: translateZ(15px) scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

/* Graphiques et tableaux avec effet glassmorphism */
.chart-card, .table-card {
    border-radius: var(--card-border-radius);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    margin-bottom: var(--spacing-xl);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
    transform-style: preserve-3d;
    perspective: 1000px;
}

.chart-card:hover, .table-card:hover {
    box-shadow: 0 1rem 3rem rgba(var(--primary-rgb), 0.3);
    transform: translateY(-10px) scale(1.01);
}

.chart-card .card-header, .table-card .card-header {
    padding: 1.5rem;
    background: var(--gradient-primary);
    color: white;
    border-bottom: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    overflow: hidden;
    animation: gradientAnimation 15s ease infinite;
    background-size: 300% 300%;
}

.chart-card .card-header::before, .table-card .card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 60%);
    transform: rotate(30deg);
    z-index: 0;
    animation: rotateGlow 10s linear infinite;
}

@keyframes rotateGlow {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.chart-card .card-header::after, .table-card .card-header::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='white' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.3;
    z-index: 0;
}

.chart-card .card-header h5, .table-card .card-header h5 {
    margin: 0;
    font-weight: 800;
    display: flex;
    align-items: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    z-index: 1;
    font-size: 1.25rem;
    letter-spacing: 0.5px;
}

.chart-card .card-header h5 i, .table-card .card-header h5 i {
    margin-right: 1rem;
    background: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    font-size: 1.25rem;
    transition: var(--transition);
}

.chart-card:hover .card-header h5 i, .table-card:hover .card-header h5 i {
    transform: rotate(15deg) scale(1.1);
    background: rgba(255, 255, 255, 0.3);
}

.chart-card .card-body, .table-card .card-body {
    padding: 1.75rem;
    background: white;
    position: relative;
    z-index: 1;
}

/* Effet de brillance sur les cartes */
.chart-card::after, .table-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
    transition: none;
}

.chart-card:hover::after, .table-card:hover::after {
    animation: shine 1.5s infinite;
}

@keyframes shine {
    100% {
        transform: translateX(50%);
    }
}

/* Styles pour les tableaux */
.table-responsive {
    overflow-x: auto;
}

.custom-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.custom-table th {
    background-color: rgba(var(--primary-rgb), 0.05);
    color: var(--primary);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(var(--primary-rgb), 0.1);
}

.custom-table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    vertical-align: middle;
}

.custom-table tr:last-child td {
    border-bottom: none;
}

.custom-table tr:hover td {
    background-color: rgba(var(--primary-rgb), 0.02);
}

/* Activités récentes avec effet glassmorphism */
.activity-timeline {
    position: relative;
    padding-left: var(--spacing-xl);
    perspective: 1000px;
}

.activity-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0.75rem;
    height: 100%;
    width: 3px;
    background: var(--gradient-primary);
    opacity: 0.5;
    border-radius: 3px;
    animation: pulseTimeline 3s infinite;
}

@keyframes pulseTimeline {
    0% { opacity: 0.5; box-shadow: 0 0 5px rgba(var(--primary-rgb), 0.3); }
    50% { opacity: 0.8; box-shadow: 0 0 15px rgba(var(--primary-rgb), 0.5); }
    100% { opacity: 0.5; box-shadow: 0 0 5px rgba(var(--primary-rgb), 0.3); }
}

.activity-item {
    padding: var(--spacing-lg);
    position: relative;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: var(--card-border-radius);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--glass-shadow);
    transition: var(--transition);
    transform-style: preserve-3d;
    animation: fadeInRight 0.5s both;
    animation-delay: calc(var(--animation-order, 0) * 0.1s);
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translate3d(20px, 0, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

.activity-item:hover {
    transform: translateX(10px) translateY(-5px) rotateX(5deg);
    box-shadow: 0 1rem 2rem rgba(var(--primary-rgb), 0.2);
}

.activity-item::before {
    content: '';
    position: absolute;
    left: -2.25rem;
    top: 1.5rem;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary);
    border: 4px solid white;
    box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.3), 0 5px 10px rgba(0, 0, 0, 0.2);
    z-index: 1;
    transition: var(--transition);
    animation: pulsePoint 2s infinite;
}

@keyframes pulsePoint {
    0% { transform: scale(1); box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.3), 0 5px 10px rgba(0, 0, 0, 0.2); }
    50% { transform: scale(1.1); box-shadow: 0 0 0 6px rgba(var(--primary-rgb), 0.4), 0 5px 15px rgba(0, 0, 0, 0.3); }
    100% { transform: scale(1); box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.3), 0 5px 10px rgba(0, 0, 0, 0.2); }
}

.activity-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 50%);
    border-radius: var(--card-border-radius);
    z-index: -1;
}

.activity-item.success { border-left-color: var(--success); background: linear-gradient(to right, rgba(var(--success-rgb), 0.05), white); }
.activity-item.warning { border-left-color: var(--warning); background: linear-gradient(to right, rgba(var(--warning-rgb), 0.05), white); }
.activity-item.danger { border-left-color: var(--danger); background: linear-gradient(to right, rgba(var(--danger-rgb), 0.05), white); }
.activity-item.info { border-left-color: var(--info); background: linear-gradient(to right, rgba(var(--info-rgb), 0.05), white); }

.activity-item.success::before { 
    background: var(--gradient-success); 
    box-shadow: 0 0 0 4px rgba(var(--success-rgb), 0.3), 0 5px 10px rgba(0, 0, 0, 0.2);
    animation: pulsePointSuccess 2s infinite;
}

@keyframes pulsePointSuccess {
    0% { transform: scale(1); box-shadow: 0 0 0 4px rgba(var(--success-rgb), 0.3), 0 5px 10px rgba(0, 0, 0, 0.2); }
    50% { transform: scale(1.1); box-shadow: 0 0 0 6px rgba(var(--success-rgb), 0.4), 0 5px 15px rgba(0, 0, 0, 0.3); }
    100% { transform: scale(1); box-shadow: 0 0 0 4px rgba(var(--success-rgb), 0.3), 0 5px 10px rgba(0, 0, 0, 0.2); }
}

.activity-item.warning::before { 
    background: var(--gradient-warning); 
    box-shadow: 0 0 0 4px rgba(var(--warning-rgb), 0.3), 0 5px 10px rgba(0, 0, 0, 0.2);
    animation: pulsePointWarning 2s infinite;
}

@keyframes pulsePointWarning {
    0% { transform: scale(1); box-shadow: 0 0 0 4px rgba(var(--warning-rgb), 0.3), 0 5px 10px rgba(0, 0, 0, 0.2); }
    50% { transform: scale(1.1); box-shadow: 0 0 0 6px rgba(var(--warning-rgb), 0.4), 0 5px 15px rgba(0, 0, 0, 0.3); }
    100% { transform: scale(1); box-shadow: 0 0 0 4px rgba(var(--warning-rgb), 0.3), 0 5px 10px rgba(0, 0, 0, 0.2); }
}

.activity-item.danger::before { 
    background: var(--gradient-danger); 
    box-shadow: 0 0 0 4px rgba(var(--danger-rgb), 0.3), 0 5px 10px rgba(0, 0, 0, 0.2);
    animation: pulsePointDanger 2s infinite;
}

@keyframes pulsePointDanger {
    0% { transform: scale(1); box-shadow: 0 0 0 4px rgba(var(--danger-rgb), 0.3), 0 5px 10px rgba(0, 0, 0, 0.2); }
    50% { transform: scale(1.1); box-shadow: 0 0 0 6px rgba(var(--danger-rgb), 0.4), 0 5px 15px rgba(0, 0, 0, 0.3); }
    100% { transform: scale(1); box-shadow: 0 0 0 4px rgba(var(--danger-rgb), 0.3), 0 5px 10px rgba(0, 0, 0, 0.2); }
}

.activity-item.info::before { 
    background: var(--gradient-info); 
    box-shadow: 0 0 0 4px rgba(var(--info-rgb), 0.3), 0 5px 10px rgba(0, 0, 0, 0.2);
    animation: pulsePointInfo 2s infinite;
}

@keyframes pulsePointInfo {
    0% { transform: scale(1); box-shadow: 0 0 0 4px rgba(var(--info-rgb), 0.3), 0 5px 10px rgba(0, 0, 0, 0.2); }
    50% { transform: scale(1.1); box-shadow: 0 0 0 6px rgba(var(--info-rgb), 0.4), 0 5px 15px rgba(0, 0, 0, 0.3); }
    100% { transform: scale(1); box-shadow: 0 0 0 4px rgba(var(--info-rgb), 0.3), 0 5px 10px rgba(0, 0, 0, 0.2); }
}

.activity-item .activity-title {
    font-weight: 800;
    margin-bottom: 0.75rem;
    color: var(--dark);
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 1.1rem;
    text-shadow: 0 1px 2px rgba(var(--dark-rgb), 0.1);
    transition: var(--transition);
}

.activity-item:hover .activity-title {
    transform: translateZ(10px);
}

.activity-item .activity-title .badge {
    font-size: 0.75rem;
    padding: 0.35rem 1rem;
    border-radius: 50px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    font-weight: 600;
    letter-spacing: 0.5px;
    animation: fadeIn 0.5s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.activity-item .activity-content {
    color: rgba(var(--dark-rgb), 0.7);
    font-size: 0.95rem;
    margin-bottom: 0.75rem;
    transition: var(--transition);
}

.activity-item:hover .activity-content {
    transform: translateZ(5px);
}

.activity-item .activity-time {
    font-size: 0.8rem;
    color: rgba(var(--dark-rgb), 0.5);
    margin-top: 0.75rem;
    display: flex;
    align-items: center;
    font-weight: 600;
    transition: var(--transition);
}

.activity-item:hover .activity-time {
    transform: translateZ(8px);
}

.activity-item .activity-time i {
    margin-right: 0.5rem;
    opacity: 0.7;
    font-size: 0.9rem;
    color: var(--primary);
}

/* Stats cards */
.stats-card {
    border: none;
    border-radius: var(--card-border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition-bounce);
    overflow: hidden;
    height: 100%;
    margin-bottom: 1.5rem;
    position: relative;
    background: var(--card-bg);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
    z-index: 1;
}

.stats-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    z-index: 2;
    transition: var(--transition);
}

.stats-card .card-body {
    position: relative;
    z-index: 1;
}

.stats-card .card-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--dark);
    margin-bottom: 1.25rem;
    position: relative;
    display: inline-block;
    transition: var(--transition);
}

.stats-card .card-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: currentColor;
    transition: width 0.3s ease;
}

.stats-card:hover .card-title::after {
    width: 100%;
}

.stats-card .card-value {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.75rem;
    color: var(--primary);
    text-shadow: 0 2px 10px rgba(var(--primary-rgb), 0.2);
    transition: var(--transition);
    transform: translateZ(0);
    display: inline-block;
    position: relative;
}

.stats-card:hover .card-value {
    transform: translateZ(20px);
    animation: pulseValue 2s infinite;
}

@keyframes pulseValue {
    0% { transform: translateZ(20px) scale(1); }
    50% { transform: translateZ(20px) scale(1.05); }
    100% { transform: translateZ(20px) scale(1); }
}

.stats-card .card-icon {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    font-size: 3rem;
    color: rgba(var(--primary-rgb), 0.15);
    transition: var(--transition-bounce);
    filter: drop-shadow(0 5px 15px rgba(var(--primary-rgb), 0.2));
}

.stats-card:hover .card-icon {
    transform: scale(1.3) rotate(25deg) translateZ(30px);
    color: rgba(var(--primary-rgb), 0.25);
    animation: floatIcon 3s ease-in-out infinite;
}

@keyframes floatIcon {
    0% { transform: scale(1.3) rotate(25deg) translateZ(30px) translateY(0); }
    50% { transform: scale(1.3) rotate(20deg) translateZ(30px) translateY(-10px); }
    100% { transform: scale(1.3) rotate(25deg) translateZ(30px) translateY(0); }
}

.stats-card.primary-card { 
    border-left-color: var(--primary); 
    background: linear-gradient(to right, rgba(var(--primary-rgb), 0.03), white);
}

.stats-card.success-card { 
    border-left-color: var(--success); 
    background: linear-gradient(to right, rgba(var(--success-rgb), 0.03), white);
}

.stats-card.warning-card { 
    border-left-color: var(--warning); 
    background: linear-gradient(to right, rgba(var(--warning-rgb), 0.03), white);
}

.stats-card.danger-card { 
    border-left-color: var(--danger); 
    background: linear-gradient(to right, rgba(var(--danger-rgb), 0.03), white);
}

.stats-card.primary-card .card-value { 
    color: var(--primary); 
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stats-card.success-card .card-value { 
    color: var(--success); 
    background: var(--gradient-success);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stats-card.warning-card .card-value { 
    color: var(--warning); 
    background: var(--gradient-warning);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stats-card.danger-card .card-value { 
    color: var(--danger); 
    background: var(--gradient-danger);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stats-card.primary-card .card-icon { color: rgba(var(--primary-rgb), 0.15); }
.stats-card.success-card .card-icon { color: rgba(var(--success-rgb), 0.15); }
.stats-card.warning-card .card-icon { color: rgba(var(--warning-rgb), 0.15); }
.stats-card.danger-card .card-icon { color: rgba(var(--danger-rgb), 0.15); }

.stats-card {
    animation: fadeInUp 0.6s both;
    animation-delay: calc(var(--animation-order, 0) * 0.1s);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 30px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

.stats-card.primary-card::after { background-color: var(--primary-color); }
.stats-card.success-card::after { background-color: var(--success-color); }
.stats-card.danger-card::after { background-color: var(--danger-color); }
.stats-card.warning-card::after { background-color: var(--warning-color); }
.stats-card.info-card::after { background-color: var(--info-color); }
.stats-card.purple-card::after { background-color: var(--purple-color); }
.stats-card.teal-card::after { background-color: var(--teal-color); }

.stats-card.primary-card { background: linear-gradient(to bottom right, white, var(--primary-bg)); }
.stats-card.success-card { background: linear-gradient(to bottom right, white, var(--success-bg)); }
.stats-card.danger-card { background: linear-gradient(to bottom right, white, var(--danger-bg)); }
.stats-card.warning-card { background: linear-gradient(to bottom right, white, var(--warning-bg)); }
.stats-card.info-card { background: linear-gradient(to bottom right, white, var(--info-bg)); }
.stats-card.purple-card { background: linear-gradient(to bottom right, white, var(--purple-bg)); }
.stats-card.teal-card { background: linear-gradient(to bottom right, white, var(--teal-bg)); }

.stats-card.primary-card .card-title { color: var(--primary-color); }
.stats-card.success-card .card-title { color: var(--success-color); }
.stats-card.danger-card .card-title { color: var(--danger-color); }
.stats-card.warning-card .card-title { color: var(--warning-color); }
.stats-card.info-card .card-title { color: var(--info-color); }
.stats-card.purple-card .card-title { color: var(--purple-color); }
.stats-card.teal-card .card-title { color: var(--teal-color); }

/* Chart */
.chart-container {
    position: relative;
    margin: auto;
    height: 350px;
    width: 100%;
    min-height: 300px;
}

.chart-card {
    border-radius: var(--card-border-radius);
    box-shadow: var(--box-shadow);
    border: none;
    overflow: hidden;
    transition: var(--transition-bounce);
    background: var(--card-bg);
    position: relative;
}

.chart-card:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-3px);
}

.chart-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, var(--primary-color), var(--purple-color));
    z-index: 10;
}

.chart-card .card-header {
    background-color: var(--card-bg);
    border-bottom: 1px solid var(--light-darker);
    padding: 1.25rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chart-card .card-header h5 {
    margin-bottom: 0;
    font-weight: 700;
    color: var(--dark-color);
    display: flex;
    align-items: center;
}

.chart-card .card-header h5 i {
    color: var(--primary-color);
    margin-right: 0.75rem;
    font-size: 1.25rem;
}

.chart-card .card-body {
    padding: 1.5rem;
    background: linear-gradient(to bottom, var(--card-bg), var(--light-color));
}

/* Tables */
.table-card {
    border-radius: var(--card-border-radius);
    box-shadow: var(--box-shadow);
    border: none;
    overflow: hidden;
    transition: var(--transition-bounce);
    margin-bottom: 1.5rem;
    background: var(--card-bg);
    position: relative;
}

.table-card:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-3px);
}

.table-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, var(--teal-color), var(--info-color));
    z-index: 10;
}

.table-card .card-header {
    background-color: var(--card-bg);
    border-bottom: 1px solid var(--light-darker);
    padding: 1.25rem 1.5rem;
}

.table-card .card-header h5 {
    margin-bottom: 0;
    font-weight: 700;
    color: var(--dark-color);
    display: flex;
    align-items: center;
}

.table-card .card-header h5 i {
    color: var(--teal-color);
    margin-right: 0.75rem;
    font-size: 1.25rem;
}

.table-card .card-body {
    padding: 0;
}

.table {
    margin-bottom: 0;
    width: 100%;
}

.table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    background-color: var(--light-dark);
    border-top: none;
    padding: 0.85rem 1.5rem;
    color: var(--dark-light);
}

.table td {
    vertical-align: middle;
    padding: 1.1rem 1.5rem;
    border-top: 1px solid var(--light-darker);
    color: var(--dark-color);
    font-weight: 500;
}

.table-hover tbody tr:hover {
    background-color: var(--light-dark);
}

.table .badge {
    font-weight: 600;
    padding: 0.5em 0.85em;
    border-radius: 50px;
    font-size: 0.75rem;
    letter-spacing: 0.03em;
    box-shadow: var(--box-shadow-sm);
}

.table .badge.bg-success {
    background-color: var(--success-color) !important;
}

.table .badge.bg-warning {
    background-color: var(--warning-color) !important;
}

.table .badge.bg-danger {
    background-color: var(--danger-color) !important;
}

.table .badge.bg-info {
    background-color: var(--info-color) !important;
}

.table .badge.bg-primary {
    background-color: var(--primary-color) !important;
}

.table .btn-icon {
    width: 2.25rem;
    height: 2.25rem;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    background-color: var(--light-dark);
    color: var(--dark-light);
    box-shadow: var(--box-shadow-sm);
}

.table .btn-icon:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Quick actions */
.quick-action {
    transition: var(--transition-bounce);
    padding: 1.75rem 1.25rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: var(--card-bg);
    border-radius: var(--card-border-radius);
    box-shadow: var(--box-shadow);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.quick-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.5) 0%, rgba(255,255,255,0) 100%);
    z-index: -1;
}

.quick-action::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--primary-color);
    transition: var(--transition);
    z-index: 0;
}

.quick-action:hover {
    transform: translateY(-7px);
    box-shadow: var(--box-shadow-lg);
}

.quick-action:hover::after {
    height: 6px;
}

.quick-action .action-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    background: var(--primary-bg);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    margin-bottom: 1.25rem;
    transition: var(--transition-bounce);
    box-shadow: var(--box-shadow-sm);
    position: relative;
    z-index: 2;
}

.quick-action:nth-child(1) .action-icon {
    background: var(--primary-bg);
    color: var(--primary-color);
}

.quick-action:nth-child(2) .action-icon {
    background: var(--info-bg);
    color: var(--info-color);
}

.quick-action:nth-child(3) .action-icon {
    background: var(--success-bg);
    color: var(--success-color);
}

.quick-action:nth-child(4) .action-icon {
    background: var(--warning-bg);
    color: var(--warning-color);
}

.quick-action:nth-child(1)::after { background: var(--primary-color); }
.quick-action:nth-child(2)::after { background: var(--info-color); }
.quick-action:nth-child(3)::after { background: var(--success-color); }
.quick-action:nth-child(4)::after { background: var(--warning-color); }

.quick-action:hover .action-icon {
    transform: scale(1.15) rotate(10deg);
}

.quick-action:nth-child(1):hover .action-icon {
    background: var(--primary-color);
    color: white;
}

.quick-action:nth-child(2):hover .action-icon {
    background: var(--info-color);
    color: white;
}

.quick-action:nth-child(3):hover .action-icon {
    background: var(--success-color);
    color: white;
}

.quick-action:nth-child(4):hover .action-icon {
    background: var(--warning-color);
    color: white;
}

.quick-action h5 {
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
    font-size: 1.1rem;
    position: relative;
    z-index: 2;
}

.quick-action p {
    color: var(--gray-dark);
    margin-bottom: 0;
    font-size: 0.875rem;
    position: relative;
    z-index: 2;
}

/* Activities */
.activity-item {
    border-left: 4px solid var(--primary-color);
    padding: 1.5rem;
    margin-bottom: 0;
    position: relative;
    background-color: var(--card-bg);
    transition: var(--transition);
    z-index: 1;
}

.activity-item:not(:last-child) {
    border-bottom: 1px solid var(--light-darker);
}

.activity-item::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 1.5rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--primary-color);
    border: 2px solid white;
    transition: var(--transition-bounce);
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
    z-index: 2;
}

.activity-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, var(--primary-bg) 0%, rgba(255,255,255,0) 10%);
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.activity-item:hover {
    background-color: var(--light-color);
}

.activity-item:hover::before {
    transform: scale(1.4);
    box-shadow: 0 0 0 6px rgba(99, 102, 241, 0.15);
}

.activity-item:hover::after {
    opacity: 1;
}

/* Styles par type d'activité */
.activity-item.success { border-left-color: var(--success-color); }
.activity-item.warning { border-left-color: var(--warning-color); }
.activity-item.danger { border-left-color: var(--danger-color); }
.activity-item.info { border-left-color: var(--info-color); }
.activity-item.purple { border-left-color: var(--purple-color); }
.activity-item.teal { border-left-color: var(--teal-color); }

.activity-item.success::before { 
    background: var(--success-color); 
    box-shadow: 0 0 0 4px var(--success-bg);
}

.activity-item.warning::before { 
    background: var(--warning-color); 
    box-shadow: 0 0 0 4px var(--warning-bg);
}

.activity-item.danger::before { 
    background: var(--danger-color); 
    box-shadow: 0 0 0 4px var(--danger-bg);
}

.activity-item.info::before { 
    background: var(--info-color); 
    box-shadow: 0 0 0 4px var(--info-bg);
}

.activity-item.purple::before { 
    background: var(--purple-color); 
    box-shadow: 0 0 0 4px var(--purple-bg);
}

.activity-item.teal::before { 
    background: var(--teal-color); 
    box-shadow: 0 0 0 4px var(--teal-bg);
}

.activity-item.success:hover::before { box-shadow: 0 0 0 6px var(--success-bg); }
.activity-item.warning:hover::before { box-shadow: 0 0 0 6px var(--warning-bg); }
.activity-item.danger:hover::before { box-shadow: 0 0 0 6px var(--danger-bg); }
.activity-item.info:hover::before { box-shadow: 0 0 0 6px var(--info-bg); }
.activity-item.purple:hover::before { box-shadow: 0 0 0 6px var(--purple-bg); }
.activity-item.teal:hover::before { box-shadow: 0 0 0 6px var(--teal-bg); }

.activity-item.success::after { background: linear-gradient(to right, var(--success-bg) 0%, rgba(255,255,255,0) 10%); }
.activity-item.warning::after { background: linear-gradient(to right, var(--warning-bg) 0%, rgba(255,255,255,0) 10%); }
.activity-item.danger::after { background: linear-gradient(to right, var(--danger-bg) 0%, rgba(255,255,255,0) 10%); }
.activity-item.info::after { background: linear-gradient(to right, var(--info-bg) 0%, rgba(255,255,255,0) 10%); }
.activity-item.purple::after { background: linear-gradient(to right, var(--purple-bg) 0%, rgba(255,255,255,0) 10%); }
.activity-item.teal::after { background: linear-gradient(to right, var(--teal-bg) 0%, rgba(255,255,255,0) 10%); }

.activity-date {
    font-size: 0.75rem;
    color: var(--gray-color);
    margin-bottom: 0.35rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.activity-date i {
    margin-right: 0.5rem;
    font-size: 0.875rem;
    opacity: 0.7;
}

.activity-title {
    font-weight: 700;
    margin-bottom: 0.35rem;
    color: var(--dark-color);
    font-size: 1rem;
}

.activity-details {
    font-size: 0.875rem;
    color: var(--gray-dark);
    margin-bottom: 0;
}

/* Loading overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    border-radius: var(--card-border-radius);
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(78, 115, 223, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Notification */
.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: var(--card-border-radius);
    background: white;
    box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.15);
    transform: translateY(100%);
    opacity: 0;
    transition: var(--transition);
    z-index: 1050;
    max-width: 90%;
    margin: 0 auto;
    border-left: 4px solid var(--primary-color);
}

.notification.success { border-left-color: var(--success-color); }
.notification.warning { border-left-color: var(--warning-color); }
.notification.danger { border-left-color: var(--danger-color); }

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .chart-container {
        height: 300px;
    }
}

@media (max-width: 992px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .stats-card .card-body {
        padding: 1.25rem;
    }
    
    .stats-card .card-value {
        font-size: 1.5rem;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .table th, .table td {
        padding: 0.75rem 1rem;
    }
    
    .quick-action {
        padding: 1.25rem;
    }
    
    .quick-action .action-icon {
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 0.75rem;
    }
    
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 1rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .chart-container {
        height: 200px;
    }
    
    .chart-card .card-header,
    .table-card .card-header {
        padding: 1rem;
    }
    
    .table th, .table td {
        padding: 0.75rem;
    }
    
    .quick-action {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .quick-action .action-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
        margin-bottom: 0.75rem;
    }
    
    .quick-action h5 {
        font-size: 1rem;
    }
}    }

    h1.h3 {
        font-size: 1.5rem;
    }

    .btn-group-sm > .btn, .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0.25rem;
    }

    .stats-card .h5 {
        font-size: 1rem;
    }

    .stats-card .text-xs {
        font-size: 0.7rem;
    }

    .stats-icon {
        font-size: 1.5rem !important;
    }

    .chart-container {
        height: 180px;
    }

    .notification {
        left: 10px;
        right: 10px;
        bottom: 10px;
        margin: 0;
    }
}
/* Styles pour les titres de section modernes et élégants */
.section-title {
    margin-bottom: var(--spacing-xl);
    font-weight: 800;
    font-size: 1.5rem;
    color: var(--dark);
    display: inline-flex;
    align-items: center;
    position: relative;
    padding-bottom: var(--spacing-sm);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 50px;
    transition: var(--transition);
}

.section-title:hover::after {
    width: 100%;
}

.section-title i {
    margin-right: var(--spacing-md);
    color: var(--primary);
    background: rgba(var(--primary-rgb), 0.1);
    padding: var(--spacing-sm);
    border-radius: 50%;
    font-size: 1.25rem;
    box-shadow: var(--box-shadow);
}
</style>
@endpush

@section('content')
<!-- Bannière de test pour confirmer que nous modifions le bon fichier -->
<div class="dashboard-wrapper" style="margin-top: 70px; padding-top: 20px;">
    <!-- En-tête ultra moderne avec design 3D et animations -->  
    <div class="dashboard-header">
        <div class="header-bg-elements">
            <div class="header-circle circle-1"></div>
            <div class="header-circle circle-2"></div>
            <div class="header-circle circle-3"></div>
            <div class="header-wave"></div>
            <div class="header-glow"></div>
        </div>
        <div class="container position-relative">
            <div class="header-content fadeInUp">
                <div class="header-left">
                    <div class="date-badge">
                        <i class="fas fa-calendar-alt"></i>
                        <span>{{ now()->format('d M Y') }}</span>
                    </div>
                    <div class="header-title-wrapper">
                        <div class="header-icon pulse-animation">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="title-content">
                            <h1 class="header-title">Tableau de bord <span class="badge-new">2025</span></h1>
                            <p class="header-subtitle">Gestion financière et suivi des approvisionnements</p>
                        </div>
                    </div>
                    
                    <div class="header-stats">
                        <div class="header-stat">
                            <div class="header-stat-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="header-stat-info">
                                <span class="header-stat-value">{{ now()->format('d M Y') }}</span>
                                <span class="header-stat-label">Date du jour</span>
                            </div>
                        </div>
                        <div class="header-stat">
                            <div class="header-stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="header-stat-info">
                                <span class="header-stat-value">{{ $stats['monthly_cement_orders'] }}</span>
                                <span class="header-stat-label">Bons ce mois</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-actions">
                        <div class="period-selector-wrapper">
                            <select id="chartPeriod" class="period-selector">
                                <option value="7">7 derniers jours</option>
                                <option value="30">30 derniers jours</option>
                                <option value="90">3 derniers mois</option>
                            </select>
                            <i class="fas fa-calendar-alt period-selector-icon"></i>
                        </div>
                        <button id="refreshStats" class="refresh-button">
                            <i class="fas fa-sync-alt"></i>
                            <span>Actualiser</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section des statistiques avec design 3D et effets de glassmorphisme -->
    <div class="stats-section">
        <div class="container">
            <h3 class="section-title fadeInUp"><i class="fas fa-chart-bar me-2"></i>Statistiques des ventes</h3>
            <div class="stats-grid">
                <!-- Carte 1: Total des bons - Design 3D amélioré -->
                <div class="stat-card primary">
                    <div class="stat-card-glow"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="stat-card-info">
                            <div class="stat-card-label">TOTAL DES BONS</div>
                            <div class="stat-card-value total-orders" data-count="{{ $stats['total_cement_orders'] ?? ($stats['monthly_cement_orders'] ?? 0) }}">{{ $stats['total_cement_orders'] ?? ($stats['monthly_cement_orders'] ?? 0) }}</div>
                        </div>
                    </div>
                    <div class="stat-card-bottom">
                        <div class="stat-card-trend primary">
                            <i class="fas fa-arrow-up"></i> +5.2% depuis le mois dernier
                        </div>
                        <div class="stat-card-progress">
                            <div class="progress-bar primary" style="width: 75%">
                                <div class="progress-glow"></div>
                            </div>
                        </div>
                    </div>
                    <div class="stat-card-decoration">
                        <div class="decoration-circle circle-1"></div>
                        <div class="decoration-circle circle-2"></div>
                    </div>
                    <div class="stat-card-bg-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                </div>

                <!-- Carte 2: Tonnage validé - Design 3D amélioré -->
                <div class="stat-card success">
                    <div class="stat-card-glow success"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-card-info">
                            <div class="stat-card-label">TONNAGE VALIDÉ</div>
                            <div class="stat-card-value validated-tonnage" data-count="{{ $stats['validated_cement_tonnage'] ?? 0 }}">{{ $stats['validated_cement_tonnage'] ?? 0 }}</div>
                        </div>
                    </div>
                    <div class="stat-card-bottom">
                        <div class="stat-card-trend success">
                            <i class="fas fa-arrow-up"></i> +3.7% depuis le mois dernier
                        </div>
                        <div class="stat-card-progress">
                            <div class="progress-bar success" style="width: 65%">
                                <div class="progress-glow success"></div>
                            </div>
                        </div>
                    </div>
                    <div class="stat-card-decoration">
                        <div class="decoration-circle circle-1 success"></div>
                        <div class="decoration-circle circle-2 success"></div>
                    </div>
                    <div class="stat-card-bg-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>

                <!-- Carte 3: Tonnage rejeté - Design 3D amélioré -->
                <div class="stat-card danger">
                    <div class="stat-card-glow danger"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-card-info">
                            <div class="stat-card-label">TONNAGE REJETÉ</div>
                            <div class="stat-card-value rejected-tonnage" data-count="{{ $stats['rejected_cement_tonnage'] ?? 0 }}">{{ $stats['rejected_cement_tonnage'] ?? 0 }}</div>
                        </div>
                    </div>
                    <div class="stat-card-bottom">
                        <div class="stat-card-trend danger">
                            <i class="fas fa-arrow-down"></i> -2.3% depuis le mois dernier
                        </div>
                        <div class="stat-card-progress">
                            <div class="progress-bar danger" style="width: 25%">
                                <div class="progress-glow danger"></div>
                            </div>
                        </div>
                    </div>
                    <div class="stat-card-decoration">
                        <div class="decoration-circle circle-1 danger"></div>
                        <div class="decoration-circle circle-2 danger"></div>
                    </div>
                    <div class="stat-card-bg-icon danger">
                        <i class="fas fa-times-circle"></i>
                    </div>
                </div>

                <!-- Carte 4: Bons en attente - Design 3D amélioré -->
                <div class="stat-card warning">
                    <div class="stat-card-glow warning"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-card-info">
                            <div class="stat-card-label">BONS EN ATTENTE</div>
                            <div class="stat-card-value pending-orders" data-count="{{ $stats['pending_orders'] ?? 0 }}">{{ $stats['pending_orders'] ?? 0 }}</div>
                        </div>
                    </div>
                    <div class="stat-card-bottom">
                        <div class="stat-card-trend warning">
                            <i class="fas fa-arrow-up"></i> +1.8% depuis le mois dernier
                        </div>
                        <div class="stat-card-progress">
                            <div class="progress-bar warning" style="width: 45%">
                                <div class="progress-glow warning"></div>
                            </div>
                        </div>
                    </div>
                    <div class="stat-card-decoration">
                        <div class="decoration-circle circle-1 warning"></div>
                        <div class="decoration-circle circle-2 warning"></div>
                    </div>
                    <div class="stat-card-bg-icon warning">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Section des statistiques d'approvisionnement -->
    <div class="stats-section supply-stats-section">
        <div class="container">
            <h3 class="section-title fadeInUp"><i class="fas fa-truck me-2"></i>Statistiques des approvisionnements</h3>
            <div class="stats-grid">
                <!-- Carte 1: Total des approvisionnements -->
                <div class="stat-card info">
                    <div class="stat-card-glow info"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="stat-card-content">
                            <h3 class="stat-card-title">Total approvisionnements</h3>
                            <div class="stat-card-value counter-animation" data-count="{{ $totalSupplies ?? 0 }}">0</div>
                        </div>
                    </div>
                    <div class="stat-card-bottom">
                        <div class="stat-card-progress">
                            <div class="progress-bar" style="width: 100%"></div>
                        </div>
                        <div class="stat-card-info">
                            <span class="stat-card-label">Montant total:</span>
                            <span class="stat-card-amount">{{ number_format($totalSupplyAmount ?? 0, 0, ',', ' ') }} FCFA</span>
                        </div>
                    </div>
                </div>
                
                <!-- Carte 2: Approvisionnements validés -->
                <div class="stat-card success">
                    <div class="stat-card-glow success"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-card-content">
                            <h3 class="stat-card-title">Approvisionnements validés</h3>
                            <div class="stat-card-value counter-animation" data-count="{{ $validatedSupplies ?? 0 }}">0</div>
                        </div>
                    </div>
                    <div class="stat-card-bottom">
                        <div class="stat-card-progress success">
                            <div class="progress-bar" style="width: {{ $totalSupplies > 0 ? ($validatedSupplies / $totalSupplies) * 100 : 0 }}%"></div>
                        </div>
                        <div class="stat-card-info">
                            <span class="stat-card-label">Tonnage total:</span>
                            <span class="stat-card-amount">{{ number_format($totalSupplyTonnage ?? 0, 1, ',', ' ') }} T</span>
                        </div>
                    </div>
                </div>
                
                <!-- Carte 3: Approvisionnements en attente -->
                <div class="stat-card warning">
                    <div class="stat-card-glow warning"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-card-content">
                            <h3 class="stat-card-title">En attente de validation</h3>
                            <div class="stat-card-value counter-animation" data-count="{{ $pendingSupplies ?? 0 }}">0</div>
                        </div>
                    </div>
                    <div class="stat-card-bottom">
                        <div class="stat-card-progress warning">
                            <div class="progress-bar" style="width: {{ $totalSupplies > 0 ? ($pendingSupplies / $totalSupplies) * 100 : 0 }}%"></div>
                        </div>
                        <div class="stat-card-info">
                            <span class="stat-card-label">Pourcentage:</span>
                            <span class="stat-card-amount">{{ $totalSupplies > 0 ? round(($pendingSupplies / $totalSupplies) * 100) : 0 }}%</span>
                        </div>
                    </div>
                </div>
                
                <!-- Carte 4: Approvisionnements rejetés -->
                <div class="stat-card danger">
                    <div class="stat-card-glow danger"></div>
                    <div class="stat-card-top">
                        <div class="stat-card-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-card-content">
                            <h3 class="stat-card-title">Approvisionnements rejetés</h3>
                            <div class="stat-card-value counter-animation" data-count="{{ $rejectedSupplies ?? 0 }}">0</div>
                        </div>
                    </div>
                    <div class="stat-card-bottom">
                        <div class="stat-card-progress danger">
                            <div class="progress-bar" style="width: {{ $totalSupplies > 0 ? ($rejectedSupplies / $totalSupplies) * 100 : 0 }}%"></div>
                        </div>
                        <div class="stat-card-info">
                            <span class="stat-card-label">Pourcentage:</span>
                            <span class="stat-card-amount">{{ $totalSupplies > 0 ? round(($rejectedSupplies / $totalSupplies) * 100) : 0 }}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section graphique et activités récentes -->
    <div class="dashboard-content">
        <div class="container">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="dashboard-card fadeInUp">
                        <div class="dashboard-card-header">
                            <h5 class="dashboard-card-title">
                                <i class="fas fa-chart-line me-2"></i>Suivi des recouvrements
                            </h5>
                            <div class="dashboard-card-actions d-flex align-items-center">
                                <!-- Filtres de période -->
                                <div class="period-filters me-3">
                                    <button class="period-filter active" data-period="all">Tout</button>
                                    <button class="period-filter" data-period="today">Aujourd'hui</button>
                                    <button class="period-filter" data-period="week">Cette semaine</button>
                                    <button class="period-filter" data-period="month">Ce mois</button>
                                    <button class="period-filter" data-period="year">Cette année</button>
                                </div>
                                <a href="{{ route('accountant.recoveries.index') }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-external-link-alt me-1"></i>Voir tout
                                </a>
                            </div>
                        </div>
                        <div class="dashboard-card-body">
                            <div class="row g-4 mb-4">
                                <div class="col-md-3">
                                    <div class="stat-summary primary">
                                        <div class="stat-icon"><i class="fas fa-file-invoice"></i></div>
                                        <div class="stat-info">
                                            <h3 class="counter-animation" data-count="{{ $totalSales ?? 0 }}">0</h3>
                                            <p>Total des ventes</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-summary success">
                                        <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                                        <div class="stat-info">
                                            <h3 class="counter-animation" data-count="{{ $paidSales ?? 0 }}">0</h3>
                                            <p>Ventes payées</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-summary warning">
                                        <div class="stat-icon"><i class="fas fa-clock"></i></div>
                                        <div class="stat-info">
                                            <h3 class="counter-animation" data-count="{{ $partialSales ?? 0 }}">0</h3>
                                            <p>Paiements partiels</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-summary danger">
                                        <div class="stat-icon"><i class="fas fa-exclamation-triangle"></i></div>
                                        <div class="stat-info">
                                            <h3 class="counter-animation" data-count="{{ $unpaidSales ?? 0 }}">0</h3>
                                            <p>Ventes impayées</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-hover align-middle">
                                    <thead>
                                        <tr>
                                            <th>Facture</th>
                                            <th>Client</th>
                                            <th>Montant</th>
                                            <th>Statut</th>
                                            <th>Progression</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(isset($recentSales) && count($recentSales) > 0)
                                            @foreach($recentSales as $sale)
                                                <tr class="{{ $sale->row_class ?? '' }}">
                                                    <td>
                                                        <span class="fw-bold">{{ $sale->invoice_number }}</span>
                                                        @if(isset($sale->days_since_creation) && $sale->days_since_creation > 15 && $sale->payment_status !== 'paid')
                                                            <span class="badge bg-danger ms-1">{{ $sale->days_since_creation }} jours</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <div class="d-flex flex-column">
                                                            <span>{{ $sale->customer_name }}</span>
                                                            <small class="text-muted">{{ $sale->customer_phone }}</small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex flex-column">
                                                            <span class="fw-bold">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</span>
                                                            <small class="text-muted">Payé: {{ number_format($sale->paid_amount, 0, ',', ' ') }} FCFA</small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        @if($sale->payment_status === 'paid')
                                                            <span class="badge bg-success">Payé</span>
                                                        @elseif($sale->payment_status === 'partial')
                                                            <span class="badge bg-warning text-dark">Partiel</span>
                                                        @else
                                                            <span class="badge bg-danger">Impayé</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <div class="progress" style="height: 8px;">
                                                            <div class="progress-bar {{ $sale->payment_status === 'paid' ? 'bg-success' : ($sale->payment_status === 'partial' ? 'bg-warning' : 'bg-danger') }}" 
                                                                role="progressbar" 
                                                                style="width: {{ $sale->payment_percentage }}%" 
                                                                aria-valuenow="{{ $sale->payment_percentage }}" 
                                                                aria-valuemin="0" 
                                                                aria-valuemax="100">
                                                            </div>
                                                        </div>
                                                        <small>{{ number_format($sale->payment_percentage, 0) }}%</small>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex gap-1">
                                                            <a href="{{ route('accountant.recoveries.show', $sale->id) }}" class="btn btn-sm btn-primary p-1" style="width: 28px; height: 28px;" data-bs-toggle="tooltip" title="Détails"><i class="fas fa-eye"></i></a>
                                                            <a href="{{ route('accountant.recoveries.edit', $sale->id) }}" class="btn btn-sm btn-success p-1" style="width: 28px; height: 28px;" data-bs-toggle="tooltip" title="Paiement"><i class="fas fa-money-bill-wave"></i></a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        @else
                                            <tr>
                                                <td colspan="6" class="text-center py-4">
                                                    <div class="d-flex flex-column align-items-center">
                                                        <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                                        <p class="text-muted">Aucune vente à recouvrer pour le moment</p>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content-grid">
                <!-- Graphique des commandes -->
                <div class="chart-card">
                    <div class="card-header">
                        <div class="card-title">
                            <div class="card-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h3>Évolution des commandes</h3>
                        </div>
                        <div class="card-actions">
                            <button class="action-button" title="Télécharger">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    <div class="chart-controls">
                        <button class="chart-toggle-btn active" data-index="0">Nombre de bons</button>
                        <button class="chart-toggle-btn" data-index="1">Montant (FCFA)</button>
                        <button class="chart-toggle-btn" data-index="2">Tonnage</button>
                    </div>
                    <div class="chart-container">
                        <canvas id="ordersChart"></canvas>
                    </div>
                </div>

                <!-- Activités récentes -->
                <div class="activity-card">
                    <div class="card-header">
                        <div class="card-title">
                            <div class="card-icon">
                                <i class="fas fa-history"></i>
                            </div>
                            <h3>Activités récentes</h3>
                        </div>
                        <div class="card-actions">
                            <button class="action-button" title="Voir tout">
                                <i class="fas fa-external-link-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="recentActivities" class="activity-list">
                            <!-- Les activités seront chargées ici via JavaScript -->
                            <div class="loading-indicator">
                                <div class="spinner">
                                    <i class="fas fa-circle-notch fa-spin"></i>
                                </div>
                                <p>Chargement des activités récentes...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section actions rapides et derniers bons -->
    <div class="container mt-4 mb-4">
        <div class="row g-4">
            <!-- Nouvelles actions rapides avec accès aux recouvrements -->
            <div class="col-12 col-xl-4">
                <div class="dashboard-card fadeInUp delay-3">
                    <div class="dashboard-card-header">
                        <h5 class="dashboard-card-title">
                            <i class="fas fa-bolt me-2"></i>Actions rapides
                        </h5>
                    </div>
                    <div class="dashboard-card-body">
                        <div class="quick-actions-grid">
                            <a href="{{ route('accountant.recoveries.index') }}" class="quick-action warning">
                                <div class="action-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="action-details">
                                    <h6>Recouvrements</h6>
                                    <p>Gérer les paiements en attente</p>
                                </div>
                            </a>
                            
                            <a href="{{ route('accountant.cement-orders.create') }}" class="quick-action primary">
                                <div class="action-icon">
                                    <i class="fas fa-plus-circle"></i>
                                </div>
                                <div class="action-details">
                                    <h6>Nouvel approvisionnement</h6>
                                    <p>Enregistrer un nouvel approvisionnement</p>
                                </div>
                            </a>
                            
                            <a href="{{ route('accountant.reports.index') }}" class="quick-action info">
                                <div class="action-icon">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <div class="action-details">
                                    <h6>Rapports</h6>
                                    <p>Consulter les rapports financiers</p>
                                </div>
                            </a>
                            
                            <a href="{{ route('accountant.payments.create') }}" class="quick-action success">
                                <div class="action-icon">
                                    <i class="fas fa-hand-holding-usd"></i>
                                </div>
                                <div class="action-details">
                                    <h6>Nouveau paiement</h6>
                                    <p>Enregistrer un nouveau paiement</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Actions rapides -->
            <div class="col-12 col-xl-4">
                <div class="dashboard-card fadeInUp delay-3">
                    <div class="dashboard-card-header">
                        <h5 class="dashboard-card-title">
                            <i class="fas fa-bolt"></i>
                            Actions rapides
                        </h5>
                    </div>
                    <div class="dashboard-card-body">
                        <div class="quick-action-grid">
                            <a href="/accountant/supplies/create" class="quick-action">
                                <div class="quick-action-icon">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div class="quick-action-title">Nouveau bon</div>
                                <div class="quick-action-description">Créer un bon d'approvisionnement</div>
                            </a>
                            <a href="/accountant/supplies" class="quick-action">
                                <div class="quick-action-icon">
                                    <i class="fas fa-list"></i>
                                </div>
                                <div class="quick-action-title">Liste des bons</div>
                                <div class="quick-action-description">Gérer tous les bons</div>
                            </a>
                            <a href="/accountant/suppliers" class="quick-action">
                                <div class="quick-action-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="quick-action-title">Fournisseurs</div>
                                <div class="quick-action-description">Gérer les fournisseurs</div>
                            </a>
                            <a href="/accountant/payments" class="quick-action">
                                <div class="quick-action-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="quick-action-title">Paiements</div>
                                <div class="quick-action-description">Suivre les paiements</div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Derniers bons -->
            <div class="col-12 col-xl-8">
                <div class="dashboard-card fadeInUp delay-4">
                    <div class="dashboard-card-header">
                        <h5 class="dashboard-card-title">
                            <i class="fas fa-file-invoice"></i>
                            Derniers bons
                        </h5>
                        <div class="card-actions">
                            <button class="btn-card-action" title="Actualiser">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button class="btn-card-action" title="Voir tous les bons">
                                <i class="fas fa-external-link-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="dashboard-card-body p-0">
                        <div class="table-responsive">
                            <table class="table align-middle mb-0">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Fournisseur</th>
                                        <th>Date</th>
                                        <th>Tonnage</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="latestOrders">
                                    <!-- Les bons seront chargés via JavaScript -->
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status" style="width: 2rem; height: 2rem;">
                                                <span class="visually-hidden">Chargement...</span>
                                            </div>
                                            <p class="mt-2 text-muted">Chargement des derniers bons...</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{ asset('js/accountant-dashboard.js') }}?v={{ time() }}"></script>
<script src="{{ asset('js/accountant-dashboard-filters.js') }}?v={{ time() }}"></script>
<script>
    // Animation des compteurs avec effets modernes
    $(document).ready(function() {
        // Fonction pour animer les compteurs
        function animateCounters() {
            $('.stat-card-value').each(function() {
                var $this = $(this);
                var countTo = $this.attr('data-count');
                var suffix = $this.attr('data-suffix') || '';
                
                // Ajouter un délai aléatoire pour un effet plus dynamique
                var delay = Math.random() * 500;
                
                setTimeout(function() {
                    $({ countNum: 0 }).animate({
                        countNum: countTo
                    }, {
                        duration: 2000,
                        easing: 'swing',
                        step: function() {
                            $this.text(Math.floor(this.countNum) + suffix);
                        },
                        complete: function() {
                            $this.text(this.countNum + suffix);
                            // Ajouter un effet de pulsation à la fin
                            $this.css('transform', 'scale(1.1)');
                            setTimeout(function() {
                                $this.css('transform', 'scale(1)');
                            }, 200);
                        }
                    });
                }, delay);
            });
        }
        
        // Déclencher l'animation après un court délai
        setTimeout(animateCounters, 300);
        
        // Ajouter des effets de survol interactifs
        $('.stat-card').hover(function() {
            $(this).find('.stat-card-icon i').addClass('fa-beat');
        }, function() {
            $(this).find('.stat-card-icon i').removeClass('fa-beat');
        });
    });
    
    // Ajouter une classe CSS pour l'animation de battement
    $('<style>\n\
        @keyframes fa-beat {\n\
            0% { transform: scale(1); }\n\
            50% { transform: scale(1.2); }\n\
            100% { transform: scale(1); }\n\
        }\n\
        .fa-beat {\n\
            animation: fa-beat 1s ease infinite;\n\
        }\n\
        .stat-card.refreshing {\n\
            animation: card-refresh 1s ease-in-out;\n\
        }\n\
        @keyframes card-refresh {\n\
            0% { transform: translateY(0) rotateX(0deg); }\n\
            50% { transform: translateY(-10px) rotateX(10deg); }\n\
            100% { transform: translateY(0) rotateX(0deg); }\n\
        }\n\
    </style>').appendTo('head');

    // Initialisation du tableau de bord quand la page est chargée
    document.addEventListener('DOMContentLoaded', function() {
        // Afficher la date actuelle dans un format français
        const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
        const today = new Date();
        if (document.getElementById('currentDate')) {
            document.getElementById('currentDate').textContent = today.toLocaleDateString('fr-FR', options);
        }
        
        // Charger les statistiques
        refreshStats();
        
        // Initialiser le graphique
        initOrdersChart();
        
        // Animer les compteurs
        animateCounters();
        
        // Rafraîchir les statistiques
        document.getElementById('refreshStats').addEventListener('click', function() {
            refreshStats();
        });
        
        // Changer la période du graphique
        document.getElementById('chartPeriod').addEventListener('change', function() {
            refreshStats();
        });
        
        // Charger les données initiales
        refreshStats();
    });
    
    function animateCounters() {
        const counters = document.querySelectorAll('.counter-animation');
        
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-count'));
            const suffix = counter.getAttribute('data-suffix') || '';
            const duration = 1500; // ms
            const frameDuration = 1000 / 60; // 60fps
            const totalFrames = Math.round(duration / frameDuration);
            const easeOutQuad = t => t * (2 - t);
            
            let frame = 0;
            const countTo = target;
            
            const animate = () => {
                frame++;
                const progress = easeOutQuad(frame / totalFrames);
                const currentCount = Math.round(countTo * progress);
                
                if (countTo > 999) {
                    counter.textContent = currentCount.toLocaleString() + suffix;
                } else {
                    counter.textContent = currentCount + suffix;
                }
                
                if (frame < totalFrames) {
                    requestAnimationFrame(animate);
                }
            };
            
            animate();
        });
    }
    
    function refreshStats() {
        const period = document.getElementById('chartPeriod').value;
        const loadingSpinner = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Chargement...</span></div>';
        
        // Afficher les spinners de chargement
        document.getElementById('recentActivities').innerHTML = '<div class="text-center py-4">' + loadingSpinner + '<p class="mt-2 text-muted">Chargement des activités récentes...</p></div>';
        
        fetch(`/accountant/dashboard/stats?period=${period}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Mettre à jour les statistiques
                    const totalOrders = document.querySelector('.total-orders');
                    const validatedTonnage = document.querySelector('.validated-tonnage');
                    const rejectedTonnage = document.querySelector('.rejected-tonnage');
                    const pendingOrders = document.querySelector('.pending-orders');
                    
                    // Mettre à jour les attributs data-count
                    totalOrders.setAttribute('data-count', data.stats.total_cement_orders || data.stats.monthly_cement_orders || 0);
                    validatedTonnage.setAttribute('data-count', data.stats.validated_cement_tonnage || 0);
                    rejectedTonnage.setAttribute('data-count', data.stats.rejected_cement_tonnage || 0);
                    pendingOrders.setAttribute('data-count', data.stats.pending_orders || 0);
                    
                    // Animer les compteurs
                    animateCounters();
                    
                    // Mettre à jour le graphique
                    updateOrdersChart(data.chartData);
                    
                    // Mettre à jour les activités récentes
                    updateRecentActivities(data.latestOrders);
                } else {
                    console.error('Erreur lors du chargement des données:', data.message);
                }
            })
            .catch(error => {
                console.error('Erreur lors de la requête:', error);
            });
    }
    
    function updateRecentActivities(orders) {
        const activitiesContainer = document.getElementById('recentActivities');
        
        if (orders.length === 0) {
            activitiesContainer.innerHTML = '<div class="text-center py-4"><p class="text-muted">Aucune activité récente</p></div>';
            return;
        }
        
        let html = '';
        
        orders.forEach(order => {
            let statusClass = '';
            let statusIcon = '';
            
            switch(order.status) {
                case 'completed':
                    statusClass = 'success';
                    statusIcon = 'check-circle';
                    break;
                case 'pending':
                    statusClass = 'warning';
                    statusIcon = 'clock';
                    break;
                case 'rejected':
                    statusClass = 'danger';
                    statusIcon = 'times-circle';
                    break;
                default:
                    statusClass = 'info';
                    statusIcon = 'info-circle';
            }
            
            html += `
            <div class="activity-item ${statusClass}">
                <div class="activity-icon">
                    <i class="fas fa-${statusIcon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">
                        <a href="/accountant/supplies/${order.id}">${order.reference}</a>
                        <span class="badge-status ${statusClass}">${order.status}</span>
                    </div>
                    <div class="activity-details">
                        <div class="activity-detail">
                            <i class="fas fa-building"></i>
                            <span>${order.client}</span>
                        </div>
                        <div class="activity-detail">
                            <i class="fas fa-weight-hanging"></i>
                            <span>${order.tonnage} T</span>
                        </div>
                        <div class="activity-detail">
                            <i class="far fa-calendar-alt"></i>
                            <span>${order.date}</span>
                        </div>
                    </div>
                </div>
            </div>
            `;
        });
        
        activitiesContainer.innerHTML = html;
    }
    
    function initOrdersChart() {
        const ctx = document.getElementById('ordersChart').getContext('2d');
        
        // Créer des dégradés colorés plus vifs pour le fond du graphique
        const gradient1 = ctx.createLinearGradient(0, 0, 0, 300);
        gradient1.addColorStop(0, 'rgba(78, 99, 255, 0.5)');
        gradient1.addColorStop(0.5, 'rgba(78, 99, 255, 0.2)');
        gradient1.addColorStop(1, 'rgba(78, 99, 255, 0)');
        
        const gradient2 = ctx.createLinearGradient(0, 0, 0, 300);
        gradient2.addColorStop(0, 'rgba(147, 51, 234, 0.4)');
        gradient2.addColorStop(0.5, 'rgba(147, 51, 234, 0.15)');
        gradient2.addColorStop(1, 'rgba(147, 51, 234, 0)');
        
        const gradient3 = ctx.createLinearGradient(0, 0, 0, 300);
        gradient3.addColorStop(0, 'rgba(236, 72, 153, 0.4)');
        gradient3.addColorStop(0.5, 'rgba(236, 72, 153, 0.15)');
        gradient3.addColorStop(1, 'rgba(236, 72, 153, 0)');
        
        // Créer des dégradés plus vifs pour les lignes du graphique
        const lineGradient = ctx.createLinearGradient(0, 0, 600, 0);
        lineGradient.addColorStop(0, '#4f46e5');
        lineGradient.addColorStop(0.3, '#7c3aed');
        lineGradient.addColorStop(0.6, '#c026d3');
        lineGradient.addColorStop(1, '#db2777');
        
        window.ordersChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'Nombre de bons',
                        data: [],
                        backgroundColor: gradient1,
                        borderColor: lineGradient,
                        borderWidth: 3,
                        pointBackgroundColor: '#ffffff',
                        pointBorderColor: '#4f46e5',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 10,
                        pointHoverBackgroundColor: '#ffffff',
                        pointHoverBorderColor: '#db2777',
                        pointHoverBorderWidth: 3,
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Montant total (FCFA)',
                        data: [],
                        backgroundColor: gradient2,
                        borderColor: '#7c3aed',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        pointBackgroundColor: '#ffffff',
                        pointBorderColor: '#7c3aed',
                        pointBorderWidth: 2,
                        pointRadius: 5,
                        pointHoverRadius: 8,
                        pointHoverBackgroundColor: '#ffffff',
                        pointHoverBorderColor: '#7c3aed',
                        pointHoverBorderWidth: 3,
                        tension: 0.4,
                        fill: false,
                        hidden: true
                    },
                    {
                        label: 'Tonnage total',
                        data: [],
                        backgroundColor: gradient3,
                        borderColor: '#c026d3',
                        borderWidth: 2,
                        borderDash: [3, 3],
                        pointBackgroundColor: '#ffffff',
                        pointBorderColor: '#c026d3',
                        pointBorderWidth: 2,
                        pointRadius: 5,
                        pointHoverRadius: 8,
                        pointHoverBackgroundColor: '#ffffff',
                        pointHoverBorderColor: '#c026d3',
                        pointHoverBorderWidth: 3,
                        tension: 0.4,
                        fill: false,
                        hidden: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                },
                interaction: {
                    mode: 'index',
                    intersect: false,
                    includeInvisible: true
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        align: 'end',
                        labels: {
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 15,
                            boxWidth: 8,
                            boxHeight: 8,
                            font: {
                                size: 12,
                                weight: '600'
                            }
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(255, 255, 255, 0.98)',
                        titleColor: '#111827',
                        bodyColor: '#4B5563',
                        borderColor: 'rgba(79, 70, 229, 0.3)',
                        borderWidth: 2,
                        padding: 16,
                        boxPadding: 10,
                        usePointStyle: true,
                        titleFont: {
                            size: 14,
                            weight: 'bold',
                            family: "'Poppins', sans-serif"
                        },
                        bodyFont: {
                            size: 13,
                            family: "'Poppins', sans-serif"
                        },
                        cornerRadius: 12,
                        caretSize: 8,
                        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                        callbacks: {
                            title: function(tooltipItems) {
                                return tooltipItems[0].label;
                            },
                            label: function(context) {
                                let label = context.dataset.label || '';
                                let value = context.parsed.y;
                                
                                if (label === 'Nombre de bons') {
                                    return '📊 ' + label + ': ' + value + ' bons';
                                } else if (label === 'Montant total (FCFA)') {
                                    return '💰 ' + label + ': ' + value.toLocaleString('fr-FR') + ' FCFA';
                                } else if (label === 'Tonnage total') {
                                    return '⚖️ ' + label + ': ' + value + ' T';
                                }
                                return label + ': ' + value;
                            },
                            labelPointStyle: function(context) {
                                const colors = ['#4f46e5', '#7c3aed', '#c026d3'];
                                return {
                                    pointStyle: 'circle',
                                    rotation: 0,
                                    backgroundColor: colors[context.datasetIndex % colors.length]
                                };
                            },
                            labelTextColor: function(context) {
                                const colors = ['#4f46e5', '#7c3aed', '#c026d3'];
                                return colors[context.datasetIndex % colors.length];
                            }
                        }
                    }
                },
                interaction: {
                    mode: 'index',
                    intersect: false,
                    axis: 'x'
                },
                hover: {
                    mode: 'nearest',
                    intersect: true
                },
                scales: {
                    x: {
                        grid: {
                            display: false,
                            drawBorder: false
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#6B7280',
                            font: {
                                size: 12,
                                family: "'Poppins', sans-serif",
                                weight: '500'
                            },
                            padding: 12,
                            maxRotation: 0
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(243, 244, 246, 0.6)',
                            drawBorder: false,
                            lineWidth: 1.5,
                            tickLength: 8
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#6B7280',
                            font: {
                                size: 12,
                                family: "'Poppins', sans-serif",
                                weight: '500'
                            },
                            padding: 12,
                            stepSize: 5,
                            callback: function(value) {
                                return value + ' bons';
                            }
                        }
                    }
                }
            }
        });
    }
    
    function updateOrdersChart(chartData) {
        window.ordersChart.data.labels = chartData.labels;
        window.ordersChart.data.datasets[0].data = chartData.data;
        
        // Ajouter des données fictives pour les autres datasets pour la démonstration
        if (chartData.data && chartData.data.length > 0) {
            // Montant total (FCFA) - données fictives basées sur le nombre de bons
            window.ordersChart.data.datasets[1].data = chartData.data.map(value => value * 150000 + Math.floor(Math.random() * 500000));
            
            // Tonnage total - données fictives basées sur le nombre de bons
            window.ordersChart.data.datasets[2].data = chartData.data.map(value => value * 5 + Math.floor(Math.random() * 20));
        }
        
        // Ajouter une animation lors de la mise à jour
        window.ordersChart.update({
            duration: 800,
            easing: 'easeOutQuart'
        });
    }
    
    // Gestion des boutons de contrôle du graphique
    document.addEventListener('DOMContentLoaded', function() {
        // Ajouter les écouteurs d'événements aux boutons de contrôle du graphique
        const chartToggleBtns = document.querySelectorAll('.chart-toggle-btn');
        chartToggleBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const index = parseInt(this.getAttribute('data-index'));
                
                // Mettre à jour les classes actives
                chartToggleBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                // Afficher/masquer les datasets
                if (window.ordersChart) {
                    window.ordersChart.data.datasets.forEach((dataset, i) => {
                        dataset.hidden = i !== index;
                    });
                    
                    // Mettre à jour le graphique avec animation
                    window.ordersChart.update({
                        duration: 800,
                        easing: 'easeOutQuart'
                    });
                }
            });
        });
                    statusClass = 'warning';
                }
                
                // Créer l'élément d'activité
                const activityItem = document.createElement('div');
                activityItem.className = `activity-item ${statusClass}`;
                activityItem.style.animationDelay = `${index * 0.1}s`;
                
                // Ajouter le contenu HTML
                activityItem.innerHTML = `
                    <div class="activity-title">
                        ${activity.type}
                        <span class="status-badge ${statusClass}">${activity.status || 'Info'}</span>
                    </div>
                    <div class="activity-content">${activity.message}</div>
                    <div class="activity-time">
                        <i class="far fa-clock"></i>
                        ${activity.time_ago}
                    </div>
                `;
                
                container.appendChild(activityItem);
            });
        } else {
            container.innerHTML = '<div class="text-center py-4"><p class="text-muted">Aucune activité récente</p></div>';
        }
    }

    // Fonction pour mettre à jour les derniers bons
    function updateLatestOrders(orders) {
        const container = document.getElementById('latestOrders');
        if (!container) return;
        
        // Vider le conteneur
        container.innerHTML = '';
        
        if (orders && orders.length > 0) {
            orders.forEach(order => {
                // Déterminer la classe de statut
                let statusClass = 'primary';
                let statusLabel = order.status || 'En attente';
                
                if (statusLabel.includes('validé') || statusLabel.includes('complété')) {
                    statusClass = 'success';
                } else if (statusLabel.includes('rejeté')) {
                    statusClass = 'danger';
                } else if (statusLabel.includes('attente') || statusLabel.includes('pending')) {
                    statusClass = 'warning';
                }
                
                // Créer la ligne de tableau
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>#${order.id}</td>
                    <td>${order.supplier || 'Non spécifié'}</td>
                    <td>${order.date || 'N/A'}</td>
                    <td>${order.tonnage || '0'} T</td>
                    <td><span class="status-badge ${statusClass}">${statusLabel}</span></td>
                    <td>
                        <button class="btn-card-action" title="Voir les détails">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                `;
                
                container.appendChild(row);
            });
        } else {
            container.innerHTML = '<tr><td colspan="6" class="text-center py-4"><p class="text-muted">Aucun bon récent</p></td></tr>';
        }
    }

    // Ajouter une classe CSS pour l'animation de rotation
    document.head.insertAdjacentHTML('beforeend', `
        <style>
            @keyframes rotating {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            .rotating {
                animation: rotating 1s linear infinite;
            }
        </style>
    `);
</script>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment/moment.min.js"></script>
<script src="{{ asset('js/accountant-dashboard-modern-2025.js') }}?v={{ time() }}"></script>
<script>
let ordersChart = null;
let loadingTimeout = null;

function loadStats(period = 7) {
    fetch(`/accountant/dashboard/stats?period=${period}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const statsContainer = document.getElementById('cement-orders-stats');
                statsContainer.innerHTML = `
            <!-- Cartes de statistiques avec glassmorphism -->
    <div class="row stats-row mb-4 g-4">
        <div class="col-md-6 col-xl-3" style="--animation-order: 0">
            <div class="stats-card primary-card">
                <div class="card-body">
                    <div class="card-title">Total des bons</div>
                    <div class="card-value total-orders">${data.stats.total_cement_orders || data.stats.monthly_cement_orders || 0}</div>
                    <i class="far fa-file-alt card-icon"></i>
                </div>
                <div class="card-trend">
                    <span><i class="fas fa-chart-line"></i> Évolution mensuelle</span>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-xl-3" style="--animation-order: 1">
            <div class="stats-card success-card">
                <div class="card-body">
                    <div class="card-title">Tonnage validé</div>
                    <div class="card-value validated-tonnage">${data.stats.validated_cement_tonnage || 0} T</div>
                    <i class="far fa-check-circle card-icon"></i>
                </div>
                <div class="card-trend success">
                    <span><i class="fas fa-arrow-up"></i> Progression positive</span>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-xl-3" style="--animation-order: 2">
            <div class="stats-card danger-card">
                <div class="card-body">
                    <div class="card-title">Tonnage rejeté</div>
                    <div class="card-value rejected-tonnage">${data.stats.rejected_cement_tonnage || 0} T</div>
                    <i class="far fa-times-circle card-icon"></i>
                </div>
                <div class="card-trend danger">
                    <span><i class="fas fa-arrow-down"></i> En diminution</span>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-xl-3" style="--animation-order: 3">
            <div class="stats-card warning-card">
                <div class="card-body">
                    <div class="card-title">Bons en attente</div>
                    <div class="card-value pending-orders">${data.stats.pending_orders || 0}</div>
                    <i class="far fa-clock card-icon"></i>
                </div>
                <div class="card-trend warning">
                    <span><i class="fas fa-exclamation-triangle"></i> À traiter</span>
                </div>
            </div>
        </div>
    </div>`;
                                    <div class="card-title">Bons ce mois</div>
                                    <div class="card-value">${data.stats.monthly_cement_orders}</div>
                                    <i class="fas fa-calendar card-icon"></i>
                                </div>
                            </div>
                        </div>
                    </div>`;

                initializeChart(data.chartData);
                updateLatestOrders(data.latestOrders);
                updateRecentActivities(data.latestOrders);
                showNotification('<i class="fas fa-check me-2"></i>Données mises à jour avec succès', 'success');
            } else {
                throw new Error(data.message || 'Erreur lors du chargement des statistiques');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            const statsContainer = document.getElementById('cement-orders-stats');
            statsContainer.innerHTML = `
                <div class="alert alert-danger animate__animated animate__shakeX">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Une erreur est survenue lors du chargement des statistiques. 
                    <button class="btn btn-sm btn-outline-danger ms-3" onclick="refreshStats()">
                        <i class="fas fa-sync-alt me-1"></i>Réessayer
                    </button>
                </div>`;
            showNotification('<i class="fas fa-exclamation-triangle me-2"></i>Erreur lors de la mise à jour des données', 'danger');
        });
}

function initializeChart(data) {
    const ctx = document.getElementById('ordersChart').getContext('2d');
    if (ordersChart) {
        ordersChart.destroy();
    }
    ordersChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.labels,
            datasets: [{
                label: 'Nombre de commandes',
                data: data.data,
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return `${context.parsed.y} commande(s)`;
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            animation: {
                duration: 1000,
                easing: 'easeInOutQuart'
            }
        }
    });
}

function updateLatestOrders(orders) {
    const tbody = document.querySelector('#latestOrdersTable tbody');
    if (!tbody) return;

    tbody.innerHTML = orders.length ? orders.map((order, index) => `
        <tr class="animate__animated animate__fadeIn" style="animation-delay: ${index * 0.05}s">
            <td><strong>${order.reference}</strong></td>
            <td><i class="far fa-calendar-alt me-1 text-muted"></i>${order.date}</td>
            <td><i class="far fa-building me-1 text-muted"></i>${order.client}</td>
            <td><i class="fas fa-weight me-1 text-muted"></i>${order.tonnage} T</td>
            <td>
                <span class="badge bg-${getStatusColor(order.status)} rounded-pill px-3">
                    <i class="fas fa-${order.status === 'completed' ? 'check' : order.status === 'pending' ? 'clock' : 'times'} me-1"></i>
                    ${getStatusLabel(order.status)}
                </span>
            </td>
            <td class="text-end">
                <a href="/accountant/supplies/${order.id}" class="btn btn-sm btn-light rounded-circle">
                    <i class="fas fa-eye"></i>
                </a>
            </td>
        </tr>
    `).join('') : '<tr><td colspan="6" class="text-center py-5"><i class="fas fa-info-circle me-2 text-muted"></i>Aucun bon trouvé</td></tr>';
}

function updateRecentActivities(orders) {
    const container = document.getElementById('recentActivities');
    if (!container) return;

    container.innerHTML = orders.length ? orders.map(order => {
        let icon = 'file-alt';
        if (order.status === 'completed') icon = 'check-circle';
        if (order.status === 'pending') icon = 'clock';
        if (order.status === 'rejected') icon = 'times-circle';
        
        return `
        <div class="activity-item ${getStatusClass(order.status)} animate__animated animate__fadeInLeft">
            <div class="activity-title">
                Bon ${order.reference}
                <span class="badge bg-${getStatusColor(order.status)}">${getStatusLabel(order.status)}</span>
            </div>
            <div class="activity-content">${order.client} - ${order.tonnage} T</div>
            <div class="activity-time"><i class="far fa-calendar-alt"></i> ${order.date}</div>
        </div>
    `}).join('') : '<div class="p-4 text-center">Aucune activité récente</div>';
}

function getStatusColor(status) {
    switch (status) {
        case 'completed':
            return 'success';
        case 'pending':
            return 'warning';
        case 'cancelled':
            return 'danger';
        default:
            return 'secondary';
    }
}

function getStatusLabel(status) {
    switch (status) {
        case 'completed':
            return 'Terminé';
        case 'pending':
            return 'En attente';
        case 'cancelled':
            return 'Annulé';
        default:
            return 'Inconnu';
    }
}

function getStatusClass(status) {
    switch (status) {
        case 'completed':
            return 'success';
        case 'pending':
            return 'warning';
        case 'cancelled':
            return 'danger';
        default:
            return '';
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification bg-${type} text-white animate__animated animate__fadeInUp`;
    notification.innerHTML = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

function refreshStats() {
    const period = document.getElementById('chartPeriod').value;
    const statsContainer = document.getElementById('cement-orders-stats');
    statsContainer.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>`;
    loadStats(period);
}

// Gestionnaire de changement de période pour le graphique
document.getElementById('chartPeriod').addEventListener('change', function() {
    const period = this.value;
    loadStats(period);
});

// Optimiser le rafraîchissement automatique
let refreshInterval;
function startAutoRefresh() {
    if (refreshInterval) clearInterval(refreshInterval);
    refreshInterval = setInterval(() => {
        if (document.visibilityState === 'visible') {
            refreshStats();
        }
    }, 300000); // 5 minutes
}

document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
        refreshStats();
        startAutoRefresh();
    } else {
        clearInterval(refreshInterval);
    }
});

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    // Initialiser les valeurs des cartes statistiques avec les données disponibles
    const totalOrdersElement = document.querySelector('.total-orders');
    const validatedTonnageElement = document.querySelector('.validated-tonnage');
    const rejectedTonnageElement = document.querySelector('.rejected-tonnage');
    const pendingOrdersElement = document.querySelector('.pending-orders');
    
    // Charger les statistiques depuis l'API
    loadStats();
    startAutoRefresh();
    
    // Forcer l'affichage des valeurs dans les cartes statistiques
    setTimeout(function() {
        fetch('/accountant/dashboard/stats?period=7')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Mettre à jour les valeurs des cartes statistiques
                    if (totalOrdersElement) {
                        totalOrdersElement.textContent = data.stats.total_cement_orders || 0;
                    }
                    if (validatedTonnageElement) {
                        validatedTonnageElement.textContent = `${data.stats.validated_cement_tonnage || 0} T`;
                    }
                    if (rejectedTonnageElement) {
                        rejectedTonnageElement.textContent = `${data.stats.rejected_cement_tonnage || 0} T`;
                    }
                    if (pendingOrdersElement) {
                        pendingOrdersElement.textContent = data.stats.pending_orders || 0;
                    }
                }
            })
            .catch(error => console.error('Erreur lors du chargement des statistiques:', error));
    }, 500);
});
</script>
@endpush
