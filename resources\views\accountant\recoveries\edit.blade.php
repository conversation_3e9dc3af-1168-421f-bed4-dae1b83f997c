@extends('layouts.accountant')

@section('title', 'Enregistrer un Paiement')

@push('styles')
<!-- Animate.css pour les animations -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<style>
    /* Variables */
    :root {
        --primary-color: #2196F3;
        --primary-light: #BBDEFB;
        --primary-dark: #1976D2;
        --success-color: #4CAF50;
        --success-light: #E8F5E9;
        --warning-color: #FF9800;
        --warning-light: #FFF3E0;
        --danger-color: #F44336;
        --danger-light: #FFEBEE;
        --info-color: #00BCD4;
        --info-light: #E0F7FA;
        --dark-color: #101828;
        --text-color: #344054;
        --border-color: #EAECF0;
        --background-color: #F9FAFB;
        --card-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    }
    
    /* Styles généraux */
    body {
        background-color: var(--background-color);
        color: var(--text-color);
    }
    
    .container-fluid {
        padding: 2rem 2.5rem;
        max-width: 1600px;
        margin: 0 auto;
        animation: fadeIn 0.5s ease-out;
    }
    
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--border-color);
    }
    
    .page-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--dark-color);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin: 0;
    }
    
    .page-title i {
        color: var(--primary-color);
    }
    
    /* Cartes de paiement */
    .payment-card {
        transition: all 0.3s ease;
        cursor: pointer;
        border: 1px solid var(--border-color);
        border-radius: 12px;
        background-color: white;
        box-shadow: 0 1px 3px rgba(16, 24, 40, 0.1);
        height: 100%;
    }
    
    .payment-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--card-shadow);
    }
    
    .payment-card.selected {
        border-color: var(--primary-color);
        background-color: var(--primary-light);
        box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.2);
    }
    
    .payment-method-icon {
        font-size: 2.5rem;
        margin-bottom: 0.75rem;
        transition: transform 0.2s ease;
    }
    
    .payment-card:hover .payment-method-icon {
        transform: scale(1.1);
    }
    
    /* Éléments d'échéancier */
    .schedule-item {
        border-left: 4px solid #ccc;
        padding: 1rem 1.25rem;
        margin-bottom: 1rem;
        transition: all 0.2s ease;
        background-color: white;
        border-radius: 0 8px 8px 0;
        box-shadow: 0 1px 3px rgba(16, 24, 40, 0.1);
    }
    
    .schedule-item:hover {
        border-left-color: var(--primary-color);
        box-shadow: 0 4px 8px rgba(16, 24, 40, 0.1);
    }
    
    .schedule-item.overdue {
        border-left-color: var(--danger-color);
        background-color: var(--danger-light);
    }
    
    .schedule-item.paid {
        border-left-color: var(--success-color);
        background-color: var(--success-light);
    }
    
    /* Résumé de paiement */
    .payment-summary {
        background-color: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: var(--card-shadow);
        border: 1px solid var(--border-color);
    }
    
    /* Cartes */
    .card {
        border-radius: 12px;
        border: 1px solid var(--border-color);
        box-shadow: var(--card-shadow);
        overflow: hidden;
        margin-bottom: 1.5rem;
        background-color: white;
    }
    
    .card-header {
        padding: 1rem 1.5rem;
        background-color: #FCFCFD;
        border-bottom: 1px solid var(--border-color);
        font-weight: 600;
        color: var(--dark-color);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .card-header i {
        color: var(--primary-color);
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    /* Formulaires */
    .form-label {
        font-weight: 500;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
    }
    
    .form-control {
        border-radius: 8px;
        border: 1px solid var(--border-color);
        padding: 0.625rem 1rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;
    }
    
    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 4px rgba(33, 150, 243, 0.1);
    }
    
    /* Boutons */
    .btn {
        border-radius: 8px;
        padding: 0.625rem 1.25rem;
        font-weight: 500;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }
    
    .btn-primary:hover {
        background-color: var(--primary-dark);
        border-color: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(33, 150, 243, 0.2);
    }
    
    .btn-outline-secondary {
        border-color: var(--border-color);
        color: var(--text-color);
    }
    
    .btn-outline-secondary:hover {
        background-color: #F9FAFB;
        color: var(--dark-color);
    }
    
    /* Alertes */
    .alert {
        border-radius: 12px;
        border: 1px solid transparent;
        padding: 1rem 1.25rem;
    }
    
    .alert-info {
        background-color: var(--info-light);
        border-color: var(--info-color);
        color: var(--info-color);
    }
    
    /* Animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .animate-fade-in {
        animation: fadeIn 0.5s ease-out forwards;
    }
    
    .payment-progress {
        height: 10px;
        border-radius: 5px;
        margin: 1rem 0;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- En-tête de page avec titre -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-cash-register"></i>
            <span>Enregistrer un paiement</span>
        </h1>
        <div class="breadcrumb-navigation">
            <a href="{{ route('accountant.dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-home"></i> Tableau de bord
            </a>
            <a href="{{ route('accountant.recoveries.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i> Liste des recouvrements
            </a>
        </div>
    </div>

    @include('partials.alerts')

    <div class="row">
        <!-- Informations sur la vente -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-info-circle me-1"></i>
                    Détails du recouvrement
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h5 class="d-flex align-items-center mb-3" style="color: var(--primary-color);">
                            <i class="fas fa-user-circle me-2"></i> Client
                        </h5>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">Nom:</span>
                            <span class="fw-medium">{{ $sale->customer_name }}</span>
                        </div>
                        @if($sale->customer_phone)
                            <div class="info-item d-flex justify-content-between mb-2">
                                <span class="text-muted">Téléphone:</span>
                                <a href="tel:{{ $sale->customer_phone }}" class="text-decoration-none fw-medium">{{ $sale->customer_phone }}</a>
                            </div>
                        @endif
                    </div>
                    
                    <div class="mb-4">
                        <h5 class="d-flex align-items-center mb-3" style="color: var(--primary-color);">
                            <i class="fas fa-file-invoice me-2"></i> Facture
                        </h5>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">Numéro:</span>
                            <span class="fw-medium">#{{ $sale->invoice_number }}</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-2">
                            <span class="text-muted">Date:</span>
                            <span class="fw-medium">{{ $sale->created_at->format('d/m/Y') }}</span>
                        </div>
                    </div>
                    
                    <div class="payment-summary">
                        <h5 class="d-flex align-items-center mb-3" style="color: var(--primary-color);">
                            <i class="fas fa-file-invoice-dollar me-2"></i> Résumé financier
                        </h5>
                        <div class="info-item d-flex justify-content-between mb-3">
                            <span class="text-muted">Montant total:</span>
                            <span class="fw-bold" style="font-size: 1.1rem;">{{ number_format($totalAmount, 0, ',', ' ') }} FCFA</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-3">
                            <span class="text-muted">Déjà payé:</span>
                            <span class="fw-medium text-success">{{ number_format($paidAmount, 0, ',', ' ') }} FCFA</span>
                        </div>
                        <div class="info-item d-flex justify-content-between mb-3" style="padding-top: 0.75rem; border-top: 1px solid var(--border-color);">
                            <span class="fw-medium">Reste à payer:</span>
                            <span class="fw-bold text-danger" style="font-size: 1.2rem;">{{ number_format($remainingAmount, 0, ',', ' ') }} FCFA</span>
                        </div>
                        
                        <div class="mt-4">
                            <label class="form-label d-flex align-items-center">
                                <i class="fas fa-chart-pie text-primary me-2"></i>
                                Progression du paiement
                            </label>
                            <div class="progress" style="height: 10px; border-radius: 10px; background-color: #F2F4F7;">
                                @php
                                    $percentage = $paymentPercentage;
                                @endphp
                                <div class="progress-bar" role="progressbar" 
                                    style="width: {{ $percentage }}%; background: linear-gradient(90deg, #4CAF50, #8BC34A); border-radius: 10px;" 
                                    aria-valuenow="{{ $percentage }}" aria-valuemin="0" aria-valuemax="100">
                                    {{ round($percentage) }}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Formulaire de paiement -->
        <div class="col-md-8">
            <div class="card mb-4 animate-fade-in" style="animation-delay: 0.2s;">
                <div class="card-header">
                    <i class="fas fa-cash-register"></i>
                    Enregistrer un paiement
                </div>
                <div class="card-body">
                    <form id="paymentForm" action="{{ route('accountant.recoveries.update', $sale->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="amount" class="form-label d-flex align-items-center">
                                        <i class="fas fa-coins text-primary me-2"></i>
                                        Montant <span class="text-danger ms-1">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control @error('amount') is-invalid @enderror" 
                                            id="amount" name="amount" step="0.01" min="0" max="{{ $remainingAmount }}" 
                                            value="{{ old('amount', $remainingAmount) }}" required>
                                        <span class="input-group-text">FCFA</span>
                                    </div>
                                    <small class="form-text text-muted">Montant maximum: {{ number_format($remainingAmount, 0, ',', ' ') }} FCFA</small>
                                    @error('amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-4">
                                    <label for="payment_date" class="form-label d-flex align-items-center">
                                        <i class="fas fa-calendar-alt text-primary me-2"></i>
                                        Date du paiement <span class="text-danger ms-1">*</span>
                                    </label>
                                    <input type="date" class="form-control @error('payment_date') is-invalid @enderror" 
                                        id="payment_date" name="payment_date" value="{{ old('payment_date', date('Y-m-d')) }}" required>
                                    @error('payment_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-4">
                                    <label for="notes" class="form-label d-flex align-items-center">
                                        <i class="fas fa-sticky-note text-primary me-2"></i>
                                        Notes / Commentaires
                                    </label>
                                    <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label d-flex align-items-center mb-3">
                                    <i class="fas fa-credit-card text-primary me-2"></i>
                                    Mode de paiement <span class="text-danger ms-1">*</span>
                                </label>
                                
                                <div class="payment-methods">
                                    <div class="row g-3">
                                        <div class="col-6">
                                            <div class="payment-card text-center p-3" data-method="cash">
                                                <input type="radio" name="payment_method" id="method_cash" value="cash" class="d-none" {{ old('payment_method') == 'cash' ? 'checked' : '' }}>
                                                <div class="payment-method-icon text-success">
                                                    <i class="fas fa-money-bill-wave"></i>
                                                </div>
                                                <h6 class="mb-0">Espèces</h6>
                                            </div>
                                        </div>
                                        
                                        <div class="col-6">
                                            <div class="payment-card text-center p-3" data-method="bank_transfer">
                                                <input type="radio" name="payment_method" id="method_bank_transfer" value="bank_transfer" class="d-none" {{ old('payment_method') == 'bank_transfer' ? 'checked' : '' }}>
                                                <div class="payment-method-icon text-primary">
                                                    <i class="fas fa-university"></i>
                                                </div>
                                                <h6 class="mb-0">Virement</h6>
                                            </div>
                                        </div>
                                        
                                        <div class="col-6">
                                            <div class="payment-card text-center p-3" data-method="check">
                                                <input type="radio" name="payment_method" id="method_check" value="check" class="d-none" {{ old('payment_method') == 'check' ? 'checked' : '' }}>
                                                <div class="payment-method-icon text-info">
                                                    <i class="fas fa-money-check"></i>
                                                </div>
                                                <h6 class="mb-0">Chèque</h6>
                                            </div>
                                        </div>
                                        
                                        <div class="col-6">
                                            <div class="payment-card text-center p-3" data-method="mobile_money">
                                                <input type="radio" name="payment_method" id="method_mobile_money" value="mobile_money" class="d-none" {{ old('payment_method') == 'mobile_money' ? 'checked' : '' }}>
                                                <div class="payment-method-icon text-warning">
                                                    <i class="fas fa-mobile-alt"></i>
                                                </div>
                                                <h6 class="mb-0">Mobile Money</h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div id="paymentMethodInfo" class="mt-4">
                                    <div class="reference-field mb-3" style="display: none;">
                                        <label for="reference_number" class="form-label d-flex align-items-center">
                                            <i class="fas fa-hashtag text-primary me-2"></i>
                                            Numéro de référence <span class="text-danger ms-1">*</span>
                                        </label>
                                        <input type="text" class="form-control @error('reference_number') is-invalid @enderror" 
                                            id="reference_number" name="reference_number" value="{{ old('reference_number') }}">
                                        <small class="form-text text-muted">Numéro de chèque, référence de transaction, etc.</small>
                                        @error('reference_number')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4 pt-3" style="border-top: 1px solid var(--border-color);">
                            <a href="{{ route('accountant.recoveries.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> Enregistrer le paiement
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Historique des paiements -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-history"></i>
                    Historique des paiements
                </div>
                <div class="card-body">
                    @if(count($payments) > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Montant</th>
                                        <th>Méthode</th>
                                        <th>Référence</th>
                                        <th>Statut</th>
                                        <th>Commentaire</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($payments as $payment)
                                    <tr>
                                        <td>{{ $payment->created_at->format('d/m/Y H:i') }}</td>
                                        <td class="fw-medium">{{ number_format($payment->amount, 0, ',', ' ') }} FCFA</td>
                                        <td>
                                            @if($payment->payment_method == 'cash')
                                                <span class="badge bg-success">Espèces</span>
                                            @elseif($payment->payment_method == 'bank_transfer')
                                                <span class="badge bg-primary">Virement</span>
                                            @elseif($payment->payment_method == 'check')
                                                <span class="badge bg-info">Chèque</span>
                                            @elseif($payment->payment_method == 'mobile_money')
                                                <span class="badge bg-warning">Mobile Money</span>
                                            @else
                                                <span class="badge bg-secondary">{{ $payment->payment_method }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $payment->reference_number ?? '-' }}</td>
                                        <td>
                                            @if($payment->status == 'completed')
                                                <span class="badge bg-success">Complété</span>
                                            @elseif($payment->status == 'pending')
                                                <span class="badge bg-warning">En attente</span>
                                            @else
                                                <span class="badge bg-danger">Échoué</span>
                                            @endif
                                        </td>
                                        <td>{{ $payment->notes ?? '-' }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="mb-0">Aucun paiement enregistré pour cette vente.</p>
                            <small class="text-muted">Les paiements s'afficheront ici une fois enregistrés</small>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialiser les tooltips Bootstrap
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
        
        // Gestion des cartes de méthode de paiement
        $('.payment-card').on('click', function() {
            // Désélectionner toutes les cartes
            $('.payment-card').removeClass('selected');
            $('.payment-method-icon i').removeClass('animate__animated animate__pulse');
            
            // Sélectionner la carte cliquée
            $(this).addClass('selected');
            $(this).find('.payment-method-icon i').addClass('animate__animated animate__pulse');
            
            // Cocher le radio button correspondant
            const method = $(this).data('method');
            $('#method_' + method).prop('checked', true);
            
            // Afficher/masquer le champ de référence selon la méthode
            if (method === 'cash') {
                $('.reference-field').slideUp();
                $('#reference_number').removeAttr('required');
            } else {
                $('.reference-field').slideDown();
                $('#reference_number').attr('required', 'required');
            }
        });
        
        // Sélectionner la méthode par défaut si une est déjà cochée
        const checkedMethod = $('input[name="payment_method"]:checked').val();
        if (checkedMethod) {
            $('.payment-card[data-method="' + checkedMethod + '"]').click();
        }
        
        // Validation du montant de paiement
        const amountInput = document.getElementById('amount');
        const maxAmount = {{ $remainingAmount }};
        
        amountInput.addEventListener('input', function() {
            if (parseFloat(this.value) > maxAmount) {
                this.value = maxAmount;
            }
        });
        
        // Validation du formulaire avant soumission
        $('#paymentForm').on('submit', function(e) {
            const amount = parseFloat($('#amount').val());
            const methodSelected = $('input[name="payment_method"]:checked').length > 0;
            
            if (amount <= 0) {
                e.preventDefault();
                Swal.fire({
                    title: 'Erreur',
                    text: 'Le montant doit être supérieur à 0',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#2196F3'
                });
                return false;
            }
            
            if (amount > maxAmount) {
                e.preventDefault();
                Swal.fire({
                    title: 'Erreur',
                    text: 'Le montant ne peut pas dépasser ' + maxAmount + ' FCFA',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#2196F3'
                });
                return false;
            }
            
            if (!methodSelected) {
                e.preventDefault();
                Swal.fire({
                    title: 'Erreur',
                    text: 'Veuillez sélectionner une méthode de paiement',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#2196F3'
                });
                return false;
            }
            
            // Afficher un indicateur de chargement
            Swal.fire({
                title: 'Traitement en cours...',
                text: 'Enregistrement du paiement',
                icon: 'info',
                showConfirmButton: false,
                allowOutsideClick: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });
            
            return true;
        });
    });
</script>
@endpush
