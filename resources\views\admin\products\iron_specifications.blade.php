@extends('layouts.admin_minimal')

@section('title', 'Spécifications des fers')

@section('content')
<!-- Section des spécifications de fer -->
<div id="iron-specifications" style="display: none;">
    <h4 class="mb-3">Spécifications de la barre de fer</h4>
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i> 
        Définissez les caractéristiques et les prix de vente pour ce type de fer.
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="diameter" class="form-label">Diamètre</label>
                <select class="form-select @error('diameter') is-invalid @enderror" 
                        id="diameter" name="diameter" required>
                    <option value="">Sélectionnez un diamètre</option>
                    <option value="6" {{ old('diameter') == 6 ? 'selected' : '' }}>Ø 6 mm (750 unités/tonne)</option>
                    <option value="8" {{ old('diameter') == 8 ? 'selected' : '' }}>Ø 8 mm (422 unités/tonne)</option>
                    <option value="10" {{ old('diameter') == 10 ? 'selected' : '' }}>Ø 10 mm (270 unités/tonne)</option>
                    <option value="12" {{ old('diameter') == 12 ? 'selected' : '' }}>Ø 12 mm (188 unités/tonne)</option>
                    <option value="14" {{ old('diameter') == 14 ? 'selected' : '' }}>Ø 14 mm (138 unités/tonne)</option>
                    <option value="16" {{ old('diameter') == 16 ? 'selected' : '' }}>Ø 16 mm (106 unités/tonne)</option>
                </select>
                @error('diameter')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <label for="length" class="form-label">Longueur (m)</label>
                <input type="number" step="0.1" class="form-control @error('length') is-invalid @enderror" 
                       id="length" name="length" value="{{ old('length', 12) }}" required>
                @error('length')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <small class="form-text text-muted">Longueur standard d'une barre (par défaut 12m)</small>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">Prix de vente</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="unit_price" class="form-label">Prix unitaire (FCFA/barre)</label>
                        <div class="input-group">
                            <input type="number" class="form-control @error('unit_price') is-invalid @enderror" 
                                   id="unit_price" name="unit_price" value="{{ old('unit_price') }}" required>
                            <span class="input-group-text">FCFA</span>
                        </div>
                        @error('unit_price')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="ton_price" class="form-label">Prix par tonne (FCFA/tonne)</label>
                        <div class="input-group">
                            <input type="number" class="form-control @error('ton_price') is-invalid @enderror" 
                                   id="ton_price" name="ton_price" value="{{ old('ton_price') }}" required>
                            <span class="input-group-text">FCFA</span>
                        </div>
                        @error('ton_price')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-info text-white">
            <h5 class="card-title mb-0">Récapitulatif</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <p class="mb-1">Unités par tonne :</p>
                    <h4 id="units-per-ton">- unités</h4>
                </div>
                <div class="col-md-4">
                    <p class="mb-1">Prix unitaire moyen :</p>
                    <h4 id="average-unit-price">- FCFA/unité</h4>
                </div>
                <div class="col-md-4">
                    <p class="mb-1">Poids par unité :</p>
                    <h4 id="weight-per-unit">- kg</h4>
                </div>
            </div>
        </div>
    </div>

    <input type="hidden" id="units_per_ton" name="units_per_ton">
    <input type="hidden" id="weight_per_unit" name="weight_per_unit">
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light fixed-bottom">
    <div class="container text-center">
        <span class="text-muted"> {{ date('Y') }} GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>
@endsection
