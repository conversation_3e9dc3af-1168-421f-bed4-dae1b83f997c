@extends('layouts.admin_minimal')

@section('title', 'Détails du Produit - ' . $product->name)

@section('content')
<div class="container-fluid py-4">
    <!-- En-tête avec navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.dashboard') }}">
                                    <i class="fas fa-home"></i> Dashboard
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.products.index') }}">
                                    <i class="fas fa-boxes"></i> Produits
                                </a>
                            </li>
                            <li class="breadcrumb-item active">{{ $product->name }}</li>
                        </ol>
                    </nav>
                    <h2 class="text-dark fw-bold mb-1">
                        @php
                            $categoryName = strtolower($product->category->name);
                            $iconClass = match($categoryName) {
                                'ciment' => 'fas fa-industry',
                                'fer' => 'fas fa-hammer',
                                'brique' => 'fas fa-th-large',
                                'sable' => 'fas fa-mountain',
                                'gravier' => 'fas fa-gem',
                                'bois' => 'fas fa-tree',
                                default => 'fas fa-cube'
                            };
                        @endphp
                        <i class="{{ $iconClass }} text-primary me-2"></i>
                        {{ $product->name }}
                    </h2>
                    <p class="text-muted mb-0">Détails complets du produit</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.products.edit', $product) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Modifier
                    </a>
                    <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Informations principales -->
        <div class="col-lg-8">
            <div class="card modern-card mb-4">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-info-circle text-primary me-2"></i>
                        Informations Générales
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="form-label text-muted">Nom du produit</label>
                                <div class="info-value">{{ $product->name }}</div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="form-label text-muted">Code produit</label>
                                <div class="info-value">
                                    <span class="badge bg-light text-dark">
                                        <i class="fas fa-barcode me-1"></i>{{ $product->code ?? 'N/A' }}
                                    </span>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="form-label text-muted">Catégorie</label>
                                <div class="info-value">
                                    <span class="badge bg-gradient-primary text-white">
                                        <i class="{{ $iconClass }} me-1"></i>{{ $product->category->name }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="form-label text-muted">Unité de mesure</label>
                                <div class="info-value">{{ $product->unit }}</div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="form-label text-muted">Stock actuel</label>
                                <div class="info-value">
                                    @php
                                        $stockLevel = $product->stock_quantity;
                                        $stockClass = $stockLevel > 50 ? 'success' : ($stockLevel > 10 ? 'warning' : 'danger');
                                        $stockIcon = $stockLevel > 50 ? 'fa-check-circle' : ($stockLevel > 10 ? 'fa-exclamation-triangle' : 'fa-times-circle');
                                    @endphp
                                    <span class="badge bg-{{ $stockClass }} text-white">
                                        <i class="fas {{ $stockIcon }} me-1"></i>
                                        {{ number_format($product->stock_quantity, 0, ',', ' ') }} {{ $product->unit }}
                                    </span>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="form-label text-muted">Statut</label>
                                <div class="info-value">
                                    <span class="badge bg-{{ $product->is_active ? 'success' : 'secondary' }} text-white">
                                        <i class="fas fa-{{ $product->is_active ? 'check-circle' : 'pause-circle' }} me-1"></i>
                                        {{ $product->is_active ? 'Actif' : 'Inactif' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informations de prix -->
            <div class="card modern-card mb-4">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-money-bill-wave text-success me-2"></i>
                        Informations de Prix
                    </h5>
                </div>
                <div class="card-body">
                    @if(strtolower($product->category->name) === 'ciment')
                        @if($product->prices->count() > 0)
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Ce produit a des prix variables selon les villes
                            </div>
                            <div class="row">
                                @foreach($product->prices->groupBy('city.region.name') as $regionName => $regionPrices)
                                    <div class="col-md-6 mb-3">
                                        <div class="region-price-card">
                                            <h6 class="fw-bold text-primary">
                                                <i class="fas fa-map-marker-alt me-1"></i>{{ $regionName }}
                                            </h6>
                                            @foreach($regionPrices as $price)
                                                <div class="city-price d-flex justify-content-between align-items-center mb-2">
                                                    <span>{{ $price->city->name }}</span>
                                                    <span class="fw-bold text-success">
                                                        {{ number_format($price->price, 0, ',', ' ') }} FCFA
                                                    </span>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Aucun prix défini pour ce produit
                            </div>
                        @endif
                    @elseif(strtolower($product->category->name) === 'fer')
                        @if($product->ironSpecification)
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="price-card bg-primary-light">
                                        <div class="price-icon">
                                            <i class="fas fa-weight-hanging text-primary"></i>
                                        </div>
                                        <div class="price-info">
                                            <h6 class="mb-1">Prix unitaire</h6>
                                            <div class="price-value">
                                                {{ number_format($product->ironSpecification->unit_price, 0, ',', ' ') }} FCFA
                                            </div>
                                            <small class="text-muted">par unité</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="price-card bg-info-light">
                                        <div class="price-icon">
                                            <i class="fas fa-balance-scale text-info"></i>
                                        </div>
                                        <div class="price-info">
                                            <h6 class="mb-1">Prix par tonne</h6>
                                            <div class="price-value">
                                                {{ number_format($product->ironSpecification->ton_price, 0, ',', ' ') }} FCFA
                                            </div>
                                            <small class="text-muted">par tonne</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <h6 class="fw-bold">Spécifications techniques</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="spec-item">
                                            <label class="text-muted">Diamètre</label>
                                            <div class="spec-value">{{ $product->ironSpecification->diameter }} mm</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="spec-item">
                                            <label class="text-muted">Longueur</label>
                                            <div class="spec-value">{{ $product->ironSpecification->length }} m</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="spec-item">
                                            <label class="text-muted">Poids unitaire</label>
                                            <div class="spec-value">{{ $product->ironSpecification->unit_weight }} kg</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Spécifications manquantes pour ce produit fer
                            </div>
                        @endif
                    @else
                        <div class="price-card bg-success-light">
                            <div class="price-icon">
                                <i class="fas fa-tag text-success"></i>
                            </div>
                            <div class="price-info">
                                <h6 class="mb-1">Prix de base</h6>
                                <div class="price-value">
                                    {{ number_format($product->price ?? 0, 0, ',', ' ') }} FCFA
                                </div>
                                <small class="text-muted">prix fixe</small>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Statistiques et actions -->
        <div class="col-lg-4">
            <!-- Statistiques -->
            <div class="card modern-card mb-4">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-chart-bar text-info me-2"></i>
                        Statistiques
                    </h5>
                </div>
                <div class="card-body">
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="stat-label">
                                <i class="fas fa-shopping-cart text-primary me-2"></i>
                                Total des ventes
                            </div>
                            <div class="stat-value fw-bold">{{ $stats['total_sales'] }}</div>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="stat-label">
                                <i class="fas fa-coins text-success me-2"></i>
                                Chiffre d'affaires
                            </div>
                            <div class="stat-value fw-bold text-success">
                                {{ number_format($stats['total_revenue'], 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="stat-label">
                                <i class="fas fa-calculator text-warning me-2"></i>
                                Prix moyen de vente
                            </div>
                            <div class="stat-value fw-bold text-warning">
                                {{ number_format($stats['average_sale_price'] ?? 0, 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="stat-label">
                                <i class="fas fa-warehouse text-info me-2"></i>
                                Valeur du stock
                            </div>
                            <div class="stat-value fw-bold text-info">
                                {{ number_format($stats['stock_value'], 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                    </div>
                    @if($stats['last_sale_date'])
                    <div class="stat-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="stat-label">
                                <i class="fas fa-clock text-secondary me-2"></i>
                                Dernière vente
                            </div>
                            <div class="stat-value">
                                {{ $stats['last_sale_date']->format('d/m/Y') }}
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Actions rapides -->
            <div class="card modern-card">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-bolt text-warning me-2"></i>
                        Actions Rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.products.edit', $product) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>Modifier le produit
                        </a>

                        <button class="btn btn-outline-danger" onclick="deleteProduct()">
                            <i class="fas fa-trash me-2"></i>Supprimer le produit
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light">
    <div class="container text-center">
        <span class="text-muted"> {{ date('Y') }} GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>
@endsection

@push('styles')
<style>
/* Styles pour la vue détaillée du produit */
.modern-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.modern-card:hover {
    box-shadow: 0 15px 35px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: #6c757d;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: #007bff;
}

.breadcrumb-item.active {
    color: #495057;
    font-weight: 600;
}

/* Styles pour les informations */
.info-item {
    position: relative;
    padding: 0.75rem;
    background: rgba(0,123,255,0.05);
    border-radius: 8px;
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
}

.info-item:hover {
    background: rgba(0,123,255,0.1);
    transform: translateX(5px);
}

.info-item .form-label {
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.info-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

/* Styles pour les cartes de prix */
.region-price-card {
    background: linear-gradient(135deg, rgba(0,123,255,0.1) 0%, rgba(0,123,255,0.05) 100%);
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid rgba(0,123,255,0.2);
    transition: all 0.3s ease;
}

.region-price-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,123,255,0.15);
}

.city-price {
    background: white;
    padding: 0.5rem;
    border-radius: 6px;
    border: 1px solid rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.city-price:hover {
    background: rgba(40,167,69,0.05);
    border-color: #28a745;
}

.price-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.price-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #28a745);
}

.price-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.price-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0,123,255,0.1);
    margin-bottom: 1rem;
}

.price-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #007bff;
    font-family: 'Courier New', monospace;
}

.bg-primary-light { background: rgba(0,123,255,0.1) !important; }
.bg-success-light { background: rgba(40,167,69,0.1) !important; }
.bg-info-light { background: rgba(23,162,184,0.1) !important; }

/* Spécifications techniques */
.spec-item {
    background: rgba(108,117,125,0.1);
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.spec-item:hover {
    background: rgba(108,117,125,0.2);
    transform: scale(1.02);
}

.spec-item label {
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
    display: block;
}

.spec-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: #495057;
}

/* Statistiques */
.stat-item {
    background: rgba(0,0,0,0.02);
    padding: 0.75rem;
    border-radius: 8px;
    border-left: 3px solid #007bff;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(0,123,255,0.05);
    transform: translateX(5px);
    border-left-color: #0056b3;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
}

.stat-value {
    font-size: 1.1rem;
    color: #495057;
}

/* Boutons d'action */
.btn {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modern-card {
    animation: fadeInUp 0.6s ease-out;
}

.modern-card:nth-child(2) { animation-delay: 0.1s; }
.modern-card:nth-child(3) { animation-delay: 0.2s; }
.modern-card:nth-child(4) { animation-delay: 0.3s; }

/* Responsive */
@media (max-width: 768px) {
    .price-card {
        margin-bottom: 1rem;
    }

    .region-price-card {
        margin-bottom: 1rem;
    }

    .info-item {
        margin-bottom: 1rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation d'entrée pour les cartes
    const cards = document.querySelectorAll('.modern-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });







    window.deleteProduct = function() {
        Swal.fire({
            title: 'Êtes-vous sûr ?',
            html: `Vous êtes sur le point de supprimer le produit <strong>"{{ $product->name }}"</strong>.<br>Cette action est irréversible.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash me-2"></i>Oui, supprimer',
            cancelButtonText: '<i class="fas fa-times me-2"></i>Annuler',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // Créer et soumettre le formulaire de suppression
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/products/{{ $product->id }}`;

                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                const methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';

                form.appendChild(csrfToken);
                form.appendChild(methodField);
                document.body.appendChild(form);
                form.submit();
            }
        });
    };



    // Effet de survol pour les statistiques
    const statItems = document.querySelectorAll('.stat-item');
    statItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(10px) scale(1.02)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0) scale(1)';
        });
    });

    // Effet de survol pour les cartes de prix
    const priceCards = document.querySelectorAll('.price-card');
    priceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
@endpush
