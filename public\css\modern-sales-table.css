/* Style moderne pour la vue des ventes - inspiré du design partagé */

/* Variables globales */
:root {
    --primary-color: #2196F3;     /* Bleu principal */
    --primary-light: #BBDEFB;     /* Bleu clair */
    --primary-dark: #1976D2;      /* Bleu foncé */
    --secondary-color: #4CAF50;   /* Vert */
    --warning-color: #FF9800;     /* Orange */
    --danger-color: #F44336;      /* Rouge */
    --info-color: #00BCD4;        /* Cyan */
    --light-color: #FAFAFA;       /* Gris très clair */
    --dark-color: #263238;        /* Bleu-gris foncé */
    --text-color: #37474F;        /* Bleu-gris */
    
    /* États des ventes */
    --paid-color: #4CAF50;        /* Vert - Payé */
    --pending-color: #FF9800;     /* Orange - En attente */
    --failed-color: #F44336;      /* Rouge - Écho<PERSON> */
    
    /* Propriétés générales */
    --border-radius: 0.5rem;
    --card-radius: 0.75rem;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    --transition: all 0.25s ease;
}

/* Réinitialisation du fond */
html, body, body::before, body::after {
    background: var(--light-color) !important;
    background-color: var(--light-color) !important;
    background-image: none !important;
}

/* Styles généraux */
body {
    color: var(--text-color);
    font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
}

/* Titre de la page */
.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-title i {
    color: var(--primary-color);
}

/* Conteneur principal */
.container-fluid {
    padding: 1.5rem;
    max-width: 1400px;
    margin: 0 auto;
}

/* Carte des ventes */
.sales-card {
    background: white;
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    border: none;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

/* En-tête de la carte */
.sales-card .card-header {
    background-color: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem;
}

/* Tableau des ventes moderne */
.modern-sales-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.modern-sales-table thead th {
    background-color: white;
    color: var(--text-color);
    font-weight: 600;
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modern-sales-table tbody tr {
    transition: var(--transition);
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.modern-sales-table tbody tr:hover {
    background-color: rgba(33, 150, 243, 0.03);
}

.modern-sales-table td {
    padding: 1rem;
    vertical-align: middle;
}

/* Badges d'état */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.4rem 0.8rem;
    border-radius: 2rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
}

.status-badge i {
    margin-right: 0.4rem;
    font-size: 0.7rem;
}

.status-badge.paid {
    background-color: var(--paid-color);
}

.status-badge.pending {
    background-color: var(--pending-color);
}

.status-badge.failed {
    background-color: var(--failed-color);
}

/* Barre de progression */
.progress {
    height: 0.5rem;
    border-radius: 1rem;
    background-color: rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: 1rem;
}

.progress-bar.paid {
    background-color: var(--paid-color);
}

.progress-bar.pending {
    background-color: var(--pending-color);
}

.progress-bar.failed {
    background-color: var(--failed-color);
}

/* Boutons d'action */
.action-button {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: var(--transition);
    margin-right: 0.25rem;
    font-size: 0.8rem;
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-button.view {
    background-color: var(--primary-color);
}

.action-button.edit {
    background-color: var(--warning-color);
}

.action-button.delete {
    background-color: var(--danger-color);
}

/* Pagination */
.pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
}

.pagination .page-item .page-link {
    padding: 0.5rem 0.75rem;
    margin: 0 0.25rem;
    border-radius: var(--border-radius);
    color: var(--primary-color);
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .page-item .page-link:hover {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

/* Responsive */
@media (max-width: 768px) {
    .modern-sales-table {
        display: block;
        overflow-x: auto;
    }
}
