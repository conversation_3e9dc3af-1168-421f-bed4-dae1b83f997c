@extends('layouts.cashier')

@section('styles')
<style>
    /* Variables globales */
    :root {
        --primary-color: #2196F3; /* Bleu principal */
        --primary-light: #BBDEFB; /* Bleu clair */
        --primary-dark: #1976D2; /* Bleu foncé */
        --secondary-color: #4CAF50; /* Vert */
        --warning-color: #FF9800; /* Orange */
        --danger-color: #F44336; /* Rouge */
        --info-color: #00BCD4; /* Cyan */
        --light-color: #FAFAFA; /* Gris très clair */
        --dark-color: #263238; /* Bleu-gris foncé */
        --text-color: #37474F; /* Bleu-gris */
        --border-radius: 1rem;
        --card-radius: 1.25rem;
        --box-shadow: 0 6px 15px rgba(0, 0, 0, 0.05);
        --transition: all 0.25s ease;
    }
    
    /* Styles généraux */
    .sale-container {
        padding: 1.5rem 1.5rem 2.5rem;
        background-color: var(--light-color);
        min-height: calc(100vh - 70px);
    }
    
    /* En-tête moderne avec design épuré */
    .sale-header {
        background: white;
        color: var(--text-color);
        border-radius: var(--card-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        box-shadow: var(--box-shadow);
        border-left: 5px solid var(--primary-color);
    }
    
    .sale-header h1 {
        font-weight: 700;
        margin-bottom: 0.75rem;
        font-size: 1.75rem;
        color: var(--primary-dark);
    }
    
    .sale-header p {
        font-size: 1rem;
        color: var(--text-color);
        opacity: 0.85;
    }

    .sale-info {
        position: relative;
        z-index: 2;
    }

    .sale-actions {
        position: relative;
        z-index: 2;
    }

    .payment-summary {
        background-color: var(--primary-light);
        border-radius: 0.75rem;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid rgba(33, 150, 243, 0.2);
    }
    
    /* Navigation par onglets moderne */
    .sale-tabs {
        margin-bottom: 1.5rem;
        background: white;
        padding: 0.75rem;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        display: flex;
        justify-content: center;
    }
    
    .sale-tabs .nav-link {
        padding: 0.85rem 1.75rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        color: var(--text-color);
        transition: var(--transition);
        margin: 0 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .sale-tabs .nav-link i {
        font-size: 1.1rem;
    }
    
    .sale-tabs .nav-link:hover {
        background-color: rgba(33, 150, 243, 0.1);
        color: var(--primary-color);
    }
    
    .sale-tabs .nav-link.active {
        background-color: var(--primary-color);
        color: white;
        box-shadow: 0 4px 10px rgba(33, 150, 243, 0.3);
    }
    
    /* Cartes modernes avec animations subtiles */
    .sale-card {
        border: none;
        border-radius: var(--card-radius);
        box-shadow: var(--box-shadow);
        margin-bottom: 1.5rem;
        transition: var(--transition);
        overflow: hidden;
        background-color: white;
    }
    
    .sale-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    }
    
    .sale-card .card-header {
        background-color: white;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1.25rem 1.5rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        color: var(--primary-dark);
    }
    
    .sale-card .card-header i {
        margin-right: 0.75rem;
        color: var(--primary-color);
        font-size: 1.2rem;
    }
    
    .sale-card .card-body {
        padding: 1.5rem;
    }
    
    /* Éléments d'information avec design moderne */
    .info-item {
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        transition: var(--transition);
        padding: 0.75rem 0.5rem;
        border-radius: 0.75rem;
    }
    
    .info-item:hover {
        background-color: rgba(33, 150, 243, 0.05);
    }
    
    .info-label {
        font-weight: 600;
        min-width: 150px;
        color: var(--dark-color);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .info-label i {
        color: var(--primary-color);
        font-size: 1rem;
    }
    
    .info-value {
        color: var(--text-color);
        font-weight: 500;
        background: rgba(33, 150, 243, 0.05);
        padding: 0.35rem 0.75rem;
        border-radius: 0.5rem;
    }
    
    /* Tableaux élégants */
    .product-table {
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.03);
        border: none;
    }
    
    .product-table thead {
        background-color: var(--primary-color);
        color: white;
    }
    
    .product-table th {
        padding: 1rem 1.25rem;
        font-weight: 600;
        border: none;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
    }
    
    .product-table td {
        padding: 1rem 1.25rem;
        vertical-align: middle;
        border-color: rgba(0, 0, 0, 0.03);
        font-weight: 500;
    }
    
    /* Styles pour le résumé financier */
    .financial-summary {
        background: white;
        border-radius: var(--card-radius);
        padding: 1.5rem;
        box-shadow: var(--box-shadow);
        height: 100%;
    }
    
    .financial-item {
        transition: var(--transition);
        padding: 0.5rem 0.75rem;
        border-radius: 0.5rem;
    }
    
    .financial-item:hover {
        background-color: rgba(33, 150, 243, 0.05);
    }
    
    .financial-value {
        font-weight: 600;
        background: rgba(33, 150, 243, 0.05);
        padding: 0.35rem 0.75rem;
        border-radius: 0.5rem;
    }
    
    .financial-total {
        background-color: rgba(33, 150, 243, 0.08);
        padding: 0.75rem 1rem;
        border-radius: 0.75rem;
        margin: 1rem 0;
    }
    
    /* Styles pour les badges de statut */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-weight: 600;
        font-size: 0.85rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    }
    
    .status-pending {
        background-color: var(--warning-color);
        color: white;
    }
    
    .status-partial {
        background-color: var(--info-color);
        color: white;
    }
    
    .status-completed {
        background-color: var(--secondary-color);
        color: white;
    }
    
    .status-cancelled {
        background-color: var(--danger-color);
        color: white;
    }
    
    /* Animation pour le bouton d'encaissement */
    .btn-success {
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .btn-success:after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba(255, 255, 255, 0.5);
        opacity: 0;
        border-radius: 100%;
        transform: scale(1, 1) translate(-50%);
        transform-origin: 50% 50%;
    }
    
    .btn-success:hover {
        transform: scale(1.05);
        box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
    }
    
    @keyframes ripple {
        0% {
            transform: scale(0, 0);
            opacity: 0.5;
        }
        100% {
            transform: scale(20, 20);
            opacity: 0;
        }
    }
    
    .btn-success:focus:not(:active)::after {
        animation: ripple 1s ease-out;
    }
    
    .product-table tbody tr {
        transition: var(--transition);
    }
    
    .product-table tbody tr:hover {
        background-color: rgba(30, 136, 229, 0.03);
    }
    
    /* Badges de statut animés */
    .status-badge {
        padding: 0.6rem 1.2rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 0.85rem;
        display: inline-flex;
        align-items: center;
        transition: var(--transition);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    }
    
    .status-badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
    }
    
    .status-badge i {
        margin-right: 0.5rem;
    }
    
    .status-pending {
        background-color: var(--warning-color);
        color: #fff;
    }
    
    .status-completed {
        background-color: var(--secondary-color);
        color: #fff;
    }
    
    .status-cancelled {
        background-color: var(--danger-color);
        color: #fff;
    }
    
    .status-partial {
        background-color: var(--info-color);
        color: #fff;
    }
    
    /* Barre de progression animée */
    .payment-progress {
        height: 10px;
        border-radius: 50px;
        margin: 1rem 0;
        background-color: rgba(0, 0, 0, 0.05);
        overflow: hidden;
        position: relative;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .payment-progress .progress-bar {
        position: relative;
        overflow: hidden;
        transition: width 1.5s cubic-bezier(0.22, 0.61, 0.36, 1);
        background: linear-gradient(to right, var(--secondary-color), #64FFDA);
    }
    
    .payment-progress .progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
        background-size: 1rem 1rem;
        animation: progress-bar-stripes 1s linear infinite;
    }
    
    @keyframes progress-bar-stripes {
        from { background-position: 1rem 0; }
        to { background-position: 0 0; }
    }
    
    /* Boutons d'action stylisés */
    .action-buttons {
        display: flex;
        gap: 0.75rem;
    }
    
    .action-buttons .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        font-weight: 600;
        transition: var(--transition);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    }
    
    .action-buttons .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    }
    
    .action-buttons .btn-success {
        background: linear-gradient(to right, var(--secondary-color), #64FFDA);
        border: none;
    }
    
    .action-buttons .btn-light {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(5px);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    .action-buttons .btn-light:hover {
        background: rgba(255, 255, 255, 0.3);
    }
    
    .action-buttons .btn-outline-primary {
        border: 2px solid var(--primary-color);
        color: var(--primary-color);
    }
    
    .action-buttons .btn-outline-primary:hover {
        background-color: var(--primary-color);
        color: white;
    }
    
    .action-buttons .btn-outline-secondary {
        border: 2px solid var(--dark-color);
        color: var(--dark-color);
    }
    
    .action-buttons .btn-outline-secondary:hover {
        background-color: var(--dark-color);
        color: white;
    }
    
    /* Sections d'information stylisées */
    .vehicle-info, .driver-info {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 1.25rem;
        margin-bottom: 1.25rem;
        transition: var(--transition);
        border-left: 4px solid var(--primary-color);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
    }
    
    .vehicle-info:hover, .driver-info:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    }
    
    .vehicle-info i, .driver-info i {
        color: var(--primary-color);
        margin-right: 0.75rem;
    }
    
    /* Résumé financier */
    .financial-summary {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }
    
    .financial-summary .card-title {
        color: var(--primary-dark);
        font-weight: 700;
        margin-bottom: 1.25rem;
        border-bottom: 2px solid rgba(0, 0, 0, 0.05);
        padding-bottom: 0.75rem;
        display: flex;
        align-items: center;
    }
    
    .financial-summary .card-title i {
        margin-right: 0.5rem;
        color: var(--primary-color);
    }
    
    .financial-summary .fw-bold {
        font-size: 1.1rem;
    }
    
    /* Badges et étiquettes */
    .badge {
        padding: 0.5rem 0.85rem;
        font-weight: 600;
        border-radius: 50px;
    }
    
    /* Animation de pulsation pour les éléments importants */
    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(30, 136, 229, 0.4); }
        70% { box-shadow: 0 0 0 10px rgba(30, 136, 229, 0); }
        100% { box-shadow: 0 0 0 0 rgba(30, 136, 229, 0); }
    }
    
    .pulse {
        animation: pulse 2s infinite;
    }
    
    /* Styles d'impression optimisés */
    @media print {
        .action-buttons, .navbar, .sidebar, footer {
            display: none !important;
        }
        
        .sale-card {
            box-shadow: none !important;
            border: 1px solid #ddd !important;
            margin-bottom: 1rem !important;
        }
        
        .sale-header {
            background: #f8f9fa !important;
            color: #000 !important;
            padding: 1rem !important;
        }
        
        .sale-header::before {
            display: none !important;
        }
        
        body {
            padding: 0 !important;
            margin: 0 !important;
        }
        
        .container-fluid {
            padding: 0.5rem !important;
        }
        
        .product-table thead {
            background: #f8f9fa !important;
            color: #000 !important;
        }
        
        .status-badge {
            border: 1px solid #ddd !important;
            background: none !important;
            color: #000 !important;
            box-shadow: none !important;
        }
    }
</style>
@endsection

@section('content')
<script>
    // Log pour confirmer que nous sommes dans le bon fichier
    console.log('%c FICHIER: show.blade.php - DÉTAILS DE LA VENTE ', 'background: #2196F3; color: white; padding: 10px; font-size: 16px; font-weight: bold; border-radius: 5px;');
    console.log('Chemin: resources/views/cashier/sales/show.blade.php');
    console.log('Date et heure: ' + new Date().toLocaleString());
    
    // Notification visuelle
    document.addEventListener('DOMContentLoaded', function() {
        const notification = document.createElement('div');
        notification.style.position = 'fixed';
        notification.style.bottom = '20px';
        notification.style.right = '20px';
        notification.style.backgroundColor = '#2196F3';
        notification.style.color = 'white';
        notification.style.padding = '15px 20px';
        notification.style.borderRadius = '5px';
        notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        notification.style.zIndex = '9999';
        notification.style.opacity = '0';
        notification.style.transition = 'opacity 0.5s ease';
        notification.innerHTML = '<strong>Vue détaillée de la vente</strong><br>Fichier: show.blade.php';
        
        document.body.appendChild(notification);
        
        // Afficher puis masquer la notification
        setTimeout(() => { notification.style.opacity = '1'; }, 500);
        setTimeout(() => { notification.style.opacity = '0'; }, 5000);
        setTimeout(() => { document.body.removeChild(notification); }, 5500);
    });
</script>
<div class="container-fluid sale-container">
    <!-- En-tête avec les actions -->
    <div class="sale-header d-flex flex-column flex-md-row justify-content-between align-items-md-center">
        <div class="sale-info">
            <div class="d-flex align-items-center mb-2">
                <h1 class="h3 mb-0 me-3">Détails de la Vente</h1>
                <div>
                    @if($sale->status == 'pending_payment')
                        <span class="status-badge status-pending"><i class="fas fa-clock"></i> En attente</span>
                    @elseif($sale->status == 'partially_paid')
                        <span class="status-badge status-partial"><i class="fas fa-percentage"></i> Partiellement payé</span>
                    @elseif($sale->status == 'paid')
                        <span class="status-badge status-completed"><i class="fas fa-check-circle"></i> Payé</span>
                    @elseif($sale->status == 'cancelled')
                        <span class="status-badge status-cancelled"><i class="fas fa-times-circle"></i> Annulée</span>
                    @else
                        <span class="status-badge">{{ $sale->status }}</span>
                                        @endif
                                    </div>
            </div>
            <div class="d-flex align-items-center gap-3 mb-1">
                <div class="d-flex align-items-center">
                    <i class="fas fa-receipt text-primary me-2"></i>
                    <span>Référence: <strong>{{ $sale->invoice_number ?? 'VNT-' . str_pad($sale->id, 6, '0', STR_PAD_LEFT) }}</strong></span>
                </div>
                <div class="d-flex align-items-center">
                    <i class="far fa-calendar-alt text-primary me-2"></i>
                    <span>{{ $sale->created_at->format('d/m/Y H:i') }}</span>
                </div>
            </div>
            <div class="d-flex align-items-center gap-3">
                <div class="d-flex align-items-center">
                    <i class="fas fa-user-tie text-primary me-2"></i>
                    <span>Client: <strong>{{ $sale->customer_name ?? 'N/A' }}</strong></span>
                </div>
                @if($sale->customer_phone)
                <div class="d-flex align-items-center">
                    <i class="fas fa-phone text-primary me-2"></i>
                    <a href="tel:{{ $sale->customer_phone }}" class="text-decoration-none">{{ $sale->customer_phone }}</a>
                </div>
                @endif
                                        @endif
                                    </div>
        </div>
        
        <div class="sale-actions mt-3 mt-md-0">
            <div class="payment-summary p-3 rounded mb-3">
                <h6 class="mb-2 d-flex align-items-center"><i class="fas fa-chart-pie text-primary me-2"></i> Progression du paiement</h6>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="fw-bold">{{ number_format($sale->payment_progress ?? 0, 0) }}% payé</span>
                    <span class="badge bg-primary">{{ number_format($sale->amount_paid ?? 0, 0, ',', ' ') }} / {{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</span>
                </div>
                <div class="progress payment-progress" style="height: 10px;">
                    <div class="progress-bar bg-primary" role="progressbar" style="width: {{ $sale->payment_progress ?? 0 }}%"></div>
                </div>
            </div>
            
            <div class="action-buttons d-flex">
                <a href="{{ route('cashier.sales.index') }}" class="btn btn-outline-primary me-2">
                    <i class="fas fa-arrow-left me-1"></i> Retour
                </a>
                <div class="dropdown me-2">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="actionDropdown" data-bs-toggle="dropdown">
                        <i class="fas fa-file-export me-1"></i> Exporter
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" onclick="window.print(); return false;"><i class="fas fa-print me-2"></i> Imprimer</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-file-pdf me-2"></i> Générer PDF</a></li>
                    </ul>
                </div>
                @if($sale->status != 'cancelled' && $sale->admin_validation_status != 'rejected' && ($sale->total_amount - ($sale->amount_paid ?? 0)) > 0)
                    <a href="{{ route('cashier.payments.process', $sale->id) }}" class="btn btn-success">
                        <i class="fas fa-cash-register me-1"></i> Encaisser
                    </a>
                @endif
                                        @endif
                                    </div>
        </div>
    </div>
    
    <!-- Navigation par onglets -->
    <div class="sale-tabs mb-4">
        <ul class="nav nav-pills nav-fill" id="saleTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab">
                    <i class="fas fa-info-circle"></i>
                    <span>Détails</span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="delivery-tab" data-bs-toggle="tab" data-bs-target="#delivery" type="button" role="tab">
                    <i class="fas fa-truck"></i>
                    <span>Livraison</span>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment" type="button" role="tab">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>Paiement</span>
                </button>
            </li>
        </ul>
    </div>

    <!-- Contenu des onglets -->
    <div class="tab-content" id="saleTabContent">
        <!-- Onglet Détails -->
        <div class="tab-pane fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
            <div class="row">
                <!-- Informations générales -->
                <div class="col-lg-8">
                    <div class="sale-card">
                        <div class="card-header">
                            <i class="fas fa-info-circle"></i> Informations générales
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <span class="info-label"><i class="far fa-calendar-alt text-primary"></i> Date de vente:</span>
                                        <span class="info-value">{{ $sale->created_at->format('d/m/Y H:i') }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label"><i class="far fa-user text-primary"></i> Créé par:</span>
                                        <span class="info-value">{{ $sale->createdBy->name ?? 'N/A' }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label"><i class="fas fa-map-marker-alt text-primary"></i> Ville:</span>
                                        <span class="info-value">{{ $sale->city->name ?? 'N/A' }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label"><i class="fas fa-tag text-primary"></i> Type de vente:</span>
                                        <span class="info-value">
                                            @if($sale->is_direct)
                                                <span class="badge bg-success">Vente directe</span>
                                            @else
                                                <span class="badge bg-info">Vente programmée</span>
                                            @endif
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <span class="info-label"><i class="fas fa-user-tie text-primary"></i> Client:</span>
                                        <span class="info-value">{{ $sale->customer_name ?? 'N/A' }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label"><i class="fas fa-phone text-primary"></i> Téléphone:</span>
                                        <span class="info-value">
                                            @if($sale->customer_phone)
                                                <a href="tel:{{ $sale->customer_phone }}" class="text-decoration-none">{{ $sale->customer_phone }}</a>
                                            @else
                                                N/A
                                            @endif
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label"><i class="fas fa-map-marked-alt text-primary"></i> Adresse:</span>
                                        <span class="info-value">{{ $sale->customer_address ?? 'N/A' }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label"><i class="fas fa-truck text-primary"></i> Livraison:</span>
                                        <span class="info-value">
                                            @if($sale->delivery_status == 'pending')
                                                <span class="badge bg-warning"><i class="fas fa-clock"></i> En attente</span>
                                            @elseif($sale->delivery_status == 'in_progress')
                                                <span class="badge bg-info"><i class="fas fa-shipping-fast"></i> En cours</span>
                                            @elseif($sale->delivery_status == 'completed')
                                                <span class="badge bg-success"><i class="fas fa-check"></i> Livrée</span>
                                            @else
                                                <span class="badge bg-secondary">{{ $sale->delivery_status ?? 'N/A' }}</span>
                                            @endif
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Détails du produit -->
                    <div class="sale-card">
                        <div class="card-header">
                            <i class="fas fa-box-open"></i> Détails du produit
                        </div>
                        <div class="card-body">
                            <div class="product-details mb-4">
                                <div class="row align-items-center">
                                    <div class="col-md-2 text-center mb-3 mb-md-0">
                                        <div class="product-icon bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 70px; height: 70px;">
                                            <i class="fas fa-box fa-2x"></i>
                                        </div>
                                    </div>
                                    <div class="col-md-10">
                                        <h5 class="product-name mb-2">
                                            @if($sale->product)
                                                {{ $sale->product->name }}
                                            @elseif($sale->supply && $sale->supply->details->isNotEmpty())
                                                {{ $sale->supply->details->first()->product->name }}
                                            @else
                                                N/A
                                            @endif
                                        </h5>
                                        <div class="product-meta d-flex flex-wrap gap-4 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-tag text-primary me-2"></i>
                                                <span>Catégorie: <strong>{{ $sale->product->category->name ?? 'N/A' }}</strong></span>
                                            </div>
                                            @if(isset($sale->product) && $sale->product->sku)
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-barcode text-primary me-2"></i>
                                                <span>SKU: <strong>{{ $sale->product->sku }}</strong></span>
                                            </div>
                                            @endif
                                        @endif
                                    </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-striped product-table">
                                    <thead>
                                        <tr>
                                            <th><i class="fas fa-cube me-2"></i>Produit</th>
                                            <th><i class="fas fa-tag me-2"></i>Prix unitaire</th>
                                            <th><i class="fas fa-weight me-2"></i>Quantité</th>
                                            <th><i class="fas fa-money-bill-wave me-2"></i>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="bg-primary rounded p-2 me-3 text-white">
                                                        <i class="fas fa-box"></i>
                                                    </div>
                                                    <div>
                                                        <strong>
                                                            @if($sale->product)
                                                                {{ $sale->product->name }}
                                                            @elseif($sale->supply && $sale->supply->details->isNotEmpty())
                                                                {{ $sale->supply->details->first()->product->name }}
                                                            @else
                                                                N/A
                                                            @endif
                                                        </strong>
                                                        <div class="small text-muted">Ciment</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="align-middle">{{ number_format($sale->unit_price, 0, ',', ' ') }} <small class="text-muted">FCFA</small></td>
                                            <td class="align-middle">{{ number_format($sale->quantity, 2, ',', ' ') }} <small class="text-muted">T</small></td>
                                            <td class="align-middle fw-bold">{{ number_format($sale->total_amount, 0, ',', ' ') }} <small class="text-muted">FCFA</small></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="card border-0 shadow-sm h-100">
                                        <div class="card-body">
                                            <h6 class="card-title mb-3"><i class="fas fa-info-circle text-primary me-2"></i>Informations supplémentaires</h6>
                                            
                                            <div class="info-item">
                                                <span class="info-label"><i class="fas fa-truck-loading text-primary me-1"></i> Voyages:</span>
                                                <span class="info-value">{{ $sale->trips ?? 'N/A' }}</span>
                                            </div>
                                            
                                            @if($sale->discount_per_ton > 0)
                                                <div class="info-item">
                                                    <span class="info-label"><i class="fas fa-percentage text-success me-1"></i> Remise/tonne:</span>
                                                    <span class="info-value text-success">{{ number_format($sale->discount_per_ton, 0, ',', ' ') }} FCFA</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label"><i class="fas fa-gift text-success me-1"></i> Remise totale:</span>
                                                    <span class="info-value text-success">{{ number_format($sale->discount_total, 0, ',', ' ') }} FCFA</span>
                                                </div>
                                            @endif
                                            
                                            @if($sale->price_modified)
                                                <div class="info-item">
                                                    <span class="info-label"><i class="fas fa-history text-primary me-1"></i> Prix original:</span>
                                                    <span class="info-value">{{ number_format($sale->original_price, 0, ',', ' ') }} FCFA</span>
                                                </div>
                                            @endif
                                        @endif
                                    </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="financial-summary">
                                        <h5 class="card-title d-flex align-items-center mb-4"><i class="fas fa-file-invoice-dollar me-2 text-primary"></i>Résumé financier</h5>
                                        
                                        <div class="financial-item d-flex justify-content-between align-items-center mb-3">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-receipt text-primary me-2"></i>
                                                <span>Montant avant remise</span>
                                            </div>
                                            <span class="financial-value">{{ number_format($sale->total_amount_before_discount ?? $sale->total_amount, 0, ',', ' ') }} FCFA</span>
                                        </div>
                                        
                                        @if(($sale->discount_amount ?? 0) > 0)
                                        <div class="financial-item d-flex justify-content-between align-items-center mb-3">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-percentage text-danger me-2"></i>
                                                <span>Remise</span>
                                            </div>
                                            <span class="financial-value text-danger">- {{ number_format($sale->discount_amount, 0, ',', ' ') }} FCFA</span>
                                        </div>
                                        @endif
                                        
                                        @if(($sale->tax_amount ?? 0) > 0)
                                        <div class="financial-item d-flex justify-content-between align-items-center mb-3">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-coins text-warning me-2"></i>
                                                <span>Taxes</span>
                                            </div>
                                            <span class="financial-value">{{ number_format($sale->tax_amount, 0, ',', ' ') }} FCFA</span>
                                        </div>
                                        @endif
                                        
                                        <div class="financial-total d-flex justify-content-between align-items-center my-3 py-3 border-top border-bottom">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-money-bill-wave text-primary me-2"></i>
                                                <span class="fw-bold">Total</span>
                                            </div>
                                            <span class="financial-value fw-bold fs-5">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</span>
                                        </div>
                                        
                                        <div class="financial-item d-flex justify-content-between align-items-center mb-3">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <span>Payé</span>
                                            </div>
                                            <span class="financial-value text-success">{{ number_format($sale->amount_paid ?? 0, 0, ',', ' ') }} FCFA</span>
                                        </div>
                                        
                                        @if(($sale->total_amount - ($sale->amount_paid ?? 0)) > 0)
                                        <div class="financial-item d-flex justify-content-between align-items-center mb-3">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-exclamation-circle text-warning me-2"></i>
                                                <span>Restant à payer</span>
                                            </div>
                                            <span class="financial-value text-warning fw-bold">{{ number_format($sale->total_amount - ($sale->amount_paid ?? 0), 0, ',', ' ') }} FCFA</span>
                                        </div>
                                        
                                        @if($sale->status != 'cancelled' && $sale->admin_validation_status != 'rejected')
                                            <div class="text-center mt-4">
                                                <a href="{{ route('cashier.payments.process', $sale->id) }}" class="btn btn-success w-100 py-2">
                                                    <i class="fas fa-cash-register me-2"></i> Encaisser le paiement
                                                </a>
                                            </div>
                                        @elseif($sale->status == 'cancelled' || $sale->admin_validation_status == 'rejected')
                                            <div class="text-center mt-4">
                                                <div class="alert alert-danger py-3">
                                                    <i class="fas fa-ban me-2"></i> Paiement non autorisé - Vente {{ $sale->status == 'cancelled' ? 'annulée' : 'rejetée' }}
                                                </div>
                                            </div>
                                        @endif
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Colonne de droite -->
                <div class="col-lg-4">
                    <!-- Validation administrative -->
                    @if($sale->discount_per_ton > 0 || $sale->price_modified)
                        <div class="sale-card">
                            <div class="card-header">
                                <i class="fas fa-check-circle"></i> Validation administrative
                            </div>
                            <div class="card-body">
                                <div class="info-item">
                                    <span class="info-label">Statut:</span>
                                    <span class="info-value">
                                        @if($sale->admin_validation_status == 'pending')
                                            <span class="badge bg-warning">En attente</span>
                                        @elseif($sale->admin_validation_status == 'approved')
                                            <span class="badge bg-success">Approuvée</span>
                                        @elseif($sale->admin_validation_status == 'rejected')
                                            <span class="badge bg-danger">Rejetée</span>
                                        @else
                                            <span class="badge bg-secondary">{{ $sale->admin_validation_status }}</span>
                                        @endif
                                    </span>
                                </div>
                                @if($sale->admin_note)
                                    <div class="info-item">
                                        <span class="info-label">Note:</span>
                                        <span class="info-value">{{ $sale->admin_note }}</span>
                                    </div>
                                @endif
                                <div class="alert alert-info mt-2 mb-0">
                                    <small><i class="fas fa-info-circle"></i> Cette vente nécessite une validation administrative car elle comporte 
                                    @if($sale->discount_per_ton > 0 && $sale->price_modified)
                                        une remise et une modification de prix.
                                    @elseif($sale->discount_per_ton > 0)
                                        une remise.
                                    @elseif($sale->price_modified)
                                        une modification de prix.
                                    @endif
                                    </small>
                                </div>
                            </div>
                        </div>
                    @endif
                                        @endif
                                    </div>
            </div>
        </div>
        
        <!-- Onglet Livraison -->
        <div class="tab-pane fade" id="delivery" role="tabpanel" aria-labelledby="delivery-tab">
            <div class="row">
                <div class="col-lg-6">
                    <!-- Informations du véhicule -->
                    <div class="sale-card">
                        <div class="card-header">
                            <i class="fas fa-truck"></i> Informations du véhicule
                        </div>
                        <div class="card-body">
                            <div class="vehicle-info">
                                <h6 class="mb-3"><i class="fas fa-truck"></i> Véhicule assigné</h6>
                                <div class="info-item">
                                    <span class="info-label">Immatriculation:</span>
                                    <span class="info-value">
                                        {{ $supplyCity && $supplyCity->vehicle ? $supplyCity->vehicle->registration_number : ($sale->vehicle ? $sale->vehicle->registration_number : 'Non assigné') }}
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Marque:</span>
                                    <span class="info-value">
                                        {{ $sale->vehicle ? $sale->vehicle->brand : 'N/A' }}
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Modèle:</span>
                                    <span class="info-value">
                                        {{ $sale->vehicle ? $sale->vehicle->model : 'N/A' }}
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Capacité:</span>
                                    <span class="info-value">
                                        {{ number_format($supplyCity && $supplyCity->vehicle && $supplyCity->vehicle->capacity ? $supplyCity->vehicle->capacity->capacity : ($sale->vehicle && $sale->vehicle->capacity ? $sale->vehicle->capacity->capacity : 0), 2, ',', ' ') }} T
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <!-- Informations du chauffeur -->
                    <div class="sale-card">
                        <div class="card-header">
                            <i class="fas fa-user"></i> Informations du chauffeur
                        </div>
                        <div class="card-body">
                            <div class="driver-info">
                                <h6 class="mb-3"><i class="fas fa-user"></i> Chauffeur assigné</h6>
                                <div class="info-item">
                                    <span class="info-label">Nom:</span>
                                    <span class="info-value">
                                        {{ $supplyCity && $supplyCity->driver ? $supplyCity->driver->first_name . ' ' . $supplyCity->driver->last_name : ($sale->driver ? $sale->driver->first_name . ' ' . $sale->driver->last_name : 'Non assigné') }}
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Téléphone:</span>
                                    <span class="info-value">
                                        @if($supplyCity && $supplyCity->driver && $supplyCity->driver->phone)
                                            <a href="tel:{{ $supplyCity->driver->phone }}" class="text-decoration-none">{{ $supplyCity->driver->phone }}</a>
                                        @elseif($sale->driver && $sale->driver->phone)
                                            <a href="tel:{{ $sale->driver->phone }}" class="text-decoration-none">{{ $sale->driver->phone }}</a>
                                        @else
                                            N/A
                                        @endif
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Statut:</span>
                                    <span class="info-value">
                                        @if($sale->driver && $sale->driver->is_active)
                                            <span class="badge bg-success">Actif</span>
                                        @else
                                            <span class="badge bg-secondary">Inactif</span>
                                        @endif
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Informations de l'approvisionnement -->
                <div class="col-lg-12 mt-4">
                    <div class="sale-card">
                        <div class="card-header">
                            <i class="fas fa-warehouse"></i> Informations de l'approvisionnement
                        </div>
                        <div class="card-body">
                            @if($sale->supply)
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-item">
                                            <span class="info-label"><i class="fas fa-hashtag text-primary me-1"></i> Référence:</span>
                                            <span class="info-value">{{ $sale->supply->reference ?? 'N/A' }}</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label"><i class="fas fa-calendar-alt text-primary me-1"></i> Date:</span>
                                            <span class="info-value">{{ $sale->supply->created_at ? $sale->supply->created_at->format('d/m/Y') : 'N/A' }}</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        @if($supplyCity)
                                            <div class="info-item">
                                                <span class="info-label"><i class="fas fa-weight-hanging text-primary me-1"></i> Quantité totale:</span>
                                                <span class="info-value">{{ number_format($supplyCity->quantity ?? 0, 2, ',', ' ') }} T</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label"><i class="fas fa-balance-scale text-primary me-1"></i> Quantité restante:</span>
                                                <span class="info-value">{{ number_format($supplyCity->remaining_quantity ?? 0, 2, ',', ' ') }} T</span>
                                            </div>
                                        @endif
                                        @endif
                                    </div>
                                </div>
                            @else
                                <p class="text-muted">Aucune information d'approvisionnement disponible</p>
                            @endif
                                        @endif
                                    </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Onglet Paiement -->
        <div class="tab-pane fade" id="payment" role="tabpanel" aria-labelledby="payment-tab">
            <div class="row">
                <div class="col-lg-6">
                    <div class="sale-card">
                        <div class="card-header">
                            <i class="fas fa-file-invoice-dollar"></i> Résumé financier
                        </div>
                        <div class="card-body">
                            <div class="financial-summary p-0 box-shadow-none">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span><i class="fas fa-receipt text-primary me-2"></i>Montant avant remise:</span>
                                    <span>{{ number_format($sale->total_before_discount ?? $sale->total_amount, 0, ',', ' ') }} FCFA</span>
                                </div>
                                
                                @if($sale->discount_total > 0)
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <span><i class="fas fa-percentage text-success me-2"></i>Remise:</span>
                                        <span class="text-success">-{{ number_format($sale->discount_total, 0, ',', ' ') }} FCFA</span>
                                    </div>
                                @endif
                                
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span><i class="fas fa-money-bill-wave text-primary me-2"></i>Montant total:</span>
                                    <span class="fw-bold">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</span>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span><i class="fas fa-check-circle text-success me-2"></i>Montant payé:</span>
                                    <span class="text-success">{{ number_format($sale->amount_paid ?? 0, 0, ',', ' ') }} FCFA</span>
                                </div>
                                
                                <hr>
                                
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span><i class="fas fa-exclamation-circle text-danger me-2"></i>Reste à payer:</span>
                                    <span class="fw-bold text-danger fs-5">{{ number_format(($sale->total_amount - ($sale->amount_paid ?? 0)), 0, ',', ' ') }} FCFA</span>
                                </div>
                            </div>
                            
                            <!-- Barre de progression du paiement animée -->
                            <div class="progress payment-progress mt-3">
                                <div class="progress-bar" role="progressbar" 
                                    style="width: {{ $sale->payment_progress ?? 0 }}%" 
                                    aria-valuenow="{{ $sale->payment_progress ?? 0 }}" aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                            
                            <div class="text-center mt-2">
                                <span class="badge {{ $sale->payment_progress > 50 ? 'bg-success' : 'bg-warning' }} p-2">
                                    <i class="fas {{ $sale->payment_progress == 100 ? 'fa-check-circle' : 'fa-chart-pie' }} me-1"></i>
                                    {{ number_format($sale->payment_progress ?? 0, 0) }}% payé
                                </span>
                            </div>
                            
                            @if(($sale->total_amount - ($sale->amount_paid ?? 0)) > 0 && $sale->status != 'cancelled' && $sale->admin_validation_status != 'rejected')
                                <div class="text-center mt-4">
                                    <a href="{{ route('cashier.payments.process', $sale->id) }}" class="btn btn-success">
                                        <i class="fas fa-cash-register me-1"></i> Effectuer un paiement
                                    </a>
                                </div>
                            @elseif($sale->status == 'cancelled' || $sale->admin_validation_status == 'rejected')
                                <div class="text-center mt-3">
                                    <div class="alert alert-danger py-2">
                                        <i class="fas fa-ban me-1"></i> Paiement non autorisé - Vente {{ $sale->status == 'cancelled' ? 'annulée' : 'rejetée' }}
                                    </div>
                                </div>
                            @endif
                                        
                                    </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="sale-card">
                        <div class="card-header">
                            <i class="fas fa-history"></i> Historique des paiements
                        </div>
                        <div class="card-body">
                            <!-- Cette section pourrait être complétée avec les données réelles des paiements -->
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> L'historique des paiements sera affiché ici lorsque des paiements seront effectués.
                            </div>
                            
                            <!-- Exemple de tableau d'historique des paiements -->
                            <table class="table table-striped mt-3">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Montant</th>
                                        <th>Méthode</th>
                                        <th>Statut</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Si aucun paiement n'existe encore -->
                                    <tr>
                                        <td colspan="4" class="text-center">Aucun paiement enregistré</td>
                                    </tr>
                                    
                                    <!-- Exemples de lignes de paiement (à afficher dynamiquement) -->
                                    <!--
                                    <tr>
                                        <td>25/05/2025</td>
                                        <td>150,000 FCFA</td>
                                        <td>Espèces</td>
                                        <td><span class="badge bg-success">Complété</span></td>
                                    </tr>
                                    -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
