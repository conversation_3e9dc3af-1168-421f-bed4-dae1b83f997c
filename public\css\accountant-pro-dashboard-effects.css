/**
 * GRADIS - Dashboard Comptable Professionnel
 * Animations et effets spéciaux
 * 2025 MOMK-Solutions
 */

/* Effets d'apparition */
.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.slide-in-left {
    animation: slideInLeft 0.5s ease forwards;
}

.slide-in-right {
    animation: slideInRight 0.5s ease forwards;
}

.slide-in-up {
    animation: slideInUp 0.5s ease forwards;
}

.slide-in-down {
    animation: slideInDown 0.5s ease forwards;
}

.zoom-in {
    animation: zoomIn 0.5s ease forwards;
}

/* Animation sur les cartes stat */
.stat-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.1);
}

.stat-card:hover .stat-icon {
    animation: pulse 1.5s infinite;
}

/* Animation des icônes */
.icon-animated {
    transition: transform 0.3s ease;
}

.icon-animated:hover {
    transform: scale(1.2) rotate(5deg);
}

/* Animation du fond de l'en-tête */
.header-bg-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    overflow: hidden;
}

.bg-circle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.1;
    background: linear-gradient(135deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0.1) 100%);
}

.bg-circle-1 {
    width: 300px;
    height: 300px;
    top: -100px;
    right: -50px;
    animation: floatAnimation 8s ease-in-out infinite alternate;
}

.bg-circle-2 {
    width: 200px;
    height: 200px;
    bottom: -50px;
    left: 10%;
    animation: floatAnimation 12s ease-in-out infinite alternate-reverse;
}

.bg-circle-3 {
    width: 150px;
    height: 150px;
    top: 30%;
    left: 30%;
    animation: floatAnimation 15s ease-in-out infinite alternate;
}

/* Ligne de séparation animée */
.animated-divider {
    height: 3px;
    width: 100%;
    background: linear-gradient(90deg, rgba(30,136,229,0.3) 0%, rgba(30,136,229,1) 50%, rgba(30,136,229,0.3) 100%);
    background-size: 200% 100%;
    animation: gradientMove 3s ease infinite;
    border-radius: 3px;
    margin: 1.5rem 0;
}

/* Effet de scintillement */
.sparkle {
    position: relative;
    overflow: hidden;
}

.sparkle::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: sparkleEffect 3s infinite;
}

/* Effet de pulsation pour les notifications */
.notification-pulse {
    position: relative;
}

.notification-pulse::before {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: var(--danger);
    border-radius: 50%;
    top: 0;
    right: 0;
    transform: translate(30%, -30%);
}

.notification-pulse::after {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: var(--danger);
    border-radius: 50%;
    top: 0;
    right: 0;
    transform: translate(30%, -30%);
    animation: pulse 2s infinite;
}

/* Effet de survol sur les éléments interactifs */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

/* Animations pour les graphiques */
.chart-appear {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.8s forwards;
    animation-delay: var(--delay, 0.2s);
}

/* Effets sur les badges de statut */
.status-badge {
    position: relative;
    overflow: hidden;
}

.status-badge::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.2);
    transform: skewX(-15deg);
    animation: shimmer 3s infinite;
}

/* Animation pour les barres de progression */
.progress-bar {
    transition: width 1s ease;
    position: relative;
}

.progress-bar::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 30px;
    height: 100%;
    background: rgba(255,255,255,0.3);
    animation: progressShimmer 2s infinite;
}

/* Définition des keyframes */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from { 
        opacity: 0;
        transform: translateX(-30px);
    }
    to { 
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from { 
        opacity: 0;
        transform: translateX(30px);
    }
    to { 
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from { 
        opacity: 0;
        transform: translateY(30px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from { 
        opacity: 0;
        transform: translateY(-30px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    from { 
        opacity: 0;
        transform: scale(0.95);
    }
    to { 
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes floatAnimation {
    0% { transform: translate(0, 0) rotate(0deg); }
    50% { transform: translate(10px, 10px) rotate(5deg); }
    100% { transform: translate(-5px, -5px) rotate(-5deg); }
}

@keyframes gradientMove {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes sparkleEffect {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes progressShimmer {
    0% { left: -30px; }
    100% { left: 100%; }
}

/* Animation séquentielle des éléments */
.sequential-fade-in > * {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s forwards;
}

.sequential-fade-in > *:nth-child(1) { animation-delay: 0.1s; }
.sequential-fade-in > *:nth-child(2) { animation-delay: 0.2s; }
.sequential-fade-in > *:nth-child(3) { animation-delay: 0.3s; }
.sequential-fade-in > *:nth-child(4) { animation-delay: 0.4s; }
.sequential-fade-in > *:nth-child(5) { animation-delay: 0.5s; }
.sequential-fade-in > *:nth-child(6) { animation-delay: 0.6s; }
.sequential-fade-in > *:nth-child(7) { animation-delay: 0.7s; }
.sequential-fade-in > *:nth-child(8) { animation-delay: 0.8s; }

/* Animation du compteur pour les chiffres */
.counter-animation {
    display: inline-block;
    transition: transform 0.5s ease;
}

.counter-animation.animate {
    transform: scale(1.2);
}

/* Effet d'alerte pulsante */
.alert-pulse {
    animation: alertPulse 2s infinite;
}

@keyframes alertPulse {
    0% { background-color: rgba(var(--warning-rgb), 0.1); }
    50% { background-color: rgba(var(--warning-rgb), 0.2); }
    100% { background-color: rgba(var(--warning-rgb), 0.1); }
}
