@extends('layouts.admin_minimal')

@section('title', 'Paramètres')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Paramètres</h1>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <!-- Paramètres généraux -->
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Paramètres généraux</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.settings.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="mb-3">
                            <label for="app_name" class="form-label">Nom de l'application</label>
                            <input type="text" 
                                   class="form-control @error('app_name') is-invalid @enderror" 
                                   id="app_name" 
                                   name="app_name" 
                                   value="{{ old('app_name', $settings['app_name']) }}"
                                   required>
                            @error('app_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="app_email" class="form-label">Email de l'application</label>
                            <input type="email" 
                                   class="form-control @error('app_email') is-invalid @enderror" 
                                   id="app_email" 
                                   name="app_email" 
                                   value="{{ old('app_email', $settings['app_email']) }}"
                                   required>
                            @error('app_email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="app_currency" class="form-label">Devise</label>
                            <input type="text" 
                                   class="form-control @error('app_currency') is-invalid @enderror" 
                                   id="app_currency" 
                                   name="app_currency" 
                                   value="{{ old('app_currency', $settings['app_currency']) }}"
                                   required>
                            @error('app_currency')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="tax_rate" class="form-label">Taux de TVA (%)</label>
                            <input type="number" 
                                   class="form-control @error('tax_rate') is-invalid @enderror" 
                                   id="tax_rate" 
                                   name="tax_rate" 
                                   value="{{ old('tax_rate', $settings['tax_rate']) }}"
                                   min="0"
                                   max="100"
                                   step="0.01"
                                   required>
                            @error('tax_rate')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input type="checkbox" 
                                       class="form-check-input @error('backup_enabled') is-invalid @enderror" 
                                       id="backup_enabled" 
                                       name="backup_enabled" 
                                       value="1"
                                       {{ old('backup_enabled', $settings['backup_enabled']) ? 'checked' : '' }}>
                                <label class="form-check-label" for="backup_enabled">Activer les sauvegardes automatiques</label>
                            </div>
                            @error('backup_enabled')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Enregistrer les modifications
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sauvegarde et restauration -->
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Sauvegarde et restauration</h6>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="mb-3">Créer une sauvegarde</h6>
                        <form action="{{ route('admin.settings.backup') }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-download me-1"></i>
                                Créer une sauvegarde
                            </button>
                        </form>
                    </div>

                    <div>
                        <h6 class="mb-3">Restaurer une sauvegarde</h6>
                        <form action="{{ route('admin.settings.restore') }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="mb-3">
                                <input type="file" 
                                       class="form-control @error('backup_file') is-invalid @enderror" 
                                       id="backup_file" 
                                       name="backup_file"
                                       accept=".sql"
                                       required>
                                @error('backup_file')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-upload me-1"></i>
                                Restaurer la sauvegarde
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light fixed-bottom">
    <div class="container text-center">
        <span class="text-muted"> {{ date('Y') }} GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>
@endsection
