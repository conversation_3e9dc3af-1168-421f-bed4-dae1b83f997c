@if($trip->status === 'validated')
    <form action="{{ route('cashier.cement-orders.convert-trip', ['order' => $order->id, 'trip' => $trip->id]) }}" 
          method="POST" class="d-inline">
        @csrf
        <button type="submit" class="btn btn-sm btn-success" 
                onclick="return confirm('Confirmer le paiement de ce voyage ?')">
            <i class="fas fa-check"></i> Valider paiement
        </button>
    </form>
    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <form action="{{ route('cashier.cement-orders.convert-to-credit', ['order' => $order->id, 'trip_id' => $trip->id]) }}" 
          method="POST" 
          class="d-inline">
        @csrf
        <input type="hidden" name="trip_id" value="{{ $trip->id }}">
        
        <div class="mb-3">
            <label for="due_date" class="form-label">Date d'échéance</label>
            <input type="date" 
                   class="form-control @error('due_date') is-invalid @enderror" 
                   id="due_date" 
                   name="due_date" 
                   value="{{ old('due_date', date('Y-m-d', strtotime('+30 days'))) }}"
                   min="{{ date('Y-m-d', strtotime('+1 day')) }}">
            @error('due_date')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <button type="submit" 
                class="btn btn-warning btn-sm" 
                onclick="return confirm('Êtes-vous sûr de vouloir convertir ce voyage en vente à crédit ? Cette action est irréversible.')">
            <i class="fas fa-credit-card"></i> Convertir en crédit
        </button>
    </form>
@elseif($trip->status === 'credited' || $trip->status === 'converted_to_sale')
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i> Ce voyage a déjà été converti en crédit
    </div>
@endif
