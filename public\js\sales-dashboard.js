// Script pour améliorer l'interface de gestion des ventes
document.addEventListener('DOMContentLoaded', function() {
    console.log('Sales Dashboard JS chargé');
    
    // Appliquer le fond bleu directement
    document.body.style.backgroundColor = '#E3F2FD';
    document.body.style.backgroundImage = 'linear-gradient(to bottom right, #1E88E5, #0D47A1)';
    document.body.style.backgroundAttachment = 'fixed';
    document.body.style.backgroundRepeat = 'no-repeat';
    document.body.style.backgroundSize = 'cover';
    
    // Gérer le bouton de basculement de la légende
    const toggleLegendBtn = document.querySelector('.toggle-legend');
    if (toggleLegendBtn) {
        toggleLegendBtn.addEventListener('click', function() {
            const icon = this.querySelector('i');
            if (this.getAttribute('aria-expanded') === 'true') {
                icon.style.transform = 'rotate(180deg)';
            } else {
                icon.style.transform = 'rotate(0deg)';
            }
        });
    }
    
    // Ajouter des effets de survol aux cartes de légende
    const legendCards = document.querySelectorAll('.legend-card');
    legendCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
            this.style.boxShadow = '0 8px 20px rgba(0, 0, 0, 0.06)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.03)';
        });
    });
    
    // Ajouter des effets aux badges
    const badges = document.querySelectorAll('.badge');
    badges.forEach(badge => {
        // S'assurer que les badges sont visibles
        badge.style.display = 'inline-flex';
        badge.style.visibility = 'visible';
        badge.style.opacity = '1';
        
        // Appliquer les styles spécifiques aux icônes
        const icon = badge.querySelector('i');
        if (icon) {
            icon.style.background = 'rgba(255, 255, 255, 0.2)';
            icon.style.padding = '0.4rem';
            icon.style.borderRadius = '50%';
            icon.style.marginRight = '0.3rem';
            icon.style.boxShadow = '0 0 0 2px rgba(255, 255, 255, 0.1)';
        }
        
        badge.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.05)';
            this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.2)';
            this.style.letterSpacing = '1px';
        });
        
        badge.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.15)';
            this.style.letterSpacing = '0.8px';
        });
        
        // Ajouter un effet de clic pour les badges
        badge.addEventListener('click', function() {
            this.style.transform = 'translateY(2px) scale(0.95)';
            this.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.1)';
            setTimeout(() => {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.15)';
            }, 200);
        });
    });
    
    // Correction spécifique pour les badges dans le tableau
    const tableBadges = document.querySelectorAll('.sales-table .badge');
    tableBadges.forEach(badge => {
        // Forcer l'affichage des badges dans le tableau
        badge.style.display = 'inline-flex';
        badge.style.visibility = 'visible';
        badge.style.opacity = '1';
        badge.style.whiteSpace = 'nowrap';
        
        // Appliquer les animations spécifiques selon le type de badge
        if (badge.classList.contains('badge-discount-sale')) {
            const icon = badge.querySelector('i');
            if (icon) {
                // Animation pour l'icône de pourcentage
                setInterval(() => {
                    icon.style.transform = 'scale(1.2)';
                    setTimeout(() => {
                        icon.style.transform = 'scale(1)';
                    }, 1000);
                }, 2000);
            }
        }
        
        if (badge.classList.contains('badge-pending')) {
            const icon = badge.querySelector('i');
            if (icon) {
                // Animation pour l'icône d'horloge
                setInterval(() => {
                    icon.style.transform = 'scale(1.1)';
                    icon.style.opacity = '0.8';
                    setTimeout(() => {
                        icon.style.transform = 'scale(1)';
                        icon.style.opacity = '1';
                    }, 750);
                }, 1500);
            }
        }
    });
    
    // Ajouter des effets aux lignes du tableau
    const tableRows = document.querySelectorAll('.sales-table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.03)';
            this.style.zIndex = '1';
            this.style.position = 'relative';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
            this.style.zIndex = 'auto';
        });
    });
    
    // Ajouter des effets aux boutons d'action
    const actionBtns = document.querySelectorAll('.action-btn');
    actionBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1)';
        });
        
        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
    
    // Ajouter des effets aux filtres
    const filterPills = document.querySelectorAll('.filter-pill');
    filterPills.forEach(pill => {
        pill.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 6px 12px rgba(0, 0, 0, 0.08)';
            }
        });
        
        pill.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.05)';
            }
        });
    });
    
    // Animation pour la barre de recherche
    const searchBox = document.querySelector('.search-box input');
    if (searchBox) {
        searchBox.addEventListener('focus', function() {
            this.style.boxShadow = '0 4px 15px rgba(25, 118, 210, 0.15)';
            this.style.borderColor = '#1976D2';
            this.style.backgroundColor = 'white';
        });
        
        searchBox.addEventListener('blur', function() {
            this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.03)';
            this.style.borderColor = 'rgba(226, 232, 240, 0.7)';
            this.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
        });
    }
});
