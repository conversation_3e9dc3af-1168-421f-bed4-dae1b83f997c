@extends('layouts.accountant')

@section('title', 'Tableau de Bord - Rapports')

@push('styles')
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<!-- Styles pour surcharger les conflits CSS existants -->

<style>
    /* SURCHARGE FORCÉE DES STYLES CONFLICTUELS */
    /* Ces styles ont la priorité absolue sur tous les autres fichiers CSS */

    /* Réinitialisation complète des styles de cartes */
    .dashboard-container .stats-grid .stat-card,
    .dashboard-container .stats-grid .stat-card:nth-child(1),
    .dashboard-container .stats-grid .stat-card:nth-child(2),
    .dashboard-container .stats-grid .stat-card:nth-child(3),
    .dashboard-container .stats-grid .stat-card:nth-child(4) {
        /* Annulation des styles conflictuels */
        animation: none !important;
        opacity: 1 !important;
        display: block !important;
        flex-direction: initial !important;
        justify-content: initial !important;
        align-items: initial !important;
        height: auto !important;
        border-left: none !important;

        /* Application de nos styles */
        background: white !important;
        border-radius: 16px !important;
        padding: 20px 24px !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
        border: 1px solid #e2e8f0 !important;
        position: relative !important;
        overflow: hidden !important;
        transition: all 0.3s ease !important;
        cursor: pointer !important;
        margin-bottom: 0 !important;
    }

    /* Design moderne avec cartes colorées et étiquettes attrayantes */
    :root {
        --bg-primary: #f1f5f9;
        --bg-secondary: #e2e8f0;
        --bg-white: #ffffff;
        --text-primary: #0f172a;
        --text-secondary: #475569;
        --text-muted: #64748b;

        /* Palette de couleurs enrichie */
        --blue: #2563eb;
        --blue-light: #dbeafe;
        --blue-dark: #1d4ed8;
        --blue-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);

        --red: #dc2626;
        --red-light: #fecaca;
        --red-dark: #b91c1c;
        --red-gradient: linear-gradient(135deg, #ef4444 0%, #b91c1c 100%);

        --green: #059669;
        --green-light: #bbf7d0;
        --green-dark: #047857;
        --green-gradient: linear-gradient(135deg, #10b981 0%, #047857 100%);

        --orange: #ea580c;
        --orange-light: #fed7aa;
        --orange-dark: #c2410c;
        --orange-gradient: linear-gradient(135deg, #f59e0b 0%, #c2410c 100%);

        --purple: #7c3aed;
        --purple-light: #ddd6fe;
        --purple-dark: #5b21b6;
        --purple-gradient: linear-gradient(135deg, #8b5cf6 0%, #5b21b6 100%);

        --indigo: #4f46e5;
        --indigo-light: #c7d2fe;
        --indigo-gradient: linear-gradient(135deg, #6366f1 0%, #4338ca 100%);

        /* Ombres améliorées */
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

        /* Bordures et rayons */
        --border: #e2e8f0;
        --border-light: #f1f5f9;
        --radius: 12px;
        --radius-lg: 16px;
        --radius-xl: 20px;
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', sans-serif;
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        color: var(--text-primary);
        line-height: 1.6;
        min-height: 100vh;
    }

    .dashboard-container {
        padding: 32px;
        max-width: 1400px;
        margin: 0 auto;
    }

    /* En-tête du tableau de bord */
    .dashboard-header {
        margin-bottom: 32px;
        text-align: center;
    }

    .dashboard-title {
        font-size: 2.5rem;
        font-weight: 800;
        color: white !important;
        margin-bottom: 8px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .dashboard-subtitle {
        font-size: 1.1rem;
        color: var(--text-secondary);
        font-weight: 500;
    }

    /* Grille des cartes de statistiques - styles prioritaires */
    .dashboard-container .stats-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
        gap: 20px !important;
        margin-bottom: 32px !important;
    }

    .dashboard-container .stat-card {
        background: var(--bg-white) !important;
        border-radius: var(--radius-lg) !important;
        padding: 20px 24px !important;
        box-shadow: var(--shadow) !important;
        border: 1px solid var(--border) !important;
        position: relative !important;
        overflow: hidden !important;
        transition: all 0.3s ease !important;
        cursor: pointer !important;
        height: auto !important;
        display: block !important;
        flex-direction: initial !important;
        justify-content: initial !important;
        align-items: initial !important;
    }

    .dashboard-container .stat-card:hover {
        transform: translateY(-2px) !important;
        box-shadow: var(--shadow-lg) !important;
    }

    /* Bordure colorée sur le côté gauche comme dans l'image - styles prioritaires */
    .dashboard-container .stat-card::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        bottom: 0 !important;
        width: 4px !important;
        border-radius: var(--radius-lg) 0 0 var(--radius-lg) !important;
    }

    .dashboard-container .stat-card.blue::before { background: var(--blue) !important; }
    .dashboard-container .stat-card.red::before { background: var(--red) !important; }
    .dashboard-container .stat-card.green::before { background: var(--green) !important; }
    .dashboard-container .stat-card.orange::before { background: var(--orange) !important; }
    .dashboard-container .stat-card.purple::before { background: var(--purple) !important; }
    .dashboard-container .stat-card.indigo::before { background: var(--indigo) !important; }

    /* Effet de lueur au survol */
    .stat-card::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
        border-radius: var(--radius-xl);
    }

    .stat-card:hover::after {
        opacity: 1;
    }

    .dashboard-container .stat-header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-bottom: 0 !important;
        position: relative !important;
        z-index: 2 !important;
    }

    .dashboard-container .stat-content {
        flex: 1 !important;
    }

    .dashboard-container .stat-title {
        font-size: 14px !important;
        font-weight: 500 !important;
        color: var(--text-secondary) !important;
        margin-bottom: 8px !important;
        line-height: 1.2 !important;
    }

    .dashboard-container .stat-value {
        font-size: 2rem !important;
        font-weight: 700 !important;
        line-height: 1 !important;
        margin-bottom: 4px !important;
        position: relative !important;
    }

    .dashboard-container .stat-value.blue { color: var(--blue) !important; }
    .dashboard-container .stat-value.red { color: var(--red) !important; }
    .dashboard-container .stat-value.green { color: var(--green) !important; }
    .dashboard-container .stat-value.orange { color: var(--orange) !important; }

    .dashboard-container .stat-change {
        font-size: 12px !important;
        font-weight: 500 !important;
        display: flex !important;
        align-items: center !important;
        gap: 4px !important;
        color: var(--text-muted) !important;
    }

    .dashboard-container .stat-change.positive {
        color: var(--green) !important;
    }

    .dashboard-container .stat-change.negative {
        color: var(--red) !important;
    }

    .dashboard-container .stat-icon {
        width: 48px !important;
        height: 48px !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 20px !important;
        color: white !important;
        flex-shrink: 0 !important;
        transition: all 0.3s ease !important;
    }

    .dashboard-container .stat-card:hover .stat-icon {
        transform: scale(1.05) !important;
    }

    .dashboard-container .stat-icon.blue { background: var(--blue) !important; }
    .dashboard-container .stat-icon.red { background: var(--red) !important; }
    .dashboard-container .stat-icon.green { background: var(--green) !important; }
    .dashboard-container .stat-icon.orange { background: var(--orange) !important; }

    /* Surcharge des animations conflictuelles */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Réinitialisation des animations pour nos cartes */
    .dashboard-container .stats-grid .stat-card {
        animation: fadeIn 0.6s ease-out forwards !important;
    }

    .dashboard-container .stats-grid .stat-card:nth-child(1) { animation-delay: 0.1s !important; }
    .dashboard-container .stats-grid .stat-card:nth-child(2) { animation-delay: 0.2s !important; }
    .dashboard-container .stats-grid .stat-card:nth-child(3) { animation-delay: 0.3s !important; }
    .dashboard-container .stats-grid .stat-card:nth-child(4) { animation-delay: 0.4s !important; }



    /* Grille des graphiques améliorée */
    .charts-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 32px;
        margin-bottom: 40px;
    }

    .chart-card {
        background: var(--bg-white);
        border-radius: var(--radius-xl);
        padding: 32px;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-light);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .chart-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
    }

    .chart-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--indigo-gradient);
        border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    }

    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 28px;
        padding-bottom: 20px;
        border-bottom: 2px solid var(--border-light);
        position: relative;
    }

    .chart-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: var(--text-primary);
        position: relative;
    }

    .chart-title::after {
        content: '';
        position: absolute;
        bottom: -22px;
        left: 0;
        width: 40px;
        height: 3px;
        background: var(--blue-gradient);
        border-radius: 2px;
    }

    .chart-menu {
        color: var(--text-muted);
        cursor: pointer;
        padding: 12px;
        border-radius: var(--radius);
        transition: all 0.3s ease;
        background: var(--bg-primary);
        border: 1px solid var(--border);
    }

    .chart-menu:hover {
        background: var(--blue);
        color: white;
        transform: scale(1.1);
        box-shadow: var(--shadow);
    }

    .chart-content {
        height: 320px;
        position: relative;
        padding: 16px;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 51, 234, 0.02) 100%);
        border-radius: var(--radius-lg);
        border: 1px solid rgba(59, 130, 246, 0.1);
    }

    /* Légende du graphique */
    .chart-legend {
        display: flex;
        gap: 24px;
        margin-bottom: 16px;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
    }

    .legend-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }

    .legend-dot.blue { background: var(--blue); }
    .legend-dot.orange { background: var(--orange); }

    /* Métriques en bas du graphique */
    .chart-metrics {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 24px;
        margin-top: 24px;
        padding-top: 24px;
        border-top: 1px solid var(--border);
    }

    .metric {
        text-align: center;
    }

    .metric-value {
        font-size: 24px;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 4px;
    }

    .metric-label {
        font-size: 12px;
        color: var(--text-secondary);
        margin-bottom: 4px;
    }

    .metric-change {
        font-size: 12px;
        font-weight: 500;
        color: var(--text-muted);
    }

    /* Graphique donut et liste des produits */
    .products-card {
        display: flex;
        flex-direction: column;
    }

    .donut-container {
        height: 200px;
        margin-bottom: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .products-list {
        flex: 1;
    }

    .product-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid var(--border);
    }

    .product-item:last-child {
        border-bottom: none;
    }

    .product-name {
        font-size: 14px;
        font-weight: 500;
        color: var(--text-primary);
    }

    .product-badge {
        background: var(--bg-primary);
        color: var(--text-primary);
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        min-width: 24px;
        text-align: center;
    }

    .product-badge.green {
        background: var(--green-gradient);
        color: white;
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    }
    .product-badge.red {
        background: var(--red-gradient);
        color: white;
        box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
    }
    .product-badge.blue {
        background: var(--blue-gradient);
        color: white;
        box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
    }
    .product-badge.orange {
        background: var(--orange-gradient);
        color: white;
        box-shadow: 0 4px 15px rgba(234, 88, 12, 0.3);
    }

    /* Cartes de liens rapides vers les rapports */
    .quick-reports-section {
        background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%);
        border-radius: var(--radius-xl);
        padding: 40px;
        margin-top: 40px;
        border: 1px solid var(--border-light);
        box-shadow: var(--shadow-md);
    }

    .reports-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 24px;
    }

    .report-link-card {
        background: var(--bg-white);
        border-radius: var(--radius-lg);
        padding: 28px;
        text-decoration: none;
        color: inherit;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: 1px solid var(--border-light);
        box-shadow: var(--shadow);
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .report-link-card:hover {
        transform: translateY(-8px) scale(1.03);
        box-shadow: var(--shadow-xl);
        text-decoration: none;
        color: inherit;
    }

    .report-link-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--purple-gradient);
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    }

    .report-link-card .report-icon {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        color: white;
        margin-bottom: 20px;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-md);
    }

    .report-link-card:hover .report-icon {
        transform: scale(1.15) rotate(10deg);
        box-shadow: var(--shadow-lg);
    }

    .report-link-card .report-icon.purple { background: var(--purple-gradient); }
    .report-link-card .report-icon.green { background: var(--green-gradient); }
    .report-link-card .report-icon.orange { background: var(--orange-gradient); }
    .report-link-card .report-icon.indigo { background: var(--indigo-gradient); }

    .report-link-card h3 {
        font-size: 1.2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 12px;
        line-height: 1.3;
    }

    .report-link-card p {
        font-size: 14px;
        color: var(--text-secondary);
        margin-bottom: 20px;
        line-height: 1.5;
    }

    .report-link-card .metric-badge {
        margin-top: auto;
        font-size: 11px;
        padding: 6px 14px;
    }

    /* Responsive amélioré */
    @media (max-width: 1024px) {
        .charts-grid {
            grid-template-columns: 1fr;
        }

        .dashboard-title {
            font-size: 2rem;
        }

        .quick-reports-section {
            padding: 24px;
        }
    }

    @media (max-width: 768px) {
        .dashboard-container {
            padding: 20px;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }

        .chart-metrics {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .dashboard-title {
            font-size: 1.8rem;
        }

        .stat-value {
            font-size: 2rem;
        }

        .reports-grid {
            grid-template-columns: 1fr;
        }

        .quick-reports-section {
            padding: 20px;
        }
    }

    @media (max-width: 480px) {
        .dashboard-container {
            padding: 16px;
        }

        .stat-card {
            padding: 20px;
        }

        .chart-card {
            padding: 20px;
        }

        .dashboard-title {
            font-size: 1.5rem;
        }
    }
</style>
@endpush

@section('content')
<div class="dashboard-container">
    <!-- En-tête du tableau de bord -->
    <div class="dashboard-header animate__animated animate__fadeInDown">
        <h1 class="dashboard-title">Tableau de Bord Financier</h1>
        <p class="dashboard-subtitle">Vue d'ensemble des performances et métriques clés</p>
    </div>

    <!-- Cartes de statistiques principales comme dans l'image -->
    <div class="stats-grid">
        <!-- Total des Ventes -->
        <div class="stat-card blue animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
            <div class="stat-header">
                <div class="stat-content">
                    <div class="stat-title">Total des Ventes</div>
                    <div class="stat-value blue" id="totalSalesValue">{{ number_format($totalSales) }}</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +{{ round(($totalSales / max($totalSales, 1)) * 100, 1) }}% ce mois
                    </div>
                </div>
                <div class="stat-icon blue">
                    <i class="fas fa-shopping-cart"></i>
                </div>
            </div>
        </div>

        <!-- Revenus Totaux -->
        <div class="stat-card red animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
            <div class="stat-header">
                <div class="stat-content">
                    <div class="stat-title">Revenus Totaux</div>
                    <div class="stat-value red" id="totalRevenueValue">{{ number_format($totalRevenue, 0, ',', ' ') }} FCFA</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +{{ $recoveryRate }}% recouvré
                    </div>
                </div>
                <div class="stat-icon red">
                    <i class="fas fa-coins"></i>
                </div>
            </div>
        </div>

        <!-- Taux de Recouvrement -->
        <div class="stat-card green animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
            <div class="stat-header">
                <div class="stat-content">
                    <div class="stat-title">Taux de Recouvrement</div>
                    <div class="stat-value green" id="recoveryRateValue">{{ $recoveryRate }}%</div>
                    <div class="stat-change {{ $recoveryRate >= 70 ? 'positive' : 'negative' }}">
                        <i class="fas fa-{{ $recoveryRate >= 70 ? 'arrow-up' : 'arrow-down' }}"></i>
                        {{ $recoveryRate >= 70 ? 'Excellent' : 'À améliorer' }}
                    </div>
                </div>
                <div class="stat-icon green">
                    <i class="fas fa-chart-pie"></i>
                </div>
            </div>
        </div>

        <!-- Total Clients -->
        <div class="stat-card orange animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
            <div class="stat-header">
                <div class="stat-content">
                    <div class="stat-title">Total Clients</div>
                    <div class="stat-value orange" id="totalCustomersValue">{{ number_format($totalCustomers) }}</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        {{ number_format($averageOrderValue, 0, ',', ' ') }} FCFA/commande
                    </div>
                </div>
                <div class="stat-icon orange">
                    <i class="fas fa-users"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques principaux avec design amélioré -->
    <div class="charts-grid">
        <!-- Aperçu des Ventes -->
        <div class="chart-card animate__animated animate__fadeInLeft" style="animation-delay: 0.5s;">
            <div class="chart-header">
                <h3 class="chart-title">
                    <i class="fas fa-chart-area" style="color: var(--blue); margin-right: 8px;"></i>
                    Aperçu des Ventes
                </h3>
                <div class="chart-menu">
                    <i class="fas fa-ellipsis-v"></i>
                </div>
            </div>

            <div class="chart-legend">
                <div class="legend-item">
                    <div class="legend-dot blue"></div>
                    <span>Ventes</span>
                    <div class="metric-badge info" style="margin-left: 8px; font-size: 10px;">
                        <i class="fas fa-arrow-up"></i>
                        +15%
                    </div>
                </div>
                <div class="legend-item">
                    <div class="legend-dot orange"></div>
                    <span>Visites</span>
                    <div class="metric-badge trending" style="margin-left: 8px; font-size: 10px;">
                        <i class="fas fa-eye"></i>
                        +8%
                    </div>
                </div>
            </div>

            <div class="chart-content">
                <canvas id="salesChart"></canvas>
            </div>

            <div class="chart-metrics">
                <div class="metric">
                    <div class="metric-value">{{ number_format($totalSales * 1000) }}</div>
                    <div class="metric-label">Visiteurs Total</div>
                    <div class="metric-change">
                        <span class="metric-badge growth" style="font-size: 10px;">↑ 2.43%</span>
                    </div>
                </div>
                <div class="metric">
                    <div class="metric-value">12:38</div>
                    <div class="metric-label">Durée Moyenne</div>
                    <div class="metric-change">
                        <span class="metric-badge info" style="font-size: 10px;">↑ 12.65%</span>
                    </div>
                </div>
                <div class="metric">
                    <div class="metric-value">{{ number_format($totalRevenue / 1000, 1) }}K</div>
                    <div class="metric-label">CA Moyen</div>
                    <div class="metric-change">
                        <span class="metric-badge trending" style="font-size: 10px;">↑ 5.62%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Produits Tendance -->
        <div class="chart-card products-card animate__animated animate__fadeInRight" style="animation-delay: 0.6s;">
            <div class="chart-header">
                <h3 class="chart-title">
                    <i class="fas fa-credit-card" style="color: var(--orange); margin-right: 8px;"></i>
                    Statuts de Paiement
                </h3>
                <div class="chart-menu">
                    <i class="fas fa-ellipsis-v"></i>
                </div>
            </div>

            <div class="donut-container">
                <canvas id="productsChart"></canvas>
            </div>

            <div class="products-list">
                <div class="product-item">
                    <span class="product-name">
                        <i class="fas fa-check-circle" style="color: var(--green); margin-right: 8px;"></i>
                        Factures Payées
                    </span>
                    <span class="product-badge green">
                        <i class="fas fa-check"></i>
                        {{ $paidInvoices }}
                    </span>
                </div>
                <div class="product-item">
                    <span class="product-name">
                        <i class="fas fa-clock" style="color: var(--orange); margin-right: 8px;"></i>
                        Paiements Partiels
                    </span>
                    <span class="product-badge orange">
                        <i class="fas fa-hourglass-half"></i>
                        {{ $partialInvoices }}
                    </span>
                </div>
                <div class="product-item">
                    <span class="product-name">
                        <i class="fas fa-exclamation-circle" style="color: var(--red); margin-right: 8px;"></i>
                        Factures Impayées
                    </span>
                    <span class="product-badge red">
                        <i class="fas fa-times"></i>
                        {{ $unpaidInvoices }}
                    </span>
                </div>
                <div class="product-item">
                    <span class="product-name">
                        <i class="fas fa-coins" style="color: var(--blue); margin-right: 8px;"></i>
                        En Attente
                    </span>
                    <span class="product-badge blue">
                        <i class="fas fa-hourglass"></i>
                        {{ number_format($pendingPayments, 0, ',', ' ') }} FCFA
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Section des liens rapides vers les rapports -->
    <div class="quick-reports-section" style="margin-top: 40px;">
        <h2 style="text-align: center; margin-bottom: 32px; font-size: 1.8rem; font-weight: 700; color: var(--text-primary);">
            <i class="fas fa-chart-pie" style="color: var(--purple); margin-right: 12px;"></i>
            Rapports Détaillés
        </h2>

        <div class="reports-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 24px;">
            <a href="{{ route('accountant.reports.sales') }}" class="report-link-card">
                <div class="report-icon purple">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3>Rapport des Ventes</h3>
                <p>Analyse détaillée des performances de vente</p>
                <div class="metric-badge info">
                    <i class="fas fa-arrow-right"></i>
                    Voir le rapport
                </div>
            </a>

            <a href="{{ route('accountant.reports.revenue') }}" class="report-link-card">
                <div class="report-icon green">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <h3>Rapport des Revenus</h3>
                <p>Suivi des revenus et de la rentabilité</p>
                <div class="metric-badge growth">
                    <i class="fas fa-arrow-right"></i>
                    Voir le rapport
                </div>
            </a>

            <a href="{{ route('accountant.reports.customers') }}" class="report-link-card">
                <div class="report-icon orange">
                    <i class="fas fa-users"></i>
                </div>
                <h3>Rapport Clients</h3>
                <p>Analyse du comportement client</p>
                <div class="metric-badge trending">
                    <i class="fas fa-arrow-right"></i>
                    Voir le rapport
                </div>
            </a>

            <a href="{{ route('accountant.reports.supplies') }}" class="report-link-card">
                <div class="report-icon indigo">
                    <i class="fas fa-boxes"></i>
                </div>
                <h3>Rapport Stocks</h3>
                <p>Gestion et suivi des approvisionnements</p>
                <div class="metric-badge alert">
                    <i class="fas fa-arrow-right"></i>
                    Voir le rapport
                </div>
            </a>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Configuration des données pour les graphiques avec vraies données
    const chartData = {
        totalSales: {{ $totalSales }},
        totalRevenue: {{ $totalRevenue }},
        totalPayments: {{ $totalPayments }},
        pendingPayments: {{ $pendingPayments }},
        recoveryRate: {{ $recoveryRate }},
        totalCustomers: {{ $totalCustomers }},
        averageOrderValue: {{ $averageOrderValue }},
        monthlyLabels: @json($monthlySales['labels']),
        monthlyData: @json($monthlySales['data']),
        monthlyCounts: @json($monthlySales['counts']),
        paymentStats: @json($paymentStats)
    };

    // Graphique des ventes avec vraies données en FCFA
    const salesCtx = document.getElementById('salesChart').getContext('2d');
    const salesChart = new Chart(salesCtx, {
        type: 'bar',
        data: {
            labels: chartData.monthlyLabels,
            datasets: [
                {
                    label: 'Revenus (FCFA)',
                    data: chartData.monthlyData,
                    backgroundColor: '#3b82f6',
                    borderRadius: 4,
                    maxBarThickness: 25,
                    categoryPercentage: 0.8,
                    barPercentage: 0.9
                },
                {
                    label: 'Nombre de ventes',
                    data: chartData.monthlyCounts,
                    backgroundColor: '#f59e0b',
                    borderRadius: 4,
                    maxBarThickness: 25,
                    categoryPercentage: 0.8,
                    barPercentage: 0.9,
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    cornerRadius: 6,
                    displayColors: true,
                    intersect: false,
                    mode: 'index',
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return 'Revenus: ' + new Intl.NumberFormat('fr-FR').format(context.parsed.y) + ' FCFA';
                            } else {
                                return 'Ventes: ' + context.parsed.y + ' commandes';
                            }
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    border: {
                        display: false
                    },
                    ticks: {
                        color: '#64748b',
                        font: {
                            size: 12
                        }
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    grid: {
                        color: '#f1f5f9',
                        borderDash: [2, 2]
                    },
                    border: {
                        display: false
                    },
                    ticks: {
                        color: '#64748b',
                        font: {
                            size: 12
                        },
                        callback: function(value) {
                            return new Intl.NumberFormat('fr-FR', {
                                notation: 'compact',
                                compactDisplay: 'short'
                            }).format(value) + ' FCFA';
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    beginAtZero: true,
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        color: '#64748b',
                        font: {
                            size: 12
                        },
                        callback: function(value) {
                            return value + ' ventes';
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });

    // Graphique donut des statuts de paiement
    const productsCtx = document.getElementById('productsChart').getContext('2d');
    const productsChart = new Chart(productsCtx, {
        type: 'doughnut',
        data: {
            labels: chartData.paymentStats.labels,
            datasets: [{
                data: chartData.paymentStats.data,
                backgroundColor: chartData.paymentStats.colors,
                borderWidth: 0,
                cutout: '70%'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });

    // Animation des compteurs
    function animateCounter(element, target, suffix = '') {
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }

            if (suffix === 'K') {
                element.textContent = (current / 1000).toFixed(1) + 'K';
            } else if (suffix === '%') {
                element.textContent = current.toFixed(1) + '%';
            } else if (suffix === '$') {
                element.textContent = '$' + Math.floor(current).toLocaleString();
            } else {
                element.textContent = Math.floor(current).toLocaleString();
            }
        }, 20);
    }

    // Initialisation au chargement de la page
    document.addEventListener('DOMContentLoaded', function() {
        // Animer les compteurs des cartes avec les vraies données
        const totalSalesCard = document.getElementById('totalSalesValue');
        const totalRevenueCard = document.getElementById('totalRevenueValue');
        const recoveryRateCard = document.getElementById('recoveryRateValue');
        const totalCustomersCard = document.getElementById('totalCustomersValue');

        if (totalSalesCard) {
            totalSalesCard.textContent = '0';
            animateCounter(totalSalesCard, chartData.totalSales);
        }

        if (totalRevenueCard) {
            totalRevenueCard.textContent = '0 FCFA';
            animateCounterFCFA(totalRevenueCard, chartData.totalRevenue);
        }

        if (recoveryRateCard) {
            recoveryRateCard.textContent = '0%';
            animateCounterPercent(recoveryRateCard, chartData.recoveryRate);
        }

        if (totalCustomersCard) {
            totalCustomersCard.textContent = '0';
            animateCounter(totalCustomersCard, chartData.totalCustomers);
        }
    });

    // Fonctions d'animation spécialisées
    function animateCounterCurrency(element, target) {
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = '$' + Math.floor(current).toLocaleString();
        }, 20);
    }

    function animateCounterPercent(element, target) {
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = current.toFixed(1) + '%';
        }, 20);
    }

    function animateCounterK(element, target) {
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = current.toFixed(1) + 'K';
        }, 20);
    }

    function animateCounterFCFA(element, target) {
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = new Intl.NumberFormat('fr-FR').format(Math.floor(current)) + ' FCFA';
        }, 20);
    }
</script>
@endpush
