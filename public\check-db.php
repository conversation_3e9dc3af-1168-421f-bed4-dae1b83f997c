<?php
// Connexion directe à la base de données
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "gradis";

try {
    $conn = new PDO("mysql:host=$servername;dbname=$dbname", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Connexion réussie à la base de données<br>";
    
    // Vérifier si la table supplies existe
    $stmt = $conn->query("SHOW TABLES LIKE 'supplies'");
    if ($stmt->rowCount() > 0) {
        echo "La table 'supplies' existe<br>";
        
        // Afficher la structure de la table
        echo "<h2>Structure de la table supplies</h2>";
        $stmt = $conn->query("DESCRIBE supplies");
        echo "<table border='1'><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            foreach ($row as $key => $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        
        // Compter les enregistrements
        $stmt = $conn->query("SELECT COUNT(*) as count FROM supplies");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<p>Nombre d'enregistrements dans la table supplies: $count</p>";
        
        if ($count > 0) {
            // Afficher quelques enregistrements
            echo "<h2>Exemples d'enregistrements</h2>";
            $stmt = $conn->query("SELECT id, reference, created_by, status, created_at FROM supplies LIMIT 10");
            echo "<table border='1'><tr><th>ID</th><th>Référence</th><th>Créé par</th><th>Statut</th><th>Date création</th></tr>";
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr>";
                foreach ($row as $key => $value) {
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
            
            // Compter par utilisateur
            echo "<h2>Nombre d'approvisionnements par utilisateur</h2>";
            $stmt = $conn->query("SELECT created_by, COUNT(*) as total FROM supplies GROUP BY created_by");
            echo "<table border='1'><tr><th>ID Utilisateur</th><th>Nombre d'approvisionnements</th></tr>";
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr><td>" . $row['created_by'] . "</td><td>" . $row['total'] . "</td></tr>";
            }
            echo "</table>";
        }
    } else {
        echo "La table 'supplies' n'existe pas<br>";
    }
    
    // Vérifier les utilisateurs comptables
    echo "<h2>Utilisateurs avec le rôle 'accountant'</h2>";
    $stmt = $conn->query("
        SELECT u.id, u.name, u.email 
        FROM users u
        JOIN model_has_roles mhr ON u.id = mhr.model_id
        JOIN roles r ON mhr.role_id = r.id
        WHERE r.name = 'accountant'
    ");
    
    if ($stmt->rowCount() > 0) {
        echo "<table border='1'><tr><th>ID</th><th>Nom</th><th>Email</th></tr>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            foreach ($row as $key => $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "Aucun utilisateur avec le rôle 'accountant' trouvé";
    }
    
} catch(PDOException $e) {
    echo "Erreur de connexion: " . $e->getMessage();
}
?>
