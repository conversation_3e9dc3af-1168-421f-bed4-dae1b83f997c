<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Supply;
use App\Models\Product;
use App\Models\City;
use App\Models\StockHistory;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class AccountantSupplyValidationTest extends TestCase
{
    use RefreshDatabase;

    protected $accountant;
    protected $product;
    protected $supply;
    protected $city;

    protected function setUp(): void
    {
        parent::setUp();

        // Créer un comptable
        $this->accountant = User::factory()->create([
            'role' => 'accountant'
        ]);

        // Créer un produit
        $this->product = Product::factory()->create([
            'name' => 'Ciment',
            'stock_quantity' => 100,
            'unit' => 'T'
        ]);

        // Créer une ville
        $this->city = City::factory()->create([
            'name' => 'Douala'
        ]);

        // Créer un approvisionnement
        $this->supply = Supply::factory()->create([
            'product_id' => $this->product->id,
            'status' => 'pending'
        ]);

        // Associer la ville à l'approvisionnement avec une quantité
        $this->supply->cities()->attach($this->city->id, [
            'quantity' => 50,
            'trips' => 2
        ]);
    }

    /** @test */
    public function an_accountant_can_validate_a_supply()
    {
        $this->actingAs($this->accountant);

        $response = $this->postJson("/accountant/supplies/{$this->supply->id}/validate");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'L\'approvisionnement a été validé avec succès.'
                ]);

        // Vérifier que le statut a été mis à jour
        $this->supply->refresh();
        $this->assertEquals('validated', $this->supply->status);
        $this->assertNotNull($this->supply->validated_at);
        $this->assertEquals($this->accountant->id, $this->supply->validator_id);

        // Vérifier que le stock a été mis à jour
        $this->product->refresh();
        $this->assertEquals(150, $this->product->stock_quantity);

        // Vérifier que l'historique a été créé
        $history = StockHistory::where('supply_id', $this->supply->id)->first();
        $this->assertNotNull($history);
        $this->assertEquals(50, $history->quantity);
        $this->assertEquals(100, $history->previous_stock);
        $this->assertEquals(150, $history->new_stock);
    }

    /** @test */
    public function it_cannot_validate_a_supply_without_product()
    {
        $this->actingAs($this->accountant);

        $supplyWithoutProduct = Supply::factory()->create([
            'product_id' => null,
            'status' => 'pending'
        ]);

        $response = $this->postJson("/accountant/supplies/{$supplyWithoutProduct->id}/validate");

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Impossible de valider un approvisionnement sans produit associé.'
                ]);
    }

    /** @test */
    public function it_cannot_validate_an_already_validated_supply()
    {
        $this->actingAs($this->accountant);

        $validatedSupply = Supply::factory()->create([
            'product_id' => $this->product->id,
            'status' => 'validated',
            'validator_id' => $this->accountant->id,
            'validated_at' => now()
        ]);

        $response = $this->postJson("/accountant/supplies/{$validatedSupply->id}/validate");

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Cet approvisionnement a déjà été validé.'
                ]);
    }

    /** @test */
    public function an_accountant_can_reject_a_supply()
    {
        $this->actingAs($this->accountant);

        $response = $this->postJson("/accountant/supplies/{$this->supply->id}/reject", [
            'rejection_reason' => 'Quantité incorrecte'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'L\'approvisionnement a été rejeté avec succès.'
                ]);

        // Vérifier que le statut a été mis à jour
        $this->supply->refresh();
        $this->assertEquals('rejected', $this->supply->status);
        $this->assertNotNull($this->supply->validated_at);
        $this->assertEquals($this->accountant->id, $this->supply->validator_id);
        $this->assertEquals('Quantité incorrecte', $this->supply->rejection_reason);
    }

    /** @test */
    public function it_cannot_reject_a_supply_without_reason()
    {
        $this->actingAs($this->accountant);

        $response = $this->postJson("/accountant/supplies/{$this->supply->id}/reject", [
            'rejection_reason' => ''
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['rejection_reason']);
    }

    /** @test */
    public function it_cannot_reject_an_already_rejected_supply()
    {
        $this->actingAs($this->accountant);

        $rejectedSupply = Supply::factory()->create([
            'product_id' => $this->product->id,
            'status' => 'rejected',
            'validator_id' => $this->accountant->id,
            'validated_at' => now(),
            'rejection_reason' => 'Déjà rejeté'
        ]);

        $response = $this->postJson("/accountant/supplies/{$rejectedSupply->id}/reject", [
            'rejection_reason' => 'Nouvelle raison'
        ]);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Cet approvisionnement a déjà été rejeté.'
                ]);
    }
}
