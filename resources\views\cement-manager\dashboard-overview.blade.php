@extends('layouts.cement_manager')

@section('title', 'Tableau de bord - Gestionnaire Ciment')

@section('content')
<div class="container-fluid">
    <!-- En-tête du tableau de bord avec date et heure actuelles -->
    <div class="dashboard-header mb-4">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="dashboard-title">Tableau de bord <span class="text-primary">Gestionnaire Ciment</span></h1>
                <p class="dashboard-subtitle">{{ \Carbon\Carbon::now()->locale('fr')->isoFormat('dddd D MMMM YYYY') }} | <span id="currentTime"></span></p>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="d-flex justify-content-md-end">
                    <button class="btn btn-primary rounded-pill" id="refreshDashboard">
                        <i class="fas fa-sync-alt me-2"></i> Actualiser
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques principales -->
    <div class="row mb-4 gx-3 gy-3">
        <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 mb-3 mb-xl-0">
            <div class="stat-card stat-primary">
                <div class="stat-card-icon-container">
                    <div class="stat-card-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
                <div class="stat-card-content">
                    <div class="stat-card-info">
                        <div class="stat-card-title">Total des ventes</div>
                        <div class="stat-card-value">{{ number_format($dashboardStats['total_sales'], 0, ',', ' ') }}</div>
                    </div>
                    <div class="stat-card-details">
                        <div class="stat-detail">
                            <span class="stat-label">Complétées</span>
                            <span class="stat-value">{{ number_format($dashboardStats['sales_completed'], 0, ',', ' ') }}</span>
                        </div>
                        <div class="stat-detail">
                            <span class="stat-label">En attente</span>
                            <span class="stat-value">{{ number_format($dashboardStats['sales_pending'], 0, ',', ' ') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-12 mb-3 mb-lg-0">
            <div class="stat-card stat-success">
                <div class="stat-card-icon-container">
                    <div class="stat-card-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
                <div class="stat-card-content">
                    <div class="stat-card-info">
                        <div class="stat-card-title">Chiffre d'affaires</div>
                        <div class="stat-card-value">{{ number_format($dashboardStats['total_revenue'], 0, ',', ' ') }} <small>FCFA</small></div>
                    </div>
                    <div class="stat-card-details">
                        <div class="stat-detail full-width">
                            <span class="stat-label">Progression mensuelle</span>
                            <div class="stat-progress">
                                <div class="stat-progress-bar" style="width: 75%"></div>
                            </div>
                            <div class="stat-progress-info">
                                <span>Mois précédent</span>
                                <span>+15%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-12 mb-3 mb-lg-0">
            <div class="stat-card stat-info">
                <div class="stat-card-icon-container">
                    <div class="stat-card-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                </div>
                <div class="stat-card-content">
                    <div class="stat-card-info">
                        <div class="stat-card-title">Livraisons</div>
                        <div class="stat-card-value">{{ number_format($dashboardStats['deliveries_pending'] + $dashboardStats['deliveries_in_progress'] + $dashboardStats['deliveries_completed'], 0, ',', ' ') }}</div>
                    </div>
                    <div class="stat-card-details">
                        <div class="stat-detail">
                            <span class="stat-label">Livrées</span>
                            <span class="stat-value">{{ number_format($dashboardStats['deliveries_completed'], 0, ',', ' ') }}</span>
                        </div>
                        <div class="stat-detail">
                            <span class="stat-label">En cours</span>
                            <span class="stat-value">{{ number_format($dashboardStats['deliveries_in_progress'], 0, ',', ' ') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-12 mb-3 mb-lg-0">
            <div class="stat-card stat-secondary">
                <div class="stat-card-icon-container">
                    <div class="stat-card-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                </div>
                <div class="stat-card-content">
                    <div class="stat-card-info">
                        <div class="stat-card-title">Approvisionnements</div>
                        <div class="stat-card-value">{{ number_format($dashboardStats['total_supplies'], 0, ',', ' ') }}</div>
                    </div>
                    <div class="stat-card-details">
                        <div class="stat-detail full-width">
                            <span class="stat-label">Montant total</span>
                            <span class="stat-value">{{ number_format($dashboardStats['total_supply_amount'], 0, ',', ' ') }} FCFA</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques des ressources -->
    <div class="row mb-4 gx-3 gy-3">
        <div class="col-lg-6 col-md-12 mb-4 mb-lg-0">
            <div class="resource-card">
                <div class="resource-card-header">
                    <h5 class="resource-card-title"><i class="fas fa-truck-moving me-2"></i> État des Camions</h5>
                    <a href="#" class="btn btn-sm btn-outline-primary rounded-pill">
                        <i class="fas fa-external-link-alt me-1"></i> Gérer
                    </a>
                </div>
                <div class="resource-card-body">
                    <div class="row">
                        <div class="col-lg-6 col-md-6 col-sm-12 mb-3 mb-md-0">
                            <div class="chart-container">
                                <canvas id="truckStatusChart"></canvas>
                                <div class="chart-center-text">
                                    <span class="chart-center-value">{{ $dashboardStats['total_trucks'] }}</span>
                                    <span class="chart-center-label">Total</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-12">
                            <div class="resource-stats">
                                <div class="resource-stat-item">
                                    <div class="resource-stat-info">
                                        <div class="resource-stat-icon" style="background-color: #28a745;">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="resource-stat-label">Disponibles</div>
                                    </div>
                                    <div class="resource-stat-value">{{ $dashboardStats['available_trucks'] }}</div>
                                </div>
                                <div class="resource-stat-item">
                                    <div class="resource-stat-info">
                                        <div class="resource-stat-icon" style="background-color: #ffc107;">
                                            <i class="fas fa-route"></i>
                                        </div>
                                        <div class="resource-stat-label">En service</div>
                                    </div>
                                    <div class="resource-stat-value">{{ $dashboardStats['busy_trucks'] }}</div>
                                </div>
                                <div class="resource-stat-item">
                                    <div class="resource-stat-info">
                                        <div class="resource-stat-icon" style="background-color: #dc3545;">
                                            <i class="fas fa-tools"></i>
                                        </div>
                                        <div class="resource-stat-label">En maintenance</div>
                                    </div>
                                    <div class="resource-stat-value">{{ $dashboardStats['maintenance_trucks'] }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-md-12">
            <div class="resource-card">
                <div class="resource-card-header">
                    <h5 class="resource-card-title"><i class="fas fa-user-hard-hat me-2"></i> Chauffeurs</h5>
                    <a href="#" class="btn btn-sm btn-outline-primary rounded-pill">
                        <i class="fas fa-external-link-alt me-1"></i> Gérer
                    </a>
                </div>
                <div class="resource-card-body">
                    <div class="row">
                        <div class="col-lg-6 col-md-6 col-sm-12 mb-3 mb-md-0">
                            <div class="chart-container">
                                <canvas id="driverStatusChart"></canvas>
                                <div class="chart-center-text">
                                    <span class="chart-center-value">{{ $dashboardStats['total_drivers'] }}</span>
                                    <span class="chart-center-label">Total</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-12">
                            <div class="resource-stats">
                                <div class="resource-stat-item">
                                    <div class="resource-stat-info">
                                        <div class="resource-stat-icon" style="background-color: #28a745;">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="resource-stat-label">Disponibles</div>
                                    </div>
                                    <div class="resource-stat-value">{{ $dashboardStats['available_drivers'] }}</div>
                                </div>
                                <div class="resource-stat-item">
                                    <div class="resource-stat-info">
                                        <div class="resource-stat-icon" style="background-color: #ffc107;">
                                            <i class="fas fa-route"></i>
                                        </div>
                                        <div class="resource-stat-label">En service</div>
                                    </div>
                                    <div class="resource-stat-value">{{ $dashboardStats['total_drivers'] - $dashboardStats['available_drivers'] }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphique des ventes et Actions rapides -->
    <div class="row mb-4 gx-3 gy-3">
        <!-- Graphique des ventes -->
        <div class="col-lg-8 col-md-12 mb-4 mb-lg-0">
            <div class="chart-card">
                <div class="chart-card-header">
                    <h5 class="chart-card-title"><i class="fas fa-chart-line me-2"></i> Évolution des ventes {{ date('Y') }}</h5>
                    <div class="chart-card-actions d-none d-md-block">
                        <div class="btn-group chart-period-selector">
                            <button type="button" class="btn btn-sm btn-outline-primary active">Année</button>
                            <button type="button" class="btn btn-sm btn-outline-primary">Trimestre</button>
                            <button type="button" class="btn btn-sm btn-outline-primary">Mois</button>
                        </div>
                    </div>
                    <!-- Sélecteur de période pour mobile -->
                    <div class="chart-period-mobile d-md-none">
                        <select class="form-select form-select-sm">
                            <option value="year" selected>Année</option>
                            <option value="quarter">Trimestre</option>
                            <option value="month">Mois</option>
                        </select>
                    </div>
                </div>
                <div class="chart-card-body">
                    <div class="chart-container-responsive">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
                <div class="chart-card-footer">
                    <div class="chart-legend">
                        <div class="chart-legend-item">
                            <span class="chart-legend-indicator" style="background-color: rgba(54, 162, 235, 0.5);"></span>
                            <span class="chart-legend-text">Chiffre d'affaires</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Actions rapides -->
        <div class="col-lg-4 col-md-12">
            <div class="quick-actions-card">
                <div class="quick-actions-header">
                    <h5 class="quick-actions-title"><i class="fas fa-bolt me-2"></i> Actions rapides</h5>
                </div>
                <div class="quick-actions-body">
                    <!-- Version desktop des actions rapides -->
                    <div class="d-none d-sm-block">
                        <a href="{{ route('cement-manager.sales.create') }}" class="quick-action-item quick-action-primary">
                            <div class="quick-action-icon">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <div class="quick-action-content">
                                <h6 class="quick-action-title">Nouvelle vente</h6>
                                <p class="quick-action-description">Créer une nouvelle vente de ciment</p>
                            </div>
                            <div class="quick-action-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a>
                        <a href="{{ route('cement-manager.supplies.index') }}" class="quick-action-item quick-action-info">
                            <div class="quick-action-icon">
                                <i class="fas fa-truck-loading"></i>
                            </div>
                            <div class="quick-action-content">
                                <h6 class="quick-action-title">Bons de commande</h6>
                                <p class="quick-action-description">Gérer les approvisionnements</p>
                            </div>
                            <div class="quick-action-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a>
                        <a href="{{ route('cement-manager.sales.index') }}" class="quick-action-item quick-action-success">
                            <div class="quick-action-icon">
                                <i class="fas fa-history"></i>
                            </div>
                            <div class="quick-action-content">
                                <h6 class="quick-action-title">Historique des ventes</h6>
                                <p class="quick-action-description">Consulter toutes les ventes</p>
                            </div>
                            <div class="quick-action-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a>
                    </div>
                    
                    <!-- Version mobile des actions rapides (boutons compacts) -->
                    <div class="d-sm-none">
                        <div class="row g-2">
                            <div class="col-4">
                                <a href="{{ route('cement-manager.sales.create') }}" class="quick-action-mobile quick-action-mobile-primary">
                                    <div class="quick-action-mobile-icon">
                                        <i class="fas fa-plus-circle"></i>
                                    </div>
                                    <div class="quick-action-mobile-title">Nouvelle vente</div>
                                </a>
                            </div>
                            <div class="col-4">
                                <a href="{{ route('cement-manager.supplies.index') }}" class="quick-action-mobile quick-action-mobile-info">
                                    <div class="quick-action-mobile-icon">
                                        <i class="fas fa-truck-loading"></i>
                                    </div>
                                    <div class="quick-action-mobile-title">Commandes</div>
                                </a>
                            </div>
                            <div class="col-4">
                                <a href="{{ route('cement-manager.sales.index') }}" class="quick-action-mobile quick-action-mobile-success">
                                    <div class="quick-action-mobile-icon">
                                        <i class="fas fa-history"></i>
                                    </div>
                                    <div class="quick-action-mobile-title">Historique</div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dernières ventes -->
    <div class="recent-sales-card">
        <div class="recent-sales-header">
            <h5 class="recent-sales-title"><i class="fas fa-receipt me-2"></i> Dernières ventes</h5>
            <a href="{{ route('cement-manager.sales.index') }}" class="btn btn-sm btn-outline-primary rounded-pill">
                <i class="fas fa-external-link-alt me-1"></i> Voir toutes
            </a>
        </div>
        <div class="recent-sales-body">
            @if(count($dashboardStats['recent_sales']) > 0)
                <!-- Version desktop et tablette du tableau -->
                <div class="table-responsive d-none d-md-block">
                    <table class="table custom-table">
                        <thead>
                            <tr>
                                <th>Référence</th>
                                <th>Date</th>
                                <th>Client</th>
                                <th class="d-none d-lg-table-cell">Ville</th>
                                <th>Montant</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($dashboardStats['recent_sales'] as $sale)
                                <tr>
                                    <td>
                                        <span class="sale-reference">{{ $sale->reference ?? 'N/A' }}</span>
                                    </td>
                                    <td>
                                        <div class="sale-date">
                                            <div class="sale-date-day">{{ \Carbon\Carbon::parse($sale->created_at)->format('d/m/Y') }}</div>
                                            <div class="sale-date-time">{{ \Carbon\Carbon::parse($sale->created_at)->format('H:i') }}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="sale-customer">{{ $sale->customer_name }}</div>
                                    </td>
                                    <td class="d-none d-lg-table-cell">
                                        <span class="sale-city">{{ $sale->city->name ?? 'N/A' }}</span>
                                    </td>
                                    <td>
                                        <span class="sale-amount">{{ number_format($sale->total_amount, 0, ',', ' ') }} <small>FCFA</small></span>
                                    </td>
                                    <td>
                                        <div class="sale-status-container">
                                            <div class="sale-status-item">
                                                @if($sale->payment_status == 'paid')
                                                    <span class="sale-status-badge sale-status-success" data-bs-toggle="tooltip" title="Paiement">
                                                        <i class="fas fa-money-bill-wave"></i>
                                                    </span>
                                                @elseif($sale->payment_status == 'partial')
                                                    <span class="sale-status-badge sale-status-warning" data-bs-toggle="tooltip" title="Paiement partiel">
                                                        <i class="fas fa-money-bill-wave"></i>
                                                    </span>
                                                @else
                                                    <span class="sale-status-badge sale-status-danger" data-bs-toggle="tooltip" title="Non payé">
                                                        <i class="fas fa-money-bill-wave"></i>
                                                    </span>
                                                @endif
                                            </div>
                                            <div class="sale-status-item">
                                                @if($sale->delivery_status == 'completed')
                                                    <span class="sale-status-badge sale-status-success" data-bs-toggle="tooltip" title="Livrée">
                                                        <i class="fas fa-truck"></i>
                                                    </span>
                                                @elseif($sale->delivery_status == 'in_progress')
                                                    <span class="sale-status-badge sale-status-warning" data-bs-toggle="tooltip" title="Livraison en cours">
                                                        <i class="fas fa-truck"></i>
                                                    </span>
                                                @else
                                                    <span class="sale-status-badge sale-status-secondary" data-bs-toggle="tooltip" title="Livraison en attente">
                                                        <i class="fas fa-truck"></i>
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <a href="{{ route('cement-manager.sales.show', $sale->id) }}" class="btn btn-sm btn-primary rounded-circle" data-bs-toggle="tooltip" title="Voir les détails">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Version mobile du tableau (affichée sous forme de cartes) -->
                <div class="d-md-none">
                    <div class="mobile-sales-list">
                        @foreach($dashboardStats['recent_sales'] as $sale)
                            <div class="mobile-sale-card">
                                <div class="mobile-sale-header">
                                    <div class="mobile-sale-ref">{{ $sale->reference ?? 'N/A' }}</div>
                                    <div class="mobile-sale-date">{{ \Carbon\Carbon::parse($sale->created_at)->format('d/m/Y') }}</div>
                                </div>
                                <div class="mobile-sale-body">
                                    <div class="mobile-sale-info">
                                        <div class="mobile-sale-customer">
                                            <span class="mobile-sale-label">Client:</span>
                                            <span class="mobile-sale-value">{{ $sale->customer_name }}</span>
                                        </div>
                                        <div class="mobile-sale-amount">
                                            <span class="mobile-sale-label">Montant:</span>
                                            <span class="mobile-sale-value">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</span>
                                        </div>
                                    </div>
                                    <div class="mobile-sale-status">
                                        <div class="mobile-status-item">
                                            <span class="mobile-status-label">Paiement:</span>
                                            @if($sale->payment_status == 'paid')
                                                <span class="mobile-status-badge mobile-status-success">Payé</span>
                                            @elseif($sale->payment_status == 'partial')
                                                <span class="mobile-status-badge mobile-status-warning">Partiel</span>
                                            @else
                                                <span class="mobile-status-badge mobile-status-danger">Non payé</span>
                                            @endif
                                        </div>
                                        <div class="mobile-status-item">
                                            <span class="mobile-status-label">Livraison:</span>
                                            @if($sale->delivery_status == 'completed')
                                                <span class="mobile-status-badge mobile-status-success">Livrée</span>
                                            @elseif($sale->delivery_status == 'in_progress')
                                                <span class="mobile-status-badge mobile-status-warning">En cours</span>
                                            @else
                                                <span class="mobile-status-badge mobile-status-secondary">En attente</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="mobile-sale-footer">
                                    <a href="{{ route('cement-manager.sales.show', $sale->id) }}" class="btn btn-sm btn-primary rounded-pill w-100">
                                        <i class="fas fa-eye me-2"></i> Voir les détails
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @else
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <h6 class="empty-state-title">Aucune vente récente</h6>
                    <p class="empty-state-description">Les dernières ventes apparaîtront ici</p>
                    <a href="{{ route('cement-manager.sales.create') }}" class="btn btn-primary rounded-pill">
                        <i class="fas fa-plus-circle me-2"></i> Créer une vente
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
    /* Styles responsifs */
    @media (max-width: 1199.98px) {
        .dashboard-title {
            font-size: 1.5rem;
        }
        
        .dashboard-subtitle {
            font-size: 0.875rem;
        }
        
        .stat-card-value {
            font-size: 1.25rem;
        }
        
        .chart-card-title, .resource-card-title, .recent-sales-title, .quick-actions-title {
            font-size: 1rem;
        }
        
        .chart-legend-text {
            font-size: 0.75rem;
        }
    }
    
    /* Style pour le mode paysage sur mobile */
    body.landscape-mode .stat-card {
        margin-bottom: 0.5rem;
    }
    
    body.landscape-mode .stat-card-value {
        font-size: 1rem;
    }
    
    body.landscape-mode .stat-detail {
        font-size: 0.7rem;
    }
    
    body.landscape-mode .chart-container {
        height: 120px;
    }
    
    @media (max-width: 991.98px) {
        .dashboard-header {
            padding: 1rem;
        }
        
        .stat-card {
            margin-bottom: 1rem;
        }
        
        .stat-card-icon {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }
        
        .stat-card-icon-container {
            width: 60px;
        }
        
        .chart-card-header, .resource-card-header, .recent-sales-header, .quick-actions-header {
            padding: 1rem;
        }
        
        .chart-card, .resource-card, .recent-sales-card, .quick-actions-card {
            padding: 1rem;
        }
        
        .chart-card-footer {
            padding: 0.75rem 1rem;
        }
        
        .quick-action-item {
            padding: 0.75rem;
        }
        
        .quick-action-icon {
            width: 35px;
            height: 35px;
            font-size: 1rem;
        }
    }
    
    @media (max-width: 767.98px) {
        .dashboard-header .row {
            flex-direction: column;
        }
        
        .dashboard-header .col-md-6 {
            width: 100%;
            text-align: center !important;
        }
        
        .dashboard-header .d-flex.justify-content-md-end {
            justify-content: center !important;
            margin-top: 1rem;
        }
        
        .dashboard-title {
            margin-top: 0.5rem;
        }
        
        .stat-card-details {
            margin-top: 0.5rem;
        }
        
        .chart-container {
            height: 180px;
        }
        
        .resource-card, .chart-card, .quick-actions-card {
            margin-bottom: 1rem;
        }
        
        .chart-period-selector {
            display: none;
        }
        
        .custom-table th, .custom-table td {
            padding: 0.75rem 0.5rem;
            font-size: 0.875rem;
        }
        
        .sale-status-badge {
            width: 25px;
            height: 25px;
        }
        
        /* Optimisation pour les tablettes en mode portrait */
        .quick-action-item {
            flex: 0 0 calc(50% - 0.5rem);
            max-width: calc(50% - 0.5rem);
        }
    }
    
    /* Styles pour la version mobile des dernières ventes */
    @media (max-width: 767.98px) {
        .recent-sales-card {
            margin-left: -5px;
            margin-right: -5px;
            border-radius: 10px;
        }
    }
    .mobile-sales-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .mobile-sale-card {
        margin-bottom: 10px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .mobile-sale-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .mobile-sale-header {
        padding: 0.75rem 1rem;
        background-color: #f8fafc;
        border-bottom: 1px solid #f1f5f9;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .mobile-sale-ref {
        font-weight: 600;
        color: #333;
        font-size: 0.875rem;
    }
    
    .mobile-sale-date {
        color: #6c757d;
        font-size: 0.75rem;
    }
    
    .mobile-sale-body {
        padding: 1rem;
    }
    
    .mobile-sale-info {
        margin-bottom: 0.75rem;
    }
    
    .mobile-sale-customer, .mobile-sale-amount {
        margin-bottom: 0.5rem;
        display: flex;
        justify-content: space-between;
    }
    
    .mobile-sale-label {
        color: #6c757d;
        font-size: 0.75rem;
    }
    
    .mobile-sale-value {
        font-weight: 600;
        color: #333;
        font-size: 0.875rem;
    }
    
    .mobile-sale-status {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .mobile-status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .mobile-status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 50px;
        font-weight: 500;
    }
    
    .mobile-status-success {
        background-color: rgba(22, 163, 74, 0.1);
        color: #16a34a;
    }
    
    .mobile-status-warning {
        background-color: rgba(234, 179, 8, 0.1);
        color: #eab308;
    }
    
    .mobile-status-danger {
        background-color: rgba(220, 38, 38, 0.1);
        color: #dc2626;
    }
    
    .mobile-status-secondary {
        background-color: rgba(71, 85, 105, 0.1);
        color: #475569;
    }
    
    .mobile-sale-footer {
        padding: 0.75rem 1rem;
        border-top: 1px solid #f1f5f9;
    }
    
    @media (max-width: 575.98px) {
        .container-fluid {
            padding-left: 10px;
            padding-right: 10px;
        }
        .dashboard-title {
            font-size: 1.25rem;
        }
        
        .dashboard-subtitle {
            font-size: 0.75rem;
        }
        
        .stat-card-value {
            font-size: 1.125rem;
        }
        
        .stat-detail {
            font-size: 0.75rem;
        }
        
        .chart-container {
            height: 150px;
        }
        
        .resource-stat-icon {
            width: 25px;
            height: 25px;
            font-size: 0.75rem;
            margin-right: 0.5rem;
        }
        
        .resource-stat-label, .resource-stat-value {
            font-size: 0.75rem;
        }
        
        .quick-action-title {
            font-size: 0.875rem;
        }
        
        .quick-action-description {
            font-size: 0.75rem;
        }
        
        .sale-reference, .sale-date-day, .sale-amount {
            font-size: 0.75rem;
        }
        
        .empty-state {
            padding: 2rem 1rem;
        }
        
        .empty-state-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .empty-state-title {
            font-size: 1rem;
        }
        
        .empty-state-description {
            font-size: 0.75rem;
        }
        
        /* Ajustements spécifiques pour les très petits écrans */
        .mobile-sale-header, .mobile-sale-body, .mobile-sale-footer {
            padding: 0.75rem;
        }
        
        .mobile-sale-ref, .mobile-sale-value {
            font-size: 0.75rem;
        }
        
        .mobile-sale-date, .mobile-sale-label {
            font-size: 0.7rem;
        }
        
        .mobile-status-badge {
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
        }
    }
    
    /* Styles généraux */
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .dashboard-header {
        background-color: #fff;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        margin-bottom: 1.5rem;
    }
    
    .dashboard-title {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
        color: #333;
    }
    
    .dashboard-subtitle {
        color: #6c757d;
        font-size: 1rem;
        margin-bottom: 0;
    }
    
    /* Cartes de statistiques */
    .stat-card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        background-color: #fff;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
        display: flex;
        position: relative;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .stat-card-icon-container {
        width: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .stat-primary .stat-card-icon-container {
        background-color: rgba(37, 99, 235, 0.1);
    }
    
    .stat-success .stat-card-icon-container {
        background-color: rgba(22, 163, 74, 0.1);
    }
    
    .stat-info .stat-card-icon-container {
        background-color: rgba(6, 182, 212, 0.1);
    }
    
    .stat-secondary .stat-card-icon-container {
        background-color: rgba(71, 85, 105, 0.1);
    }
    
    .stat-card-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
    }
    
    .stat-primary .stat-card-icon {
        background-color: #2563eb;
        color: white;
    }
    
    .stat-success .stat-card-icon {
        background-color: #16a34a;
        color: white;
    }
    
    .stat-info .stat-card-icon {
        background-color: #06b6d4;
        color: white;
    }
    
    .stat-secondary .stat-card-icon {
        background-color: #475569;
        color: white;
    }
    
    .stat-card-content {
        flex: 1;
        padding: 1.25rem;
        display: flex;
        flex-direction: column;
    }
    
    .stat-card-info {
        margin-bottom: 1rem;
    }
    
    .stat-card-title {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }
    
    .stat-card-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 0;
    }
    
    .stat-card-value small {
        font-size: 0.875rem;
        font-weight: 400;
        color: #6c757d;
    }
    
    .stat-card-details {
        margin-top: auto;
    }
    
    .stat-detail {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }
    
    .stat-detail:last-child {
        margin-bottom: 0;
    }
    
    .stat-detail.full-width {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .stat-detail.full-width .stat-value {
        margin-top: 0.25rem;
        font-weight: 600;
    }
    
    .stat-label {
        color: #6c757d;
    }
    
    .stat-value {
        font-weight: 600;
        color: #333;
    }
    
    .stat-progress {
        height: 6px;
        background-color: #e9ecef;
        border-radius: 3px;
        margin-bottom: 0.5rem;
        overflow: hidden;
    }
    
    .stat-progress-bar {
        height: 100%;
        background-color: #2563eb;
    }
    
    .stat-success .stat-progress-bar {
        background-color: #16a34a;
    }
    
    .stat-progress-info {
        display: flex;
        justify-content: space-between;
        font-size: 0.75rem;
        color: #6c757d;
    }
    
    /* Cartes de ressources */
    .resource-card {
        background-color: #fff;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }
    
    .resource-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .resource-card-header {
        padding: 1.25rem;
        border-bottom: 1px solid #f1f5f9;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .resource-card-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0;
    }
    
    .resource-card-body {
        padding: 1.25rem;
    }
    
    .chart-container {
        position: relative;
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .chart-center-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
    }
    
    .chart-center-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #333;
        display: block;
    }
    
    .chart-center-label {
        font-size: 0.75rem;
        color: #6c757d;
    }
    
    .resource-stats {
        padding: 0.5rem 0;
    }
    
    .resource-stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f1f5f9;
    }
    
    .resource-stat-item:last-child {
        border-bottom: none;
    }
    
    .resource-stat-info {
        display: flex;
        align-items: center;
    }
    
    .resource-stat-icon {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
        color: white;
        font-size: 0.875rem;
    }
    
    .resource-stat-label {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .resource-stat-value {
        font-size: 0.875rem;
        font-weight: 600;
        color: #333;
    }
    
    /* Carte de graphique */
    .chart-card {
        background-color: #fff;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }
    
    .chart-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .chart-card-header {
        padding: 1.25rem;
        border-bottom: 1px solid #f1f5f9;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .chart-card-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0;
    }
    
    .chart-card-body {
        padding: 1.25rem;
    }
    
    .chart-card-footer {
        padding: 1rem 1.25rem;
        border-top: 1px solid #f1f5f9;
    }
    
    .chart-legend {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .chart-legend-item {
        display: flex;
        align-items: center;
    }
    
    .chart-legend-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 0.5rem;
    }
    
    .chart-legend-text {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .chart-period-selector .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    /* Carte d'actions rapides */
    .quick-actions-card {
        background-color: #fff;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }
    
    .quick-actions-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .quick-actions-header {
        padding: 1.25rem;
        border-bottom: 1px solid #f1f5f9;
    }
    
    .quick-actions-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0;
    }
    
    .quick-actions-body {
        padding: 1rem;
    }
    
    .quick-action-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 0.75rem;
        text-decoration: none;
        transition: all 0.3s ease;
        background-color: #f8fafc;
    }
    
    .quick-action-item:last-child {
        margin-bottom: 0;
    }
    
    .quick-action-primary:hover {
        background-color: rgba(37, 99, 235, 0.1);
    }
    
    .quick-action-info:hover {
        background-color: rgba(6, 182, 212, 0.1);
    }
    
    .quick-action-success:hover {
        background-color: rgba(22, 163, 74, 0.1);
    }
    
    .quick-action-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.25rem;
    }
    
    .quick-action-primary .quick-action-icon {
        background-color: #2563eb;
        color: white;
    }
    
    .quick-action-info .quick-action-icon {
        background-color: #06b6d4;
        color: white;
    }
    
    .quick-action-success .quick-action-icon {
        background-color: #16a34a;
        color: white;
    }
    
    .quick-action-content {
        flex: 1;
    }
    
    .quick-action-title {
        font-size: 1rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.25rem;
    }
    
    .quick-action-description {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 0;
    }
    
    .quick-action-arrow {
        color: #6c757d;
        margin-left: 0.5rem;
        transition: transform 0.3s ease;
    }
    
    .quick-action-item:hover .quick-action-arrow {
        transform: translateX(5px);
    }
    
    /* Carte des dernières ventes */
    .recent-sales-card {
        background-color: #fff;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        margin-bottom: 1.5rem;
    }
    
    .recent-sales-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .recent-sales-header {
        padding: 1.25rem;
        border-bottom: 1px solid #f1f5f9;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .recent-sales-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0;
    }
    
    .recent-sales-body {
        padding: 1.25rem;
    }
    
    .custom-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }
    
    .custom-table thead th {
        background-color: #f8fafc;
        color: #6c757d;
        font-weight: 600;
        font-size: 0.875rem;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #e9ecef;
    }
    
    .custom-table tbody tr {
        transition: background-color 0.3s ease;
    }
    
    .custom-table tbody tr:hover {
        background-color: #f8fafc;
    }
    
    .custom-table tbody td {
        padding: 1rem;
        vertical-align: middle;
        border-bottom: 1px solid #f1f5f9;
    }
    
    .custom-table tbody tr:last-child td {
        border-bottom: none;
    }
    
    .sale-reference {
        font-weight: 600;
        color: #333;
        font-size: 0.875rem;
    }
    
    .sale-date {
        display: flex;
        flex-direction: column;
    }
    
    .sale-date-day {
        font-weight: 600;
        color: #333;
        font-size: 0.875rem;
    }
    
    .sale-date-time {
        color: #6c757d;
        font-size: 0.75rem;
    }
    
    .sale-customer {
        font-weight: 500;
        color: #333;
        font-size: 0.875rem;
    }
    
    .sale-city {
        color: #6c757d;
        font-size: 0.875rem;
    }
    
    .sale-amount {
        font-weight: 600;
        color: #333;
        font-size: 0.875rem;
    }
    
    .sale-amount small {
        font-weight: 400;
        color: #6c757d;
        font-size: 0.75rem;
    }
    
    .sale-status-container {
        display: flex;
        gap: 0.5rem;
    }
    
    .sale-status-badge {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
    }
    
    .sale-status-success {
        background-color: rgba(22, 163, 74, 0.1);
        color: #16a34a;
    }
    
    .sale-status-warning {
        background-color: rgba(234, 179, 8, 0.1);
        color: #eab308;
    }
    
    .sale-status-danger {
        background-color: rgba(220, 38, 38, 0.1);
        color: #dc2626;
    }
    
    .sale-status-secondary {
        background-color: rgba(71, 85, 105, 0.1);
        color: #475569;
    }
    
    /* État vide */
    .empty-state {
        text-align: center;
        padding: 3rem 1.5rem;
    }
    
    .empty-state-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background-color: #f8fafc;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 2rem;
        color: #6c757d;
    }
    
    .empty-state-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
    }
    
    .empty-state-description {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 1.5rem;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Fonction pour ajuster la hauteur des cartes sur mobile
    function adjustCardHeights() {
        if (window.innerWidth <= 767) {
            // Réinitialiser les hauteurs
            document.querySelectorAll('.stat-card, .resource-card, .chart-card, .quick-actions-card').forEach(card => {
                card.style.height = 'auto';
            });
        }
    }
    
    // Exécuter au chargement et au redimensionnement
    window.addEventListener('resize', adjustCardHeights);
    
    document.addEventListener('DOMContentLoaded', function() {
        adjustCardHeights();
        // Graphique des camions
        const truckCtx = document.getElementById('truckStatusChart').getContext('2d');
        const truckChart = new Chart(truckCtx, {
            type: 'doughnut',
            data: {
                labels: ['Disponibles', 'Occupés', 'En maintenance'],
                datasets: [{
                    data: [
                        {{ $dashboardStats['available_trucks'] }}, 
                        {{ $dashboardStats['busy_trucks'] }}, 
                        {{ $dashboardStats['maintenance_trucks'] }}
                    ],
                    backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                cutout: '70%'
            }
        });

        // Graphique des chauffeurs
        const driverCtx = document.getElementById('driverStatusChart').getContext('2d');
        const driverChart = new Chart(driverCtx, {
            type: 'doughnut',
            data: {
                labels: ['Disponibles', 'Occupés'],
                datasets: [{
                    data: [
                        {{ $dashboardStats['available_drivers'] }}, 
                        {{ $dashboardStats['total_drivers'] - $dashboardStats['available_drivers'] }}
                    ],
                    backgroundColor: ['#28a745', '#ffc107'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                cutout: '70%'
            }
        });

        // Initialisation des tooltips Bootstrap
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })

        // Graphique des ventes par mois
        const salesCtx = document.getElementById('salesChart').getContext('2d');
        const salesChart = new Chart(salesCtx, {
            type: 'line',
            data: {
                labels: [
                    @foreach($dashboardStats['sales_by_month'] as $month)
                        '{{ $month["name"] }}',
                    @endforeach
                ],
                datasets: [{
                    label: 'Chiffre d\'affaires',
                    data: [
                        @foreach($dashboardStats['sales_by_month'] as $month)
                            {{ $month["value"] }},
                        @endforeach
                    ],
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#ffffff',
                    pointBorderColor: 'rgba(54, 162, 235, 1)',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: '#333',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        bodyFont: {
                            size: 14
                        },
                        titleFont: {
                            size: 16,
                            weight: 'bold'
                        },
                        padding: 12,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                let value = context.raw;
                                return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ") + ' FCFA';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            callback: function(value) {
                                if (value >= 1000000) {
                                    return (value / 1000000).toFixed(1) + 'M';
                                } else if (value >= 1000) {
                                    return (value / 1000).toFixed(0) + 'k';
                                }
                                return value;
                            },
                            font: {
                                size: 12
                            },
                            color: '#6c757d'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 12
                            },
                            color: '#6c757d'
                        }
                    }
                }
            }
        });

        // Gestion des boutons de période pour le graphique
        document.querySelectorAll('.chart-period-selector .btn').forEach(function(button) {
            button.addEventListener('click', function() {
                // Retirer la classe active de tous les boutons
                document.querySelectorAll('.chart-period-selector .btn').forEach(function(btn) {
                    btn.classList.remove('active');
                });
                
                // Ajouter la classe active au bouton cliqué
                this.classList.add('active');
                
                // Ici, vous pourriez ajouter une logique pour charger des données différentes
                // en fonction de la période sélectionnée (année, trimestre, mois)
                // Pour l'instant, c'est juste une démonstration visuelle
            });
        });

        // Bouton d'actualisation
        document.getElementById('refreshDashboard').addEventListener('click', function() {
            location.reload();
        });
    });
</script>
@endpush
