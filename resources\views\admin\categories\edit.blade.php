@extends('layouts.admin_minimal')

@section('title', 'Modifier la catégorie - ' . $category->name)

@section('content')
<div class="container-fluid py-0">
    <!-- En-tête moderne et attrayant -->
    <div class="modern-header-wrapper">
        <div class="modern-header-background">
            <div class="header-gradient-overlay"></div>
            <div class="header-pattern"></div>
        </div>

        <div class="modern-header-content">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <!-- Navigation breadcrumb moderne -->
                    <nav aria-label="breadcrumb" class="modern-breadcrumb">
                        <ol class="breadcrumb-modern">
                            <li class="breadcrumb-item-modern">
                                <a href="{{ route('admin.dashboard') }}" class="breadcrumb-link">
                                    <i class="fas fa-home"></i>
                                    <span>Dashboard</span>
                                </a>
                            </li>
                            <li class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </li>
                            <li class="breadcrumb-item-modern">
                                <a href="{{ route('admin.categories.index') }}" class="breadcrumb-link">
                                    <i class="fas fa-layer-group"></i>
                                    <span>Catégories</span>
                                </a>
                            </li>
                            <li class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </li>
                            <li class="breadcrumb-item-modern active">
                                <span>Modifier {{ $category->name }}</span>
                            </li>
                        </ol>
                    </nav>

                    <!-- Titre principal avec animation -->
                    <div class="header-title-section">
                        <div class="title-icon-wrapper">
                            <div class="title-icon-bg">
                                @php
                                    $categoryName = strtolower($category->name);
                                    $iconClass = match($categoryName) {
                                        'ciment' => 'fas fa-industry',
                                        'fer' => 'fas fa-hammer',
                                        'brique' => 'fas fa-th-large',
                                        'sable' => 'fas fa-mountain',
                                        'gravier' => 'fas fa-gem',
                                        'bois' => 'fas fa-tree',
                                        'acier' => 'fas fa-cog',
                                        'peinture' => 'fas fa-paint-brush',
                                        'carrelage' => 'fas fa-th',
                                        'plomberie' => 'fas fa-wrench',
                                        'électricité' => 'fas fa-bolt',
                                        default => 'fas fa-cube'
                                    };
                                @endphp
                                <i class="{{ $iconClass }}"></i>
                            </div>
                        </div>
                        <div class="title-content">
                            <h1 class="header-title">
                                Modifier la catégorie
                                <span class="title-highlight">{{ $category->name }}</span>
                            </h1>
                            <p class="header-subtitle">
                                <i class="fas fa-info-circle me-2"></i>
                                Modifiez les informations de cette catégorie
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <!-- Boutons d'action -->
                    <div class="header-actions">
                        <div class="action-buttons-group">
                            <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-light btn-modern">
                                <i class="fas fa-arrow-left me-2"></i>
                                Retour aux catégories
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="main-content-wrapper">
        <!-- Statistiques de la catégorie -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card stats-primary">
                    <div class="stats-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stats-content">
                        <h3 class="stats-number">{{ $category->products_count }}</h3>
                        <p class="stats-label">{{ $category->products_count > 1 ? 'Produits' : 'Produit' }}</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card stats-success">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-content">
                        <h3 class="stats-number">{{ $category->is_active ? 'Active' : 'Inactive' }}</h3>
                        <p class="stats-label">Statut</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card stats-info">
                    <div class="stats-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="stats-content">
                        <h3 class="stats-number">{{ $category->created_at->format('d/m/Y') }}</h3>
                        <p class="stats-label">Date de création</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card stats-warning">
                    <div class="stats-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="stats-content">
                        <h3 class="stats-number">{{ $category->updated_at->format('d/m/Y') }}</h3>
                        <p class="stats-label">Dernière modification</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Formulaire principal -->
            <div class="col-lg-8">
                <form action="{{ route('admin.categories.update', $category) }}" method="POST" id="category-form">
                    @csrf
                    @method('PUT')

                    <!-- Informations de base -->
                    <div class="card modern-card mb-4">
                        <div class="card-header bg-white border-0">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-edit text-primary me-2"></i>
                                Informations de base
                            </h5>
                            <p class="text-muted mb-0 small">Modifiez les informations principales de votre catégorie</p>
                        </div>
                        <div class="card-body">
                            <div class="row g-4">
                                <!-- Nom de la catégorie -->
                                <div class="col-12">
                                    <div class="form-floating">
                                        <input type="text"
                                               class="form-control modern-input @error('name') is-invalid @enderror"
                                               id="categoryName"
                                               name="name"
                                               placeholder="Nom de la catégorie"
                                               value="{{ old('name', $category->name) }}"
                                               required>
                                        <label for="categoryName">
                                            <i class="fas fa-tag me-2"></i>Nom de la catégorie *
                                        </label>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="form-help-text">
                                        <i class="fas fa-lightbulb text-warning me-1"></i>
                                        Le nom doit être unique et descriptif (ex: Ciment, Fer, Brique...)
                                    </div>
                                </div>

                                <!-- Slug automatique -->
                                <div class="col-12">
                                    <div class="slug-preview">
                                        <label class="form-label">
                                            <i class="fas fa-link me-2"></i>URL générée automatiquement
                                        </label>
                                        <div class="slug-display">
                                            <span class="slug-base">{{ url('/categories/') }}/</span>
                                            <span class="slug-value" id="slugPreview">{{ $category->slug }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Description -->
                                <div class="col-12">
                                    <div class="form-floating">
                                        <textarea class="form-control modern-textarea @error('description') is-invalid @enderror"
                                                  id="categoryDescription"
                                                  name="description"
                                                  placeholder="Description de la catégorie"
                                                  style="height: 120px;">{{ old('description', $category->description) }}</textarea>
                                        <label for="categoryDescription">
                                            <i class="fas fa-align-left me-2"></i>Description (optionnel)
                                        </label>
                                        @error('description')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="form-help-text">
                                        <i class="fas fa-info-circle text-info me-1"></i>
                                        Une brève description pour aider à identifier la catégorie
                                    </div>
                                </div>

                                <!-- Statut -->
                                <div class="col-12">
                                    <div class="status-toggle-section">
                                        <label class="form-label">
                                            <i class="fas fa-toggle-on me-2"></i>Statut de la catégorie
                                        </label>
                                        <div class="modern-switch">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input"
                                                       type="checkbox"
                                                       id="isActive"
                                                       name="is_active"
                                                       value="1"
                                                       {{ old('is_active', $category->is_active) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="isActive">
                                                    <span class="switch-text">Catégorie active</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-help-text">
                                            <i class="fas fa-info-circle text-info me-1"></i>
                                            Les catégories inactives ne sont pas visibles sur le site
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Aperçu de la catégorie -->
                    <div class="card modern-card mb-4">
                        <div class="card-header bg-white border-0">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-eye text-info me-2"></i>
                                Aperçu de la catégorie
                            </h5>
                            <p class="text-muted mb-0 small">Voici comment votre catégorie apparaîtra après modification</p>
                        </div>
                        <div class="card-body">
                            <div class="category-preview" id="categoryPreview">
                                <div class="preview-card">
                                    <div class="preview-icon" id="previewIcon">
                                        <i class="{{ $iconClass }}"></i>
                                    </div>
                                    <div class="preview-content">
                                        <h6 class="preview-name" id="previewName">{{ $category->name }}</h6>
                                        <p class="preview-description" id="previewDescription">{{ $category->description ?: 'Description de la catégorie' }}</p>
                                        <div class="preview-stats">
                                            <span class="preview-badge">{{ $category->products_count }} {{ $category->products_count > 1 ? 'produits' : 'produit' }}</span>
                                            <span class="preview-badge {{ $category->is_active ? 'active' : 'inactive' }}" id="previewStatus">
                                                {{ $category->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Historique des modifications -->
                    <div class="card modern-card mb-4">
                        <div class="card-header bg-white border-0">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-history text-secondary me-2"></i>
                                Historique
                            </h5>
                            <p class="text-muted mb-0 small">Informations sur les modifications de cette catégorie</p>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-icon created">
                                        <i class="fas fa-plus"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Catégorie créée</h6>
                                        <p class="timeline-date">{{ $category->created_at->format('d/m/Y à H:i') }}</p>
                                        <p class="timeline-description">La catégorie "{{ $category->name }}" a été créée</p>
                                    </div>
                                </div>
                                @if($category->updated_at != $category->created_at)
                                <div class="timeline-item">
                                    <div class="timeline-icon updated">
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Dernière modification</h6>
                                        <p class="timeline-date">{{ $category->updated_at->format('d/m/Y à H:i') }}</p>
                                        <p class="timeline-description">Informations mises à jour</p>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Boutons d'action -->
                    <div class="card modern-card">
                        <div class="card-body text-center">
                            <div class="action-buttons-final">
                                <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary btn-lg me-3">
                                    <i class="fas fa-times me-2"></i>
                                    Annuler
                                </a>
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-save me-2"></i>
                                    Enregistrer les modifications
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Panneau d'informations -->
            <div class="col-lg-4">
                <!-- Informations sur la catégorie -->
                <div class="card modern-card mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0 fw-bold">
                            <i class="fas fa-info-circle text-info me-2"></i>
                            Informations détaillées
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="info-section">
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-tag text-primary me-2"></i>
                                    Nom actuel
                                </div>
                                <div class="info-value">{{ $category->name }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-link text-secondary me-2"></i>
                                    Slug
                                </div>
                                <div class="info-value">{{ $category->slug }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-boxes text-warning me-2"></i>
                                    Produits associés
                                </div>
                                <div class="info-value">{{ $category->products_count }} {{ $category->products_count > 1 ? 'produits' : 'produit' }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-toggle-on text-success me-2"></i>
                                    Statut
                                </div>
                                <div class="info-value">
                                    <span class="badge {{ $category->is_active ? 'bg-success' : 'bg-secondary' }}">
                                        {{ $category->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Produits de la catégorie -->
                @if($category->products_count > 0)
                <div class="card modern-card mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0 fw-bold">
                            <i class="fas fa-boxes text-primary me-2"></i>
                            Produits de cette catégorie
                        </h5>
                        <p class="text-muted mb-0 small">{{ $category->products_count }} {{ $category->products_count > 1 ? 'produits trouvés' : 'produit trouvé' }}</p>
                    </div>
                    <div class="card-body">
                        <div class="products-list">
                            @foreach($category->products()->limit(5)->get() as $product)
                            <div class="product-item">
                                <div class="product-icon">
                                    <i class="fas fa-cube"></i>
                                </div>
                                <div class="product-info">
                                    <strong>{{ Str::limit($product->name, 25) }}</strong>
                                    <small class="text-muted d-block">Stock: {{ $product->stock_quantity }} {{ $product->unit }}</small>
                                </div>
                                <div class="product-actions">
                                    <a href="{{ route('admin.products.edit', $product) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </div>
                            @endforeach

                            @if($category->products_count > 5)
                            <div class="text-center mt-3">
                                <a href="{{ route('admin.products.index', ['category' => $category->id]) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>
                                    Voir tous les produits ({{ $category->products_count }})
                                </a>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
                @endif

                <!-- Actions rapides -->
                <div class="card modern-card">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0 fw-bold">
                            <i class="fas fa-bolt text-warning me-2"></i>
                            Actions rapides
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="quick-actions">
                            <a href="{{ route('admin.products.create', ['category' => $category->id]) }}" class="quick-action-btn">
                                <div class="quick-action-icon">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div class="quick-action-content">
                                    <strong>Ajouter un produit</strong>
                                    <small>Créer un nouveau produit dans cette catégorie</small>
                                </div>
                            </a>

                            <a href="{{ route('admin.products.index', ['category' => $category->id]) }}" class="quick-action-btn">
                                <div class="quick-action-icon">
                                    <i class="fas fa-list"></i>
                                </div>
                                <div class="quick-action-content">
                                    <strong>Voir les produits</strong>
                                    <small>Gérer tous les produits de cette catégorie</small>
                                </div>
                            </a>

                            <button type="button" class="quick-action-btn" onclick="confirmDelete()">
                                <div class="quick-action-icon danger">
                                    <i class="fas fa-trash"></i>
                                </div>
                                <div class="quick-action-content">
                                    <strong>Supprimer la catégorie</strong>
                                    <small>Attention: action irréversible</small>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-modal">
            <div class="modal-header border-0 pb-0">
                <div class="w-100 text-center">
                    <div class="delete-icon-wrapper mb-3">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                    </div>
                    <h4 class="modal-title">Confirmer la suppression</h4>
                </div>
            </div>
            <div class="modal-body text-center">
                <p class="mb-3">Êtes-vous sûr de vouloir supprimer la catégorie :</p>
                <p class="fw-bold text-primary mb-3">"{{ $category->name }}"</p>
                @if($category->products_count > 0)
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Cette catégorie contient {{ $category->products_count }} {{ $category->products_count > 1 ? 'produits' : 'produit' }}.
                    Ils seront également supprimés.
                </div>
                @endif
                <p class="text-muted small">Cette action est irréversible.</p>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-secondary btn-modern me-2" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Annuler
                </button>
                <form action="{{ route('admin.categories.destroy', $category) }}" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger btn-modern">
                        <i class="fas fa-trash me-2"></i>Supprimer définitivement
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* ===== STYLES POUR LA PAGE D'ÉDITION DE CATÉGORIE ===== */

/* Variables CSS */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

/* En-tête moderne */
.modern-header-wrapper {
    position: relative;
    margin: -1.5rem -1.5rem 2rem -1.5rem;
    overflow: hidden;
}

.modern-header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
}

.header-gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
}

.header-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 80% 50%, rgba(255,255,255,0.1) 2px, transparent 2px);
    background-size: 50px 50px;
}

.modern-header-content {
    position: relative;
    z-index: 2;
    padding: 2rem;
    color: white;
}

/* Breadcrumb moderne */
.modern-breadcrumb {
    margin-bottom: 1.5rem;
}

.breadcrumb-modern {
    display: flex;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
    flex-wrap: wrap;
}

.breadcrumb-item-modern {
    display: flex;
    align-items: center;
}

.breadcrumb-link {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
}

.breadcrumb-link:hover {
    color: white;
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.breadcrumb-link i {
    margin-right: 0.5rem;
}

.breadcrumb-separator {
    margin: 0 0.5rem;
    color: rgba(255,255,255,0.6);
}

.breadcrumb-item-modern.active span {
    color: white;
    font-weight: 600;
}

/* Titre principal */
.header-title-section {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.title-icon-wrapper {
    margin-right: 1.5rem;
}

.title-icon-bg {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.3);
}

.header-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
}

.title-highlight {
    color: rgba(255,255,255,0.8);
    font-weight: 400;
}

.header-subtitle {
    margin: 0.5rem 0 0 0;
    color: rgba(255,255,255,0.8);
    font-size: 1.1rem;
}

/* Boutons d'action dans l'en-tête */
.header-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 100%;
}

.action-buttons-group {
    display: flex;
    gap: 1rem;
}

.btn-modern {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-outline-light.btn-modern {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

.btn-outline-light.btn-modern:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

/* Contenu principal */
.main-content-wrapper {
    padding: 0 1.5rem;
}

/* Cartes de statistiques */
.stats-card {
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: none;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.12);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.stats-primary::before { background: linear-gradient(90deg, #667eea, #764ba2); }
.stats-success::before { background: linear-gradient(90deg, #28a745, #20c997); }
.stats-info::before { background: linear-gradient(90deg, #17a2b8, #6f42c1); }
.stats-warning::before { background: linear-gradient(90deg, #ffc107, #fd7e14); }

.stats-card {
    display: flex;
    align-items: center;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 1rem;
    color: white;
}

.stats-primary .stats-icon { background: linear-gradient(135deg, #667eea, #764ba2); }
.stats-success .stats-icon { background: linear-gradient(135deg, #28a745, #20c997); }
.stats-info .stats-icon { background: linear-gradient(135deg, #17a2b8, #6f42c1); }
.stats-warning .stats-icon { background: linear-gradient(135deg, #ffc107, #fd7e14); }

.stats-content {
    flex: 1;
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #2d3748;
}

.stats-label {
    color: #718096;
    margin: 0;
    font-weight: 500;
}

/* Cartes modernes */
.modern-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.modern-card:hover {
    box-shadow: 0 20px 40px rgba(0,0,0,0.12);
}

.modern-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding: 1.5rem;
}

.modern-card .card-body {
    padding: 2rem;
}

/* Champs de formulaire modernes */
.modern-input, .modern-textarea {
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.8);
    backdrop-filter: blur(10px);
    padding: 0.75rem 1rem;
}

.modern-input:focus, .modern-textarea:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.form-floating > label {
    color: #6c757d;
    font-weight: 500;
}

/* Texte d'aide */
.form-help-text {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #6c757d;
    padding: 0.5rem 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
    border-left: 3px solid #667eea;
}

/* Switch moderne */
.status-toggle-section {
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.modern-switch .form-check-input {
    width: 3rem;
    height: 1.5rem;
    border-radius: 1rem;
    background-color: #dee2e6;
    border: none;
    transition: all 0.3s ease;
}

.modern-switch .form-check-input:checked {
    background-color: #28a745;
    background-image: none;
}

.modern-switch .form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.switch-text {
    font-weight: 600;
    color: #2d3748;
    margin-left: 0.5rem;
}

/* Aperçu du slug */
.slug-preview {
    margin-bottom: 1rem;
}

.slug-display {
    padding: 1rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 12px;
    border-left: 4px solid #667eea;
    font-family: 'Courier New', monospace;
}

.slug-base {
    color: #6c757d;
}

.slug-value {
    color: #667eea;
    font-weight: 600;
}

/* Aperçu de la catégorie */
.category-preview {
    padding: 1rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    border-radius: 12px;
}

.preview-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.preview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.preview-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 1rem;
    transition: all 0.3s ease;
}

.preview-content {
    flex: 1;
}

.preview-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.preview-description {
    color: #718096;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.preview-stats {
    display: flex;
    gap: 0.5rem;
}

.preview-badge {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.preview-badge.active {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.preview-badge.inactive {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

/* Timeline/Historique */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #667eea, #764ba2);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-icon {
    position: absolute;
    left: -2rem;
    top: 0;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    z-index: 2;
}

.timeline-icon.created {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.timeline-icon.updated {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.timeline-content {
    background: rgba(102, 126, 234, 0.05);
    padding: 1rem;
    border-radius: 12px;
    border-left: 3px solid #667eea;
}

.timeline-title {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.timeline-date {
    color: #667eea;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.timeline-description {
    color: #718096;
    margin: 0;
    font-size: 0.9rem;
}

/* Informations détaillées */
.info-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-item {
    padding: 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.info-item:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(5px);
}

.info-label {
    font-size: 0.9rem;
    color: #718096;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.info-value {
    font-weight: 600;
    color: #2d3748;
}

/* Liste des produits */
.products-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.product-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.product-item:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(5px);
}

.product-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
    font-size: 1rem;
}

.product-info {
    flex: 1;
}

.product-actions {
    margin-left: 1rem;
}

/* Actions rapides */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.quick-action-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(5px);
    color: inherit;
}

.quick-action-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
    font-size: 1rem;
}

.quick-action-icon.danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.quick-action-content {
    flex: 1;
}

.quick-action-content strong {
    display: block;
    color: #2d3748;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.quick-action-content small {
    color: #718096;
    font-size: 0.85rem;
}

/* Boutons d'action finaux */
.action-buttons-final {
    padding: 2rem 0;
}

.action-buttons-final .btn {
    border-radius: 25px;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.action-buttons-final .btn:hover {
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

/* Modal moderne */
.modern-modal {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

.delete-icon-wrapper {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    background: rgba(255, 193, 7, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
}

/* Responsive */
@media (max-width: 768px) {
    .modern-header-content {
        padding: 1rem;
    }

    .header-title {
        font-size: 1.8rem;
    }

    .title-icon-bg {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .action-buttons-group {
        flex-direction: column;
        width: 100%;
    }

    .main-content-wrapper {
        padding: 0 0.5rem;
    }

    .modern-card .card-body {
        padding: 1rem;
    }

    .preview-card {
        flex-direction: column;
        text-align: center;
    }

    .preview-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }

    .timeline {
        padding-left: 1.5rem;
    }

    .timeline::before {
        left: 0.75rem;
    }

    .timeline-icon {
        left: -1.5rem;
    }
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modern-card {
    animation: slideInUp 0.5s ease-out;
}

.stats-card {
    animation: slideInUp 0.3s ease-out;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.preview-icon {
    animation: pulse 2s infinite;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // ===== ÉLÉMENTS DU FORMULAIRE =====

    const categoryNameInput = document.getElementById('categoryName');
    const categoryDescriptionInput = document.getElementById('categoryDescription');
    const isActiveSwitch = document.getElementById('isActive');
    const slugPreview = document.getElementById('slugPreview');
    const previewName = document.getElementById('previewName');
    const previewDescription = document.getElementById('previewDescription');
    const previewIcon = document.getElementById('previewIcon');
    const previewStatus = document.getElementById('previewStatus');
    const form = document.getElementById('category-form');

    // ===== GÉNÉRATION AUTOMATIQUE DU SLUG =====

    function generateSlug(text) {
        return text
            .toLowerCase()
            .trim()
            .replace(/[àáâãäå]/g, 'a')
            .replace(/[èéêë]/g, 'e')
            .replace(/[ìíîï]/g, 'i')
            .replace(/[òóôõö]/g, 'o')
            .replace(/[ùúûü]/g, 'u')
            .replace(/[ç]/g, 'c')
            .replace(/[ñ]/g, 'n')
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }

    // ===== MISE À JOUR DE L'APERÇU EN TEMPS RÉEL =====

    function updatePreview() {
        const name = categoryNameInput.value.trim();
        const description = categoryDescriptionInput.value.trim();
        const isActive = isActiveSwitch.checked;

        // Mise à jour du nom
        if (name) {
            previewName.textContent = name;
            const slug = generateSlug(name);
            slugPreview.textContent = slug || '{{ $category->slug }}';
        } else {
            previewName.textContent = '{{ $category->name }}';
            slugPreview.textContent = '{{ $category->slug }}';
        }

        // Mise à jour de la description
        if (description) {
            previewDescription.textContent = description;
            previewDescription.style.display = 'block';
        } else {
            previewDescription.textContent = '{{ $category->description ?: "Description de la catégorie" }}';
            previewDescription.style.display = 'block';
        }

        // Mise à jour du statut
        previewStatus.textContent = isActive ? 'Active' : 'Inactive';
        previewStatus.className = `preview-badge ${isActive ? 'active' : 'inactive'}`;

        // Mise à jour de l'icône selon le nom
        updatePreviewIcon(name || '{{ $category->name }}');
    }

    // ===== MISE À JOUR DE L'ICÔNE SELON LE NOM =====

    function updatePreviewIcon(name) {
        const categoryName = name.toLowerCase();
        let iconClass = 'fas fa-cube'; // Icône par défaut
        let gradientClass = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';

        // Correspondance nom -> icône
        const iconMap = {
            'ciment': { icon: 'fas fa-industry', gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' },
            'fer': { icon: 'fas fa-hammer', gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)' },
            'brique': { icon: 'fas fa-th-large', gradient: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)' },
            'sable': { icon: 'fas fa-mountain', gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)' },
            'gravier': { icon: 'fas fa-gem', gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)' },
            'bois': { icon: 'fas fa-tree', gradient: 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)' },
            'acier': { icon: 'fas fa-cog', gradient: 'linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)' },
            'peinture': { icon: 'fas fa-paint-brush', gradient: 'linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%)' },
            'carrelage': { icon: 'fas fa-th', gradient: 'linear-gradient(135deg, #ee9ca7 0%, #ffdde1 100%)' },
            'plomberie': { icon: 'fas fa-wrench', gradient: 'linear-gradient(135deg, #2196f3 0%, #21cbf3 100%)' },
            'électricité': { icon: 'fas fa-bolt', gradient: 'linear-gradient(135deg, #673ab7 0%, #512da8 100%)' }
        };

        // Recherche de correspondance
        for (const [key, value] of Object.entries(iconMap)) {
            if (categoryName.includes(key)) {
                iconClass = value.icon;
                gradientClass = value.gradient;
                break;
            }
        }

        // Application de l'icône et du gradient
        previewIcon.innerHTML = `<i class="${iconClass}"></i>`;
        previewIcon.style.background = gradientClass;

        // Animation de changement
        previewIcon.style.transform = 'scale(1.1)';
        setTimeout(() => {
            previewIcon.style.transform = 'scale(1)';
        }, 200);
    }

    // ===== FONCTION DE CONFIRMATION DE SUPPRESSION =====

    window.confirmDelete = function() {
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    };

    // ===== ÉCOUTEURS D'ÉVÉNEMENTS =====

    // Mise à jour en temps réel
    categoryNameInput.addEventListener('input', updatePreview);
    categoryDescriptionInput.addEventListener('input', updatePreview);
    isActiveSwitch.addEventListener('change', updatePreview);

    // Validation du formulaire
    form.addEventListener('submit', function(e) {
        const name = categoryNameInput.value.trim();

        if (!name) {
            e.preventDefault();
            alert('Veuillez saisir un nom pour la catégorie.');
            categoryNameInput.focus();
            return;
        }

        if (name.length < 2) {
            e.preventDefault();
            alert('Le nom de la catégorie doit contenir au moins 2 caractères.');
            categoryNameInput.focus();
            return;
        }

        // Animation de soumission
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enregistrement en cours...';
            submitBtn.disabled = true;
        }
    });

    // ===== ANIMATIONS AU CHARGEMENT =====

    // Animation des cartes de statistiques
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Animation des cartes principales
    const cards = document.querySelectorAll('.modern-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${(index * 0.1) + 0.3}s`;
    });

    // Effet de focus sur les champs
    const inputs = document.querySelectorAll('.modern-input, .modern-textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.closest('.form-floating')?.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.closest('.form-floating')?.classList.remove('focused');
        });
    });

    // Initialisation de l'aperçu
    updatePreview();

    // ===== EFFETS VISUELS SUPPLÉMENTAIRES =====

    // Effet de parallaxe sur l'en-tête
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const headerBackground = document.querySelector('.modern-header-background');
        if (headerBackground) {
            headerBackground.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });

    // Animation des actions rapides au hover
    const quickActionBtns = document.querySelectorAll('.quick-action-btn');
    quickActionBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.quick-action-icon');
            icon.style.transform = 'scale(1.1) rotate(5deg)';
        });

        btn.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.quick-action-icon');
            icon.style.transform = 'scale(1) rotate(0deg)';
        });
    });

    // Animation des éléments d'information au hover
    const infoItems = document.querySelectorAll('.info-item');
    infoItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(10px) scale(1.02)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0) scale(1)';
        });
    });

    // Animation des produits au hover
    const productItems = document.querySelectorAll('.product-item');
    productItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.product-icon');
            icon.style.transform = 'scale(1.1) rotate(5deg)';
        });

        item.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.product-icon');
            icon.style.transform = 'scale(1) rotate(0deg)';
        });
    });

    // ===== NOTIFICATIONS DE SUCCÈS =====

    // Afficher une notification si la catégorie a été mise à jour
    @if(session('success'))
        // Créer une notification toast moderne
        const toast = document.createElement('div');
        toast.className = 'toast-notification success';
        toast.innerHTML = `
            <div class="toast-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="toast-content">
                <strong>Succès !</strong>
                <p>{{ session('success') }}</p>
            </div>
            <button class="toast-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        document.body.appendChild(toast);

        // Animation d'apparition
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // Suppression automatique après 5 secondes
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, 5000);
    @endif
});

// Styles CSS supplémentaires pour les effets
const style = document.createElement('style');
style.textContent = `
    .focused {
        transform: translateY(-2px);
        transition: transform 0.3s ease;
    }

    .quick-action-icon, .product-icon {
        transition: transform 0.3s ease;
    }

    .toast-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        padding: 1rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        z-index: 9999;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 400px;
    }

    .toast-notification.show {
        transform: translateX(0);
    }

    .toast-notification.success {
        border-left: 4px solid #28a745;
    }

    .toast-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #28a745, #20c997);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }

    .toast-content {
        flex: 1;
    }

    .toast-content strong {
        color: #2d3748;
        display: block;
        margin-bottom: 0.25rem;
    }

    .toast-content p {
        color: #718096;
        margin: 0;
        font-size: 0.9rem;
    }

    .toast-close {
        background: none;
        border: none;
        color: #718096;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .toast-close:hover {
        background: rgba(0,0,0,0.1);
        color: #2d3748;
    }
`;
document.head.appendChild(style);
</script>
@endpush
