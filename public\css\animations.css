/* Animations pour l'interface moderne des ventes */

/* Définition des animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes slideInRight {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes shimmer {
    100% { transform: translateX(100%); }
}

/* Animation des éléments au chargement */
.stat-card {
    animation: fadeIn 0.5s ease-out forwards;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

.card {
    animation: fadeIn 0.5s ease-out forwards;
    animation-delay: 0.5s;
}

.filters-container {
    animation: fadeIn 0.5s ease-out forwards;
    animation-delay: 0.3s;
}

/* Animation des lignes du tableau */
.modern-table tbody tr {
    animation: slideInRight 0.3s ease-out forwards;
    opacity: 0;
}

/* Animation des 10 premières lignes avec délai progressif */
.modern-table tbody tr:nth-child(1) { animation-delay: 0.5s; }
.modern-table tbody tr:nth-child(2) { animation-delay: 0.55s; }
.modern-table tbody tr:nth-child(3) { animation-delay: 0.6s; }
.modern-table tbody tr:nth-child(4) { animation-delay: 0.65s; }
.modern-table tbody tr:nth-child(5) { animation-delay: 0.7s; }
.modern-table tbody tr:nth-child(6) { animation-delay: 0.75s; }
.modern-table tbody tr:nth-child(7) { animation-delay: 0.8s; }
.modern-table tbody tr:nth-child(8) { animation-delay: 0.85s; }
.modern-table tbody tr:nth-child(9) { animation-delay: 0.9s; }
.modern-table tbody tr:nth-child(10) { animation-delay: 0.95s; }

/* Animation des badges */
.badge {
    transition: all 0.2s ease;
}

.badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(16, 24, 40, 0.1);
}

/* Animation des icônes */
.stat-icon {
    transition: transform 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1);
    animation: pulse 1.5s infinite;
}

/* Animation de la barre de progression */
.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
}

/* Animation des boutons */
.action-button {
    transition: all 0.2s ease;
}

.action-button:hover {
    transform: translateY(-1px);
}

/* Animation des filtres */
.filter-select {
    transition: all 0.2s ease;
}

.filter-select:focus {
    transform: scale(1.02);
}
