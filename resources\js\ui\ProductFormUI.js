export class ProductFormUI {
    constructor(formState, categoryConfig) {
        this.formState = formState;
        this.categoryConfig = categoryConfig;
        this.elements = this.getFormElements();
        this.setupEventListeners();
        this.setupStateSubscription();
    }

    getFormElements() {
        return {
            form: document.getElementById('productForm'),
            category: document.getElementById('category_id'),
            name: document.getElementById('name'),
            basePrice: document.getElementById('base_price'),
            unit: document.getElementById('unit'),
            stock: document.getElementById('stock'),
            regionalPricing: document.getElementById('regional-pricing'),
            ironSpecifications: document.getElementById('iron-specifications'),
            regionTabs: document.querySelectorAll('.region-tab'),
            regionContents: document.querySelectorAll('.region-content'),
            progressBar: document.querySelector('.progress-bar'),
            submitButton: document.querySelector('button[type="submit"]')
        };
    }

    setupEventListeners() {
        // Événements de base du formulaire
        this.elements.category.addEventListener('change', this.handleCategoryChange.bind(this));
        this.elements.form.addEventListener('submit', this.handleSubmit.bind(this));

        // Événements des prix régionaux
        document.querySelectorAll('.city-price').forEach(input => {
            input.addEventListener('input', (e) => {
                const { region, city } = e.target.dataset;
                this.formState.updateRegionalPrice(region, city, parseFloat(e.target.value) || 0);
            });
        });

        // Événements des spécifications du fer
        ['diameter', 'length', 'unit_price'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('input', () => {
                    this.formState.updateIronSpecifications({
                        diameter: parseInt(document.getElementById('diameter').value) || null,
                        length: parseFloat(document.getElementById('length').value) || 12,
                        unitPrice: parseFloat(document.getElementById('unit_price').value) || 0
                    });
                });
            }
        });

        // Gestion des onglets de région
        this.elements.regionTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                this.switchRegionTab(tab.dataset.region);
            });
        });
    }

    setupStateSubscription() {
        this.formState.subscribe(this.updateUI.bind(this));
    }

    // Gestionnaires d'événements
    handleCategoryChange(e) {
        const categoryId = e.target.value;
        const categoryConfig = this.categoryConfig[categoryId];
        if (categoryConfig) {
            this.formState.updateCategory(categoryId, categoryConfig);
        }
    }

    handleSubmit(e) {
        e.preventDefault();
        if (!this.formState.state.validation.isValid) {
            this.showValidationErrors();
            return;
        }
        this.submitForm();
    }

    // Méthodes de mise à jour de l'interface
    updateUI(state) {
        this.updateVisibility(state);
        this.updateUnits(state);
        this.updateValidation(state);
        this.updateCalculations(state);
        this.updateProgressBar(state);
    }

    updateVisibility(state) {
        const category = this.categoryConfig[state.category];
        if (!category) return;

        // Affichage/masquage des sections principales
        this.elements.regionalPricing.style.display = 
            category.features.requiresRegionalPricing ? 'block' : 'none';
        this.elements.ironSpecifications.style.display = 
            category.features.requiresSpecifications ? 'block' : 'none';
        this.elements.basePrice.closest('.col-md-4').style.display = 
            category.features.requiresBasePrice ? 'block' : 'none';

        // Animation de transition
        requestAnimationFrame(() => {
            const visibleSections = document.querySelectorAll('[style*="display: block"]');
            visibleSections.forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                requestAnimationFrame(() => {
                    section.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                });
            });
        });
    }

    updateUnits(state) {
        const category = this.categoryConfig[state.category];
        if (!category) return;

        // Mise à jour des options d'unité
        const currentUnits = Array.from(this.elements.unit.options).map(opt => opt.value);
        const newUnits = category.units;

        // Supprimer les anciennes options
        while (this.elements.unit.options.length > 1) {
            this.elements.unit.remove(1);
        }

        // Ajouter les nouvelles options
        newUnits.forEach(unit => {
            const option = new Option(unit.label, unit.value);
            option.selected = unit.default;
            this.elements.unit.add(option);
        });
    }

    updateValidation(state) {
        const { errors } = state.validation;
        
        // Réinitialiser les messages d'erreur
        document.querySelectorAll('.invalid-feedback').forEach(el => el.remove());
        document.querySelectorAll('.is-invalid').forEach(el => {
            el.classList.remove('is-invalid');
        });

        // Afficher les nouvelles erreurs
        errors.forEach((message, field) => {
            const element = document.querySelector(`[name="${field}"]`);
            if (element) {
                element.classList.add('is-invalid');
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = message;
                element.parentNode.appendChild(feedback);
            }
        });

        // Mettre à jour l'état du bouton de soumission
        this.elements.submitButton.disabled = !state.validation.isValid;
    }

    updateCalculations(state) {
        if (!state.category || !this.categoryConfig[state.category].features.requiresSpecifications) {
            return;
        }

        const { calculations } = state.ironSpecifications;
        
        // Mettre à jour les champs de calcul
        ['units-per-ton', 'weight-per-unit', 'ton-price'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                let value = calculations[id.replace(/-/g, '')];
                if (typeof value === 'number') {
                    if (id === 'ton-price') {
                        value = value.toLocaleString() + ' FCFA';
                    } else if (id === 'weight-per-unit') {
                        value = value.toFixed(3) + ' kg';
                    } else {
                        value = value + ' unités';
                    }
                }
                element.textContent = value;
            }
        });
    }

    updateProgressBar(state) {
        if (!this.categoryConfig[state.category]?.features.requiresRegionalPricing) {
            return;
        }

        let totalPrices = 0;
        let completedPrices = 0;

        state.regionalPrices.forEach(cities => {
            cities.forEach(price => {
                totalPrices++;
                if (price > 0) completedPrices++;
            });
        });

        const percentage = totalPrices ? (completedPrices / totalPrices) * 100 : 0;
        this.elements.progressBar.style.width = `${percentage}%`;

        // Mettre à jour les indicateurs de statut des régions
        this.elements.regionTabs.forEach(tab => {
            const region = tab.dataset.region;
            const status = tab.querySelector('.region-status');
            const cities = state.regionalPrices.get(region);
            
            if (cities) {
                let allComplete = true;
                cities.forEach(price => {
                    if (!price || price <= 0) allComplete = false;
                });
                status.className = `region-status ${allComplete ? 'status-complete' : 'status-incomplete'}`;
            }
        });
    }

    switchRegionTab(regionId) {
        this.elements.regionTabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.region === regionId);
        });

        this.elements.regionContents.forEach(content => {
            if (content.dataset.region === regionId) {
                content.style.display = 'none';
                content.classList.add('active');
                requestAnimationFrame(() => {
                    content.style.display = 'block';
                    content.style.opacity = '0';
                    requestAnimationFrame(() => {
                        content.style.transition = 'opacity 0.3s ease';
                        content.style.opacity = '1';
                    });
                });
            } else {
                content.classList.remove('active');
                content.style.display = 'none';
            }
        });
    }

    showValidationErrors() {
        const { errors } = this.formState.state.validation;
        let errorMessage = 'Veuillez corriger les erreurs suivantes :\n\n';
        errors.forEach((message, field) => {
            errorMessage += `- ${message}\n`;
        });
        alert(errorMessage);
    }

    async submitForm() {
        try {
            const formData = new FormData(this.elements.form);
            const response = await fetch(this.elements.form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (!response.ok) {
                throw new Error('Erreur lors de la soumission du formulaire');
            }

            const result = await response.json();
            if (result.success) {
                window.location.href = result.redirect || '/admin/products';
            } else {
                throw new Error(result.message || 'Erreur lors de la création du produit');
            }
        } catch (error) {
            console.error('Erreur:', error);
            alert(error.message);
        }
    }
}
