<?php $__env->startSection('title', 'Modifier le chauffeur'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
    <!-- Header avec breadcrumb et actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb bg-transparent p-0 mb-2">
                            <li class="breadcrumb-item">
                                <a href="<?php echo e(route('admin.dashboard')); ?>" class="text-decoration-none">
                                    <i class="fas fa-home me-1"></i>Tableau de bord
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="<?php echo e(route('admin.drivers.index')); ?>" class="text-decoration-none">
                                    <i class="fas fa-users me-1"></i>Chauffeurs
                                </a>
                            </li>
                            <li class="breadcrumb-item active">Modifier <?php echo e($driver->full_name); ?></li>
                        </ol>
                    </nav>
                    <h1 class="h3 mb-0 text-gray-800 d-flex align-items-center">
                        <div class="avatar-circle bg-primary me-3">
                            <i class="fas fa-user-edit"></i>
                        </div>
                        <div>
                            <span class="fw-bold">Modifier le chauffeur</span>
                            <small class="d-block text-muted"><?php echo e($driver->full_name); ?></small>
                        </div>
                    </h1>
                </div>
                <div class="d-flex gap-2">
                    <a href="<?php echo e(route('admin.drivers.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages de session -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if($errors->any()): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Erreurs de validation :</strong>
            <ul class="mb-0 mt-2">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Formulaire principal -->
    <div class="row">
        <div class="col-lg-8">
            <!-- Informations personnelles -->
            <div class="card shadow-sm border-0 mb-4 animate-card">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="card-title mb-0 d-flex align-items-center">
                        <i class="fas fa-user me-2"></i>
                        Informations personnelles
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form id="driverForm" action="<?php echo e(route('admin.drivers.update', $driver->id)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text"
                                           class="form-control <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="first_name"
                                           name="first_name"
                                           value="<?php echo e(old('first_name', $driver->first_name)); ?>"
                                           placeholder="Prénom"
                                           required>
                                    <label for="first_name">
                                        <i class="fas fa-user me-1 text-primary"></i>Prénom <span class="text-danger">*</span>
                                    </label>
                                    <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                                <div class="form-floating mb-3">
                                    <input type="text"
                                           class="form-control <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="last_name"
                                           name="last_name"
                                           value="<?php echo e(old('last_name', $driver->last_name)); ?>"
                                           placeholder="Nom"
                                           required>
                                    <label for="last_name">
                                        <i class="fas fa-user me-1 text-primary"></i>Nom <span class="text-danger">*</span>
                                    </label>
                                    <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="email"
                                           class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="email"
                                           name="email"
                                           value="<?php echo e(old('email', $driver->email)); ?>"
                                           placeholder="Email"
                                           required>
                                    <label for="email">
                                        <i class="fas fa-envelope me-1 text-primary"></i>Email <span class="text-danger">*</span>
                                    </label>
                                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="tel"
                                           class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="phone"
                                           name="phone"
                                           value="<?php echo e(old('phone', $driver->phone)); ?>"
                                           placeholder="Téléphone"
                                           required>
                                    <label for="phone">
                                        <i class="fas fa-phone me-1 text-primary"></i>Téléphone <span class="text-danger">*</span>
                                    </label>
                                    <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="form-floating mb-3">
                            <textarea class="form-control <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                      id="address"
                                      name="address"
                                      style="height: 80px"
                                      placeholder="Adresse complète"><?php echo e(old('address', $driver->address)); ?></textarea>
                            <label for="address">
                                <i class="fas fa-map-marker-alt me-1 text-primary"></i>Adresse
                            </label>
                            <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                </div>
            </div>

            <!-- Informations du permis -->
            <div class="card shadow-sm border-0 mb-4 animate-card">
                <div class="card-header bg-gradient-warning text-white">
                    <h5 class="card-title mb-0 d-flex align-items-center">
                        <i class="fas fa-id-card me-2"></i>
                        Informations du permis de conduire
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text"
                                       class="form-control <?php $__errorArgs = ['license_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="license_number"
                                       name="license_number"
                                       value="<?php echo e(old('license_number', $driver->license_number)); ?>"
                                       placeholder="Numéro de permis"
                                       required>
                                <label for="license_number">
                                    <i class="fas fa-credit-card me-1 text-warning"></i>Numéro de permis <span class="text-danger">*</span>
                                </label>
                                <?php $__errorArgs = ['license_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="date"
                                       class="form-control <?php $__errorArgs = ['license_expiry'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="license_expiry"
                                       name="license_expiry"
                                       value="<?php echo e(old('license_expiry', $driver->license_expiry ? $driver->license_expiry->format('Y-m-d') : '')); ?>"
                                       required>
                                <label for="license_expiry">
                                    <i class="fas fa-calendar-alt me-1 text-warning"></i>Date d'expiration <span class="text-danger">*</span>
                                </label>
                                <?php $__errorArgs = ['license_expiry'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Indicateur d'expiration -->
                    <div class="alert alert-info border-0 d-flex align-items-center" id="license-status">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="license-status-text">Vérification de la validité du permis...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar droite -->
        <div class="col-lg-4">
            <!-- Statut et actions -->
            <div class="card shadow-sm border-0 mb-4 animate-card">
                <div class="card-header bg-gradient-success text-white">
                    <h5 class="card-title mb-0 d-flex align-items-center">
                        <i class="fas fa-toggle-on me-2"></i>
                        Statut et disponibilité
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="form-floating mb-3">
                        <select class="form-select <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                id="status"
                                name="status"
                                required>
                            <option value="">Sélectionner un statut</option>
                            <option value="available" <?php echo e(old('status', $driver->status) == 'available' ? 'selected' : ''); ?>>
                                Disponible
                            </option>
                            <option value="unavailable" <?php echo e(old('status', $driver->status) == 'unavailable' ? 'selected' : ''); ?>>
                                Indisponible
                            </option>
                        </select>
                        <label for="status">
                            <i class="fas fa-user-check me-1 text-success"></i>Statut <span class="text-danger">*</span>
                        </label>
                        <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Indicateur visuel du statut -->
                    <div class="status-indicator p-3 rounded-3 text-center" id="status-indicator">
                        <div class="status-icon mb-2">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                        <div class="status-text fw-bold"></div>
                        <small class="status-description text-muted"></small>
                    </div>
                </div>
            </div>

            <!-- Notes -->
            <div class="card shadow-sm border-0 mb-4 animate-card">
                <div class="card-header bg-gradient-info text-white">
                    <h5 class="card-title mb-0 d-flex align-items-center">
                        <i class="fas fa-sticky-note me-2"></i>
                        Notes et observations
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="form-floating">
                        <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                  id="notes"
                                  name="notes"
                                  style="height: 120px"
                                  placeholder="Notes supplémentaires..."><?php echo e(old('notes', $driver->notes)); ?></textarea>
                        <label for="notes">
                            <i class="fas fa-comment me-1 text-info"></i>Notes supplémentaires
                        </label>
                        <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow-sm border-0 animate-card">
                <div class="card-body p-4">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>
                            Enregistrer les modifications
                        </button>
                        <a href="<?php echo e(route('admin.drivers.index')); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            Annuler
                        </a>
                    </div>
                </div>
            </div>
                    </form>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light fixed-bottom">
    <div class="container text-center">
        <span class="text-muted"> <?php echo e(date('Y')); ?> GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Variables CSS personnalisées */
    :root {
        --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --gradient-info: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        --shadow-soft: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        --shadow-hover: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    /* Animations */
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    /* Classes d'animation */
    .animate-card {
        animation: slideInUp 0.6s ease-out;
        animation-fill-mode: both;
    }

    .animate-card:nth-child(1) { animation-delay: 0.1s; }
    .animate-card:nth-child(2) { animation-delay: 0.2s; }
    .animate-card:nth-child(3) { animation-delay: 0.3s; }
    .animate-card:nth-child(4) { animation-delay: 0.4s; }

    /* Cartes avec gradients */
    .bg-gradient-primary {
        background: var(--gradient-primary) !important;
    }

    .bg-gradient-warning {
        background: var(--gradient-warning) !important;
    }

    .bg-gradient-success {
        background: var(--gradient-success) !important;
    }

    .bg-gradient-info {
        background: var(--gradient-info) !important;
    }

    /* Avatar circle */
    .avatar-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        box-shadow: var(--shadow-soft);
    }

    /* Cartes améliorées */
    .card {
        border: none;
        border-radius: 15px;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-soft);
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-hover);
    }

    .card-header {
        border: none;
        padding: 1.5rem;
        border-radius: 15px 15px 0 0 !important;
    }

    /* Form floating amélioré */
    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label,
    .form-floating > .form-select ~ label {
        opacity: 0.85;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    }

    .form-floating > label {
        padding: 1rem 0.75rem;
        font-weight: 500;
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
        font-size: 1rem;
        padding: 1rem 0.75rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        transform: scale(1.02);
    }

    /* Indicateur de statut */
    .status-indicator {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 2px solid #dee2e6;
        transition: all 0.3s ease;
    }

    .status-indicator.available {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        border-color: #28a745;
        color: #155724;
    }

    .status-indicator.unavailable {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        border-color: #dc3545;
        color: #721c24;
    }

    /* Boutons améliorés */
    .btn {
        border-radius: 10px;
        font-weight: 500;
        transition: all 0.3s ease;
        border: none;
        padding: 0.75rem 1.5rem;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-hover);
    }

    .btn-primary {
        background: var(--gradient-primary);
        color: white;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        color: white;
    }

    .btn-lg {
        padding: 1rem 2rem;
        font-size: 1.1rem;
    }

    /* Breadcrumb amélioré */
    .breadcrumb-item a {
        color: #667eea;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a:hover {
        color: #764ba2;
        transform: translateX(5px);
    }

    /* Alertes personnalisées */
    .alert {
        border-radius: 10px;
        border: none;
        font-weight: 500;
    }

    .alert-info {
        background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
        color: #0c5460;
    }

    /* Responsive améliorations */
    @media (max-width: 768px) {
        .avatar-circle {
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
        }

        .card-body {
            padding: 1.5rem !important;
        }

        .btn-lg {
            padding: 0.875rem 1.5rem;
            font-size: 1rem;
        }
    }

    /* Effets de focus améliorés */
    .form-control:focus,
    .form-select:focus {
        animation: pulse 0.6s ease-in-out;
    }

    /* Indicateur de validation */
    .form-control.is-valid,
    .form-select.is-valid {
        border-color: #28a745;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.17z'/%3e%3c/svg%3e");
    }

    /* Texte avec gradient */
    .text-gradient {
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Footer fixe amélioré */
    .footer {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        border-top: 1px solid #dee2e6;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('driverForm');
    const statusSelect = document.getElementById('status');
    const statusIndicator = document.getElementById('status-indicator');
    const licenseExpiryInput = document.getElementById('license_expiry');
    const licenseStatus = document.getElementById('license-status');
    const licenseStatusText = document.getElementById('license-status-text');

    // Configuration des dates
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    licenseExpiryInput.min = tomorrow.toISOString().split('T')[0];

    // Fonction pour mettre à jour l'indicateur de statut
    function updateStatusIndicator() {
        const status = statusSelect.value;
        const statusIcon = statusIndicator.querySelector('.status-icon i');
        const statusText = statusIndicator.querySelector('.status-text');
        const statusDescription = statusIndicator.querySelector('.status-description');

        // Réinitialiser les classes
        statusIndicator.className = 'status-indicator p-3 rounded-3 text-center';

        if (status === 'available') {
            statusIndicator.classList.add('available');
            statusIcon.className = 'fas fa-user-check fa-2x text-success';
            statusText.textContent = 'Disponible';
            statusDescription.textContent = 'Le chauffeur est disponible pour de nouvelles missions';
        } else if (status === 'unavailable') {
            statusIndicator.classList.add('unavailable');
            statusIcon.className = 'fas fa-user-times fa-2x text-danger';
            statusText.textContent = 'Indisponible';
            statusDescription.textContent = 'Le chauffeur n\'est pas disponible actuellement';
        } else {
            statusIcon.className = 'fas fa-user-question fa-2x text-muted';
            statusText.textContent = 'Statut non défini';
            statusDescription.textContent = 'Veuillez sélectionner un statut';
        }
    }

    // Fonction pour vérifier la validité du permis
    function checkLicenseValidity() {
        if (!licenseExpiryInput.value) {
            licenseStatus.className = 'alert alert-warning border-0 d-flex align-items-center';
            licenseStatusText.innerHTML = '<strong>⚠️ Date manquante</strong> Veuillez saisir la date d\'expiration du permis.';
            return;
        }

        const expiryDate = new Date(licenseExpiryInput.value);
        const today = new Date();
        const diffTime = expiryDate - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        licenseStatus.className = 'alert border-0 d-flex align-items-center';

        if (diffDays < 0) {
            licenseStatus.classList.add('alert-danger');
            licenseStatusText.innerHTML = '<strong>⚠️ Permis expiré!</strong> Le permis a expiré il y a ' + Math.abs(diffDays) + ' jour(s).';
        } else if (diffDays <= 30) {
            licenseStatus.classList.add('alert-warning');
            licenseStatusText.innerHTML = '<strong>⚠️ Expiration proche!</strong> Le permis expire dans ' + diffDays + ' jour(s).';
        } else if (diffDays <= 90) {
            licenseStatus.classList.add('alert-info');
            licenseStatusText.innerHTML = '<strong>ℹ️ À surveiller</strong> Le permis expire dans ' + diffDays + ' jour(s).';
        } else {
            licenseStatus.classList.add('alert-success');
            licenseStatusText.innerHTML = '<strong>✅ Permis valide</strong> Le permis expire dans ' + diffDays + ' jour(s).';
        }
    }

    // Validation en temps réel
    function setupRealTimeValidation() {
        const inputs = form.querySelectorAll('input, select, textarea');

        inputs.forEach(input => {
            input.addEventListener('input', function() {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                }
            });

            input.addEventListener('blur', function() {
                if (this.value.trim() !== '' && !this.checkValidity()) {
                    this.classList.add('is-invalid');
                }
            });
        });
    }

    // Animation des cartes au scroll
    function setupScrollAnimations() {
        const cards = document.querySelectorAll('.animate-card');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, { threshold: 0.1 });

        cards.forEach(card => {
            observer.observe(card);
        });
    }

    // Soumission du formulaire avec animation simple
    form.addEventListener('submit', function(e) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;

        // Validation côté client avant soumission
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            showNotification('Veuillez remplir tous les champs obligatoires', 'error');
            return;
        }

        // Animation du bouton de soumission
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enregistrement...';
        submitBtn.disabled = true;

        // Le formulaire sera soumis normalement (pas d'e.preventDefault())
        // Laravel gérera la redirection et les messages
    });

    // Fonction pour afficher les notifications
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove après 5 secondes
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Initialisation
    updateStatusIndicator();
    if (licenseExpiryInput.value) {
        checkLicenseValidity();
    }
    setupRealTimeValidation();
    setupScrollAnimations();

    // Event listeners
    statusSelect.addEventListener('change', updateStatusIndicator);
    licenseExpiryInput.addEventListener('change', checkLicenseValidity);

    // Animation d'entrée pour les cartes
    setTimeout(() => {
        document.querySelectorAll('.animate-card').forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }, 100);
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin_minimal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/admin/drivers/edit.blade.php ENDPATH**/ ?>