// Configuration globale pour SweetAlert2
const Toast = Swal.mixin({
    toast: true,
    position: 'top-end',
    showConfirmButton: false,
    timer: 3000,
    timerProgressBar: true
});

// Configuration globale pour les requêtes AJAX
const ajaxConfig = {
    headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
        'Accept': 'application/json'
    }
};

// Fonction utilitaire pour gérer les erreurs AJAX
function handleAjaxError(error) {
    console.error('Erreur AJAX:', error);
    Toast.fire({
        icon: 'error',
        title: 'Une erreur est survenue'
    });
}
