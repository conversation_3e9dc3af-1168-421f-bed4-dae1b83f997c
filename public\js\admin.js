// Fonctions de gestion des approvisionnements
function confirmValidation(id) {
    console.log('Validation appelée pour ID:', id);
    
    Swal.fire({
        title: 'Validation de l\'approvisionnement',
        text: 'Cette action est irréversible et mettra à jour les stocks. Voulez-vous continuer ?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#198754',
        cancelButtonColor: '#dc3545',
        confirmButtonText: 'Oui, valider',
        cancelButtonText: 'Annuler'
    }).then((result) => {
        if (result.isConfirmed) {
            console.log('Première confirmation OK pour ID:', id);
            
            Swal.fire({
                title: 'Confirmation de sécurité',
                text: 'Pour confirmer la validation et mettre à jour les stocks, veuillez taper "VALIDER" en majuscules',
                input: 'text',
                icon: 'info',
                showCancelButton: true,
                confirmButtonText: 'Confirmer',
                cancelButtonText: 'Annuler',
                confirmButtonColor: '#198754',
                cancelButtonColor: '#dc3545',
                inputValidator: (value) => {
                    if (value !== 'VALIDER') {
                        return 'Veuillez taper exactement "VALIDER" en majuscules';
                    }
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    console.log('Deuxième confirmation OK pour ID:', id);
                    
                    // Créer et soumettre le formulaire
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/admin/supplies/${id}/validateSupply`;
                    
                    // Ajouter le token CSRF
                    const token = document.querySelector('meta[name="csrf-token"]').content;
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_token';
                    csrfInput.value = token;
                    form.appendChild(csrfInput);
                    
                    // Ajouter et soumettre le formulaire
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }
    });
}

function showRejectModal(id) {
    console.log('Rejet appelé pour ID:', id);
    
    const modal = document.getElementById('rejectModal');
    const form = document.getElementById('rejectForm');
    
    if (!modal || !form) {
        console.error('Modal ou formulaire non trouvé');
        return;
    }
    
    form.action = `/admin/supplies/${id}/rejectSupply`;
    console.log('Action du formulaire définie:', form.action);
    
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

// Rendre les fonctions disponibles globalement
window.confirmValidation = confirmValidation;
window.showRejectModal = showRejectModal;

// Gestion des messages flash
function showFlashMessage(type, message) {
    const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.addEventListener('mouseenter', Swal.stopTimer)
            toast.addEventListener('mouseleave', Swal.resumeTimer)
        }
    });

    Toast.fire({
        icon: type,
        title: message
    });
}

// Gestion du menu latéral
document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');
    const topNavbar = document.getElementById('top-navbar');

    if (sidebarToggle && sidebar && mainContent && topNavbar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
            topNavbar.classList.toggle('expanded');
        });
    }

    // Initialisation des tooltips Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Initialisation de l'interface admin
    console.log('Initialisation de l\'interface admin');

    // Sidebar Toggle
    const sidebarToggle2 = document.getElementById('sidebarToggle');

    if (sidebarToggle2) {
        sidebarToggle2.addEventListener('click', function() {
            if (sidebar && mainContent) {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            }
        });
    }

    // Profile Toggle
    const profileToggle = document.querySelector('.profile-toggle');
    if (profileToggle) {
        profileToggle.addEventListener('click', function() {
            this.classList.toggle('active');
        });
    }

    // Gestion des messages flash
    function showAlert(message, type = 'info') {
        if (!message) return;
        
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.role = 'alert';
        
        const messageText = document.createElement('span');
        messageText.textContent = message;
        alertContainer.appendChild(messageText);
        
        const closeButton = document.createElement('button');
        closeButton.type = 'button';
        closeButton.className = 'btn-close';
        closeButton.setAttribute('data-bs-dismiss', 'alert');
        closeButton.setAttribute('aria-label', 'Close');
        alertContainer.appendChild(closeButton);
        
        const container = document.querySelector('.container-fluid') || document.body;
        container.insertBefore(alertContainer, container.firstChild);
        
        // SUPPRESSION du masquage automatique des alertes (pour la validation des ventes à remise)
        // setTimeout(() => {
        //     if (alertContainer && alertContainer.parentNode) {
        //         alertContainer.remove();
        //     }
        // }, 5000);
    }

    // Récupérer les messages flash de la session
    const flashMessages = document.querySelectorAll('.alert');
    flashMessages.forEach(function(alert) {
        const message = alert.textContent.trim();
        const type = alert.classList.contains('alert-success') ? 'success' : 
                    alert.classList.contains('alert-danger') ? 'danger' : 
                    alert.classList.contains('alert-warning') ? 'warning' : 'info';
        // Supprimer l'alerte originale
        // alert.remove();
        // Afficher le nouveau message
        showAlert(message, type);
    });
});

// Validation d'un approvisionnement
function confirmValidation2(supplyId) {
    Swal.fire({
        title: 'Confirmer la validation',
        text: 'Êtes-vous sûr de vouloir valider cet approvisionnement ? Cette action mettra à jour le stock du produit.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#4caf50',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Oui, valider',
        cancelButtonText: 'Annuler'
    }).then((result) => {
        if (result.isConfirmed) {
            // Afficher un indicateur de chargement
            Swal.fire({
                title: 'Validation en cours...',
                html: 'Veuillez patienter pendant la mise à jour du stock',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Envoyer la requête de validation
            fetch(`/accountant/supplies/${supplyId}/validate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'Succès !',
                        text: 'L\'approvisionnement a été validé avec succès.',
                        icon: 'success',
                        confirmButtonColor: '#4caf50'
                    }).then(() => {
                        window.location.reload();
                    });
                } else {
                    throw new Error(data.message || 'Une erreur est survenue lors de la validation');
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                Swal.fire({
                    title: 'Erreur',
                    text: error.message || 'Une erreur est survenue lors de la validation',
                    icon: 'error',
                    confirmButtonColor: '#dc3545'
                });
            });
        }
    });
}

// Rejet d'un approvisionnement
function showRejectModal2(supplyId) {
    const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
    const form = document.getElementById('rejectForm');
    
    // Mettre à jour l'action du formulaire
    form.action = `/accountant/supplies/${supplyId}/reject`;
    
    // Réinitialiser le formulaire
    form.reset();
    
    // Afficher la modal
    modal.show();
    
    // Gérer la soumission du formulaire
    form.onsubmit = function(e) {
        e.preventDefault();
        
        // Afficher un indicateur de chargement
        Swal.fire({
            title: 'Rejet en cours...',
            html: 'Veuillez patienter',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
        
        // Envoyer la requête de rejet
        fetch(form.action, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                rejection_reason: document.getElementById('rejection_reason').value
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                modal.hide();
                Swal.fire({
                    title: 'Succès !',
                    text: 'L\'approvisionnement a été rejeté avec succès.',
                    icon: 'success',
                    confirmButtonColor: '#4caf50'
                }).then(() => {
                    window.location.reload();
                });
            } else {
                throw new Error(data.message || 'Une erreur est survenue lors du rejet');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            modal.hide();
            Swal.fire({
                title: 'Erreur',
                text: error.message || 'Une erreur est survenue lors du rejet',
                icon: 'error',
                confirmButtonColor: '#dc3545'
            });
        });
    };
}

// Gestion des erreurs JavaScript
window.addEventListener('error', function(e) {
    console.error('Erreur JavaScript:', e.error);
    // Ne pas afficher les erreurs de SweetAlert2 ou Bootstrap
    if (!e.error?.stack?.includes('sweetalert2') && !e.error?.stack?.includes('bootstrap')) {
        Swal.fire({
            icon: 'error',
            title: 'Erreur',
            text: 'Une erreur est survenue. Veuillez réessayer.',
            confirmButtonText: 'OK',
            confirmButtonColor: '#dc3545'
        });
    }
});
