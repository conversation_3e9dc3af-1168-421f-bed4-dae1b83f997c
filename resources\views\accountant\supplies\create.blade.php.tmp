@extends('layouts.accountant')

@section('title', 'Nouvel Approvisionnement')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Nouvel Approvisionnement</h1>
        <a href="{{ route('accountant.supplies.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> Retour
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <form action="{{ route('accountant.supplies.store') }}" method="POST" id="supplyForm">
                @csrf

                <!-- Informations de base -->
                <div class="row mb-4">
                    <!-- Catégorie -->
                    <div class="col-md-12 mb-3">
                        <label for="category_id" class="form-label">Catégorie</label>
                        <select name="category_id" id="category_id" class="form-control @error('category_id') is-invalid @enderror" required>
                            <option value="">Sélectionner une catégorie</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('category_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Fournisseur -->
                    <div class="col-md-12 mb-3">
                        <label for="supplier_id" class="form-label">Fournisseur</label>
                        <div class="input-group">
                            <select name="supplier_id" id="supplier_id" class="form-control @error('supplier_id') is-invalid @enderror" required>
                                <option value="">Sélectionner un fournisseur</option>
                                @foreach($suppliers as $supplier)
                                    <option value="{{ $supplier->id }}" {{ old('supplier_id') == $supplier->id ? 'selected' : '' }}>
                                        {{ $supplier->name }}
                                    </option>
                                @endforeach
                            </select>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newSupplierModal">
                                <i class="fas fa-plus"></i>
                            </button>
                            @error('supplier_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Dates -->
                    <div class="col-md-6">
                        <label for="date" class="form-label">Date d'approvisionnement</label>
                        <input type="date" name="date" id="date" 
                               class="form-control @error('date') is-invalid @enderror"
                               value="{{ old('date', date('Y-m-d')) }}" required>
                        @error('date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="expected_delivery_date" class="form-label">Date de livraison prévue</label>
                        <input type="date" name="expected_delivery_date" id="expected_delivery_date" 
                               class="form-control @error('expected_delivery_date') is-invalid @enderror"
                               value="{{ old('expected_delivery_date', date('Y-m-d', strtotime('+1 day'))) }}" required>
                        @error('expected_delivery_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Champs dynamiques selon la catégorie -->
                <div id="categorySpecificFields" class="mb-4">
                    <!-- Les champs seront injectés ici via JavaScript -->
                </div>

                <!-- Notes -->
                <div class="mb-3">
                    <label for="notes" class="form-label">Notes</label>
                    <textarea name="notes" id="notes" 
                              class="form-control @error('notes') is-invalid @enderror"
                              rows="3">{{ old('notes') }}</textarea>
                    @error('notes')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="d-flex justify-content-between align-items-center">
                    <div class="h4 mb-0">Total: <span id="totalAmount">0</span> FCFA</div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('accountant.supplies.index') }}" class="btn btn-secondary">Annuler</a>
                        <button type="submit" class="btn btn-primary">Enregistrer</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Template pour les champs de catégorie standard -->
<template id="standardCategoryTemplate">
    <div class="row">
        <div class="col-md-3">
            <label for="product_id" class="form-label">Produit</label>
            <select name="product_id" class="form-control product-select" required>
                <option value="">Sélectionner un produit</option>
            </select>
        </div>
        <div class="col-md-2">
            <label for="unit" class="form-label">Unité</label>
            <select name="unit" class="form-control" required>
                <option value="">Sélectionner une unité</option>
            </select>
        </div>
        <div class="col-md-2">
            <label for="purchase_price" class="form-label">Prix d'achat</label>
            <input type="number" name="purchase_price" class="form-control" min="0" step="0.01" required>
        </div>
        <div class="col-md-2">
            <label for="quantity" class="form-label">Quantité</label>
            <input type="number" name="quantity" class="form-control" min="0" required>
        </div>
    </div>
</template>

<!-- Template pour les champs de catégorie Fer -->
<template id="ferCategoryTemplate">
    <div class="row">
        <div class="col-md-3">
            <label for="product_id" class="form-label">Produit</label>
            <select name="product_id" class="form-control product-select" required>
                <option value="">Sélectionner un produit</option>
            </select>
        </div>
        <div class="col-md-2">
            <label for="unit" class="form-label">Unité</label>
            <select name="unit" class="form-control" required>
                <option value="">Sélectionner une unité</option>
                <option value="kg">Kg</option>
                <option value="barre">Barre</option>
                <option value="tonne">Tonne</option>
            </select>
        </div>
        <div class="col-md-2">
            <label for="diameter" class="form-label">Diamètre (mm)</label>
            <select name="diameter" class="form-control" required>
                <option value="">Sélectionner</option>
            </select>
        </div>
        <div class="col-md-2">
            <label for="length" class="form-label">Longueur (m)</label>
            <input type="number" name="length" class="form-control" min="0" step="0.01" required>
        </div>
        <div class="col-md-3">
            <label for="unit_price" class="form-label">Prix unitaire (FCFA/barre)</label>
            <input type="number" name="unit_price" class="form-control" min="0" step="0.01" required>
        </div>
    </div>
    <div class="row mt-3">
        <div class="col-md-4">
            <div class="form-text">Unités par tonne: <span class="units-per-ton">0</span> unités</div>
        </div>
        <div class="col-md-4">
            <div class="form-text">Poids par unité: <span class="weight-per-unit">0</span> kg</div>
        </div>
        <div class="col-md-4">
            <div class="form-text">Prix par tonne: <span class="price-per-ton">0</span> FCFA</div>
        </div>
    </div>
</template>

<!-- Template pour les champs de catégorie Ciment -->
<template id="cimentCategoryTemplate">
    <div class="row mb-3">
        <div class="col-md-12">
            <label for="product_id" class="form-label">Produit</label>
            <select name="product_id" class="form-control product-select" required>
                <option value="">Sélectionner un produit</option>
            </select>
        </div>
    </div>
    <div class="accordion" id="regionPricesAccordion">
        @foreach($regions as $region)
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed" type="button" 
                        data-bs-toggle="collapse" 
                        data-bs-target="#region{{ $region->id }}">
                    {{ $region->name }}
                </button>
            </h2>
            <div id="region{{ $region->id }}" class="accordion-collapse collapse" 
                 data-bs-parent="#regionPricesAccordion">
                <div class="accordion-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Villes</label>
                            <div class="city-checkboxes">
                                @foreach($region->cities as $city)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           name="cities[{{ $region->id }}][]" 
                                           value="{{ $city->id }}" 
                                           id="city{{ $city->id }}">
                                    <label class="form-check-label" for="city{{ $city->id }}">
                                        {{ $city->name }}
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="price{{ $region->id }}" class="form-label">Prix d'achat</label>
                            <input type="number" name="prices[{{ $region->id }}]" 
                                   id="price{{ $region->id }}" class="form-control"
                                   min="0" step="0.01">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    </div>
</template>

<!-- Modals -->
@include('accountant.supplies.partials.supplier-modal')

@push('scripts')
<script>
class SupplyManager {
    constructor() {
        this.initializeElements();
        this.initializeEventListeners();
        this.categoryHandlers = {
            'fer': this.handleFerCategory.bind(this),
            'ciment': this.handleCimentCategory.bind(this),
            'standard': this.handleStandardCategory.bind(this)
        };
    }

    initializeElements() {
        this.form = document.getElementById('supplyForm');
        this.categorySelect = document.getElementById('category_id');
        this.categoryFields = document.getElementById('categorySpecificFields');
        this.templates = {
            standard: document.getElementById('standardCategoryTemplate'),
            fer: document.getElementById('ferCategoryTemplate'),
            ciment: document.getElementById('cimentCategoryTemplate')
        };
    }

    initializeEventListeners() {
        this.categorySelect.addEventListener('change', () => this.handleCategoryChange());
        this.form.addEventListener('submit', (e) => this.handleFormSubmit(e));
    }

    handleCategoryChange() {
        const categoryId = this.categorySelect.value;
        if (!categoryId) {
            this.categoryFields.innerHTML = '';
            return;
        }

        const categoryName = this.categorySelect.options[this.categorySelect.selectedIndex].text.toLowerCase();
        const handler = this.categoryHandlers[categoryName] || this.categoryHandlers.standard;
        handler();
    }

    handleStandardCategory() {
        const template = this.templates.standard.content.cloneNode(true);
        this.categoryFields.innerHTML = '';
        this.categoryFields.appendChild(template);
        this.loadProductsForCategory();
    }

    handleFerCategory() {
        const template = this.templates.fer.content.cloneNode(true);
        this.categoryFields.innerHTML = '';
        this.categoryFields.appendChild(template);
        this.loadProductsForCategory();
        this.initializeFerCalculations();
    }

    handleCimentCategory() {
        const template = this.templates.ciment.content.cloneNode(true);
        this.categoryFields.innerHTML = '';
        this.categoryFields.appendChild(template);
        this.loadProductsForCategory();
        this.initializeCimentPrices();
    }

    async loadProductsForCategory() {
        const categoryId = this.categorySelect.value;
        try {
            const response = await fetch(`/api/categories/${categoryId}/products`);
            const products = await response.json();
            const productSelect = this.categoryFields.querySelector('.product-select');
            productSelect.innerHTML = '<option value="">Sélectionner un produit</option>';
            products.forEach(product => {
                const option = new Option(product.name, product.id);
                productSelect.add(option);
            });
        } catch (error) {
            console.error('Erreur lors du chargement des produits:', error);
        }
    }

    initializeFerCalculations() {
        const inputs = this.categoryFields.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('change', () => this.calculateFerMetrics());
        });
    }

    calculateFerMetrics() {
        const diameter = parseFloat(this.categoryFields.querySelector('[name="diameter"]').value) || 0;
        const length = parseFloat(this.categoryFields.querySelector('[name="length"]').value) || 0;
        const unitPrice = parseFloat(this.categoryFields.querySelector('[name="unit_price"]').value) || 0;

        // Calculs pour le fer
        const PI = Math.PI;
        const density = 7.85; // densité de l'acier en g/cm³
        
        if (diameter && length) {
            // Calcul du poids par barre en kg
            const radius = diameter / 2 / 1000; // conversion en mètres
            const volume = PI * radius * radius * length; // en m³
            const weightPerUnit = volume * density * 1000; // conversion en kg
            
            // Calcul des unités par tonne
            const unitsPerTon = 1000 / weightPerUnit;
            
            // Calcul du prix par tonne
            const pricePerTon = unitPrice * unitsPerTon;

            // Mise à jour des affichages
            this.categoryFields.querySelector('.weight-per-unit').textContent = weightPerUnit.toFixed(2);
            this.categoryFields.querySelector('.units-per-ton').textContent = unitsPerTon.toFixed(2);
            this.categoryFields.querySelector('.price-per-ton').textContent = pricePerTon.toLocaleString('fr-FR');
        }
    }

    initializeCimentPrices() {
        const checkboxes = this.categoryFields.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => this.validateCitySelection(checkbox));
        });
    }

    validateCitySelection(checkbox) {
        const regionContainer = checkbox.closest('.accordion-item');
        const priceInput = regionContainer.querySelector('input[type="number"]');
        
        if (checkbox.checked && !priceInput.value) {
            alert('Veuillez spécifier un prix pour cette région avant de sélectionner une ville.');
            checkbox.checked = false;
        }
    }

    handleFormSubmit(e) {
        e.preventDefault();
        
        if (!this.validateForm()) {
            return;
        }

        this.form.submit();
    }

    validateForm() {
        const requiredFields = this.form.querySelectorAll('[required]');
        for (const field of requiredFields) {
            if (!field.value) {
                alert('Veuillez remplir tous les champs requis.');
                field.focus();
                return false;
            }
        }

        const categoryName = this.categorySelect.options[this.categorySelect.selectedIndex].text.toLowerCase();
        if (categoryName === 'ciment') {
            const hasSelectedCity = Array.from(this.categoryFields.querySelectorAll('input[type="checkbox"]'))
                .some(checkbox => checkbox.checked);
            if (!hasSelectedCity) {
                alert('Veuillez sélectionner au moins une ville pour le ciment.');
                return false;
            }
        }

        return true;
    }
}

// Initialiser le gestionnaire d'approvisionnement
document.addEventListener('DOMContentLoaded', () => {
    window.supplyManager = new SupplyManager();
});
</script>
@endpush
@endsection
