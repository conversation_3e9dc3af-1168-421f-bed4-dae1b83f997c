/**
 * GRADIS - Dashboard Comptable Professionnel
 * Styles de base pour l'interface professionnelle du comptable
 * 2025 MOMK-Solutions
 */

:root {
    /* Palette de couleurs professionnelle - Tons de bleu */
    --primary: #1E88E5;
    --primary-light: #64B5F6;
    --primary-dark: #0D47A1;
    --secondary: #26A69A;
    --success: #2E7D32;
    --warning: #F57C00;
    --danger: #D32F2F;
    --info: #0288D1;
    --light: #ECEFF1;
    --dark: #263238;
    --white: #FFFFFF;
    --gray-100: #F8F9FA;
    --gray-200: #E9ECEF;
    --gray-300: #DEE2E6;
    --gray-400: #CED4DA;
    --gray-500: #ADB5BD;
    --gray-600: #6C757D;
    --gray-700: #495057;
    --gray-800: #343A40;
    --gray-900: #212529;
    
    /* Variables RVB pour les manipulations d'opacité */
    --primary-rgb: 30, 136, 229;
    --primary-light-rgb: 100, 181, 246;
    --primary-dark-rgb: 13, 71, 161;
    --success-rgb: 46, 125, 50;
    --warning-rgb: 245, 124, 0;
    --danger-rgb: 211, 47, 47;
    --info-rgb: 2, 136, 209;
    
    /* Dégradés professionnels */
    --gradient-primary: linear-gradient(135deg, #1E88E5 0%, #0D47A1 100%);
    --gradient-success: linear-gradient(135deg, #2E7D32 0%, #1B5E20 100%);
    --gradient-warning: linear-gradient(135deg, #F57C00 0%, #E65100 100%);
    --gradient-danger: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    --gradient-info: linear-gradient(135deg, #0288D1 0%, #01579B 100%);
    
    /* Ombres et effets */
    --card-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    --card-shadow-hover: 0 10px 30px rgba(0, 0, 0, 0.15);
    --card-border-radius: 12px;
    --btn-border-radius: 8px;
    --transition-speed: 0.25s;
    
    /* Espacement et typographie */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
}

body {
    background-color: #F5F7FA;
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    color: var(--gray-800);
    overflow-x: hidden;
}

/* Style du conteneur principal du dashboard */
.accountant-dashboard-wrapper {
    padding-top: 1.5rem;
    padding-bottom: 2rem;
}

/* En-tête de page professionnelle */
.dashboard-header {
    background: var(--gradient-primary);
    color: white;
    border-radius: 12px;
    padding: 2rem 1.5rem;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(13, 71, 161, 0.25);
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 50%);
    z-index: 1;
}

.dashboard-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 15%;
    background: rgba(255,255,255,0.1);
    z-index: 1;
}

.dashboard-header-content {
    position: relative;
    z-index: 2;
}

.dashboard-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    letter-spacing: -0.01em;
}

.dashboard-subtitle {
    font-size: 1rem;
    opacity: 0.85;
    font-weight: 500;
}

.header-actions {
    margin-top: 1.25rem;
    display: flex;
    gap: 0.75rem;
}

.header-action-btn {
    background: rgba(255,255,255,0.15);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    text-decoration: none;
}

.header-action-btn:hover {
    background: rgba(255,255,255,0.25);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    color: white;
}

.dashboard-header-data {
    display: flex;
    justify-content: space-around;
    gap: 0.5rem;
    margin-top: 1rem;
    width: 100%;
}

.header-data-item {
    display: flex;
    flex-direction: column;
    text-align: center;
    flex: 1;
    min-width: 33%;
    padding: 0 0.25rem;
}

.header-data-value {
    font-size: 1.05rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: visible;
}

.header-data-label {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* Cartes de statistiques professionnelles */
.stat-card {
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: var(--card-shadow);
    transition: all var(--transition-speed) ease;
    height: 100%;
    position: relative;
    overflow: hidden;
    padding: 1.5rem;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow-hover);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.stat-icon.primary {
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
}

.stat-icon.success {
    background: rgba(var(--success-rgb), 0.1);
    color: var(--success);
}

.stat-icon.warning {
    background: rgba(var(--warning-rgb), 0.1);
    color: var(--warning);
}

.stat-icon.danger {
    background: rgba(var(--danger-rgb), 0.1);
    color: var(--danger);
}

.stat-icon.info {
    background: rgba(var(--info-rgb), 0.1);
    color: var(--info);
}

.stat-title {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: var(--gray-900);
}

.stat-trend {
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-weight: 600;
}

.stat-trend.up {
    background: rgba(var(--success-rgb), 0.1);
    color: var(--success);
}

.stat-trend.down {
    background: rgba(var(--danger-rgb), 0.1);
    color: var(--danger);
}

.stat-trend.neutral {
    background: rgba(var(--gray-600), 0.1);
    color: var(--gray-600);
}

.stat-subtitle {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-top: 0.25rem;
}
