@extends('layouts.accountant')

@push('styles')
<style>
    .expense-card {
        transition: all 0.3s ease;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 0 15px rgba(0,0,0,0.05);
    }
    
    .expense-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
    }
    
    .status-paid {
        background-color: rgba(0, 200, 81, 0.1);
        color: #00c851;
    }
    
    .status-pending {
        background-color: rgba(255, 152, 0, 0.1);
        color: #ff9800;
    }
    
    .status-cancelled {
        background-color: rgba(255, 53, 71, 0.1);
        color: #ff3547;
    }
    
    .stats-card {
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
        box-shadow: 0 0 15px rgba(0,0,0,0.05);
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15px;
    }
    
    .stats-icon i {
        font-size: 24px;
        color: white;
    }
    
    .filter-section {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 0 15px rgba(0,0,0,0.05);
    }
    
    .table-responsive {
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0,0,0,0.05);
        overflow: hidden;
    }
    
    table.table-hover tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.02);
    }
    
    .progress-bar-container {
        height: 8px;
        background-color: #f5f5f5;
        border-radius: 4px;
        overflow: hidden;
        margin-top: 10px;
    }
    
    .progress-bar {
        height: 100%;
        border-radius: 4px;
    }
    
    .animate-in {
        animation: fadeInUp 0.5s ease forwards;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
@endpush

@section('title', 'Gestion des dépenses')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Gestion des dépenses</h1>
        <a href="{{ route('accountant.expenses.create') }}" class="btn btn-primary shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50 mr-2"></i>Ajouter une dépense
        </a>
    </div>

    <!-- Statistiques -->
    <div class="row animate-in" style="--delay: 0.1s">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="stats-card bg-white">
                <div class="stats-icon bg-primary">
                    <i class="fas fa-wallet"></i>
                </div>
                <h5>Total des dépenses</h5>
                <h3 class="text-primary">{{ number_format($totalExpenses, 0, ',', ' ') }} FCFA</h3>
                <p class="text-muted mb-0">Toutes les dépenses enregistrées</p>
            </div>
        </div>
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="stats-card bg-white">
                <div class="stats-icon bg-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h5>Dépenses payées</h5>
                <h3 class="text-success">{{ number_format($paidExpenses, 0, ',', ' ') }} FCFA</h3>
                <p class="text-muted mb-0">{{ round(($paidExpenses / max($totalExpenses, 1)) * 100) }}% du total</p>
                <div class="progress-bar-container">
                    <div class="progress-bar bg-success" style="width: {{ ($paidExpenses / max($totalExpenses, 1)) * 100 }}%"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="stats-card bg-white">
                <div class="stats-icon bg-warning">
                    <i class="fas fa-clock"></i>
                </div>
                <h5>Dépenses en attente</h5>
                <h3 class="text-warning">{{ number_format($pendingExpenses, 0, ',', ' ') }} FCFA</h3>
                <p class="text-muted mb-0">{{ round(($pendingExpenses / max($totalExpenses, 1)) * 100) }}% du total</p>
                <div class="progress-bar-container">
                    <div class="progress-bar bg-warning" style="width: {{ ($pendingExpenses / max($totalExpenses, 1)) * 100 }}%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="filter-section animate-in" style="--delay: 0.2s">
        <form action="{{ route('accountant.expenses.index') }}" method="GET">
            <div class="row">
                <div class="col-md-3 mb-2">
                    <label for="category" class="form-label small mb-1">Catégorie</label>
                    <select class="form-control" id="category" name="category">
                        <option value="all">Toutes les catégories</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3 mb-2">
                    <label for="status" class="form-label small mb-1">Statut</label>
                    <select class="form-control" id="status" name="status">
                        <option value="all">Tous les statuts</option>
                        <option value="paid" {{ request('status') == 'paid' ? 'selected' : '' }}>Payée</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>En attente</option>
                        <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Annulée</option>
                    </select>
                </div>
                <div class="col-md-2 mb-2">
                    <label for="start_date" class="form-label small mb-1">Date début</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ request('start_date') }}">
                </div>
                <div class="col-md-2 mb-2">
                    <label for="end_date" class="form-label small mb-1">Date fin</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ request('end_date') }}">
                </div>
                <div class="col-md-2 mb-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter mr-2"></i>Filtrer
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Liste des dépenses -->
    <div class="card shadow mb-4 animate-in" style="--delay: 0.3s">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Liste des dépenses</h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                    <div class="dropdown-header">Actions:</div>
                    <a class="dropdown-item" href="#"><i class="fas fa-file-excel mr-2"></i>Exporter Excel</a>
                    <a class="dropdown-item" href="#"><i class="fas fa-file-pdf mr-2"></i>Exporter PDF</a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="#"><i class="fas fa-print mr-2"></i>Imprimer</a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="thead-light">
                        <tr>
                            <th>Référence</th>
                            <th>Titre</th>
                            <th>Catégorie</th>
                            <th>Montant</th>
                            <th>Date</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($expenses as $expense)
                        <tr>
                            <td>{{ $expense->reference_number }}</td>
                            <td>
                                <strong>{{ $expense->title }}</strong>
                                @if ($expense->description)
                                    <br><small class="text-muted">{{ Str::limit($expense->description, 50) }}</small>
                                @endif
                            </td>
                            <td>{{ $expense->category->name ?? 'N/A' }}</td>
                            <td>{{ number_format($expense->amount, 0, ',', ' ') }} FCFA</td>
                            <td>{{ \Carbon\Carbon::parse($expense->date)->format('d/m/Y') }}</td>
                            <td>
                                @if ($expense->status == 'paid')
                                    <span class="status-badge status-paid">Payée</span>
                                @elseif ($expense->status == 'pending')
                                    <span class="status-badge status-pending">En attente</span>
                                @elseif ($expense->status == 'cancelled')
                                    <span class="status-badge status-cancelled">Annulée</span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ route('accountant.expenses.show', $expense) }}" class="btn btn-sm btn-primary" title="Voir">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('accountant.expenses.edit', $expense) }}" class="btn btn-sm btn-info" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('accountant.expenses.print', $expense) }}" class="btn btn-sm btn-secondary" title="Imprimer" target="_blank">
                                        <i class="fas fa-print"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="empty-state">
                                    <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                                    <h4>Aucune dépense trouvée</h4>
                                    <p class="text-muted">Aucune dépense ne correspond à vos critères de recherche.</p>
                                    <a href="{{ route('accountant.expenses.create') }}" class="btn btn-primary mt-2">
                                        <i class="fas fa-plus mr-2"></i>Ajouter une dépense
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-end mt-4">
                {{ $expenses->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Animation des éléments au chargement de la page
        const animatedElements = document.querySelectorAll('.animate-in');
        animatedElements.forEach((element, index) => {
            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // Soumission automatique du formulaire lors du changement des champs de filtrage
        const filterForm = document.querySelector('.filter-section form');
        const filterInputs = filterForm.querySelectorAll('select, input[type="date"]');
        
        filterInputs.forEach(input => {
            input.addEventListener('change', function() {
                filterForm.submit();
            });
        });
    });
</script>
@endpush
