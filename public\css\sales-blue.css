/* Variables globales */
:root {
    --primary-color: #1976D2; /* Bleu principal */
    --primary-dark: #0D47A1;  /* Bleu foncé */
    --secondary-color: #64B5F6; /* Bleu clair */
    --success-color: #00E676; /* Vert vif */
    --warning-color: #FFC107; /* Jaune ambre */
    --danger-color: #FF5252;  /* Rouge vif */
    --info-color: #29B6F6;    /* Bleu ciel */
    --light-color: #FFFFFF;   /* Blanc pur */
    --dark-color: #0D47A1;    /* Bleu très foncé */
    --text-color: #0D47A1;    /* Bleu très foncé */
    --border-radius: 0.75rem;
    --box-shadow: 0 15px 35px rgba(25, 118, 210, 0.15);
    --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    
    /* Types de ventes */
    --direct-sale-color: #1976D2;  /* Bleu principal */
    --discount-sale-color: #FF6B95; /* Rose vif */
    --price-increase-color: #FF9A3C; /* Orange vif */
    --pending-validation-color: #FFC107; /* Jaune ambre */
    --approved-color: #00E676; /* Vert vif */
    --rejected-color: #FF5252; /* Rouge vif */
}

/* Styles généraux */
body {
    background-color: #E3F2FD; /* Fond bleu clair */
    background-image: linear-gradient(to bottom right, #1E88E5, #0D47A1);
    background-attachment: fixed;
    min-height: 100vh;
}

.container-fluid {
    padding: 1.5rem;
    position: relative;
    z-index: 1;
}

/* Carte principale avec design moderne et vif */
.main-card {
    background: linear-gradient(135deg, #1976D2, #64B5F6);
    color: white;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(25, 118, 210, 0.3);
    border: none;
    position: relative;
    animation: cardGlow 8s infinite alternate;
}

@keyframes cardGlow {
    0% {
        box-shadow: 0 15px 35px rgba(25, 118, 210, 0.2);
        background: linear-gradient(135deg, #1976D2, #64B5F6);
    }
    50% {
        box-shadow: 0 20px 45px rgba(13, 71, 161, 0.3);
        background: linear-gradient(135deg, #64B5F6, #1976D2);
    }
    100% {
        box-shadow: 0 15px 35px rgba(25, 118, 210, 0.2);
        background: linear-gradient(135deg, #1976D2, #64B5F6);
    }
}

/* Carte des ventes */
.sales-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius);
    box-shadow: 0 8px 32px rgba(13, 71, 161, 0.2);
    padding: 0;
    overflow: hidden;
    border: none;
}

/* Tableau des ventes avec styles améliorés */
.sales-table {
    margin-bottom: 0;
}

.sales-table th {
    background-color: rgba(25, 118, 210, 0.05);
    color: var(--primary-color);
    font-weight: 600;
    border-top: none;
    padding: 1rem;
}

.sales-table th i {
    margin-right: 0.5rem;
    opacity: 0.7;
}

.sales-table td {
    padding: 1rem;
    vertical-align: middle;
}

/* Styles pour les différents types de ventes */
.sales-table tr.direct-sale {
    background-color: rgba(25, 118, 210, 0.03);
}

.sales-table tr.discount-sale {
    background-color: rgba(255, 107, 149, 0.03);
}

.sales-table tr.price-increase {
    background-color: rgba(255, 154, 60, 0.03);
}

/* Styles pour les statuts de validation */
.sales-table tr.pending {
    border-left: 3px solid var(--pending-validation-color);
}

.sales-table tr.approved {
    border-left: 3px solid var(--approved-color);
}

.sales-table tr.rejected {
    border-left: 3px solid var(--rejected-color);
}

/* Indicateur de statut vertical */
.status-bar {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.pending-bar {
    background-color: var(--pending-validation-color);
}

.approved-bar {
    background-color: var(--approved-color);
}

.rejected-bar {
    background-color: var(--rejected-color);
}

/* Indicateurs de type de vente */
.discount-indicator, .price-increase-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.discount-indicator {
    background-color: var(--discount-sale-color);
}

.price-increase-indicator {
    background-color: var(--price-increase-color);
}

/* Styles des badges avec plus de visibilité */
.badge {
    font-weight: 700;
    padding: 0.6rem 1rem;
    border-radius: 50px;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-direct-sale {
    background: linear-gradient(135deg, var(--direct-sale-color), #1565C0);
    color: white;
}

.badge-discount-sale {
    background: linear-gradient(135deg, var(--discount-sale-color), #FF4081);
    color: white;
}

.badge-price-increase {
    background: linear-gradient(135deg, var(--price-increase-color), #FF8F00);
    color: white;
}

.badge-approved {
    background: linear-gradient(135deg, var(--approved-color), #00C853);
    color: white;
}

.badge-pending {
    background: linear-gradient(135deg, var(--pending-validation-color), #FFB300);
    color: white;
}

.badge-rejected {
    background: linear-gradient(135deg, var(--danger-color), #D50000);
    color: white;
}

/* Styles pour les boutons d'action */
.action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    transition: var(--transition);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin: 0 0.25rem;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.action-btn-view {
    background: linear-gradient(135deg, var(--info-color), #0091EA);
    color: white;
}

.action-btn-edit {
    background: linear-gradient(135deg, var(--success-color), #00b248);
    color: white;
}

.action-btn-delete {
    background: linear-gradient(135deg, var(--danger-color), #c50e29);
    color: white;
}

/* Styles pour les statistiques */
.sales-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 1.25rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.stat-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.stat-item h4 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.9);
}

.stat-item .value {
    font-size: 2rem;
    font-weight: 700;
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
}

.stat-item .unit {
    font-size: 0.9rem;
    opacity: 0.7;
    font-weight: 400;
}

/* Styles pour les filtres */
.filters-section {
    background: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.filters-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 2rem;
    width: 50px;
    height: 3px;
    background: white;
    border-radius: 0 0 3px 3px;
}

.filters-section h5 {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
}

.filter-pills {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    margin-bottom: 1.5rem;
}

.filter-pill {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    padding: 0.6rem 1.25rem;
    border-radius: 50px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-pill:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

.filter-pill.active {
    background: white;
    color: var(--primary-color);
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Barre de recherche stylisée */
.search-box {
    position: relative;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
}

.search-box input {
    background: rgba(255, 255, 255, 0.15);
    border: none;
    color: white;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border-radius: 50px;
    width: 100%;
    transition: var(--transition);
}

.search-box input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.search-box input:focus {
    background: rgba(255, 255, 255, 0.25);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
    outline: none;
}

/* Styles pour la légende des statuts */
.legend-item {
    display: flex;
    flex-direction: column;
    margin-right: 1.5rem;
    margin-bottom: 0.75rem;
}

.legend-item small {
    margin-top: 0.25rem;
}

/* Styles pour les résultats vides */
.no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
    color: var(--secondary-color);
}

.no-results i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-results p {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.no-results small {
    opacity: 0.7;
}

/* Styles pour la pagination */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: var(--primary-color);
    border-radius: 0.5rem;
    margin: 0 0.25rem;
    border: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Media queries pour la responsivité */
@media (max-width: 992px) {
    .sales-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .sales-stats {
        grid-template-columns: 1fr;
    }
    
    .filter-pills {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: 0.5rem;
        -webkit-overflow-scrolling: touch;
    }
}
