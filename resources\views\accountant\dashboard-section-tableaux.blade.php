<!-- Section des tableaux de données récentes -->
<div class="animated-divider mb-4"></div>

<h3 class="section-title mb-4 fade-in">
    <i class="fas fa-table me-2"></i>Données récentes
</h3>

<div class="row mb-4">
    <!-- Tableau des ventes récentes -->
    <div class="col-lg-7">
        <div class="dashboard-card mb-4 slide-in-up" style="--delay: 0.3s">
            <div class="dashboard-card-header">
                <div class="dashboard-card-title">
                    <i class="fas fa-receipt me-2"></i>Ventes récentes
                </div>
                <a href="{{ route('accountant.sales.index') }}" class="btn-link">
                    Voir tout <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
            <div class="dashboard-card-body">
                <div class="mobile-scroll-wrapper">
                    <table class="pro-table table-to-cards">
                        <thead>
                            <tr>
                                <th>Référence</th>
                                <th>Client</th>
                                <th>Date</th>
                                <th>Montant</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($recentSales ?? [] as $sale)
                                <tr>
                                    <td data-label="Référence">#{{ $sale->reference ?? 'N/A' }}</td>
                                    <td data-label="Client">{{ $sale->customer->name ?? 'N/A' }}</td>
                                    <td data-label="Date">{{ $sale->created_at ? $sale->created_at->format('d/m/Y') : 'N/A' }}</td>
                                    <td data-label="Montant">{{ number_format($sale->total_amount ?? 0) }} F</td>
                                    <td data-label="Statut">
                                        @if($sale->payment_status === 'paid')
                                            <span class="status-badge success">Payé</span>
                                        @elseif($sale->payment_status === 'partial')
                                            <span class="status-badge warning">Partiel</span>
                                        @else
                                            <span class="status-badge danger">En attente</span>
                                        @endif
                                    </td>
                                    <td data-label="Actions">
                                        <div class="action-buttons">
                                            <a href="{{ route('accountant.sales.show', $sale->id) }}" class="btn-action primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('accountant.payments.create', ['sale_id' => $sale->id]) }}" class="btn-action success">
                                                <i class="fas fa-money-bill-wave"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="empty-table-message">
                                        <i class="fas fa-info-circle me-2"></i>Aucune vente récente
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Activités récentes et résumé des factures -->
    <div class="col-lg-5">
        <!-- Statuts des factures -->
        <div class="dashboard-card mb-4 slide-in-up" style="--delay: 0.4s">
            <div class="dashboard-card-header">
                <div class="dashboard-card-title">
                    <i class="fas fa-file-invoice-dollar me-2"></i>Statut des factures
                </div>
                <a href="{{ route('accountant.invoices.index') }}" class="btn-link">
                    Gérer <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
            <div class="dashboard-card-body">
                <div class="invoice-statuses-grid">
                    <div class="invoice-status-item">
                        <div class="invoice-status-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="invoice-status-content">
                            <div class="invoice-status-value sparkle">{{ $paidInvoices ?? 0 }}</div>
                            <div class="invoice-status-label">Payées</div>
                        </div>
                    </div>
                    <div class="invoice-status-item">
                        <div class="invoice-status-icon warning">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="invoice-status-content">
                            <div class="invoice-status-value sparkle">{{ $pendingInvoices ?? 0 }}</div>
                            <div class="invoice-status-label">En attente</div>
                        </div>
                    </div>
                    <div class="invoice-status-item">
                        <div class="invoice-status-icon danger">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="invoice-status-content">
                            <div class="invoice-status-value sparkle">{{ $overdueInvoices ?? 0 }}</div>
                            <div class="invoice-status-label">En retard</div>
                        </div>
                    </div>
                    <div class="invoice-status-item">
                        <div class="invoice-status-icon info">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="invoice-status-content">
                            <div class="invoice-status-value sparkle">{{ $totalInvoices ?? 0 }}</div>
                            <div class="invoice-status-label">Total</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Activités récentes -->
        <div class="dashboard-card slide-in-up" style="--delay: 0.5s">
            <div class="dashboard-card-header">
                <div class="dashboard-card-title">
                    <i class="fas fa-history me-2"></i>Activités récentes
                </div>
            </div>
            <div class="dashboard-card-body">
                <div class="activities-list">
                    @forelse($recentActivities ?? [] as $activity)
                        <div class="activity-item">
                            <div class="activity-icon {{ isset($activity['type']) && $activity['type'] == 'sale' ? 'primary' : (isset($activity['type']) && $activity['type'] == 'payment' ? 'success' : 'info') }}">
                                @if(isset($activity['type']) && $activity['type'] == 'sale')
                                    <i class="fas fa-shopping-cart"></i>
                                @elseif(isset($activity['type']) && $activity['type'] == 'payment')
                                    <i class="fas fa-hand-holding-usd"></i>
                                @elseif(isset($activity['type']) && $activity['type'] == 'supply')
                                    <i class="fas fa-truck-loading"></i>
                                @else
                                    <i class="fas fa-file-alt"></i>
                                @endif
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">{{ $activity['description'] ?? 'Activité' }}</div>
                                <div class="activity-meta">
                                    <span class="activity-time"><i class="far fa-clock me-1"></i>{{ isset($activity['date']) ? \Carbon\Carbon::parse($activity['date'])->diffForHumans() : 'N/A' }}</span>
                                    @if(isset($activity['amount']))
                                        <span class="activity-amount">{{ number_format($activity['amount']) }} F</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="empty-activities">
                            <i class="fas fa-info-circle mb-2"></i>
                            <p>Aucune activité récente</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</div>
