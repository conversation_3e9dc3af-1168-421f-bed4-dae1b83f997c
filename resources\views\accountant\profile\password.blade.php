@extends('layouts.accountant')

@section('title', 'Changer le mot de passe')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <!-- <PERSON><PERSON><PERSON> de gauche (navigation du profil) -->
        <div class="col-12 col-lg-3 mb-4">
            <div class="profile-sidebar">
                <div class="profile-sidebar-header">
                    <h5 class="profile-sidebar-title">Mon <PERSON></h5>
                    <p class="profile-sidebar-subtitle">Gérez vos informations personnelles</p>
                </div>
                
                <div class="profile-menu">
                    <a href="{{ route('accountant.profile.show') }}" class="profile-menu-item">
                        <div class="profile-menu-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="profile-menu-content">
                            <span class="profile-menu-label">Informations personnelles</span>
                            <span class="profile-menu-description">Consultez vos informations</span>
                        </div>
                    </a>
                    
                    <a href="{{ route('accountant.profile.edit') }}" class="profile-menu-item">
                        <div class="profile-menu-icon">
                            <i class="fas fa-user-edit"></i>
                        </div>
                        <div class="profile-menu-content">
                            <span class="profile-menu-label">Modifier le profil</span>
                            <span class="profile-menu-description">Mettez à jour vos informations</span>
                        </div>
                    </a>
                    
                    <a href="{{ route('accountant.profile.password') }}" class="profile-menu-item active">
                        <div class="profile-menu-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="profile-menu-content">
                            <span class="profile-menu-label">Mot de passe</span>
                            <span class="profile-menu-description">Modifiez votre mot de passe</span>
                        </div>
                        <div class="profile-menu-indicator"></div>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Colonne de droite (formulaire) -->
        <div class="col-12 col-lg-9">
            @if(session('success'))
                <div class="profile-alert success" id="success-alert">
                    <div class="profile-alert-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="profile-alert-content">
                        {{ session('success') }}
                    </div>
                    <button class="profile-alert-close" onclick="document.getElementById('success-alert').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            @endif
            
            <div class="profile-card">
                <div class="profile-card-header">
                    <h4 class="profile-card-title">
                        <i class="fas fa-key text-primary me-2"></i> Changer le mot de passe
                    </h4>
                    <p class="profile-card-subtitle">Mettez à jour votre mot de passe pour sécuriser votre compte</p>
                </div>

                <div class="profile-card-body">
                    <form action="{{ route('accountant.profile.password.update') }}" method="POST" class="needs-validation" novalidate>
                        @csrf
                        @method('PUT')

                        <div class="password-strength-info mb-4">
                            <div class="d-flex align-items-center mb-3">
                                <div class="password-icon me-3">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div>
                                    <h5 class="password-title mb-1">Sécurité du mot de passe</h5>
                                    <p class="password-description mb-0">Un mot de passe fort doit contenir au moins 8 caractères, incluant des lettres majuscules, minuscules, des chiffres et des caractères spéciaux.</p>
                                </div>
                            </div>
                        </div>

                        <div class="row g-4">
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="current_password" class="form-label">Mot de passe actuel</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                        <input type="password" 
                                               class="form-control @error('current_password') is-invalid @enderror" 
                                               id="current_password" 
                                               name="current_password" 
                                               placeholder="Entrez votre mot de passe actuel"
                                               required>
                                        <button type="button" class="btn btn-outline-secondary toggle-password" tabindex="-1">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        @error('current_password')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-group">
                                    <label for="password" class="form-label">Nouveau mot de passe</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-key"></i></span>
                                        <input type="password" 
                                               class="form-control @error('password') is-invalid @enderror" 
                                               id="password" 
                                               name="password" 
                                               placeholder="Entrez votre nouveau mot de passe"
                                               required>
                                        <button type="button" class="btn btn-outline-secondary toggle-password" tabindex="-1">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        @error('password')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="password-strength mt-2">
                                        <div class="progress" style="height: 5px;">
                                            <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <div class="password-feedback text-muted mt-1 small">Force du mot de passe: <span id="password-strength-text">Non évalué</span></div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-group">
                                    <label for="password_confirmation" class="form-label">Confirmer le nouveau mot de passe</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-check-double"></i></span>
                                        <input type="password" 
                                               class="form-control @error('password_confirmation') is-invalid @enderror" 
                                               id="password_confirmation" 
                                               name="password_confirmation" 
                                               placeholder="Confirmez votre nouveau mot de passe"
                                               required>
                                        <button type="button" class="btn btn-outline-secondary toggle-password" tabindex="-1">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        @error('password_confirmation')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="profile-divider"></div>
                            </div>

                            <!-- Boutons d'action -->
                            <div class="col-12">
                                <div class="profile-actions">
                                    <a href="{{ route('accountant.profile.show') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i> Annuler
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> Mettre à jour le mot de passe
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* Styles pour la page de profil */
    .profile-sidebar {
        background: white;
        border-radius: 16px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        height: 100%;
    }
    
    .profile-sidebar-header {
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .profile-sidebar-title {
        font-weight: 700;
        color: #1E293B;
        margin-bottom: 0.5rem;
    }
    
    .profile-sidebar-subtitle {
        color: #64748B;
        font-size: 0.875rem;
        margin-bottom: 0;
    }
    
    .profile-menu {
        padding: 1rem 0;
    }
    
    .profile-menu-item {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        color: #475569;
        text-decoration: none;
        position: relative;
        transition: all 0.3s ease;
    }
    
    .profile-menu-item:hover {
        background-color: rgba(37, 99, 235, 0.05);
        color: #2563EB;
    }
    
    .profile-menu-item.active {
        background-color: rgba(37, 99, 235, 0.1);
        color: #2563EB;
        font-weight: 600;
    }
    
    .profile-menu-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background-color: rgba(37, 99, 235, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: #2563EB;
        transition: all 0.3s ease;
    }
    
    .profile-menu-item:hover .profile-menu-icon,
    .profile-menu-item.active .profile-menu-icon {
        background-color: #2563EB;
        color: white;
    }
    
    .profile-menu-content {
        flex: 1;
    }
    
    .profile-menu-label {
        display: block;
        font-weight: 500;
        margin-bottom: 0.25rem;
    }
    
    .profile-menu-description {
        display: block;
        font-size: 0.75rem;
        color: #64748B;
    }
    
    .profile-menu-indicator {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 24px;
        background-color: #2563EB;
        border-radius: 4px 0 0 4px;
    }
    
    /* Alerte de profil */
    .profile-alert {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        margin-bottom: 1.5rem;
        animation: slideDown 0.3s ease-out;
    }
    
    .profile-alert.success {
        background-color: rgba(16, 185, 129, 0.1);
        border-left: 4px solid #10B981;
    }
    
    .profile-alert-icon {
        margin-right: 1rem;
        font-size: 1.25rem;
    }
    
    .profile-alert.success .profile-alert-icon {
        color: #10B981;
    }
    
    .profile-alert-content {
        flex: 1;
        color: #1E293B;
    }
    
    .profile-alert-close {
        background: transparent;
        border: none;
        color: #94A3B8;
        cursor: pointer;
        transition: color 0.2s ease;
    }
    
    .profile-alert-close:hover {
        color: #475569;
    }
    
    @keyframes slideDown {
        from { transform: translateY(-20px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }
    
    /* Carte principale */
    .profile-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }
    
    .profile-card-header {
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .profile-card-title {
        font-weight: 700;
        color: #1E293B;
        margin-bottom: 0.5rem;
    }
    
    .profile-card-subtitle {
        color: #64748B;
        font-size: 0.875rem;
        margin-bottom: 0;
    }
    
    .profile-card-body {
        padding: 1.5rem;
    }
    
    /* Séparateur */
    .profile-divider {
        height: 1px;
        background-color: rgba(0, 0, 0, 0.05);
        margin: 1.5rem 0;
    }
    
    /* Formulaire */
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 500;
        color: #1E293B;
        margin-bottom: 0.5rem;
    }
    
    .input-group {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        border-radius: 8px;
        overflow: hidden;
    }
    
    .input-group-text {
        background-color: #F8FAFC;
        border-color: #E2E8F0;
        color: #64748B;
        padding: 0.75rem 1rem;
    }
    
    .form-control {
        border-color: #E2E8F0;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
    }
    
    .form-control:focus {
        border-color: #2563EB;
        box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.25);
    }
    
    /* Boutons d'action */
    .profile-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .btn-outline-secondary {
        border-color: #E2E8F0;
        color: #64748B;
    }
    
    .btn-outline-secondary:hover {
        background-color: #F1F5F9;
        border-color: #CBD5E1;
        color: #475569;
    }
    
    .btn-primary {
        background-color: #2563EB;
        border-color: #2563EB;
    }
    
    .btn-primary:hover {
        background-color: #1D4ED8;
        border-color: #1D4ED8;
    }
    
    /* Styles spécifiques pour la page de mot de passe */
    .password-strength-info {
        background-color: #F8FAFC;
        border-radius: 12px;
        padding: 1rem;
        border-left: 4px solid #3B82F6;
    }
    
    .password-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background-color: rgba(59, 130, 246, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #3B82F6;
        font-size: 1.25rem;
    }
    
    .password-title {
        font-weight: 600;
        color: #1E293B;
    }
    
    .password-description {
        color: #64748B;
        font-size: 0.875rem;
    }
    
    .password-strength .progress-bar {
        transition: width 0.3s ease;
        background-color: #EF4444;
    }
    
    .password-strength .progress-bar.weak {
        background-color: #EF4444;
    }
    
    .password-strength .progress-bar.medium {
        background-color: #F59E0B;
    }
    
    .password-strength .progress-bar.strong {
        background-color: #10B981;
    }
    
    .toggle-password {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }
    
    /* Styles responsives */
    @media (max-width: 992px) {
        .profile-sidebar {
            margin-bottom: 1.5rem;
        }
        
        .profile-actions {
            flex-direction: column;
            gap: 1rem;
        }
        
        .profile-actions a, 
        .profile-actions button {
            width: 100%;
        }
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation du formulaire
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
    
    // Gérer les animations du menu latéral
    const menuItems = document.querySelectorAll('.profile-menu-item');
    menuItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.querySelector('.profile-menu-icon').classList.add('pulse');
        });
        
        item.addEventListener('mouseleave', function() {
            this.querySelector('.profile-menu-icon').classList.remove('pulse');
        });
    });
    
    // Fermeture automatique de l'alerte après 5 secondes
    const alert = document.getElementById('success-alert');
    if (alert) {
        setTimeout(() => {
            alert.classList.add('fade-out');
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    }
    
    // Afficher/masquer le mot de passe
    const toggleButtons = document.querySelectorAll('.toggle-password');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const input = this.previousElementSibling;
            const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
            input.setAttribute('type', type);
            this.querySelector('i').classList.toggle('fa-eye');
            this.querySelector('i').classList.toggle('fa-eye-slash');
        });
    });
    
    // Évaluer la force du mot de passe
    const passwordInput = document.getElementById('password');
    const progressBar = document.querySelector('.password-strength .progress-bar');
    const strengthText = document.getElementById('password-strength-text');
    
    passwordInput.addEventListener('input', function() {
        const password = this.value;
        let strength = 0;
        
        if (password.length >= 8) strength += 1;
        if (password.match(/[a-z]+/)) strength += 1;
        if (password.match(/[A-Z]+/)) strength += 1;
        if (password.match(/[0-9]+/)) strength += 1;
        if (password.match(/[^a-zA-Z0-9]+/)) strength += 1;
        
        let width = 0;
        let text = 'Non évalué';
        let className = '';
        
        if (password.length > 0) {
            switch(strength) {
                case 1:
                    width = 20;
                    text = 'Très faible';
                    className = 'weak';
                    break;
                case 2:
                    width = 40;
                    text = 'Faible';
                    className = 'weak';
                    break;
                case 3:
                    width = 60;
                    text = 'Moyen';
                    className = 'medium';
                    break;
                case 4:
                    width = 80;
                    text = 'Fort';
                    className = 'strong';
                    break;
                case 5:
                    width = 100;
                    text = 'Très fort';
                    className = 'strong';
                    break;
            }
        }
        
        progressBar.style.width = width + '%';
        progressBar.setAttribute('aria-valuenow', width);
        strengthText.textContent = text;
        
        progressBar.classList.remove('weak', 'medium', 'strong');
        if (className) {
            progressBar.classList.add(className);
        }
    });
});
</script>
@endpush
