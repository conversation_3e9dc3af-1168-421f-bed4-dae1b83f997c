// Constantes pour les spécifications de fer
const IRON_SPECS = {
    6: { unitsPerTon: 750, weightPerMeter: 0.222 },
    8: { unitsPerTon: 422, weightPerMeter: 0.395 },
    10: { unitsPerTon: 270, weightPerMeter: 0.617 },
    12: { unitsPerTon: 188, weightPerMeter: 0.888 },
    14: { unitsPerTon: 138, weightPerMeter: 1.208 },
    16: { unitsPerTon: 106, weightPerMeter: 1.578 }
};

// Éléments du formulaire pour le fer
const ironSpecifications = document.getElementById('iron-specifications');
const diameterSelect = document.getElementById('diameter');
const lengthInput = document.getElementById('length');
const unitPriceInput = document.getElementById('unit_price');
const tonPriceInput = document.getElementById('ton_price');
const unitsPerTonInput = document.getElementById('units_per_ton');
const weightPerUnitInput = document.getElementById('weight_per_unit');
const unitsPerTonDisplay = document.getElementById('units-per-ton');
const averageUnitPriceDisplay = document.getElementById('average-unit-price');
const weightPerUnitDisplay = document.getElementById('weight-per-unit');
const basePriceInput = document.getElementById('base_price');

// Fonction pour mettre à jour l'affichage en fonction de la catégorie
function updateDisplay() {
    const selectedOption = categorySelect.options[categorySelect.selectedIndex];
    const isCement = selectedOption.textContent.trim().toLowerCase() === 'ciment';
    const isIron = selectedOption.textContent.trim().toLowerCase() === 'fer';
    
    regionalPricing.style.display = isCement ? 'block' : 'none';
    ironSpecifications.style.display = isIron ? 'block' : 'none';
    cementUnits.style.display = isCement ? 'block' : 'none';
    ironUnits.style.display = isIron ? 'block' : 'none';

    // Réinitialiser les champs si nécessaire
    if (isIron) {
        basePriceInput.readOnly = true;
    } else {
        basePriceInput.readOnly = false;
    }
}

// Fonction pour mettre à jour les calculs de fer
function updateIronCalculations() {
    const diameter = parseInt(diameterSelect.value);
    const length = parseFloat(lengthInput.value) || 12;

    if (diameter && IRON_SPECS[diameter]) {
        const spec = IRON_SPECS[diameter];
        const weightPerUnit = spec.weightPerMeter * length;
        const unitsPerTon = spec.unitsPerTon;

        // Mettre à jour les champs cachés
        unitsPerTonInput.value = unitsPerTon;
        weightPerUnitInput.value = weightPerUnit.toFixed(3);

        // Mettre à jour les affichages
        unitsPerTonDisplay.textContent = unitsPerTon + ' unités';
        weightPerUnitDisplay.textContent = weightPerUnit.toFixed(3) + ' kg';

        // Calculer et afficher les prix
        const unitPrice = parseFloat(unitPriceInput.value) || 0;
        const tonPrice = parseFloat(tonPriceInput.value) || 0;

        if (unitPrice > 0) {
            const calculatedTonPrice = unitPrice * unitsPerTon;
            if (!tonPriceInput.value || parseFloat(tonPriceInput.value) === 0) {
                tonPriceInput.value = Math.round(calculatedTonPrice);
            }
            basePriceInput.value = unitPrice;
        } else if (tonPrice > 0) {
            const calculatedUnitPrice = tonPrice / unitsPerTon;
            if (!unitPriceInput.value || parseFloat(unitPriceInput.value) === 0) {
                unitPriceInput.value = Math.round(calculatedUnitPrice);
            }
            basePriceInput.value = tonPrice;
        }

        if (unitPrice > 0) {
            averageUnitPriceDisplay.textContent = unitPrice.toLocaleString() + ' FCFA/unité';
        }
    }
}

// Écouteurs d'événements pour les calculs de fer
[diameterSelect, lengthInput].forEach(input => {
    input.addEventListener('input', updateIronCalculations);
});

// Écouteur spécifique pour le prix unitaire
unitPriceInput.addEventListener('input', function() {
    this.dataset.lastModified = Date.now();
    const unitPrice = parseFloat(this.value) || 0;
    const unitsPerTon = parseInt(unitsPerTonInput.value) || 0;
    if (unitPrice > 0 && unitsPerTon > 0) {
        tonPriceInput.value = Math.round(unitPrice * unitsPerTon);
        basePriceInput.value = unitPrice;
        unitSelect.value = 'barre';
    }
    updateIronCalculations();
});

// Écouteur spécifique pour le prix par tonne
tonPriceInput.addEventListener('input', function() {
    this.dataset.lastModified = Date.now();
    const tonPrice = parseFloat(this.value) || 0;
    const unitsPerTon = parseInt(unitsPerTonInput.value) || 0;
    if (tonPrice > 0 && unitsPerTon > 0) {
        unitPriceInput.value = Math.round(tonPrice / unitsPerTon);
        basePriceInput.value = tonPrice;
        unitSelect.value = 'tonne';
    }
    updateIronCalculations();
});
