/* Fichier CSS moderne pour le tableau de bord du caissier */
:root {
    /* Palette de couleurs principale */
    --primary-color: #2563EB;
    --primary-light: #60A5FA;
    --primary-dark: #1E40AF;
    --secondary-color: #EFF6FF;
    
    /* Couleurs d'accentuation */
    --accent-color: #8B5CF6;
    --accent-light: #C4B5FD;
    --accent-dark: #6D28D9;
    
    /* Couleurs de statut */
    --success-color: #10B981;
    --success-light: #D1FAE5;
    --warning-color: #F59E0B;
    --warning-light: #FEF3C7;
    --danger-color: #EF4444;
    --danger-light: #FEE2E2;
    --info-color: #0EA5E9;
    --info-light: #E0F2FE;
    
    /* Couleurs de texte et fond */
    --text-color: #1F2937;
    --text-light: #6B7280;
    --text-lighter: #9CA3AF;
    --border-color: #E5E7EB;
    --background-light: #F9FAFB;
    --background-white: #FFFFFF;
    
    /* Effets */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Dégradés */
    --gradient-blue: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    --gradient-purple: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
    --gradient-success: linear-gradient(135deg, var(--success-color), #059669);
    --gradient-warning: linear-gradient(135deg, var(--warning-color), #D97706);
    --gradient-danger: linear-gradient(135deg, var(--danger-color), #DC2626);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Arrondis */
    --rounded-sm: 0.125rem;
    --rounded-md: 0.375rem;
    --rounded-lg: 0.5rem;
    --rounded-xl: 0.75rem;
    --rounded-2xl: 1rem;
    --rounded-3xl: 1.5rem;
    --rounded-full: 9999px;
}

/* Styles généraux */
body {
    color: var(--text-color);
    background-color: var(--background-light);
    font-family: 'Inter', 'Segoe UI', Roboto, -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.dashboard-container {
    padding: 1.75rem 1rem;
    max-width: 1600px;
    margin: 0 auto;
}

/* En-tête de page */
.page-header {
    position: relative;
    margin-bottom: 2rem;
    padding-bottom: 1.25rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.page-header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100px;
    height: 3px;
    background: var(--gradient-blue);
    border-radius: var(--rounded-full);
}

.page-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 0.875rem;
    margin: 0;
}

.page-title i {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42px;
    height: 42px;
    background: var(--gradient-blue);
    color: white;
    border-radius: var(--rounded-xl);
    font-size: 1.25rem;
    box-shadow: var(--shadow-md);
}

.page-subtitle {
    color: var(--text-light);
    font-size: 1.05rem;
    margin: 0.25rem 0 0 0;
    font-weight: 400;
    max-width: 80%;
}

/* Cartes de statistiques */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--background-white);
    border-radius: var(--rounded-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    position: relative;
    transition: all var(--transition-normal);
    border: 1px solid var(--border-color);
}

.stat-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-light);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card.sales::before {
    background: var(--gradient-blue);
}

.stat-card.payments::before {
    background: var(--gradient-success);
}

.stat-card.orders::before {
    background: var(--gradient-purple);
}

.stat-card.credit::before {
    background: var(--gradient-warning);
}

.stat-card-header {
    padding: 1.5rem 1.5rem 0.75rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.stat-card-icon {
    width: 56px;
    height: 56px;
    border-radius: var(--rounded-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.stat-card-icon::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(45deg);
    transition: transform var(--transition-normal);
}

.stat-card:hover .stat-card-icon::after {
    transform: rotate(45deg) translate(50%, 50%);
}

.stat-card-icon.sales {
    background: var(--gradient-blue);
}

.stat-card-icon.payments {
    background: var(--gradient-success);
}

.stat-card-icon.orders {
    background: var(--gradient-purple);
}

.stat-card-icon.credit {
    background: var(--gradient-warning);
}

.stat-card-content {
    padding: 0.75rem 1.5rem 1.5rem;
}

.stat-card-title {
    font-size: 0.875rem;
    color: var(--text-light);
    margin-bottom: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-card-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.stat-card-subtitle {
    font-size: 0.875rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stat-card-progress {
    margin-top: 1.25rem;
}

.progress {
    height: 8px;
    background-color: var(--background-light);
    border-radius: var(--rounded-full);
    overflow: hidden;
    position: relative;
}

.progress-bar {
    height: 100%;
    border-radius: var(--rounded-full);
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3), rgba(255,255,255,0.1));
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

.progress-bar.sales {
    background: var(--gradient-blue);
}

.progress-bar.payments {
    background: var(--gradient-success);
}

.progress-bar.orders {
    background: var(--gradient-purple);
}

.progress-bar.credit {
    background: var(--gradient-warning);
}

/* Sections du tableau de bord */
.dashboard-section {
    background: var(--background-white);
    border-radius: var(--rounded-xl);
    box-shadow: var(--shadow-md);
    margin-bottom: 2rem;
    overflow: hidden;
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    position: relative;
}

.dashboard-section:hover {
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-light);
}

.dashboard-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-blue);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.dashboard-section:hover::before {
    opacity: 1;
}

.dashboard-section-header {
    padding: 1.5rem 1.75rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--background-white);
}

.dashboard-section-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dashboard-section-title i {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: var(--gradient-blue);
    color: white;
    border-radius: var(--rounded-lg);
    font-size: 1rem;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.dashboard-section:hover .dashboard-section-title i {
    transform: scale(1.1);
}

.dashboard-section-body {
    padding: 1.75rem;
    position: relative;
}

.dashboard-section-footer {
    padding: 1.25rem 1.75rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    background-color: var(--background-light);
}

/* Tableaux */
.modern-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 0.95rem;
}

.modern-table th {
    font-weight: 600;
    color: var(--text-color);
    padding: 1.25rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    position: relative;
    background-color: var(--background-white);
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

.modern-table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: var(--border-color);
}

.modern-table td {
    padding: 1.25rem 1rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
    transition: all var(--transition-fast);
}

.modern-table tr:last-child td {
    border-bottom: none;
}

.modern-table tr {
    transition: all var(--transition-fast);
    position: relative;
}

.modern-table tr:hover {
    background-color: var(--secondary-color);
    transform: translateX(4px);
}

.modern-table tr:hover td:first-child {
    position: relative;
}

.modern-table tr:hover td:first-child::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: var(--gradient-blue);
    border-radius: 0 var(--rounded-sm) var(--rounded-sm) 0;
}

/* Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    padding: 0.4rem 0.85rem;
    border-radius: var(--rounded-full);
    font-size: 0.75rem;
    font-weight: 600;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.status-badge:hover::before {
    opacity: 1;
}

.status-badge i {
    font-size: 0.85rem;
}

.status-badge.paid {
    background-color: var(--success-light);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.paid:hover {
    background-color: var(--success-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.25);
}

.status-badge.pending {
    background-color: var(--warning-light);
    color: var(--warning-color);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-badge.pending:hover {
    background-color: var(--warning-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.25);
}

.status-badge.failed {
    background-color: var(--danger-light);
    color: var(--danger-color);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-badge.failed:hover {
    background-color: var(--danger-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.25);
}

.payment-method-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    padding: 0.4rem 0.85rem;
    border-radius: var(--rounded-full);
    font-size: 0.75rem;
    font-weight: 600;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.payment-method-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.payment-method-badge:hover::before {
    opacity: 1;
}

.payment-method-badge i {
    font-size: 0.85rem;
}

.payment-method-badge.cash {
    background-color: var(--success-light);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.payment-method-badge.cash:hover {
    background-color: var(--success-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.25);
}

.payment-method-badge.bank_transfer {
    background-color: var(--info-light);
    color: var(--info-color);
    border: 1px solid rgba(14, 165, 233, 0.2);
}

.payment-method-badge.bank_transfer:hover {
    background-color: var(--info-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(14, 165, 233, 0.25);
}

.payment-method-badge.check {
    background-color: var(--warning-light);
    color: var(--warning-color);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.payment-method-badge.check:hover {
    background-color: var(--warning-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.25);
}

.product-category-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    padding: 0.4rem 0.85rem;
    border-radius: var(--rounded-full);
    font-size: 0.75rem;
    font-weight: 600;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.product-category-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.product-category-badge:hover::before {
    opacity: 1;
}

.product-category-badge i {
    font-size: 0.85rem;
}

.product-category-badge.cement {
    background-color: var(--primary-light);
    color: var(--primary-dark);
    border: 1px solid rgba(37, 99, 235, 0.2);
}

.product-category-badge.cement:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.25);
}

.product-category-badge.iron {
    background-color: rgba(31, 41, 55, 0.1);
    color: var(--text-color);
    border: 1px solid rgba(31, 41, 55, 0.2);
}

.product-category-badge.iron:hover {
    background-color: var(--text-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(31, 41, 55, 0.25);
}

.product-category-badge.other {
    background-color: var(--info-light);
    color: var(--info-color);
    border: 1px solid rgba(14, 165, 233, 0.2);
}

.product-category-badge.other:hover {
    background-color: var(--info-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(14, 165, 233, 0.25);
}

.stock-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--rounded-full);
    font-size: 0.9rem;
    font-weight: 700;
    color: white;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stock-badge::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(45deg);
    transition: transform var(--transition-normal);
}

.stock-badge:hover {
    transform: scale(1.1);
}

.stock-badge:hover::after {
    transform: rotate(45deg) translate(50%, 50%);
}

.stock-badge.high {
    background: var(--gradient-success);
}

.stock-badge.medium {
    background: var(--gradient-warning);
}

.stock-badge.low {
    background: var(--gradient-danger);
}

/* Boutons d'action */
.action-button {
    width: 36px;
    height: 36px;
    border-radius: var(--rounded-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.action-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: scale(0);
    opacity: 0;
    border-radius: 50%;
    transition: transform 0.5s ease, opacity 0.3s ease;
}

.action-button:hover::before {
    transform: scale(2.5);
    opacity: 1;
}

.action-button.view {
    background: var(--gradient-blue);
}

.action-button.edit {
    background: var(--gradient-warning);
}

.action-button.delete {
    background: var(--gradient-danger);
}

.action-button:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: var(--shadow-md);
}

.action-button:active {
    transform: translateY(0) scale(0.95);
}

.action-button i {
    position: relative;
    z-index: 2;
    transition: all var(--transition-normal);
}

.action-button:hover i {
    transform: scale(1.2);
}

.action-buttons {
    display: flex;
    gap: 0.65rem;
}

/* Bouton "Voir tout" */
.view-all-button {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    padding: 0.6rem 1.2rem;
    border-radius: var(--rounded-lg);
    background: var(--gradient-blue);
    color: white;
    font-size: 0.85rem;
    font-weight: 600;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    border: none;
    outline: none;
}

.view-all-button::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: -100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
}

.view-all-button:hover {
    background: var(--gradient-purple);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.view-all-button:hover::after {
    left: 100%;
}

.view-all-button:active {
    transform: translateY(0);
}

.view-all-button i {
    transition: transform var(--transition-normal);
}

.view-all-button:hover i {
    transform: translateX(4px);
}

/* Graphiques */
.chart-container {
    position: relative;
    height: 250px;
    margin-top: 1rem;
}

/* Actions rapides */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.25rem;
    margin-bottom: 2rem;
}

.quick-action-card {
    background: var(--background-white);
    border-radius: var(--rounded-xl);
    box-shadow: var(--shadow-md);
    padding: 1.5rem 1.25rem;
    text-align: center;
    transition: all var(--transition-normal);
    cursor: pointer;
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.quick-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-light);
    opacity: 0;
    z-index: -1;
    transition: opacity var(--transition-normal);
}

.quick-action-card:hover {
    transform: translateY(-8px) scale(1.03);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-light);
}

.quick-action-card:hover::before {
    opacity: 0.05;
}

.quick-action-icon {
    width: 64px;
    height: 64px;
    border-radius: var(--rounded-2xl);
    background: var(--gradient-blue);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 1.25rem;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.quick-action-icon::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(45deg) translateY(-100%);
    transition: transform 0.6s ease;
}

.quick-action-card:hover .quick-action-icon {
    transform: scale(1.1) rotate(5deg);
}

.quick-action-card:hover .quick-action-icon::after {
    transform: rotate(45deg) translateY(0);
}

.quick-action-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    transition: all var(--transition-normal);
}

.quick-action-card:hover .quick-action-title {
    color: var(--primary-dark);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.fade-in-delay-1 {
    animation-delay: 0.1s;
}

.fade-in-delay-2 {
    animation-delay: 0.2s;
}

.fade-in-delay-3 {
    animation-delay: 0.3s;
}

.fade-in-delay-4 {
    animation-delay: 0.4s;
}

/* Responsive */
@media (max-width: 992px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
}
