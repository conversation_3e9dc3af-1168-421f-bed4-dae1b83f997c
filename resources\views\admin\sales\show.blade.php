@extends('layouts.admin_minimal')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-5 fw-bold text-primary">
            <i class="fas fa-receipt me-2"></i>Détails de la Vente <span class="text-secondary">#{{ $sale->id }}</span>
        </h1>
        <div>
            <a href="{{ route('admin.sales.index') }}" class="btn btn-outline-primary rounded-pill shadow-sm">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
        </div>
    </div>

    <div class="row g-4">
        <!-- Bloc général + client -->
        <div class="col-lg-5">
            <div class="card shadow-lg border-0 mb-4">
                <div class="card-header bg-gradient-primary text-white d-flex align-items-center">
                    <i class="fas fa-info-circle me-2"></i>
                    <span class="fw-bold">Informations générales</span>
                </div>
                <div class="card-body bg-light">
                    <ul class="list-group list-group-flush mb-3">
                        <li class="list-group-item bg-transparent d-flex justify-content-between align-items-center">
                            <span>ID</span>
                            <span class="fw-bold text-primary">#{{ $sale->id }}</span>
                        </li>
                        <li class="list-group-item bg-transparent d-flex justify-content-between align-items-center">
                            <span>Date</span>
                            <span><i class="far fa-calendar-alt"></i> {{ $sale->created_at->format('d/m/Y H:i') }}</span>
                        </li>
                        <li class="list-group-item bg-transparent d-flex justify-content-between align-items-center">
                            <span>Vendeur</span>
                            <span><i class="fas fa-user"></i> {{ $sale->user?->name ?? 'N/A' }}</span>
                        </li>
                        <li class="list-group-item bg-transparent d-flex justify-content-between align-items-center">
                            <span>Créé par</span>
                            <span>{{ $sale->createdBy?->name ?? 'N/A' }}</span>
                        </li>
                        <li class="list-group-item bg-transparent d-flex justify-content-between align-items-center">
                            <span>Ville</span>
                            <span><i class="fas fa-map-marker-alt"></i> {{ $sale->city?->name ?? 'N/A' }}</span>
                        </li>
                        <li class="list-group-item bg-transparent d-flex justify-content-between align-items-center">
                            <span>Approvisionnement</span>
                            <span>{{ $sale->supply?->reference ?? 'N/A' }}</span>
                        </li>
                    </ul>
                    <div class="d-flex flex-wrap gap-2">
                        <span class="badge rounded-pill bg-gradient-warning text-dark px-3 py-2">
                            <i class="fas fa-money-check-alt"></i> @switch($sale->payment_status)
                                @case('pending') En attente @break
                                @case('paid') Payé @break
                                @case('cancelled') Annulé @break
                                @default Inconnu
                            @endswitch
                        </span>
                        <span class="badge rounded-pill bg-gradient-info text-dark px-3 py-2">
                            <i class="fas fa-truck"></i> @switch($sale->delivery_status)
                                @case('pending') En attente @break
                                @case('in_progress') En cours @break
                                @case('completed') Livrée @break
                                @default Non spécifié
                            @endswitch
                        </span>
                        <span class="badge rounded-pill bg-gradient-success text-white px-3 py-2">
                            <i class="fas fa-user-shield"></i> @switch($sale->admin_validation_status)
                                @case('pending') En attente @break
                                @case('approved') Validée @break
                                @case('rejected') Rejetée @break
                                @default Non spécifié
                            @endswitch
                        </span>
                    </div>
                </div>
            </div>

            <div class="card shadow-lg border-0 mb-4">
                <div class="card-header bg-gradient-secondary text-white d-flex align-items-center">
                    <i class="fas fa-user me-2"></i>
                    <span class="fw-bold">Client</span>
                </div>
                <div class="card-body bg-light">
                    <div class="d-flex align-items-center mb-3">
                        <div class="avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px; font-size: 1.5rem;">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <div class="fw-bold fs-5 mb-1">{{ $sale->customer_name ?? '-' }}</div>
                            <div class="text-muted small"><i class="fas fa-phone-alt"></i> {{ $sale->customer_phone ?? '-' }}</div>
                            <div class="text-muted small"><i class="fas fa-envelope"></i> {{ $sale->customer_email ?? '-' }}</div>
                        </div>
                    </div>
                    <div>
                        <span class="text-muted"><i class="fas fa-home"></i> Adresse :</span>
                        <span class="fw-semibold ms-2">{{ $sale->customer_address ?? '-' }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bloc produit + paiement -->
        <div class="col-lg-7">
            <div class="card shadow-lg border-0 mb-4">
                <div class="card-header bg-gradient-info text-white d-flex align-items-center">
                    <i class="fas fa-cube me-2"></i>
                    <span class="fw-bold">Détails du produit</span>
                </div>
                <div class="card-body bg-light">
                    <ul class="list-group list-group-flush mb-3">
                        <li class="list-group-item bg-transparent d-flex justify-content-between align-items-center">
                            <span>Produit</span>
                            <span class="fw-bold">{{ $sale->product?->name ?? '-' }}</span>
                        </li>
                        <li class="list-group-item bg-transparent d-flex justify-content-between align-items-center">
                            <span>Quantité</span>
                            <span>{{ $sale->quantity }}</span>
                        </li>
                        <li class="list-group-item bg-transparent d-flex justify-content-between align-items-center">
                            <span>Prix unitaire</span>
                            <span>{{ number_format($sale->unit_price, 2, ',', ' ') }} FCFA</span>
                        </li>
                        <li class="list-group-item bg-transparent d-flex justify-content-between align-items-center">
                            <span>Prix total</span>
                            <span class="fw-bold text-success">
                                {{ number_format(($sale->quantity * $sale->unit_price) - ($sale->discount_total ?? 0), 2, ',', ' ') }} FCFA
                            </span>
                        </li>
                        <li class="list-group-item bg-transparent d-flex justify-content-between align-items-center">
                            <span>Montant avant remise</span>
                            <span>{{ $sale->total_before_discount ? number_format($sale->total_before_discount, 2, ',', ' ') . ' FCFA' : '-' }}</span>
                        </li>
                        <li class="list-group-item bg-transparent d-flex justify-content-between align-items-center">
                            <span>Remise/tonne</span>
                            <span>{{ $sale->discount_per_ton ? number_format($sale->discount_per_ton, 2, ',', ' ') . ' FCFA' : '-' }}</span>
                        </li>
                        <li class="list-group-item bg-transparent d-flex justify-content-between align-items-center">
                            <span>Remise totale</span>
                            <span>{{ $sale->discount_total ? number_format($sale->discount_total, 2, ',', ' ') . ' FCFA' : '-' }}</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="card shadow-lg border-0 mb-4">
                <div class="card-header bg-gradient-success text-white d-flex align-items-center">
                    <i class="fas fa-credit-card me-2"></i>
                    <span class="fw-bold">Paiement</span>
                </div>
                <div class="card-body bg-light">
                    <div class="row mb-2">
                        <div class="col-6">
                            <span class="text-muted">Montant payé</span>
                            <div class="fs-4 fw-bold text-success">{{ number_format($sale->amount_paid, 2, ',', ' ') }} FCFA</div>
                        </div>
                        <div class="col-6">
                            <span class="text-muted">Reste à payer</span>
                            <div class="fs-4 fw-bold text-danger">{{ number_format($sale->total_amount - $sale->amount_paid, 2, ',', ' ') }} FCFA</div>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mt-3">
                        <span class="me-2 text-muted">Méthode :</span>
                        <span class="badge bg-gradient-primary text-white">
                            @switch($sale->payment_method)
                                @case('cash') Espèces @break
                                @case('card') Carte @break
                                @case('transfer') Virement @break
                                @default -
                            @endswitch
                        </span>
                    </div>
                </div>
            </div>

            @if($sale->notes)
                <div class="card shadow-lg border-0 mb-4">
                    <div class="card-header bg-gradient-warning text-dark d-flex align-items-center">
                        <i class="fas fa-sticky-note me-2"></i>
                        <span class="fw-bold">Notes</span>
                    </div>
                    <div class="card-body bg-light">
                        <blockquote class="blockquote mb-0">
                            <p class="mb-0">{{ $sale->notes }}</p>
                        </blockquote>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

@push('styles')
<style>
    .bg-gradient-primary {
        background: linear-gradient(90deg, #4e73df 0%, #224abe 100%) !important;
    }
    .bg-gradient-secondary {
        background: linear-gradient(90deg, #858796 0%, #6c757d 100%) !important;
    }
    .bg-gradient-success {
        background: linear-gradient(90deg, #1cc88a 0%, #138c59 100%) !important;
    }
    .bg-gradient-info {
        background: linear-gradient(90deg, #36b9cc 0%, #258fa2 100%) !important;
    }
    .bg-gradient-warning {
        background: linear-gradient(90deg, #f6c23e 0%, #dda20a 100%) !important;
    }
    .avatar {
        box-shadow: 0 2px 8px rgba(78,115,223,0.15);
    }
    .card-header {
        font-size: 1.1rem;
        letter-spacing: 0.5px;
    }
</style>
@endpush
@endsection
