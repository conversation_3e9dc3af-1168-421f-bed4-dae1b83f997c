<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Rapport des Paiements</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #F6C23E;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #F6C23E;
            font-size: 24px;
            margin: 0;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .summary {
            margin-bottom: 30px;
        }
        .summary-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .summary-item {
            display: table-cell;
            width: 20%;
            text-align: center;
            padding: 10px;
            background-color: #f8f9fc;
            border: 1px solid #e3e6f0;
        }
        .summary-item h3 {
            margin: 0 0 5px 0;
            font-size: 10px;
            color: #5a5c69;
        }
        .summary-item .amount {
            font-size: 12px;
            font-weight: bold;
            color: #F6C23E;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #F6C23E;
            font-size: 14px;
            margin-bottom: 15px;
            border-bottom: 1px solid #e3e6f0;
            padding-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 8px;
        }
        table th, table td {
            border: 1px solid #e3e6f0;
            padding: 4px;
            text-align: left;
        }
        table th {
            background-color: #F6C23E;
            color: white;
            font-weight: bold;
        }
        table tr:nth-child(even) {
            background-color: #f8f9fc;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 7px;
            font-weight: bold;
        }
        .badge-success { background-color: #1cc88a; color: white; }
        .badge-warning { background-color: #f6c23e; color: white; }
        .badge-danger { background-color: #e74a3b; color: white; }
        .badge-info { background-color: #36b9cc; color: white; }
        .badge-secondary { background-color: #858796; color: white; }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 8px;
            color: #666;
            border-top: 1px solid #e3e6f0;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>RAPPORT DES PAIEMENTS</h1>
        <p>Période: {{ \Carbon\Carbon::parse($start_date)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($end_date)->format('d/m/Y') }}</p>
        <p>Généré le {{ \Carbon\Carbon::now()->format('d/m/Y à H:i') }}</p>
    </div>

    <div class="summary">
        <div class="summary-grid">
            <div class="summary-item">
                <h3>Total paiements</h3>
                <div class="amount">{{ number_format($stats['total_payments']) }}</div>
            </div>
            <div class="summary-item">
                <h3>Montant total</h3>
                <div class="amount">{{ number_format($stats['total_amount'], 0, ',', ' ') }} FCFA</div>
            </div>
            <div class="summary-item">
                <h3>Espèces</h3>
                <div class="amount">{{ number_format($stats['cash_payments'], 0, ',', ' ') }} FCFA</div>
            </div>
            <div class="summary-item">
                <h3>Virements</h3>
                <div class="amount">{{ number_format($stats['transfer_payments'], 0, ',', ' ') }} FCFA</div>
            </div>
            <div class="summary-item">
                <h3>En attente</h3>
                <div class="amount">{{ number_format($stats['pending_amount'], 0, ',', ' ') }} FCFA</div>
            </div>
        </div>
    </div>

    @if($payment_methods->count() > 0)
    <div class="section">
        <h2>Répartition par méthode de paiement</h2>
        <table>
            <thead>
                <tr>
                    <th>Méthode</th>
                    <th class="text-center">Nombre</th>
                    <th class="text-right">Montant</th>
                    <th class="text-center">Pourcentage</th>
                </tr>
            </thead>
            <tbody>
                @foreach($payment_methods as $method)
                @php
                    $percentage = $stats['total_amount'] > 0 ? ($method->total / $stats['total_amount']) * 100 : 0;
                    $methodName = [
                        'cash' => 'Espèces',
                        'bank_transfer' => 'Virement bancaire',
                        'check' => 'Chèque',
                        'mobile_money' => 'Mobile Money'
                    ][$method->payment_method] ?? $method->payment_method;
                @endphp
                <tr>
                    <td>{{ $methodName }}</td>
                    <td class="text-center">{{ number_format($method->count) }}</td>
                    <td class="text-right">{{ number_format($method->total, 0, ',', ' ') }} FCFA</td>
                    <td class="text-center">{{ number_format($percentage, 1) }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <div class="section">
        <h2>Liste des paiements ({{ $payments->count() }} résultats)</h2>
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Référence</th>
                    <th>Vente</th>
                    <th>Montant</th>
                    <th>Méthode</th>
                    <th>Statut</th>
                    <th>Caissier</th>
                </tr>
            </thead>
            <tbody>
                @foreach($payments->take(50) as $payment)
                <tr>
                    <td>{{ $payment->payment_date->format('d/m/Y') }}</td>
                    <td>{{ $payment->reference }}</td>
                    <td>{{ $payment->sale ? ($payment->sale->invoice_number ?? 'Vente #' . $payment->sale->id) : 'N/A' }}</td>
                    <td class="text-right">{{ number_format($payment->amount, 0, ',', ' ') }}</td>
                    <td class="text-center">
                        @switch($payment->payment_method)
                            @case('cash')
                                <span class="badge badge-success">Espèces</span>
                                @break
                            @case('bank_transfer')
                                <span class="badge badge-info">Virement</span>
                                @break
                            @case('check')
                                <span class="badge badge-warning">Chèque</span>
                                @break
                            @case('mobile_money')
                                <span class="badge badge-secondary">Mobile Money</span>
                                @break
                            @default
                                {{ $payment->payment_method }}
                        @endswitch
                    </td>
                    <td class="text-center">
                        @switch($payment->status)
                            @case('completed')
                                <span class="badge badge-success">Terminé</span>
                                @break
                            @case('pending')
                                <span class="badge badge-warning">En attente</span>
                                @break
                            @case('cancelled')
                                <span class="badge badge-danger">Annulé</span>
                                @break
                            @default
                                {{ $payment->status }}
                        @endswitch
                    </td>
                    <td>{{ $payment->cashier ? $payment->cashier->name : 'N/A' }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
        @if($payments->count() > 50)
            <p><em>Note: Seuls les 50 premiers paiements sont affichés dans ce rapport PDF.</em></p>
        @endif
    </div>

    <div class="footer">
        <p>GRADIS - Système de gestion des approvisionnements et ventes</p>
        <p>Ce rapport a été généré automatiquement le {{ \Carbon\Carbon::now()->format('d/m/Y à H:i') }}</p>
    </div>
</body>
</html>
