@extends('layouts.cement-manager')

@section('title', 'Ventes à Crédit')

@section('content')
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Ventes à Crédit</h1>

    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Référence</th>
                            <th>Client</th>
                            <th>Produit</th>
                            <th>Quantité</th>
                            <th>Montant Total</th>
                            <th>Chauffeur</th>
                            <th>Camion</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($creditSales as $sale)
                            <tr>
                                <td>{{ $sale->cementOrderDetail->cementOrder->reference }}</td>
                                <td>{{ $sale->cementOrderDetail->customer->name }}</td>
                                <td>{{ $sale->cementOrderDetail->product->name }}</td>
                                <td>{{ number_format($sale->quantity, 2) }} tonnes</td>
                                <td>{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</td>
                                <td>{{ $sale->tripAssignment->driver->name }}</td>
                                <td>{{ $sale->tripAssignment->truck->registration_number }}</td>
                                <td>
                                    <span class="badge badge-{{ $sale->status === 'pending_payment' ? 'warning' : 'success' }}">
                                        {{ $sale->status === 'pending_payment' ? 'En attente de paiement' : 'Payé' }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ route('cement-manager.credit-sales.show', $sale) }}" 
                                       class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> Détails
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center">Aucune vente à crédit trouvée</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-center mt-4">
                {{ $creditSales->links() }}
            </div>
        </div>
    </div>
</div>
@endsection
