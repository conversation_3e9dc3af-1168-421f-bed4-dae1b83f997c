@extends('layouts.cashier')

<!-- Script pour confirmer que nous sommes dans le bon fichier -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('%c VUE PRINCIPALE DES VENTES MODERNISÉE ', 'background: #2196F3; color: white; padding: 10px; font-size: 16px; font-weight: bold; border-radius: 5px;');
        console.log('Fichier: resources/views/cashier/sales/index.blade.php');
        
        // Notification visuelle
        const notification = document.createElement('div');
        notification.style.position = 'fixed';
        notification.style.bottom = '20px';
        notification.style.right = '20px';
        notification.style.backgroundColor = '#2196F3';
        notification.style.color = 'white';
        notification.style.padding = '15px 20px';
        notification.style.borderRadius = '5px';
        notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        notification.style.zIndex = '9999';
        notification.style.opacity = '0';
        notification.style.transition = 'opacity 0.5s ease';
        notification.innerHTML = '<strong>Interface des ventes modernisée</strong><br>Design épuré et intuitif';
        
        document.body.appendChild(notification);
        
        // Afficher puis masquer la notification
        setTimeout(() => { notification.style.opacity = '1'; }, 500);
        setTimeout(() => { notification.style.opacity = '0'; }, 5000);
        setTimeout(() => { document.body.removeChild(notification); }, 5500);
    });
</script>

@section('styles')
<link rel="stylesheet" href="{{ asset('css/cashier-dashboard-modern.css') }}">
<link rel="stylesheet" href="{{ asset('css/cashier-sales-responsive.css') }}">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
@endsection

@section('title', 'Gestion des Ventes')

@section('content')
<div class="container-fluid">
    <!-- En-tête de page avec titre -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-shopping-cart"></i>
            <span>Gestion des Ventes</span>
        </h1>
    </div>
    
    <!-- Statistiques des ventes style Rocker -->
    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-title">Total Ventes</div>
            <div class="stat-value">{{ $sales->total() }}</div>
            <div class="stat-trend positive">
                <i class="fas fa-arrow-up"></i>
                <span>+2.5% depuis la semaine dernière</span>
            </div>
            <div class="stat-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-title">Chiffre d'Affaires</div>
            <div class="stat-value">{{ number_format($sales->sum('total_amount'), 0, ',', ' ') }} FCFA</div>
            <div class="stat-trend positive">
                <i class="fas fa-arrow-up"></i>
                <span>+5.4% depuis la semaine dernière</span>
            </div>
            <div class="stat-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-title">Taux de Conversion</div>
            <div class="stat-value">34.6%</div>
            <div class="stat-trend negative">
                <i class="fas fa-arrow-down"></i>
                <span>-4.5% depuis la semaine dernière</span>
            </div>
            <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-title">Total Clients</div>
            <div class="stat-value">{{ $sales->groupBy('customer_name')->count() }}</div>
            <div class="stat-trend positive">
                <i class="fas fa-arrow-up"></i>
                <span>+8.4% depuis la semaine dernière</span>
            </div>
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
        </div>
    </div>
    
    <!-- Section de filtrage -->
    <div class="filter-section">
        <div class="filter-group">
            <span class="filter-label">Filtrer par type:</span>
            <button class="filter-button active" data-filter="all">
                <i class="fas fa-list"></i> Toutes les ventes
            </button>
            <button class="filter-button" data-filter="direct">
                <i class="fas fa-shopping-bag"></i> Ventes directes
            </button>
            <button class="filter-button" data-filter="discount">
                <i class="fas fa-percentage"></i> Ventes avec remise
            </button>
            <button class="filter-button" data-filter="price-increase">
                <i class="fas fa-arrow-up"></i> Augmentation de prix
            </button>
        </div>
        
        <div class="filter-group">
            <span class="filter-label">Filtrer par validation:</span>
            <button class="filter-button active" data-validation="all">
                <i class="fas fa-list"></i> Tous les statuts
            </button>
            <button class="filter-button" data-validation="pending">
                <i class="fas fa-clock"></i> En attente
            </button>
            <button class="filter-button" data-validation="approved">
                <i class="fas fa-check"></i> Validées
            </button>
            <button class="filter-button" data-validation="rejected">
                <i class="fas fa-times"></i> Rejetées
            </button>
        </div>
    </div>

    <!-- Tableau des ventes récentes style Rocker -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">
                <i class="fas fa-list"></i>
                <span>Ventes Récentes</span>
            </h2>
            <div class="card-actions">
                <button class="btn btn-outline-primary btn-sm" id="exportButton">
                    <i class="fas fa-file-export me-1"></i> Exporter
                </button>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th>Produit</th>
                        <th>Référence</th>
                        <th>Statut</th>
                        <th>Montant Total</th>
                        <th>Déjà Payé</th>
                        <th>Date</th>
                        <th>Progression</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($sales as $sale)
                        @php
                            // Calculer la progression du paiement
                            $totalAmount = $sale->total_amount;
                            $paidAmount = $sale->amount_paid ?? 0;
                            $paymentProgress = $totalAmount > 0 ? ($paidAmount / $totalAmount) * 100 : 0;
                            
                            // Déterminer le statut de paiement et la classe de ligne UNIQUEMENT basé sur le montant payé
                            $statusClass = '';
                            $statusText = '';
                            $statusIcon = '';
                            $rowClass = ''; // Variable pour la classe de la ligne
                            
                            if ($sale->status == 'cancelled') {
                                // Cas spécial pour les ventes annulées
                                $statusClass = 'failed';
                                $statusText = 'Annulé';
                                $statusIcon = 'times-circle';
                                $rowClass = 'row-failed';
                            } else if ($paymentProgress >= 99.9) {
                                // Règlement terminé (100% payé ou presque)
                                $statusClass = 'paid';
                                $statusText = 'Payé';
                                $statusIcon = 'check-circle';
                                $rowClass = 'row-paid'; // Ligne BLEUE pour règlement terminé
                            } else if ($paymentProgress > 0) {
                                // Règlement commencé mais pas terminé
                                $statusClass = 'pending';
                                $statusText = 'En cours';
                                $statusIcon = 'clock';
                                $rowClass = 'row-partially-paid'; // Ligne ORANGE pour règlement commencé
                            } else {
                                // Règlement non démarré (0% payé)
                                $statusClass = 'pending';
                                $statusText = 'Non démarré';
                                $statusIcon = 'exclamation-circle';
                                $rowClass = 'row-not-paid'; // Ligne ROUGE pour règlement non démarré
                            }
                            
                            // Référence de la vente
                            $reference = $sale->invoice_number ?? 'VNT-' . str_pad($sale->id, 6, '0', STR_PAD_LEFT);
                            
                            // Nom du produit
                            $productName = $sale->product ? $sale->product->name : ($sale->supply && $sale->supply->details->isNotEmpty() ? $sale->supply->details->first()->product->name : 'N/A');
                            // Déterminer le type de vente
                            $saleType = 'direct';
                            if (isset($sale->is_discount_sale) && $sale->is_discount_sale) {
                                $saleType = 'discount';
                            } elseif (isset($sale->is_price_increase) && $sale->is_price_increase) {
                                $saleType = 'price-increase';
                            }
                            
                            // Déterminer le statut de validation
                            $validationStatus = 'pending';
                            if (isset($sale->is_validated) && $sale->is_validated) {
                                $validationStatus = 'approved';
                            } elseif (isset($sale->is_rejected) && $sale->is_rejected) {
                                $validationStatus = 'rejected';
                            }
                        @endphp
                        <tr data-sale-type="{{ $saleType }}" data-validation-status="{{ $validationStatus }}" class="{{ $rowClass }}">
                            <td>
                                <div class="d-flex align-items-center">
                                    @if($rowClass == 'row-paid')
                                        <div class="payment-indicator paid me-2" title="Paiement terminé">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                    @elseif($rowClass == 'row-partially-paid')
                                        <div class="payment-indicator partially-paid me-2" title="Paiement en cours">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                    @elseif($rowClass == 'row-not-paid')
                                        <div class="payment-indicator not-paid me-2" title="Paiement non démarré">
                                            <i class="fas fa-exclamation-circle"></i>
                                        </div>
                                    @elseif($rowClass == 'row-failed')
                                        <div class="payment-indicator failed me-2" title="Vente annulée">
                                            <i class="fas fa-times-circle"></i>
                                        </div>
                                    @endif
                                    <div class="product-photo me-2">
                                        <i class="fas fa-box"></i>
                                    </div>
                                    <span>{{ $productName }}</span>
                                </div>
                            </td>
                            <td>#{{ $reference }}</td>
                            <td>
                                <span class="status-badge {{ $statusClass }}">
                                    <i class="fas fa-{{ $statusIcon }}"></i>
                                    {{ $statusText }}
                                </span>
                            </td>
                            <td>{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</td>
                            <td>
                                <div class="amount-paid-container">
                                    <span class="amount-paid {{ $paymentProgress > 0 ? ($paymentProgress < 100 ? 'partially-paid' : 'fully-paid') : 'not-paid' }}">
                                        {{ number_format($paidAmount, 0, ',', ' ') }} FCFA
                                    </span>
                                    @if($paymentProgress > 0 && $paymentProgress < 100)
                                        <span class="amount-remaining">
                                            (Reste: {{ number_format($totalAmount - $paidAmount, 0, ',', ' ') }} FCFA)
                                        </span>
                                    @endif
                                </div>
                            </td>
                            <td>{{ $sale->created_at->format('d M Y') }}</td>
                            <td>
                                <div class="shipping-progress">
                                    <div class="shipping-progress-bar {{ $statusClass }}" style="width: {{ min($paymentProgress, 100) }}%;"></div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex">
                                    <a href="{{ route('cashier.sales.show', $sale) }}" class="action-button view" title="FAIRE LE PAIEMENT">
                                    <i class="fas fa-credit-card"></i>
                                    </a>
                                    <!-- Bouton "Traiter le paiement" supprimé -->
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="py-5">
                                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                    <h4>Aucune vente trouvée</h4>
                                    <p class="text-muted">Aucune vente ne correspond à vos critères de recherche ou aucune vente n'a encore été enregistrée.</p>
                                    <a href="{{ route('cashier.sales.create') }}" class="btn btn-primary mt-3">
                                        <i class="fas fa-plus me-1"></i> Créer une nouvelle vente
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="card-footer bg-white d-flex justify-content-end">
            {{ $sales->links() }}
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Gestion des filtres
        const filterButtons = document.querySelectorAll('.filter-button[data-filter]');
        const validationButtons = document.querySelectorAll('.filter-button[data-validation]');
        const tableRows = document.querySelectorAll('.modern-table tbody tr');
        
        // Fonction pour filtrer les lignes du tableau
        function filterTable() {
            const activeFilterType = document.querySelector('.filter-button[data-filter].active').dataset.filter;
            const activeValidation = document.querySelector('.filter-button[data-validation].active').dataset.validation;
            
            tableRows.forEach(row => {
                const rowType = row.dataset.saleType;
                const rowValidation = row.dataset.validationStatus;
                
                const typeMatch = activeFilterType === 'all' || activeFilterType === rowType;
                const validationMatch = activeValidation === 'all' || activeValidation === rowValidation;
                
                row.style.display = typeMatch && validationMatch ? '' : 'none';
            });
        }
        
        // Gestion des clics sur les boutons de filtre
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                filterTable();
            });
        });
        
        validationButtons.forEach(button => {
            button.addEventListener('click', function() {
                validationButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                filterTable();
            });
        });
        
        // Gestion du bouton d'exportation
        const exportButton = document.getElementById('exportButton');
        if (exportButton) {
            exportButton.addEventListener('click', function() {
                alert('Fonctionnalité d\'exportation à venir');
            });
        }
    });
</script>
@endpush
