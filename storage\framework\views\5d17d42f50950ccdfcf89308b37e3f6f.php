<?php $__env->startSection('title', 'Gestion des Utilisateurs'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
    <!-- Header avec breadcrumb et statistiques -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb bg-transparent p-0 mb-2">
                            <li class="breadcrumb-item">
                                <a href="<?php echo e(route('admin.dashboard')); ?>" class="text-decoration-none">
                                    <i class="fas fa-home me-1"></i>Tableau de bord
                                </a>
                            </li>
                            <li class="breadcrumb-item active">Gestion des utilisateurs</li>
                        </ol>
                    </nav>
                    <h1 class="h3 mb-0 text-gray-800 d-flex align-items-center">
                        <div class="avatar-circle bg-primary me-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <span class="fw-bold">Gestion des utilisateurs</span>
                            <small class="d-block text-muted"><?php echo e($users->total()); ?> utilisateur(s) au total</small>
                        </div>
                    </h1>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary" id="toggleView" title="Changer la vue">
                        <i class="fas fa-th-large" id="viewIcon"></i>
                    </button>
                    <a href="<?php echo e(route('admin.users.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Nouvel utilisateur
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages de session -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show animate-card" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show animate-card" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card bg-gradient-primary text-white animate-card">
                <div class="stats-card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                        <div class="stats-content">
                            <h3 class="stats-number"><?php echo e($users->total()); ?></h3>
                            <p class="stats-label">Total utilisateurs</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card bg-gradient-success text-white animate-card">
                <div class="stats-card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                        <div class="stats-content">
                            <h3 class="stats-number"><?php echo e($users->where('is_active', true)->count()); ?></h3>
                            <p class="stats-label">Utilisateurs actifs</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card bg-gradient-warning text-white animate-card">
                <div class="stats-card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon">
                            <i class="fas fa-user-shield fa-2x"></i>
                        </div>
                        <div class="stats-content">
                            <h3 class="stats-number"><?php echo e($users->filter(function($user) { return $user->hasRole('admin'); })->count()); ?></h3>
                            <p class="stats-label">Administrateurs</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card bg-gradient-info text-white animate-card">
                <div class="stats-card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon">
                            <i class="fas fa-user-plus fa-2x"></i>
                        </div>
                        <div class="stats-content">
                            <h3 class="stats-number"><?php echo e($users->filter(function($user) { return $user->created_at->isToday(); })->count()); ?></h3>
                            <p class="stats-label">Nouveaux aujourd'hui</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres et recherche -->
    <div class="card shadow-sm border-0 mb-4 animate-card">
        <div class="card-header bg-gradient-secondary text-white">
            <h5 class="card-title mb-0 d-flex align-items-center">
                <i class="fas fa-filter me-2"></i>
                Filtres et recherche
            </h5>
        </div>
        <div class="card-body p-4">
            <div class="row g-3">
                <div class="col-md-4">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="searchInput" placeholder="Rechercher...">
                        <label for="searchInput">
                            <i class="fas fa-search me-1"></i>Rechercher par nom ou email
                        </label>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-floating">
                        <select class="form-select" id="roleFilter">
                            <option value="">Tous les rôles</option>
                            <option value="admin">Administrateur</option>
                            <option value="manager">Manager</option>
                            <option value="user">Utilisateur</option>
                        </select>
                        <label for="roleFilter">
                            <i class="fas fa-user-tag me-1"></i>Filtrer par rôle
                        </label>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-floating">
                        <select class="form-select" id="statusFilter">
                            <option value="">Tous les statuts</option>
                            <option value="active">Actif</option>
                            <option value="inactive">Inactif</option>
                        </select>
                        <label for="statusFilter">
                            <i class="fas fa-toggle-on me-1"></i>Filtrer par statut
                        </label>
                    </div>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-secondary w-100 h-100" id="clearFilters">
                        <i class="fas fa-times me-1"></i>Effacer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Vue principale -->
    <div class="main-content-area">
        <!-- Vue tableau (par défaut) -->
        <div id="tableView" class="view-container">
            <div class="card shadow-sm border-0 animate-card">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0 text-gradient">
                            <i class="fas fa-table me-2"></i>Liste des utilisateurs
                        </h5>
                        <small class="text-muted"><?php echo e($users->count()); ?> utilisateur(s) affiché(s)</small>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="usersTable">
                            <thead class="table-light">
                                <tr>
                                    <th class="ps-4">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-hashtag me-2 text-muted"></i>N°
                                        </div>
                                    </th>
                                    <th>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-user me-2 text-muted"></i>Utilisateur
                                        </div>
                                    </th>
                                    <th class="d-none d-lg-table-cell">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-envelope me-2 text-muted"></i>Email
                                        </div>
                                    </th>
                                    <th>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-user-tag me-2 text-muted"></i>Rôles
                                        </div>
                                    </th>
                                    <th class="text-center">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <i class="fas fa-toggle-on me-2 text-muted"></i>Statut
                                        </div>
                                    </th>
                                    <th class="text-center">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <i class="fas fa-calendar me-2 text-muted"></i>Inscription
                                        </div>
                                    </th>
                                    <th class="text-end pe-4">
                                        <div class="d-flex align-items-center justify-content-end">
                                            <i class="fas fa-cogs me-2 text-muted"></i>Actions
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="searchable">
                                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="user-row" data-user-id="<?php echo e($user->id); ?>">
                                    <td class="ps-4">
                                        <span class="fw-bold text-primary"><?php echo e($users->firstItem() + $index); ?></span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="user-avatar me-3">
                                                <?php echo e(strtoupper(substr($user->name, 0, 2))); ?>

                                            </div>
                                            <div>
                                                <div class="fw-bold"><?php echo e($user->name); ?></div>
                                                <div class="text-muted small d-lg-none"><?php echo e($user->email); ?></div>
                                                <div class="text-muted small">
                                                    <i class="fas fa-clock me-1"></i>
                                                    Inscrit <?php echo e($user->created_at->diffForHumans()); ?>

                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="d-none d-lg-table-cell">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-envelope text-muted me-2"></i>
                                            <span><?php echo e($user->email); ?></span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-wrap gap-1">
                                            <?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php
                                                    $roleConfig = [
                                                        'admin' => ['color' => 'danger', 'icon' => 'user-shield', 'name' => 'Administrateur'],
                                                        'accountant' => ['color' => 'primary', 'icon' => 'calculator', 'name' => 'Comptable'],
                                                        'cement_manager' => ['color' => 'success', 'icon' => 'industry', 'name' => 'Gest. Ciment'],
                                                        'iron_manager' => ['color' => 'info', 'icon' => 'hammer', 'name' => 'Gest. Fer'],
                                                        'cashier' => ['color' => 'warning', 'icon' => 'cash-register', 'name' => 'Caissier'],
                                                        'customer_service' => ['color' => 'secondary', 'icon' => 'headset', 'name' => 'Service Client'],
                                                    ];
                                                    $config = $roleConfig[$role->name] ?? ['color' => 'dark', 'icon' => 'user', 'name' => ucfirst($role->name)];
                                                ?>
                                                <span class="badge bg-<?php echo e($config['color']); ?> d-flex align-items-center">
                                                    <i class="fas fa-<?php echo e($config['icon']); ?> me-1"></i>
                                                    <span class="d-none d-md-inline"><?php echo e($config['name']); ?></span>
                                                    <span class="d-inline d-md-none"><?php echo e(substr($config['name'], 0, 4)); ?></span>
                                                </span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="status-indicator <?php echo e($user->is_active ? 'active' : 'inactive'); ?>">
                                            <i class="fas fa-circle me-1"></i>
                                            <span class="d-none d-md-inline"><?php echo e($user->is_active ? 'Actif' : 'Inactif'); ?></span>
                                        </div>
                                    </td>
                                    <td class="text-center d-none d-lg-table-cell">
                                        <div class="text-muted small">
                                            <div><?php echo e($user->created_at->format('d/m/Y')); ?></div>
                                            <div><?php echo e($user->created_at->format('H:i')); ?></div>
                                        </div>
                                    </td>
                                    <td class="text-end pe-4">
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.users.edit', $user)); ?>"
                                               class="btn btn-sm btn-outline-primary"
                                               title="Modifier"
                                               data-bs-toggle="tooltip">
                                                <i class="fas fa-edit"></i>
                                            </a>

                                            <form action="<?php echo e(route('admin.users.toggle-active', $user)); ?>"
                                                  method="POST"
                                                  class="d-inline">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit"
                                                        class="btn btn-sm btn-outline-<?php echo e($user->is_active ? 'warning' : 'success'); ?>"
                                                        title="<?php echo e($user->is_active ? 'Désactiver' : 'Activer'); ?>"
                                                        data-bs-toggle="tooltip">
                                                    <i class="fas <?php echo e($user->is_active ? 'fa-user-slash' : 'fa-user-check'); ?>"></i>
                                                </button>
                                            </form>

                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger"
                                                    title="Supprimer"
                                                    data-bs-toggle="tooltip"
                                                    onclick="confirmDelete(<?php echo e($user->id); ?>, '<?php echo e($user->name); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vue cartes -->
        <div id="cardView" class="view-container d-none">
            <div class="row" id="usersCards">
                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-xl-4 col-lg-6 col-md-6 mb-4 user-card-container" data-user-id="<?php echo e($user->id); ?>">
                    <div class="card user-card shadow-sm border-0 h-100 animate-card">
                        <div class="card-header bg-gradient-primary text-white border-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar-large me-3">
                                        <?php echo e(strtoupper(substr($user->name, 0, 2))); ?>

                                    </div>
                                    <div>
                                        <h6 class="mb-0 fw-bold"><?php echo e($user->name); ?></h6>
                                        <small class="opacity-75"><?php echo e($user->email); ?></small>
                                    </div>
                                </div>
                                <div class="status-indicator <?php echo e($user->is_active ? 'active' : 'inactive'); ?>">
                                    <i class="fas fa-circle"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <h6 class="text-muted mb-2">
                                    <i class="fas fa-user-tag me-1"></i>Rôles
                                </h6>
                                <div class="d-flex flex-wrap gap-1">
                                    <?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            $roleConfig = [
                                                'admin' => ['color' => 'danger', 'icon' => 'user-shield', 'name' => 'Administrateur'],
                                                'accountant' => ['color' => 'primary', 'icon' => 'calculator', 'name' => 'Comptable'],
                                                'cement_manager' => ['color' => 'success', 'icon' => 'industry', 'name' => 'Gest. Ciment'],
                                                'iron_manager' => ['color' => 'info', 'icon' => 'hammer', 'name' => 'Gest. Fer'],
                                                'cashier' => ['color' => 'warning', 'icon' => 'cash-register', 'name' => 'Caissier'],
                                                'customer_service' => ['color' => 'secondary', 'icon' => 'headset', 'name' => 'Service Client'],
                                            ];
                                            $config = $roleConfig[$role->name] ?? ['color' => 'dark', 'icon' => 'user', 'name' => ucfirst($role->name)];
                                        ?>
                                        <span class="badge bg-<?php echo e($config['color']); ?> d-flex align-items-center">
                                            <i class="fas fa-<?php echo e($config['icon']); ?> me-1"></i>
                                            <?php echo e($config['name']); ?>

                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>

                            <div class="mb-3">
                                <h6 class="text-muted mb-2">
                                    <i class="fas fa-info-circle me-1"></i>Informations
                                </h6>
                                <div class="row g-2">
                                    <div class="col-6">
                                        <div class="info-item">
                                            <small class="text-muted">Statut</small>
                                            <div class="fw-bold <?php echo e($user->is_active ? 'text-success' : 'text-danger'); ?>">
                                                <i class="fas fa-circle me-1"></i>
                                                <?php echo e($user->is_active ? 'Actif' : 'Inactif'); ?>

                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="info-item">
                                            <small class="text-muted">Inscription</small>
                                            <div class="fw-bold"><?php echo e($user->created_at->format('d/m/Y')); ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-light border-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    Inscrit <?php echo e($user->created_at->diffForHumans()); ?>

                                </small>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('admin.users.edit', $user)); ?>"
                                       class="btn btn-sm btn-primary"
                                       title="Modifier"
                                       data-bs-toggle="tooltip">
                                        <i class="fas fa-edit"></i>
                                    </a>

                                    <form action="<?php echo e(route('admin.users.toggle-active', $user)); ?>"
                                          method="POST"
                                          class="d-inline">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit"
                                                class="btn btn-sm btn-<?php echo e($user->is_active ? 'warning' : 'success'); ?>"
                                                title="<?php echo e($user->is_active ? 'Désactiver' : 'Activer'); ?>"
                                                data-bs-toggle="tooltip">
                                            <i class="fas <?php echo e($user->is_active ? 'fa-user-slash' : 'fa-user-check'); ?>"></i>
                                        </button>
                                    </form>

                                    <button type="button"
                                            class="btn btn-sm btn-danger"
                                            title="Supprimer"
                                            data-bs-toggle="tooltip"
                                            onclick="confirmDelete(<?php echo e($user->id); ?>, '<?php echo e($user->name); ?>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <div class="d-flex justify-content-center mt-4">
        <?php echo e($users->links()); ?>

    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Confirmer la suppression
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer l'utilisateur <strong id="userName"></strong> ?</p>
                <p class="text-muted">Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteForm" method="POST" class="d-inline">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>Supprimer
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light fixed-bottom">
    <div class="container text-center">
        <span class="text-muted"> <?php echo e(date('Y')); ?> GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>

<?php $__env->startPush('styles'); ?>
<style>
/* Variables CSS personnalisées */
:root {
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --gradient-info: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --gradient-danger: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    --shadow-soft: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-hover: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --border-radius: 15px;
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Classes d'animation */
.animate-card {
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.animate-card:nth-child(1) { animation-delay: 0.1s; }
.animate-card:nth-child(2) { animation-delay: 0.2s; }
.animate-card:nth-child(3) { animation-delay: 0.3s; }
.animate-card:nth-child(4) { animation-delay: 0.4s; }

/* Avatar circle */
.avatar-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: var(--shadow-soft);
}

/* Cartes de statistiques */
.stats-card {
    border: none;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.stats-card-body {
    padding: 2rem;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
}

.stats-label {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

/* Gradients pour les cartes */
.bg-gradient-primary { background: var(--gradient-primary) !important; }
.bg-gradient-secondary { background: var(--gradient-secondary) !important; }
.bg-gradient-success { background: var(--gradient-success) !important; }
.bg-gradient-warning { background: var(--gradient-warning) !important; }
.bg-gradient-info { background: var(--gradient-info) !important; }
.bg-gradient-danger { background: var(--gradient-danger) !important; }

/* Cartes améliorées */
.card {
    border: none;
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-soft);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.card-header {
    border: none;
    padding: 1.5rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
}

/* Form floating amélioré */
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
    opacity: 0.85;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.form-floating > label {
    padding: 1rem 0.75rem;
    font-weight: 500;
}

.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    font-size: 1rem;
    padding: 1rem 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: scale(1.02);
}

/* Avatars utilisateur */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    box-shadow: var(--shadow-soft);
}

.user-avatar-large {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
    box-shadow: var(--shadow-soft);
}

/* Indicateurs de statut */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.status-indicator.active {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.status-indicator.inactive {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

/* Tableau amélioré */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    border: none;
    background: #f8f9fa;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: 1rem;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #f8f9fa;
}

.table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateX(5px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border: none;
}

/* Badges améliorés */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
    border-radius: 20px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.badge:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-soft);
}

/* Boutons améliorés */
.btn {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    padding: 0.5rem 1rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-group .btn {
    border-radius: 8px;
    margin: 0 2px;
}

/* Vue cartes utilisateur */
.user-card {
    transition: all 0.3s ease;
    height: 100%;
}

.user-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.user-card .card-header {
    background: var(--gradient-primary);
    color: white;
}

.info-item {
    text-align: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

/* Breadcrumb amélioré */
.breadcrumb-item a {
    color: #667eea;
    text-decoration: none;
    transition: all 0.3s ease;
}

.breadcrumb-item a:hover {
    color: #764ba2;
    transform: translateX(5px);
}

/* Texte avec gradient */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Responsive */
@media (max-width: 768px) {
    .stats-card-body {
        padding: 1.5rem;
    }

    .stats-number {
        font-size: 1.5rem;
    }

    .avatar-circle {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .user-avatar {
        width: 35px;
        height: 35px;
        font-size: 0.8rem;
    }

    .card-body {
        padding: 1.5rem !important;
    }

    .table tbody tr:hover {
        transform: none;
    }

    .main-content-area {
        margin-top: 1rem;
    }

    .view-toggle-container {
        margin-bottom: 1rem;
    }
}

/* Animation pour les filtres */
.form-floating {
    position: relative;
    overflow: hidden;
}

.form-floating::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.5s;
    z-index: 1;
}

.form-floating:hover::before {
    left: 100%;
}

/* Pagination améliorée */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #667eea;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: var(--gradient-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-soft);
}

.pagination .page-item.active .page-link {
    background: var(--gradient-primary);
    border-color: transparent;
}

/* Modal amélioré */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--shadow-hover);
}

.modal-header {
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* Effets de focus améliorés */
.form-control:focus,
.form-select:focus {
    animation: pulse 0.6s ease-in-out;
}

/* Vue container */
.view-container {
    transition: all 0.5s ease;
}

.main-content-area {
    min-height: 400px;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Variables globales
    const tableView = document.getElementById('tableView');
    const cardView = document.getElementById('cardView');
    const toggleViewBtn = document.getElementById('toggleView');
    const viewIcon = document.getElementById('viewIcon');
    const searchInput = document.getElementById('searchInput');
    const roleFilter = document.getElementById('roleFilter');
    const statusFilter = document.getElementById('statusFilter');
    const clearFiltersBtn = document.getElementById('clearFilters');

    let currentView = 'table'; // 'table' ou 'card'

    // Initialiser les tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Basculer entre vue tableau et vue cartes
    if (toggleViewBtn) {
        toggleViewBtn.addEventListener('click', function() {
            if (currentView === 'table') {
                // Passer à la vue cartes
                tableView.classList.add('d-none');
                cardView.classList.remove('d-none');
                viewIcon.className = 'fas fa-table';
                toggleViewBtn.title = 'Vue tableau';
                currentView = 'card';

                // Animation d'entrée pour les cartes
                animateCards();
            } else {
                // Passer à la vue tableau
                cardView.classList.add('d-none');
                tableView.classList.remove('d-none');
                viewIcon.className = 'fas fa-th-large';
                toggleViewBtn.title = 'Vue cartes';
                currentView = 'table';
            }

            // Sauvegarder la préférence
            localStorage.setItem('usersViewPreference', currentView);
        });

        // Restaurer la préférence de vue
        const savedView = localStorage.getItem('usersViewPreference');
        if (savedView === 'card') {
            toggleViewBtn.click();
        }
    }

    // Animation des cartes
    function animateCards() {
        const cards = document.querySelectorAll('.user-card-container');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // Fonction de recherche et filtrage
    function filterUsers() {
        const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
        const selectedRole = roleFilter ? roleFilter.value.toLowerCase() : '';
        const selectedStatus = statusFilter ? statusFilter.value.toLowerCase() : '';

        // Filtrer la vue tableau
        const tableRows = document.querySelectorAll('#usersTable tbody .user-row');
        let visibleTableRows = 0;

        tableRows.forEach(row => {
            const userName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
            const userEmail = row.querySelector('td:nth-child(3)')?.textContent.toLowerCase() || '';
            const userRoles = row.querySelector('td:nth-child(4)').textContent.toLowerCase();
            const userStatus = row.querySelector('.status-indicator').classList.contains('active') ? 'active' : 'inactive';

            const matchesSearch = userName.includes(searchTerm) || userEmail.includes(searchTerm);
            const matchesRole = !selectedRole || userRoles.includes(selectedRole);
            const matchesStatus = !selectedStatus || userStatus === selectedStatus;

            if (matchesSearch && matchesRole && matchesStatus) {
                row.style.display = '';
                visibleTableRows++;
            } else {
                row.style.display = 'none';
            }
        });

        // Filtrer la vue cartes
        const cardContainers = document.querySelectorAll('.user-card-container');
        let visibleCards = 0;

        cardContainers.forEach(container => {
            const card = container.querySelector('.user-card');
            const userName = card.querySelector('.card-header h6').textContent.toLowerCase();
            const userEmail = card.querySelector('.card-header small').textContent.toLowerCase();
            const userRoles = card.querySelector('.card-body').textContent.toLowerCase();
            const userStatus = card.querySelector('.status-indicator').classList.contains('active') ? 'active' : 'inactive';

            const matchesSearch = userName.includes(searchTerm) || userEmail.includes(searchTerm);
            const matchesRole = !selectedRole || userRoles.includes(selectedRole);
            const matchesStatus = !selectedStatus || userStatus === selectedStatus;

            if (matchesSearch && matchesRole && matchesStatus) {
                container.style.display = '';
                visibleCards++;
            } else {
                container.style.display = 'none';
            }
        });

        // Mettre à jour le compteur
        const counter = document.querySelector('.card-title small');
        if (counter) {
            const count = currentView === 'table' ? visibleTableRows : visibleCards;
            counter.textContent = `${count} utilisateur(s) affiché(s)`;
        }
    }

    // Event listeners pour les filtres
    if (searchInput) searchInput.addEventListener('input', filterUsers);
    if (roleFilter) roleFilter.addEventListener('change', filterUsers);
    if (statusFilter) statusFilter.addEventListener('change', filterUsers);

    // Effacer les filtres
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', function() {
            if (searchInput) searchInput.value = '';
            if (roleFilter) roleFilter.value = '';
            if (statusFilter) statusFilter.value = '';
            filterUsers();

            // Animation de nettoyage
            [searchInput, roleFilter, statusFilter].forEach(input => {
                if (input) {
                    input.style.transform = 'scale(1.05)';
                    setTimeout(() => {
                        input.style.transform = 'scale(1)';
                    }, 200);
                }
            });
        });
    }

    // Fonction de confirmation de suppression
    window.confirmDelete = function(userId, userName) {
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        document.getElementById('userName').textContent = userName;
        document.getElementById('deleteForm').action = `/admin/users/${userId}`;
        modal.show();
    };

    // Animation d'entrée pour les statistiques
    function animateStats() {
        const statsCards = document.querySelectorAll('.stats-card');
        statsCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 150);
        });
    }

    // Démarrer les animations
    setTimeout(() => {
        animateStats();
    }, 100);

    console.log('🚀 Interface utilisateurs chargée avec succès!');
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin_minimal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/admin/users/index.blade.php ENDPATH**/ ?>