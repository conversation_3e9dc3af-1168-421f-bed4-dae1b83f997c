@extends('layouts.admin_minimal')

@php
// Si la variable $user n'est pas définie, utiliser Auth::user()
if (!isset($user)) {
    $user = \Illuminate\Support\Facades\Auth::user();
}
@endphp

@section('title', 'Modifier le profil')

@section('content')
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 fw-bold text-primary">
                        <i class="fas fa-user-edit me-2"></i> Modifier mon profil
                    </h5>
                    <a href="{{ route('admin.profile.show') }}" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Retour
                    </a>
                </div>
                <div class="card-body p-4">
                    <form action="{{ route('admin.profile.update') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="text-center mb-4">
                            <div class="avatar-upload-container mx-auto position-relative">
                                <div class="avatar-preview rounded-circle mb-3 mx-auto">
                                    @if($user->avatar && file_exists(public_path($user->avatar)))
                                        <img src="{{ asset($user->avatar) }}" 
                                             alt="Photo de profil" 
                                             id="avatar-preview"
                                             class="rounded-circle">
                                    @else
                                        <div class="avatar-placeholder" id="avatar-placeholder">
                                            {{ substr($user->name, 0, 1) }}
                                        </div>
                                        <img src="" 
                                             alt="Photo de profil" 
                                             id="avatar-preview"
                                             class="rounded-circle d-none">
                                    @endif
                                </div>
                                
                                <div class="avatar-edit position-absolute">
                                    <label for="avatar" class="btn btn-sm btn-primary rounded-circle">
                                        <i class="fas fa-camera"></i>
                                    </label>
                                    <input type="file" 
                                           id="avatar" 
                                           name="avatar" 
                                           class="d-none" 
                                           accept="image/*"
                                           onchange="previewImage(this)">
                                </div>
                            </div>
                            
                            <p class="text-muted small">Cliquez sur l'icône de caméra pour changer votre photo de profil</p>
                            
                            @error('avatar')
                                <div class="text-danger mt-2 small">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Nom complet</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" 
                                           class="form-control @error('name') is-invalid @enderror" 
                                           id="name" 
                                           name="name" 
                                           value="{{ old('name', $user->name) }}" 
                                           required>
                                </div>
                                @error('name')
                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input type="email" 
                                           class="form-control @error('email') is-invalid @enderror" 
                                           id="email" 
                                           name="email" 
                                           value="{{ old('email', $user->email) }}" 
                                           required>
                                </div>
                                @error('email')
                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Téléphone</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                    <input type="text" 
                                           class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" 
                                           name="phone" 
                                           value="{{ old('phone', $user->phone) }}">
                                </div>
                                @error('phone')
                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="position" class="form-label">Poste</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-briefcase"></i></span>
                                    <input type="text" 
                                           class="form-control @error('position') is-invalid @enderror" 
                                           id="position" 
                                           name="position" 
                                           value="{{ old('position', $user->position) }}">
                                </div>
                                @error('position')
                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="{{ route('admin.profile.show') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i> Annuler
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Enregistrer les modifications
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.avatar-upload-container {
    width: 150px;
    height: 150px;
}

.avatar-preview {
    width: 150px;
    height: 150px;
    overflow: hidden;
    border: 3px solid #f8f9fa;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    background-color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    font-weight: 600;
}

.avatar-edit {
    bottom: 5px;
    right: 5px;
}

.avatar-edit .btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}
</style>
@endpush

@push('scripts')
<script>
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            const preview = document.getElementById('avatar-preview');
            const placeholder = document.getElementById('avatar-placeholder');
            
            preview.src = e.target.result;
            preview.classList.remove('d-none');
            
            if (placeholder) {
                placeholder.classList.add('d-none');
            }
        }
        
        reader.readAsDataURL(input.files[0]);
    }
}
</script>
@endpush
@endsection
