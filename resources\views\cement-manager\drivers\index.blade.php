@extends('layouts.cement-manager')

@section('title', 'Liste des Chauffeurs')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Gestion des Chauffeurs</h1>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Liste des Chauffeurs</h6>
                    <div class="card-tools">
                        <a href="{{ route('cement-manager.drivers.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Ajouter un Chauffeur
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Nom Complet</th>
                                    <th>Téléphone</th>
                                    <th>N° Permis</th>
                                    <th>Expiration Permis</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($drivers as $driver)
                                    <tr>
                                        <td>{{ $driver->first_name }} {{ $driver->last_name }}</td>
                                        <td>{{ $driver->phone_number }}</td>
                                        <td>{{ $driver->license_number }}</td>
                                        <td>{{ $driver->license_expiry->format('d/m/Y') }}</td>
                                        <td>
                                            @switch($driver->status)
                                                @case('available')
                                                    <span class="badge bg-success">Disponible</span>
                                                    @break
                                                @case('busy')
                                                    <span class="badge bg-warning">En mission</span>
                                                    @break
                                                @case('off_duty')
                                                    <span class="badge bg-danger">En congé</span>
                                                    @break
                                                @case('maintenance')
                                                    <span class="badge bg-info">En maintenance</span>
                                                    @break
                                                @default
                                                    <span class="badge bg-secondary">Inconnu</span>
                                            @endswitch
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ route('cement-manager.drivers.show', $driver->id) }}" 
                                                   class="btn btn-info btn-sm" 
                                                   title="Voir les détails">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">Aucun chauffeur enregistré</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    @if($drivers->hasPages())
                        <div class="mt-4">
                            {{ $drivers->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Êtes-vous sûr de vouloir supprimer ce chauffeur ?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Supprimer</button>
            </div>
        </div>
    </div>
</div>

<!-- Toast de notification -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="alertToast" class="toast align-items-center text-white bg-success" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const toast = new bootstrap.Toast(document.getElementById('alertToast'));
    let driverIdToDelete = null;

    // Gestionnaire pour les boutons de suppression
    document.querySelectorAll('.delete-driver').forEach(button => {
        button.addEventListener('click', function() {
            driverIdToDelete = this.dataset.id;
            deleteModal.show();
        });
    });

    // Gestionnaire pour la confirmation de suppression
    document.getElementById('confirmDelete').addEventListener('click', async function() {
        if (!driverIdToDelete) return;

        try {
            const response = await fetch(`/cement-manager/drivers/${driverIdToDelete}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                }
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.message || 'Une erreur est survenue');
            }

            // Success
            const alertToast = document.getElementById('alertToast');
            alertToast.classList.remove('bg-danger');
            alertToast.classList.add('bg-success');
            alertToast.querySelector('.toast-body').textContent = 'Chauffeur supprimé avec succès';
            toast.show();

            // Fermer le modal
            deleteModal.hide();

            // Recharger la page après 1 seconde
            setTimeout(() => {
                window.location.reload();
            }, 1000);

        } catch (error) {
            console.error('Error:', error);
            const alertToast = document.getElementById('alertToast');
            alertToast.classList.remove('bg-success');
            alertToast.classList.add('bg-danger');
            alertToast.querySelector('.toast-body').textContent = error.message;
            toast.show();
            deleteModal.hide();
        }
    });
});
</script>
@endpush
