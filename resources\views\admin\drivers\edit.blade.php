@extends('layouts.admin_minimal')

@section('title', 'Modifier le chauffeur')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card mt-4">
                <div class="card-header">
                    <h3 class="card-title">Modifier un Chauffeur</h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.drivers.update', $driver->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="first_name">Prénom <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('first_name') is-invalid @enderror" 
                                           id="first_name" name="first_name" value="{{ old('first_name', $driver->first_name) }}" required>
                                    @error('first_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="last_name">Nom <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('last_name') is-invalid @enderror" 
                                           id="last_name" name="last_name" value="{{ old('last_name', $driver->last_name) }}" required>
                                    @error('last_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="email">Email <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email', $driver->email) }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="phone">Téléphone <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" name="phone" value="{{ old('phone', $driver->phone) }}" required>
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="license_number">Numéro de Permis <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('license_number') is-invalid @enderror" 
                                           id="license_number" name="license_number" value="{{ old('license_number', $driver->license_number) }}" required>
                                    @error('license_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="license_expiry">Date d'Expiration du Permis <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('license_expiry') is-invalid @enderror" 
                                           id="license_expiry" name="license_expiry" value="{{ old('license_expiry', $driver->license_expiry->format('Y-m-d')) }}" required>
                                    @error('license_expiry')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="address">Adresse</label>
                            <textarea class="form-control @error('address') is-invalid @enderror" 
                                      id="address" name="address" rows="2">{{ old('address', $driver->address) }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group mb-3">
                            <label for="notes">Notes</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                      id="notes" name="notes" rows="3">{{ old('notes', $driver->notes) }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group mb-3">
                            <label for="status">Statut <span class="text-danger">*</span></label>
                            <select class="form-control @error('status') is-invalid @enderror" 
                                    id="status" name="status" required>
                                <option value="available" {{ old('status', $driver->status) == 'available' ? 'selected' : '' }}>Disponible</option>
                                <option value="unavailable" {{ old('status', $driver->status) == 'unavailable' ? 'selected' : '' }}>Indisponible</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.drivers.index') }}" class="btn btn-secondary">Retour</a>
                            <button type="submit" class="btn btn-primary">Enregistrer les modifications</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light fixed-bottom">
    <div class="container text-center">
        <span class="text-muted"> {{ date('Y') }} GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>
@endsection

@push('styles')
<style>
    .form-label {
        font-weight: 500;
    }
    textarea {
        resize: none;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('driverForm');
    const toast = new bootstrap.Toast(document.getElementById('alertToast'));

    // Set min date for license expiry
    const licenseExpiryInput = document.getElementById('license_expiry');
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    licenseExpiryInput.min = tomorrow.toISOString().split('T')[0];

    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Reset previous error states
        form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        form.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');

        const formData = new FormData(form);
        const data = {};
        formData.forEach((value, key) => {
            if (key !== '_token' && key !== '_method') {
                data[key] = value;
            }
        });

        try {
            const response = await fetch('{{ route("admin.drivers.update", $driver->id) }}', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (!response.ok) {
                if (response.status === 422) {
                    // Validation errors
                    Object.keys(result.errors).forEach(field => {
                        const input = form.querySelector(`[name="${field}"]`);
                        const feedback = form.querySelector(`[data-field="${field}"]`);
                        if (input && feedback) {
                            input.classList.add('is-invalid');
                            feedback.textContent = result.errors[field][0];
                        }
                    });
                } else {
                    throw new Error(result.message || 'Une erreur est survenue');
                }
                return;
            }

            // Success
            const alertToast = document.getElementById('alertToast');
            alertToast.classList.remove('bg-danger');
            alertToast.classList.add('bg-success');
            alertToast.querySelector('.toast-body').textContent = result.message;
            toast.show();

            // Redirect after 2 seconds
            setTimeout(() => {
                window.location.href = '{{ route("admin.drivers.index") }}';
            }, 2000);

        } catch (error) {
            console.error('Error:', error);
            const alertToast = document.getElementById('alertToast');
            alertToast.classList.remove('bg-success');
            alertToast.classList.add('bg-danger');
            alertToast.querySelector('.toast-body').textContent = error.message;
            toast.show();
        }
    });
});
</script>
@endpush
