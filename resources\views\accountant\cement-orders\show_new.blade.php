@extends('layouts.accountant')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Détails du bon de commande</h1>
        <div class="btn-group">
            <a href="{{ route('accountant.cement-orders.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour
            </a>
        </div>
    </div>

    <!-- Informations générales -->
    <div class="row g-3">
        <div class="col-md-6">
            <div class="card shadow h-100">
                <div class="card-header">
                    <h5 class="m-0 font-weight-bold text-primary">Informations générales</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="35%">Référence</th>
                            <td>{{ $cement_order->reference }}</td>
                        </tr>
                        <tr>
                            <th>Produit</th>
                            <td>{{ $cement_order->product->name }}</td>
                        </tr>
                        <tr>
                            <th>Créé par</th>
                            <td>{{ $cement_order->creator->name }}</td>
                        </tr>
                        <tr>
                            <th>Date de création</th>
                            <td>{{ $cement_order->created_at->format('d/m/Y H:i') }}</td>
                        </tr>
                        <tr>
                            <th>Statut</th>
                            <td>
                                @switch($cement_order->status)
                                    @case('pending')
                                        <span class="badge bg-warning text-dark">En attente</span>
                                        @break
                                    @case('validated')
                                        <span class="badge bg-success">Validé</span>
                                        @break
                                    @case('rejected')
                                        <span class="badge bg-danger">Rejeté</span>
                                        @break
                                    @default
                                        <span class="badge bg-secondary">{{ $cement_order->status }}</span>
                                @endswitch
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card shadow h-100">
                <div class="card-header">
                    <h5 class="m-0 font-weight-bold text-primary">Totaux</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-sm-6">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="text-xs text-uppercase mb-1">Total Tonnage</div>
                                    <div class="h5 mb-0">{{ number_format($cement_order->total_tonnage, 2) }} T</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="text-xs text-uppercase mb-1">Montant Total</div>
                                    <div class="h5 mb-0">{{ number_format($cement_order->total_amount, 2) }} FCFA</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="text-xs text-uppercase mb-1">Montant Payé</div>
                                    <div class="h5 mb-0">{{ number_format($cement_order->total_amount - $cement_order->remaining_amount, 2) }} FCFA</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="text-xs text-uppercase mb-1">Reste à Payer</div>
                                    <div class="h5 mb-0">{{ number_format($cement_order->remaining_amount, 2) }} FCFA</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Détails de la commande -->
    <div class="card shadow mt-4">
        <div class="card-header">
            <h5 class="m-0 font-weight-bold text-primary">Détails des livraisons</h5>
        </div>
        <div class="card-body">
            @if($cement_order->details->isNotEmpty())
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Ville</th>
                                <th>Client</th>
                                <th>Quantité commandée</th>
                                <th>Quantité livrée</th>
                                <th>Quantité restante</th>
                                <th>Voyages</th>
                                <th>Prix unitaire</th>
                                <th>Montant total</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($cement_order->details as $detail)
                                <tr>
                                    <td>{{ optional($detail->city)->name ?? 'N/A' }}</td>
                                    <td>{{ optional($detail->customer)->name ?? 'N/A' }}</td>
                                    <td>{{ number_format($detail->quantity, 2) }} T</td>
                                    <td>{{ number_format($detail->delivered_quantity, 2) }} T</td>
                                    <td>{{ number_format($detail->remaining_quantity, 2) }} T</td>
                                    <td><span class="badge bg-info">{{ $detail->trips_count ?? 0 }} voyages</span></td>
                                    <td>{{ number_format($detail->unit_price, 2) }} FCFA</td>
                                    <td>{{ number_format($detail->total_price, 2) }} FCFA</td>
                                    <td>
                                        @switch($detail->status)
                                            @case('pending')
                                                <span class="badge bg-warning text-dark">En attente</span>
                                                @break
                                            @case('processing')
                                                <span class="badge bg-info">En cours</span>
                                                @break
                                            @case('completed')
                                                <span class="badge bg-success">Terminé</span>
                                                @break
                                            @default
                                                <span class="badge bg-secondary">{{ $detail->status }}</span>
                                        @endswitch
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                        <tfoot>
                            <tr class="bg-light">
                                <td colspan="2" class="text-end"><strong>Total:</strong></td>
                                <td><strong>{{ number_format($cement_order->details->sum('quantity'), 2) }} T</strong></td>
                                <td><strong>{{ number_format($cement_order->details->sum('delivered_quantity'), 2) }} T</strong></td>
                                <td><strong>{{ number_format($cement_order->details->sum('remaining_quantity'), 2) }} T</strong></td>
                                <td><strong>{{ $cement_order->details->sum('trips_count') }} voyages</strong></td>
                                <td></td>
                                <td><strong>{{ number_format($cement_order->total_amount, 2) }} FCFA</strong></td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            @else
                <div class="alert alert-info mb-0">
                    <i class="fas fa-info-circle me-2"></i> Aucun détail disponible pour cette commande
                </div>
            @endif
        </div>
    </div>

    <!-- Section d'affectation des véhicules -->
    <div class="card shadow mt-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="m-0 font-weight-bold text-primary">Affectation des véhicules</h5>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#assignVehicleModal">
                <i class="fas fa-truck me-2"></i>Nouvelle affectation
            </button>
        </div>
        <div class="card-body">
            @if($assignments->isNotEmpty())
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Détail</th>
                                <th>Camion</th>
                                <th>Chauffeur</th>
                                <th>Voyage N°</th>
                                <th>Tonnage</th>
                                <th>Date début</th>
                                <th>Date fin</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($assignments as $assignment)
                                <tr>
                                    <td>
                                        {{ optional($assignment->cementOrderDetail->city)->name }} -
                                        {{ optional($assignment->cementOrderDetail->customer)->name }}
                                    </td>
                                    <td>{{ optional($assignment->truck)->registration_number }}</td>
                                    <td>{{ optional($assignment->driver)->name }}</td>
                                    <td>{{ $assignment->trip_number }}</td>
                                    <td>{{ number_format($assignment->tonnage, 2) }} T</td>
                                    <td>{{ $assignment->start_date->format('d/m/Y H:i') }}</td>
                                    <td>{{ $assignment->end_date->format('d/m/Y H:i') }}</td>
                                    <td>
                                        @switch($assignment->status)
                                            @case('pending')
                                                <span class="badge bg-warning text-dark">En attente</span>
                                                @break
                                            @case('completed')
                                                <span class="badge bg-success">Terminé</span>
                                                @break
                                            @default
                                                <span class="badge bg-secondary">{{ $assignment->status }}</span>
                                        @endswitch
                                    </td>
                                    <td>
                                        @if($assignment->status === 'pending')
                                            <form action="{{ route('accountant.assignments.update-status', $assignment) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('PUT')
                                                <input type="hidden" name="status" value="completed">
                                                <button type="submit" class="btn btn-success btn-sm">
                                                    <i class="fas fa-check me-1"></i>Terminer
                                                </button>
                                            </form>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="alert alert-info mb-0">
                    <i class="fas fa-info-circle me-2"></i> Aucune affectation de véhicule pour cette commande
                </div>
            @endif
        </div>
    </div>

    <!-- Modal d'affectation de véhicule -->
    <div class="modal fade" id="assignVehicleModal" tabindex="-1" aria-labelledby="assignVehicleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form action="{{ route('accountant.cement-orders.assign-vehicle', $cement_order) }}" method="POST">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title" id="assignVehicleModalLabel">Nouvelle affectation de véhicule</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label for="cement_order_detail_id" class="form-label">Détail de commande</label>
                                <select name="cement_order_detail_id" id="cement_order_detail_id" class="form-select" required>
                                    <option value="">Sélectionner un détail</option>
                                    @foreach($cement_order->details as $detail)
                                        <option value="{{ $detail->id }}">
                                            {{ optional($detail->city)->name }} - 
                                            {{ optional($detail->customer)->name }} 
                                            ({{ number_format($detail->remaining_quantity, 2) }} T restants)
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="truck_id" class="form-label">Camion</label>
                                <select name="truck_id" id="truck_id" class="form-select" required>
                                    <option value="">Sélectionner un camion</option>
                                    @foreach($trucks as $truck)
                                        <option value="{{ $truck->id }}">
                                            {{ $truck->registration_number }} 
                                            ({{ optional($truck->capacity)->tonnage ?? 'N/A' }} T)
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="driver_id" class="form-label">Chauffeur</label>
                                <select name="driver_id" id="driver_id" class="form-select" required>
                                    <option value="">Sélectionner un chauffeur</option>
                                    @foreach($drivers as $driver)
                                        <option value="{{ $driver->id }}">{{ $driver->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="trip_number" class="form-label">Numéro de voyage</label>
                                <input type="number" class="form-control" id="trip_number" name="trip_number" min="1" required>
                            </div>
                            <div class="col-md-4">
                                <label for="start_date" class="form-label">Date de début</label>
                                <input type="datetime-local" class="form-control" id="start_date" name="start_date" required>
                            </div>
                            <div class="col-md-4">
                                <label for="end_date" class="form-label">Date de fin</label>
                                <input type="datetime-local" class="form-control" id="end_date" name="end_date" required>
                            </div>
                            <div class="col-12">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-primary">Affecter</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @if($cement_order->notes)
        <!-- Notes -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="m-0 font-weight-bold text-primary">Notes</h5>
            </div>
            <div class="card-body">
                {{ $cement_order->notes }}
            </div>
        </div>
    @endif
</div>
@endsection
