/**
 * GRADIS - Dashboard Comptable Professionnel
 * Styles des composants pour l'interface professionnelle du comptable
 * 2025 MOMK-Solutions
 */

/* Cartes dashboard avancées et professionnelles */
.dashboard-card {
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: var(--card-shadow);
    transition: all var(--transition-speed) ease;
    margin-bottom: 1.5rem;
    overflow: hidden;
    border: none;
}

.dashboard-card:hover {
    box-shadow: var(--card-shadow-hover);
}

.dashboard-card-header {
    padding: 1.25rem 1.5rem;
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.dashboard-card-header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, var(--primary) 0%, rgba(255,255,255,0) 100%);
}

.dashboard-card-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dashboard-card-body {
    padding: 1.5rem;
}

/* Tableaux professionnels et modernes */
.pro-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.pro-table thead th {
    background: var(--gray-100);
    padding: 0.875rem 1rem;
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 1px solid var(--gray-200);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pro-table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-800);
    font-size: 0.9375rem;
}

.pro-table tbody tr:last-child td {
    border-bottom: none;
}

.pro-table tbody tr {
    transition: background-color 0.2s ease;
}

.pro-table tbody tr:hover {
    background-color: var(--gray-100);
}

/* Badges professionnels pour statuts */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    border-radius: 50px;
    font-size: 0.8125rem;
    font-weight: 600;
}

.status-badge-paid {
    background-color: rgba(var(--success-rgb), 0.1);
    color: var(--success);
}

.status-badge-partial {
    background-color: rgba(var(--warning-rgb), 0.1);
    color: var(--warning);
}

.status-badge-unpaid {
    background-color: rgba(var(--danger-rgb), 0.1);
    color: var(--danger);
}

.status-badge-pending {
    background-color: rgba(var(--info-rgb), 0.1);
    color: var(--info);
}

.status-badge i {
    font-size: 0.75rem;
}

/* Boutons d'action professionnels */
.btn-action {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    border: 1px solid var(--gray-300);
    background: transparent;
    color: var(--gray-700);
}

.btn-action:hover {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(var(--primary-rgb), 0.25);
}

.btn-action.action-view:hover {
    background: var(--primary);
    border-color: var(--primary);
}

.btn-action.action-edit:hover {
    background: var(--secondary);
    border-color: var(--secondary);
}

.btn-action.action-delete:hover {
    background: var(--danger);
    border-color: var(--danger);
}

/* Cartes de graphiques professionnels */
.chart-card {
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    height: 100%;
}

.chart-card-header {
    padding: 1.25rem 1.5rem;
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-card-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chart-card-body {
    padding: 1.5rem;
}

.chart-container {
    position: relative;
    min-height: 300px;
}

/* Période et filtres */
.period-filters {
    display: flex;
    gap: 0.25rem;
    background: var(--gray-100);
    border-radius: 6px;
    padding: 0.25rem;
}

.period-filter {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
    font-weight: 600;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--gray-700);
}

.period-filter.active, .period-filter:hover {
    background: var(--primary);
    color: var(--white);
}

/* Widget d'information */
.info-widget {
    background: white;
    border-radius: var(--card-border-radius);
    padding: 1.25rem;
    box-shadow: var(--card-shadow);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.info-widget-icon {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.info-widget-icon.primary {
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
}

.info-widget-icon.success {
    background: rgba(var(--success-rgb), 0.1);
    color: var(--success);
}

.info-widget-icon.warning {
    background: rgba(var(--warning-rgb), 0.1);
    color: var(--warning);
}

.info-widget-icon.danger {
    background: rgba(var(--danger-rgb), 0.1);
    color: var(--danger);
}

.info-widget-title {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 0.25rem;
}

.info-widget-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.info-widget-subtitle {
    font-size: 0.8125rem;
    color: var(--gray-500);
}

.info-widget-progress {
    margin-top: auto;
    padding-top: 1rem;
}

.progress-bar-container {
    height: 6px;
    background: var(--gray-200);
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: 10px;
}

.progress-bar.success {
    background: var(--success);
}

.progress-bar.warning {
    background: var(--warning);
}

.progress-bar.danger {
    background: var(--danger);
}

.progress-bar.info {
    background: var(--info);
}

.progress-bar.primary {
    background: var(--primary);
}

.progress-text {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.8125rem;
    color: var(--gray-600);
}

/* Carte des actions rapides */
.quick-action-card {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 10px;
    box-shadow: var(--card-shadow);
    transition: all 0.2s ease;
    padding: 1.25rem;
    cursor: pointer;
    margin-bottom: 1rem;
    border-left: 3px solid transparent;
}

.quick-action-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--card-shadow-hover);
}

.quick-action-card.primary {
    border-left-color: var(--primary);
}

.quick-action-card.success {
    border-left-color: var(--success);
}

.quick-action-card.warning {
    border-left-color: var(--warning);
}

.quick-action-card.info {
    border-left-color: var(--info);
}

.quick-action-icon {
    width: 45px;
    height: 45px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin-right: 1rem;
}

.quick-action-icon.primary {
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
}

.quick-action-icon.success {
    background: rgba(var(--success-rgb), 0.1);
    color: var(--success);
}

.quick-action-icon.warning {
    background: rgba(var(--warning-rgb), 0.1);
    color: var(--warning);
}

.quick-action-icon.info {
    background: rgba(var(--info-rgb), 0.1);
    color: var(--info);
}

.quick-action-content {
    flex: 1;
}

.quick-action-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.quick-action-desc {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.quick-action-arrow {
    color: var(--gray-400);
    font-size: 1.25rem;
    transition: all 0.2s ease;
}

.quick-action-card:hover .quick-action-arrow {
    color: var(--primary);
    transform: translateX(3px);
}
