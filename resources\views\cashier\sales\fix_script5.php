<?php
// Script pour corriger les erreurs de syntaxe dans show.blade.php
$filePath = 'c:/xampp/htdocs/gradis/resources/views/cashier/sales/show.blade.php';
$content = file_get_contents($filePath);

// Lignes contenant des @endif problématiques identifiés par l'analyse
$problematicLines = [644, 678, 816, 889, 1014, 1137, 1138, 1213];

// Supprimer complètement les lignes problématiques
$lines = explode("\n", $content);
$corrected = 0;

foreach ($problematicLines as $lineNumber) {
    $index = $lineNumber - 1; // Ajuster pour l'index basé sur 0
    if (isset($lines[$index]) && (strpos($lines[$index], '@endif') !== false || strpos($lines[$index], '<!-- @endif') !== false)) {
        // Supprimer complètement la ligne
        unset($lines[$index]);
        $corrected++;
    }
}

// Reconstruire le contenu
$newContent = implode("\n", array_values($lines));

// Écrire le contenu modifié dans le fichier
file_put_contents($filePath, $newContent);

echo "Correction appliquée avec succès! $corrected lignes problématiques ont été complètement supprimées.\n";
?>
