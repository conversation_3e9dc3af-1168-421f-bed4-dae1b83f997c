@extends('layouts.cement_manager')

@section('title', '<PERSON><PERSON><PERSON> de la vente')

@push('styles')
<style>
    .sale-header {
        background: linear-gradient(90deg, #0d6efd 60%, #6c757d 100%);
        color: #fff;
        border-radius: 1rem 1rem 0 0;
        padding: 2rem 1.5rem 1.5rem 1.5rem;
        box-shadow: 0 4px 12px rgba(13,110,253,0.08);
        margin-bottom: -2.5rem;
        position: relative;
        z-index: 2;
    }
    .sale-header .badge {
        font-size: 1rem;
        padding: 0.5rem 1.2rem;
        border-radius: 1rem;
        background: rgba(255,255,255,0.2);
        color: #fff;
        font-weight: 600;
    }
    .detail-card {
        background: #fff;
        border-radius: 1rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        padding: 2rem 1.5rem 1.5rem 1.5rem;
        margin-bottom: 2rem;
        transition: box-shadow 0.2s;
    }
    .detail-card:hover {
        box-shadow: 0 6px 24px rgba(13,110,253,0.08);
    }
    .price-info-card {
        border-left: 4px solid;
        padding-left: 1rem;
        margin-bottom: 1.5rem;
    }
    .price-info-card.discount {
        border-color: #0dcaf0;
    }
    .price-info-card.increase {
        border-color: #ffc107;
    }
    @media print {
        .no-print {
            display: none !important;
        }
        .container-fluid {
            width: 100%;
            padding: 0;
            margin: 0;
        }
        .detail-card {
            box-shadow: none;
            border: 1px solid #ddd;
            page-break-inside: avoid;
        }
    }
</style>
@endpush

@section('content')
<div class="container-fluid py-4">
    <div class="sale-header mb-4 d-flex flex-column flex-md-row justify-content-between align-items-md-center">
        <div>
            <h1 class="h3 mb-2">Vente #{{ $sale->invoice_number }}
                @if($sale->delivery_status === 'completed')
                <span class="badge bg-success"><i class="fas fa-check-circle me-1"></i> Livré</span>
                @elseif($sale->delivery_status === 'in_progress')
                <span class="badge bg-warning"><i class="fas fa-truck me-1"></i> En cours de livraison</span>
                @else
                <span class="badge bg-secondary"><i class="fas fa-clock me-1"></i> En attente</span>
                @endif
            </h1>
            <p class="mb-0">Créée le {{ $sale->created_at->format('d/m/Y à H:i') }} par {{ $sale->createdBy->name }}</p>
        </div>
        <div class="mt-3 mt-md-0">
            <button onclick="printInvoice()" class="btn btn-outline-light no-print"><i class="fas fa-print me-2"></i> Imprimer</button>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="detail-card">
                <h5 class="mb-4"><i class="fas fa-user me-2"></i> Informations client</h5>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Nom :</div>
                    <div class="col-md-8">{{ $sale->customer_name }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Téléphone :</div>
                    <div class="col-md-8">{{ $sale->customer_phone }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Adresse :</div>
                    <div class="col-md-8">{{ $sale->customer_address }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Ville :</div>
                    <div class="col-md-8">{{ $sale->city->name }}</div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="detail-card">
                <h5 class="mb-4"><i class="fas fa-truck me-2"></i> Informations de livraison</h5>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Statut :</div>
                    <div class="col-md-8">
                        @if($sale->delivery_status === 'completed')
                        <span class="badge bg-success">Livré</span>
                        @elseif($sale->delivery_status === 'in_progress')
                        <span class="badge bg-warning">En cours de livraison</span>
                        @else
                        <span class="badge bg-secondary">En attente</span>
                        @endif
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Nombre de voyages :</div>
                    <div class="col-md-8">{{ $sale->trips }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Date de livraison :</div>
                    <div class="col-md-8">{{ $sale->delivery_date ? $sale->delivery_date->format('d/m/Y') : 'Non définie' }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Véhicule :</div>
                    <div class="col-md-8">{{ $sale->vehicle ? $sale->vehicle->registration_number : 'Non assigné' }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Chauffeur :</div>
                    <div class="col-md-8">{{ $sale->driver ? $sale->driver->name : 'Non assigné' }}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="detail-card mt-4">
        <h5 class="mb-4"><i class="fas fa-shopping-cart me-2"></i> Détails de la commande</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Produit</th>
                        <th>Quantité</th>
                        <th>Prix unitaire</th>
                        <th>Montant total</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{{ $sale->supply->details->first()->product->name }}</td>
                        <td>{{ number_format($sale->quantity, 2, ',', ' ') }} T</td>
                        <td>
                            {{ number_format($sale->unit_price, 0, ',', ' ') }} FCFA
                            @if($sale->price_modified && $sale->original_price)
                                <small class="d-block text-danger">
                                    <i class="fas fa-arrow-up"></i> Augmenté
                                </small>
                            @endif
                        </td>
                        <td>{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</td>
                    </tr>
                </tbody>
                <tfoot class="table-light">
                    <tr>
                        <th colspan="3" class="text-end">Total :</th>
                        <th>{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    
    <!-- Informations détaillées sur le produit et le stock -->
    <div class="detail-card mt-4">
        <h5 class="mb-4"><i class="fas fa-info-circle me-2"></i> Informations détaillées du produit</h5>
        <div class="row">
            <div class="col-md-6">
                <div class="card border-primary h-100">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="fas fa-box me-2"></i> Détails du produit</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            @php
                                $supplyCity = $sale->supply->cities->where('vehicle_id', $sale->vehicle_id)->first();
                                $totalQuantity = $supplyCity ? $supplyCity->quantity : 0;
                                $remainingQuantity = $supplyCity ? ($supplyCity->remaining_quantity ?? $supplyCity->quantity) : 0;
                                $usedQuantity = $totalQuantity - $remainingQuantity;
                                $vehicleCapacity = $sale->vehicle ? ($sale->vehicle->capacity->capacity ?? 0) : 0;
                            @endphp
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-weight me-2 text-primary"></i> Quantité totale :</span>
                                <span class="fw-bold">{{ number_format($totalQuantity, 2, ',', ' ') }} T</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-shopping-cart me-2 text-success"></i> Quantité vendue :</span>
                                <span class="fw-bold">{{ number_format($usedQuantity, 2, ',', ' ') }} T</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-warehouse me-2 text-info"></i> Quantité restante :</span>
                                <span class="fw-bold">{{ number_format($remainingQuantity, 2, ',', ' ') }} T</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-tag me-2 text-danger"></i> Prix de vente par tonne :</span>
                                <span class="fw-bold">{{ number_format($sale->unit_price, 0, ',', ' ') }} FCFA</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-secondary h-100">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0"><i class="fas fa-truck me-2"></i> Informations du transport</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-truck me-2 text-secondary"></i> Véhicule assigné :</span>
                                <span class="fw-bold">{{ $sale->vehicle ? $sale->vehicle->registration_number : 'Non assigné' }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-balance-scale me-2 text-warning"></i> Capacité :</span>
                                <span class="fw-bold">{{ number_format($vehicleCapacity, 2, ',', ' ') }} T</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-user me-2 text-info"></i> Chauffeur :</span>
                                <span class="fw-bold">{{ $sale->driver ? $sale->driver->first_name . ' ' . $sale->driver->last_name : 'Non assigné' }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-phone me-2 text-success"></i> Téléphone chauffeur :</span>
                                <span class="fw-bold">{{ $sale->driver ? $sale->driver->phone : 'N/A' }}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Carte récapitulative pour les remises et augmentations de prix -->
    @if(($sale->discount_per_ton ?? 0) > 0 || ($sale->price_modified && $sale->original_price))
    <div class="detail-card mt-4">
        <h5 class="mb-4">
            <i class="fas fa-calculator me-2"></i> 
            @if(($sale->discount_per_ton ?? 0) > 0)
                Récapitulatif de la remise (diminution du prix)
            @elseif($sale->price_modified && $sale->original_price)
                Récapitulatif de l'augmentation de prix
            @else
                Récapitulatif des modifications de prix
            @endif
        </h5>
        
        <div class="row">
            @if(($sale->discount_per_ton ?? 0) > 0)
            <div class="col-md-6 mb-4">
                <div class="card border-info h-100">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-percentage me-2"></i> Informations de remise (diminution du prix)</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle me-2"></i> Une remise a été appliquée à cette vente, ce qui a <strong>diminué</strong> le prix par tonne.
                        </div>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Prix standard par tonne
                                <span class="badge bg-secondary rounded-pill">{{ number_format($sale->original_price ?? $sale->unit_price + $sale->discount_per_ton, 0, ',', ' ') }} FCFA</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Remise par tonne
                                <span class="badge bg-info rounded-pill">{{ number_format($sale->discount_per_ton, 0, ',', ' ') }} FCFA</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Prix final par tonne
                                <span class="badge bg-success rounded-pill">{{ number_format($sale->unit_price, 0, ',', ' ') }} FCFA</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Remise totale
                                <span class="badge bg-info rounded-pill">{{ number_format($sale->discount_total, 0, ',', ' ') }} FCFA</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Montant avant remise
                                <span class="badge bg-secondary rounded-pill">{{ number_format($sale->total_before_discount, 0, ',', ' ') }} FCFA</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            @endif
            
            @if($sale->price_modified && $sale->original_price)
            <div class="col-md-6 mb-4">
                <div class="card border-warning h-100">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0"><i class="fas fa-arrow-up me-2"></i> Informations d'augmentation de prix</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning mb-3">
                            <i class="fas fa-exclamation-triangle me-2"></i> Le prix par tonne a été <strong>augmenté</strong> pour cette vente.
                        </div>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Prix standard par tonne
                                <span class="badge bg-secondary rounded-pill">{{ number_format($sale->original_price, 0, ',', ' ') }} FCFA</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Augmentation par tonne
                                <span class="badge bg-danger rounded-pill">{{ number_format($sale->unit_price - $sale->original_price, 0, ',', ' ') }} FCFA</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Prix final par tonne
                                <span class="badge bg-warning rounded-pill">{{ number_format($sale->unit_price, 0, ',', ' ') }} FCFA</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Augmentation totale
                                <span class="badge bg-danger rounded-pill">{{ number_format(($sale->unit_price - $sale->original_price) * $sale->quantity, 0, ',', ' ') }} FCFA</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Montant sans augmentation
                                <span class="badge bg-secondary rounded-pill">{{ number_format($sale->original_price * $sale->quantity, 0, ',', ' ') }} FCFA</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
    @endif
    
    @if($sale->notes)
    <div class="detail-card mt-4">
        <h5 class="mb-3"><i class="fas fa-sticky-note me-2"></i> Notes</h5>
        <p class="mb-0">{{ $sale->notes }}</p>
    </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
function printInvoice() {
    window.print();
}
</script>
@endpush
