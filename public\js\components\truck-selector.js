class TruckSelector {
    constructor() {
        this.vehicleSelect = document.getElementById('cityVehicleSelect');
        this.vehicleDetails = document.getElementById('vehicleDetails');
        this.driverInfo = document.getElementById('driverInfo');
        this.capacityInfo = document.getElementById('capacityInfo');
        
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        if (this.vehicleSelect) {
            this.vehicleSelect.addEventListener('change', () => this.onVehicleSelect());
        }
    }

    updateTrucks(trucks) {
        if (!this.vehicleSelect) return;
        
        this.vehicleSelect.innerHTML = '<option value="">Sélectionner un véhicule</option>';
        
        trucks.forEach(truck => {
            const option = document.createElement('option');
            option.value = truck.id;
            option.textContent = truck.text;
            this.vehicleSelect.appendChild(option);
        });

        // Déclencher l'événement change pour mettre à jour les détails
        this.onVehicleSelect();
    }

    async loadAvailableTrucks() {
        try {
            const response = await fetch('/api/trucks/available');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();

            if (data.success && Array.isArray(data.trucks)) {
                this.updateTrucks(data.trucks);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des camions:', error);
            toastr.error('Erreur lors du chargement des camions disponibles');
        }
    }

    onVehicleSelect() {
        const selectedOption = this.vehicleSelect.selectedOptions[0];
        if (selectedOption && selectedOption.value) {
            const text = selectedOption.textContent;
            this.driverInfo.textContent = text;
            this.vehicleDetails.style.display = 'block';
        } else {
            this.vehicleDetails.style.display = 'none';
        }
    }
}

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    const truckSelector = new TruckSelector();
    window.truckSelector = truckSelector; // Rendre accessible globalement
    
    // Recharger les camions à l'ouverture du modal
    const citySelectionModal = document.getElementById('citySelectionModal');
    if (citySelectionModal) {
        citySelectionModal.addEventListener('shown.bs.modal', () => {
            truckSelector.loadAvailableTrucks();
        });
    }
});
