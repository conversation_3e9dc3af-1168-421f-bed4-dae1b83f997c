<script>
$(document).ready(function() {
    // Gérer la soumission du formulaire d'assignation
    $('.assign-vehicle-form').on('submit', function(e) {
        e.preventDefault();
        
        var form = $(this);
        var modal = form.closest('.modal');
        var submitBtn = form.find('button[type="submit"]');
        var originalBtnHtml = submitBtn.html();
        var driverId = form.find('input[name="driver_id"]').val();
        var truckId = form.find('select[name="truck_id"]').val();
        
        if (!truckId) {
            Swal.fire({
                icon: 'error',
                title: 'Erreur!',
                text: 'Veuillez sélectionner un véhicule.',
            });
            return;
        }
        
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> Enregistrement...');
        
        $.ajax({
            url: '{{ route("accountant.drivers.reassign-vehicle") }}',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: {
                driver_id: driverId,
                truck_id: truckId
            },
            success: function(response) {
                if (response.success) {
                    modal.modal('hide');
                    Swal.fire({
                        icon: 'success',
                        title: 'Succès!',
                        text: response.message,
                        timer: 1500,
                        showConfirmButton: false
                    }).then(function() {
                        window.location.reload();
                    });
                }
            },
            error: function(xhr) {
                Swal.fire({
                    icon: 'error',
                    title: 'Erreur!',
                    text: xhr.responseJSON?.message || 'Une erreur est survenue lors de l\'assignation du véhicule.',
                });
            },
            complete: function() {
                submitBtn.prop('disabled', false).html(originalBtnHtml);
            }
        });
    });

    // Gérer le retrait d'assignation
    $('#removeAssignment').on('click', function() {
        var button = $(this);
        var originalBtnHtml = button.html();
        var driverId = $('input[name="driver_id"]').val();
        
        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> Traitement...');
        
        $.ajax({
            url: '{{ route("accountant.drivers.reassign-vehicle") }}',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: {
                driver_id: driverId,
                truck_id: null
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Succès!',
                        text: response.message,
                        timer: 1500,
                        showConfirmButton: false
                    }).then(function() {
                        window.location.reload();
                    });
                }
            },
            error: function(xhr) {
                Swal.fire({
                    icon: 'error',
                    title: 'Erreur!',
                    text: xhr.responseJSON?.message || 'Une erreur est survenue lors du retrait de l\'assignation.',
                });
            },
            complete: function() {
                button.prop('disabled', false).html(originalBtnHtml);
            }
        });
    });
});</script>
