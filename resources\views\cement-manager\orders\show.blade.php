@extends('layouts.cement-manager')

@section('title', '<PERSON><PERSON><PERSON> de la commande')

@push('styles')
<style>
    .content-wrapper {
        margin-top: 2rem;
    }
    .order-header {
        background-color: #f8fafc;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #e5e7eb;
    }
    .order-status {
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-weight: 500;
    }
    .status-pending {
        background-color: #fef3c7;
        color: #92400e;
    }
    .status-approved {
        background-color: #d1fae5;
        color: #065f46;
    }
    .status-rejected {
        background-color: #fee2e2;
        color: #991b1b;
    }
    .order-info {
        background-color: #fff;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border: 1px solid #e5e7eb;
    }
    .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }
    .info-item i {
        width: 24px;
        margin-right: 0.75rem;
        color: #6b7280;
    }
    .info-item:last-child {
        margin-bottom: 0;
    }
    .details-table {
        border: none;
        margin-top: 1rem;
    }
    .details-table th {
        background-color: #f8fafc;
        color: #1f2937;
        font-weight: 600;
        padding: 1rem;
        border: none;
        border-bottom: 2px solid #e5e7eb;
    }
    .details-table td {
        padding: 1rem;
        border: none;
        border-bottom: 1px solid #e5e7eb;
        vertical-align: middle;
    }
    .details-table tr:hover {
        background-color: #f8fafc;
    }
    .quantity-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        background-color: #f3f4f6;
    }
    .back-button {
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        transition: all 0.2s;
    }
    .back-button:hover {
        transform: translateX(-2px);
    }
</style>
@endpush

@section('content')
<div class="container-fluid content-wrapper">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 mt-5 text-gray-800">
            <i class="fas fa-file-invoice me-2"></i>
            Bon de commande #{{ $cement_order->reference }}
        </h1>
        <a href="{{ route('cement-manager.cement-orders.index') }}" class="btn btn-secondary back-button">
            <i class="fas fa-arrow-left me-2"></i> Retour à la liste
        </a>
    </div>

    <div class="row">
        <div class="col-12">
            <!-- En-tête de la commande -->
            <div class="order-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <div class="me-4">
                            <div class="text-muted mb-1">Statut</div>
                            @if($cement_order->status === 'pending')
                                <span class="order-status status-pending">
                                    <i class="fas fa-clock me-2"></i>En attente
                                </span>
                            @elseif($cement_order->status === 'approved')
                                <span class="order-status status-approved">
                                    <i class="fas fa-check me-2"></i>Validée
                                </span>
                            @else
                                <span class="order-status status-rejected">
                                    <i class="fas fa-times me-2"></i>Rejetée
                                </span>
                            @endif
                        </div>
                        <div>
                            <div class="text-muted mb-1">Montant total</div>
                            <div class="h4 mb-0">{{ number_format($cement_order->total_amount, 0, ',', ' ') }} FCFA</div>
                        </div>
                    </div>
                    <div class="text-end">
                        <div class="text-muted mb-1">Date de création</div>
                        <div>
                            <i class="fas fa-calendar-alt me-2"></i>
                            {{ $cement_order->created_at->format('d/m/Y H:i') }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Détails des produits -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>Détails des produits
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table details-table">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Client</th>
                                    <th>Ville</th>
                                    <th class="text-center">Quantité</th>
                                    <th class="text-center">Livré</th>
                                    <th class="text-center">Restant</th>
                                    <th class="text-center">Voyages</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($cement_order->details as $detail)
                                    <tr>
                                        <td>
                                            <i class="fas fa-box me-2 text-muted"></i>
                                            {{ $detail->product?->name ?? 'N/A' }}
                                        </td>
                                        <td>
                                            <i class="fas fa-user me-2 text-muted"></i>
                                            {{ $detail->customer?->name ?? 'N/A' }}
                                        </td>
                                        <td>
                                            <i class="fas fa-map-marker-alt me-2 text-muted"></i>
                                            {{ $detail->city?->name ?? 'N/A' }}
                                        </td>
                                        <td class="text-center">
                                            <span class="quantity-badge">
                                                {{ number_format($detail->quantity, 2) }} T
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <span class="quantity-badge bg-success text-white">
                                                {{ number_format($detail->delivered_quantity, 2) }} T
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <span class="quantity-badge bg-warning">
                                                {{ number_format($detail->remaining_quantity, 2) }} T
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info">
                                                <i class="fas fa-truck me-1"></i>
                                                {{ $detail->trips_count ?? 0 }}
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
