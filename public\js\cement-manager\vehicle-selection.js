// Exportation de la fonction d'initialisation
window.initializeVehicleSelection = function() {
    console.log('Initialisation de la sélection des véhicules');
    
    // Éléments du DOM
    const vehicleSelectionModal = document.getElementById('cementVehicleSelectionModal');
    const vehicleSelect = document.getElementById('cementVehicleSelect');
    const selectedDestination = document.getElementById('cementSelectedDestination');
    const saveVehicleBtn = document.getElementById('cementSaveVehicleBtn');
    const numberOfTours = document.getElementById('cementNumberOfTours');
    const vehicleInfo = document.querySelector('.cement-vehicle-info');
    const driverInfo = document.querySelector('.cement-driver-info');

    let currentCity = null;
    let availableVehicles = [];

    // Initialisation du modal Bootstrap
    const modal = new bootstrap.Modal(vehicleSelectionModal);

    // Fonction pour charger les véhicules disponibles
    async function loadAvailableVehicles() {
        console.log('Début du chargement des véhicules');
        try {
            console.log('Envoi de la requête à /accountant/trucks/available');
            const response = await fetch('/accountant/trucks/available', {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            const data = await response.json();
            
            console.log('Réponse reçue:', data);
            
            if (data.success) {
                availableVehicles = data.trucks;
                console.log('Véhicules disponibles:', availableVehicles);
                updateVehicleSelect();
            } else {
                console.error('Erreur:', data.message);
                showError('Erreur lors du chargement des véhicules');
            }
        } catch (error) {
            console.error('Erreur complète:', error);
            showError('Erreur de connexion au serveur');
        }
    }

    // Fonction pour mettre à jour le select des véhicules
    function updateVehicleSelect() {
        console.log('Mise à jour du select avec les véhicules:', availableVehicles);
        if (!vehicleSelect) {
            console.error('Element vehicleSelect non trouvé');
            return;
        }
        
        vehicleSelect.innerHTML = '<option value="" selected disabled>Sélectionnez un véhicule</option>';
        
        if (!Array.isArray(availableVehicles)) {
            console.error('availableVehicles n\'est pas un tableau:', availableVehicles);
            return;
        }

        availableVehicles.forEach(vehicle => {
            console.log('Traitement du véhicule:', vehicle);
            const option = document.createElement('option');
            option.value = vehicle.id;
            option.textContent = `${vehicle.registration_number} (${vehicle.capacity ? vehicle.capacity.capacity + vehicle.capacity.unit : 'N/A'})`;
            vehicleSelect.appendChild(option);
        });
        console.log('Select mis à jour avec', availableVehicles.length, 'véhicules');
    }

    // Événement de changement de véhicule
    if (vehicleSelect) {
        vehicleSelect.addEventListener('change', function() {
            const selectedVehicle = availableVehicles.find(v => v.id === parseInt(this.value));
            
            if (selectedVehicle) {
                vehicleInfo.style.display = 'block';
                driverInfo.textContent = `Chauffeur: ${selectedVehicle.driver ? selectedVehicle.driver.first_name + ' ' + selectedVehicle.driver.last_name : 'Non assigné'}`;
            } else {
                vehicleInfo.style.display = 'none';
            }
        });
    }

    // Fonction pour ouvrir le modal avec les informations de la ville
    window.openVehicleSelectionModal = function(city) {
        console.log('Ouverture du modal pour la ville:', city);
        currentCity = city;
        selectedDestination.textContent = city.name;
        loadAvailableVehicles();
        modal.show();
    }

    // Événement de sauvegarde
    if (saveVehicleBtn) {
        saveVehicleBtn.addEventListener('click', function() {
            const selectedVehicleId = vehicleSelect.value;
            const tours = parseInt(numberOfTours.value);
            
            if (!selectedVehicleId) {
                showError('Veuillez sélectionner un véhicule');
                return;
            }

            if (!tours || tours < 1) {
                showError('Le nombre de tours doit être supérieur à 0');
                return;
            }

            const selectedVehicle = availableVehicles.find(v => v.id === parseInt(selectedVehicleId));
            
            // Émettre un événement personnalisé avec les données
            const event = new CustomEvent('vehicleSelected', {
                detail: {
                    city: currentCity,
                    vehicle: selectedVehicle,
                    numberOfTours: tours
                }
            });
            document.dispatchEvent(event);

            // Fermer le modal
            modal.hide();
        });
    }

    // Réinitialiser le formulaire à la fermeture du modal
    if (vehicleSelectionModal) {
        vehicleSelectionModal.addEventListener('hidden.bs.modal', function() {
            vehicleSelect.value = '';
            numberOfTours.value = '1';
            vehicleInfo.style.display = 'none';
        });
    }
};

// Fonction utilitaire pour afficher les erreurs
function showError(message) {
    if (window.Swal) {
        Swal.fire({
            icon: 'error',
            title: 'Erreur',
            text: message,
            timer: 3000,
            showConfirmButton: false
        });
    } else {
        console.error(message);
    }
}

// Initialisation automatique
document.addEventListener('DOMContentLoaded', function() {
    console.log('Script vehicle-selection.js chargé');
    window.initializeVehicleSelection();
});
