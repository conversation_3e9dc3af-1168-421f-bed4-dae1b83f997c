@extends('layouts.accountant')

@php
use Illuminate\Support\Str;
@endphp

@section('title', 'Modifier le profil')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <!-- <PERSON><PERSON><PERSON> gau<PERSON> (navigation du profil) -->
        <div class="col-12 col-lg-3 mb-4">
            <div class="profile-sidebar">
                <div class="profile-sidebar-header">
                    <h5 class="profile-sidebar-title">Mon Compte</h5>
                    <p class="profile-sidebar-subtitle">Gérez vos informations personnelles</p>
                </div>
                
                <div class="profile-menu">
                    <a href="{{ route('accountant.profile.show') }}" class="profile-menu-item">
                        <div class="profile-menu-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="profile-menu-content">
                            <span class="profile-menu-label">Informations personnelles</span>
                            <span class="profile-menu-description">Consultez vos informations</span>
                        </div>
                    </a>
                    
                    <a href="{{ route('accountant.profile.edit') }}" class="profile-menu-item active">
                        <div class="profile-menu-icon">
                            <i class="fas fa-user-edit"></i>
                        </div>
                        <div class="profile-menu-content">
                            <span class="profile-menu-label">Modifier le profil</span>
                            <span class="profile-menu-description">Mettez à jour vos informations</span>
                        </div>
                        <div class="profile-menu-indicator"></div>
                    </a>
                    
                    <a href="{{ route('accountant.profile.password') }}" class="profile-menu-item">
                        <div class="profile-menu-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="profile-menu-content">
                            <span class="profile-menu-label">Mot de passe</span>
                            <span class="profile-menu-description">Modifiez votre mot de passe</span>
                        </div>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Colonne de droite (formulaire) -->
        <div class="col-12 col-lg-9">
            <div class="profile-card">
                <div class="profile-card-header">
                    <h4 class="profile-card-title">
                        <i class="fas fa-user-edit text-primary me-2"></i> Modifier le profil
                    </h4>
                    <p class="profile-card-subtitle">Mettez à jour vos informations personnelles</p>
                </div>

                <div class="profile-card-body">
                    <form action="{{ route('accountant.profile.update') }}" method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                        @csrf
                        @method('PUT')

                        <!-- Section avatar -->
                        <div class="avatar-upload-section">
                            <div class="avatar-container">
                                <div class="avatar-wrapper">
                                    <img src="{{ $user->avatar ? (Str::startsWith($user->avatar, 'avatars/') ? asset('storage/' . $user->avatar) : asset($user->avatar)) : asset('images/default-avatar.png') }}" 
                                        alt="Photo de profil" 
                                        class="avatar-image"
                                        id="avatar-preview">
                                    
                                    <div class="avatar-overlay">
                                        <label for="avatar" class="avatar-edit-btn">
                                            <i class="fas fa-camera"></i>
                                        </label>
                                    </div>
                                </div>
                                <input type="file" 
                                       id="avatar" 
                                       name="avatar" 
                                       class="d-none" 
                                       accept="image/*"
                                       onchange="previewImage(this)">
                            </div>
                            <div class="avatar-info">
                                <h5 class="avatar-title">Photo de profil</h5>
                                <p class="avatar-description">Une photo claire de votre visage aide les autres utilisateurs à vous reconnaître</p>
                                @error('avatar')
                                    <div class="text-danger mt-2 small">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="profile-divider"></div>

                        <!-- Formulaire -->
                        <div class="row g-4">
                            <div class="col-12 col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">Nom complet</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" 
                                               class="form-control @error('name') is-invalid @enderror" 
                                               id="name" 
                                               name="name" 
                                               placeholder="Votre nom complet"
                                               value="{{ old('name', $user->name) }}" 
                                               required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="col-12 col-md-6">
                                <div class="form-group">
                                    <label for="email" class="form-label">Adresse email</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                        <input type="email" 
                                               class="form-control @error('email') is-invalid @enderror" 
                                               id="email" 
                                               name="email" 
                                               placeholder="<EMAIL>"
                                               value="{{ old('email', $user->email) }}" 
                                               required>
                                        @error('email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12 col-md-6">
                                <div class="form-group">
                                    <label for="phone" class="form-label">Numéro de téléphone</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                        <input type="text" 
                                               class="form-control @error('phone') is-invalid @enderror" 
                                               id="phone" 
                                               name="phone" 
                                               placeholder="Votre numéro de téléphone"
                                               value="{{ old('phone', $user->phone) }}">
                                        @error('phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12 col-md-6">
                                <div class="form-group">
                                    <label for="position" class="form-label">Poste / Fonction</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-briefcase"></i></span>
                                        <input type="text" 
                                               class="form-control @error('position') is-invalid @enderror" 
                                               id="position" 
                                               name="position" 
                                               placeholder="Votre poste ou fonction"
                                               value="{{ old('position', $user->position) }}">
                                        @error('position')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="profile-divider"></div>
                            </div>

                            <!-- Boutons d'action -->
                            <div class="col-12">
                                <div class="profile-actions">
                                    <a href="{{ route('accountant.profile.show') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i> Annuler
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> Enregistrer les modifications
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
        </div>
    </div>
</div>

@push('styles')
<style>
    /* Styles pour la page de profil */
    .profile-sidebar {
        background: white;
        border-radius: 16px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        height: 100%;
    }
    
    .profile-sidebar-header {
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .profile-sidebar-title {
        font-weight: 700;
        color: #1E293B;
        margin-bottom: 0.5rem;
    }
    
    .profile-sidebar-subtitle {
        color: #64748B;
        font-size: 0.875rem;
        margin-bottom: 0;
    }
    
    .profile-menu {
        padding: 1rem 0;
    }
    
    .profile-menu-item {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        color: #475569;
        text-decoration: none;
        position: relative;
        transition: all 0.3s ease;
    }
    
    .profile-menu-item:hover {
        background-color: rgba(37, 99, 235, 0.05);
        color: #2563EB;
    }
    
    .profile-menu-item.active {
        background-color: rgba(37, 99, 235, 0.1);
        color: #2563EB;
        font-weight: 600;
    }
    
    .profile-menu-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background-color: rgba(37, 99, 235, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: #2563EB;
        transition: all 0.3s ease;
    }
    
    .profile-menu-item:hover .profile-menu-icon,
    .profile-menu-item.active .profile-menu-icon {
        background-color: #2563EB;
        color: white;
    }
    
    .profile-menu-content {
        flex: 1;
    }
    
    .profile-menu-label {
        display: block;
        font-weight: 500;
        margin-bottom: 0.25rem;
    }
    
    .profile-menu-description {
        display: block;
        font-size: 0.75rem;
        color: #64748B;
    }
    
    .profile-menu-indicator {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 24px;
        background-color: #2563EB;
        border-radius: 4px 0 0 4px;
    }
    
    /* Carte principale */
    .profile-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }
    
    .profile-card-header {
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .profile-card-title {
        font-weight: 700;
        color: #1E293B;
        margin-bottom: 0.5rem;
    }
    
    .profile-card-subtitle {
        color: #64748B;
        font-size: 0.875rem;
        margin-bottom: 0;
    }
    
    .profile-card-body {
        padding: 1.5rem;
    }
    
    /* Section Avatar */
    .avatar-upload-section {
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
    }
    
    .avatar-container {
        margin-right: 2rem;
    }
    
    .avatar-wrapper {
        position: relative;
        width: 120px;
        height: 120px;
        border-radius: 50%;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .avatar-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: all 0.3s ease;
    }
    
    .avatar-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: all 0.3s ease;
    }
    
    .avatar-wrapper:hover .avatar-overlay {
        opacity: 1;
    }
    
    .avatar-wrapper:hover .avatar-image {
        transform: scale(1.05);
    }
    
    .avatar-edit-btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #2563EB;
        cursor: pointer;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
    }
    
    .avatar-edit-btn:hover {
        background-color: #2563EB;
        color: white;
    }
    
    .avatar-info {
        flex: 1;
    }
    
    .avatar-title {
        font-weight: 600;
        color: #1E293B;
        margin-bottom: 0.5rem;
    }
    
    .avatar-description {
        color: #64748B;
        font-size: 0.875rem;
        margin-bottom: 0;
    }
    
    /* Séparateur */
    .profile-divider {
        height: 1px;
        background-color: rgba(0, 0, 0, 0.05);
        margin: 1.5rem 0;
    }
    
    /* Formulaire */
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 500;
        color: #1E293B;
        margin-bottom: 0.5rem;
    }
    
    .input-group {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        border-radius: 8px;
        overflow: hidden;
    }
    
    .input-group-text {
        background-color: #F8FAFC;
        border-color: #E2E8F0;
        color: #64748B;
        padding: 0.75rem 1rem;
    }
    
    .form-control {
        border-color: #E2E8F0;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
    }
    
    .form-control:focus {
        border-color: #2563EB;
        box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.25);
    }
    
    /* Boutons d'action */
    .profile-actions {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
    }
    
    .btn-outline-secondary {
        border-color: #E2E8F0;
        color: #64748B;
    }
    
    .btn-outline-secondary:hover {
        background-color: #F1F5F9;
        border-color: #CBD5E1;
        color: #475569;
    }
    
    .btn-primary {
        background-color: #2563EB;
        border-color: #2563EB;
        padding: 0.75rem 1.5rem;
    }
    
    .btn-primary:hover {
        background-color: #1D4ED8;
        border-color: #1D4ED8;
    }
    
    /* Animation de chargement pour l'image */
    .img-loading {
        opacity: 0.5;
        transition: opacity 0.3s ease-in-out;
    }
    
    /* Styles pour les notifications */
    #notification-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        display: flex;
        flex-direction: column;
        gap: 10px;
        max-width: 350px;
    }
    
    .profile-notification {
        display: flex;
        align-items: center;
        background: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 12px 15px;
        transform: translateX(100%);
        opacity: 0;
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
    }
    
    .profile-notification::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 4px;
    }
    
    .profile-notification.info::before {
        background-color: #3B82F6;
    }
    
    .profile-notification.success::before {
        background-color: #10B981;
    }
    
    .profile-notification.error::before {
        background-color: #EF4444;
    }
    
    .profile-notification.loading::before {
        background-color: #F59E0B;
    }
    
    .profile-notification.show {
        transform: translateX(0);
        opacity: 1;
    }
    
    .profile-notification.hide {
        transform: translateX(100%);
        opacity: 0;
    }
    
    .notification-icon {
        margin-right: 12px;
        font-size: 18px;
    }
    
    .profile-notification.info .notification-icon {
        color: #3B82F6;
    }
    
    .profile-notification.success .notification-icon {
        color: #10B981;
    }
    
    .profile-notification.error .notification-icon {
        color: #EF4444;
    }
    
    .profile-notification.loading .notification-icon {
        color: #F59E0B;
    }
    
    .notification-content {
        flex: 1;
        font-size: 14px;
        color: #1E293B;
    }
    
    .notification-close {
        background: transparent;
        border: none;
        color: #94A3B8;
        cursor: pointer;
        font-size: 14px;
        padding: 0;
        margin-left: 10px;
        transition: color 0.2s ease;
    }
    
    .notification-close:hover {
        color: #475569;
    }
    
    /* Animations */
    .pulse {
        animation: pulse 0.6s ease-in-out;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    .input-focused {
        transition: all 0.3s ease;
    }
    
    .input-focused .input-group-text {
        color: #2563EB;
        background-color: rgba(37, 99, 235, 0.05);
    }
    
    .avatar-wrapper.uploading {
        box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.3);
    }
    
    .avatar-wrapper.uploaded {
        box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.3);
        animation: success-pulse 1s ease-in-out;
    }
    
    @keyframes success-pulse {
        0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
        100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
    }
    
    /* Styles responsives */
    @media (max-width: 992px) {
        .profile-sidebar {
            margin-bottom: 1.5rem;
        }
        
        .avatar-upload-section {
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        
        .avatar-container {
            margin-right: 0;
            margin-bottom: 1.5rem;
        }
        
        #notification-container {
            left: 20px;
            right: 20px;
            max-width: calc(100% - 40px);
        }
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation du formulaire
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
            
            // Afficher une notification d'erreur
            showNotification('Veuillez corriger les erreurs dans le formulaire', 'error');
        } else {
            // Afficher une notification de chargement
            showNotification('Enregistrement en cours...', 'loading');
        }
        form.classList.add('was-validated');
    });
    
    // Animation des champs du formulaire
    const formInputs = document.querySelectorAll('.form-control');
    formInputs.forEach(input => {
        // Ajouter une classe si le champ a une valeur
        if (input.value.trim() !== '') {
            input.classList.add('has-value');
        }
        
        // Écouter les événements de focus et blur
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('input-focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('input-focused');
            if (this.value.trim() !== '') {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });
    });
    
    // Gérer les animations du menu latéral
    const menuItems = document.querySelectorAll('.profile-menu-item');
    menuItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.querySelector('.profile-menu-icon').classList.add('pulse');
        });
        
        item.addEventListener('mouseleave', function() {
            this.querySelector('.profile-menu-icon').classList.remove('pulse');
        });
    });
});

// Prévisualisation de l'image
function previewImage(input) {
    const preview = document.getElementById('avatar-preview');
    const container = document.querySelector('.avatar-wrapper');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        // Ajouter une classe de chargement
        preview.classList.add('img-loading');
        container.classList.add('uploading');
        
        reader.onload = function(e) {
            preview.src = e.target.result;
            
            // Retirer la classe de chargement après le chargement
            preview.onload = function() {
                preview.classList.remove('img-loading');
                container.classList.remove('uploading');
                container.classList.add('uploaded');
                
                // Afficher une notification de succès
                showNotification('Photo téléchargée avec succès', 'success');
                
                // Réinitialiser après un délai
                setTimeout(() => {
                    container.classList.remove('uploaded');
                }, 2000);
            }
        }
        
        reader.readAsDataURL(input.files[0]);
    }
}

// Fonction pour afficher des notifications
function showNotification(message, type = 'info') {
    // Créer l'élément de notification s'il n'existe pas déjà
    let notificationContainer = document.getElementById('notification-container');
    
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'notification-container';
        document.body.appendChild(notificationContainer);
    }
    
    // Créer la notification
    const notification = document.createElement('div');
    notification.className = `profile-notification ${type}`;
    
    // Icône en fonction du type
    let icon = '';
    switch(type) {
        case 'success':
            icon = '<i class="fas fa-check-circle"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle"></i>';
            break;
        case 'loading':
            icon = '<i class="fas fa-spinner fa-spin"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle"></i>';
    }
    
    notification.innerHTML = `
        <div class="notification-icon">${icon}</div>
        <div class="notification-content">${message}</div>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;
    
    // Ajouter au conteneur
    notificationContainer.appendChild(notification);
    
    // Animation d'entrée
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Fermeture automatique après 3 secondes (sauf pour loading)
    if (type !== 'loading') {
        setTimeout(() => {
            closeNotification(notification);
        }, 3000);
    }
    
    // Événement de fermeture manuelle
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        closeNotification(notification);
    });
    
    return notification;
}

// Fermer une notification
function closeNotification(notification) {
    notification.classList.remove('show');
    notification.classList.add('hide');
    
    setTimeout(() => {
        notification.remove();
    }, 300);
}
</script>
@endpush
@endsection
