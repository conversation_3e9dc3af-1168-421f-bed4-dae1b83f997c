@extends('layouts.admin_minimal')

@php
// Si la variable $user n'est pas définie, utiliser Auth::user()
if (!isset($user)) {
    $user = \Illuminate\Support\Facades\Auth::user();
}
@endphp

@section('title', 'Mon Profil')

@section('content')
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 fw-bold text-primary">
                        <i class="fas fa-user me-2"></i> Mon Profil
                    </h5>
                    <div>
                        <a href="{{ route('admin.dashboard') }}" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="fas fa-home me-1"></i> Tableau de bord
                        </a>
                        <a href="{{ route('admin.profile.edit') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-edit me-1"></i> Modifier
                        </a>
                    </div>
                </div>
                <div class="card-body p-4">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="fas fa-check-circle me-2"></i> {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-4 mb-4 text-center">
                            <div class="profile-avatar-container mx-auto mb-3">
                                @if($user->avatar && file_exists(public_path($user->avatar)))
                                    <img src="{{ asset($user->avatar) }}" 
                                         alt="Photo de profil" 
                                         class="profile-avatar-img">
                                @else
                                    <div class="profile-avatar-placeholder">
                                        {{ substr($user->name, 0, 1) }}
                                    </div>
                                @endif
                            </div>
                            <h5 class="fw-bold mb-1">{{ $user->name }}</h5>
                            <span class="badge bg-primary mb-3">Administrateur</span>
                            <div class="d-grid gap-2">
                                <a href="{{ route('admin.profile.edit') }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit me-1"></i> Modifier le profil
                                </a>
                                <a href="{{ route('admin.profile.password') }}" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-lock me-1"></i> Changer le mot de passe
                                </a>
                            </div>
                        </div>

                        <div class="col-md-8">
                            <div class="card border-0 bg-light">
                                <div class="card-body">
                                    <h6 class="fw-bold mb-3">Informations personnelles</h6>
                                    
                                    <div class="profile-info-item">
                                        <div class="profile-info-icon">
                                            <i class="fas fa-envelope"></i>
                                        </div>
                                        <div class="profile-info-content">
                                            <span class="profile-info-label">Email</span>
                                            <span class="profile-info-value">{{ $user->email }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="profile-info-item">
                                        <div class="profile-info-icon">
                                            <i class="fas fa-phone"></i>
                                        </div>
                                        <div class="profile-info-content">
                                            <span class="profile-info-label">Téléphone</span>
                                            <span class="profile-info-value">{{ $user->phone ?? 'Non renseigné' }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="profile-info-item">
                                        <div class="profile-info-icon">
                                            <i class="fas fa-briefcase"></i>
                                        </div>
                                        <div class="profile-info-content">
                                            <span class="profile-info-label">Poste</span>
                                            <span class="profile-info-value">{{ $user->position ?? 'Non renseigné' }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="profile-info-item">
                                        <div class="profile-info-icon">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                        <div class="profile-info-content">
                                            <span class="profile-info-label">Membre depuis</span>
                                            <span class="profile-info-value">{{ $user->created_at->format('d/m/Y') }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="profile-info-item">
                                        <div class="profile-info-icon">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div class="profile-info-content">
                                            <span class="profile-info-label">Dernière connexion</span>
                                            <span class="profile-info-value">{{ $user->last_login_at ? $user->last_login_at->format('d/m/Y H:i') : 'Jamais' }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <h6 class="fw-bold mb-3">Sécurité du compte</h6>
                                <div class="d-flex align-items-center mb-3">
                                    <div class="security-icon me-3 bg-success">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">Mot de passe</h6>
                                        <p class="text-muted small mb-0">Dernière modification: {{ $user->password_changed_at ? $user->password_changed_at->format('d/m/Y') : 'Inconnue' }}</p>
                                    </div>
                                    <div class="ms-auto">
                                        <a href="{{ route('admin.profile.password') }}" class="btn btn-sm btn-outline-secondary">
                                            Modifier
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.profile-avatar-container {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    background-color: var(--primary-color);
    margin-bottom: 1rem;
}

.profile-avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    font-weight: 600;
}

.profile-info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.25rem;
    padding-bottom: 1.25rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.profile-info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.profile-info-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.profile-info-content {
    flex: 1;
}

.profile-info-label {
    display: block;
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.profile-info-value {
    font-weight: 600;
    color: #212529;
}

.security-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
@endpush
