@extends('layouts.accountant')

@section('title', 'Bons de commande')

@push('styles')
<style>
    .order-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    .order-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .badge-pending {
        background-color: #fbbf24;
        color: #92400e;
    }
    .badge-validated {
        background-color: #34d399;
        color: #065f46;
    }
    .badge-rejected {
        background-color: #f87171;
        color: #991b1b;
    }
    .order-details {
        font-size: 0.9rem;
    }
    .detail-container {
        border: 2px solid #e2e8f0;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        background-color: #f8fafc;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    .detail-container:last-child {
        margin-bottom: 0;
    }
    .detail-container:hover {
        border-color: #64748b;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .quantity-info {
        display: flex;
        justify-content: space-between;
        color: #6b7280;
        margin: 0.75rem 0;
        padding: 0.75rem;
        background-color: #fff;
        border-radius: 0.5rem;
    }
    .quantity-item {
        display: flex;
        align-items: center;
    }
    .quantity-item i {
        margin-right: 0.5rem;
        color: #6b7280;
    }
    .price-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 0.75rem;
        margin-top: 0.75rem;
        border-top: 1px solid #e5e7eb;
    }
    .order-product {
        padding: 1.5rem;
        margin: 1.25rem 0;
        background-color: #fff;
        border: 4px solid #334155;
        border-radius: 12px;
    }
    .order-product:hover {
        border-color: #0f172a;
    }
    .order-product .d-flex {
        margin-bottom: 1rem;
    }
    .order-product .d-flex:last-child {
        margin-bottom: 0;
    }
    .order-product .text-muted {
        margin: 0.75rem 0;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- En-tête -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Bons de commande</h1>
        <a href="{{ route('accountant.cement-orders.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Nouveau bon de commande
        </a>
    </div>

    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">En attente</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Validées</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['validated'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Rejetées</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['rejected'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filtres</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('accountant.cement-orders.index') }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">Statut</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">Tous les statuts</option>
                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>En attente</option>
                        <option value="validated" {{ request('status') === 'validated' ? 'selected' : '' }}>Validé</option>
                        <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Rejeté</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="start_date" class="form-label">Date début</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ request('start_date') }}">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">Date fin</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ request('end_date') }}">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Filtrer
                    </button>
                    <a href="{{ route('accountant.cement-orders.index') }}" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des bons de commande -->
    <div class="row">
        @forelse($orders as $order)
            <div class="col-12 mb-4">
                <div class="card order-card">
                    <div class="card-body">
                        <div class="row">
                            <!-- En-tête -->
                            <div class="col-md-3">
                                <h5 class="card-title mb-1">
                                    <i class="fas fa-file-invoice"></i> {{ $order->reference }}
                                </h5>
                                <div class="text-muted mb-2">
                                    <i class="fas fa-calendar"></i> {{ $order->created_at->format('d/m/Y H:i') }}
                                </div>
                                <div class="mb-2">
                                    @if($order->status === 'pending')
                                        <span class="badge badge-pending">En attente</span>
                                    @elseif($order->status === 'validated')
                                        <span class="badge badge-validated">Validé</span>
                                    @else
                                        <span class="badge badge-rejected">Rejeté</span>
                                    @endif
                                </div>
                            </div>

                            <!-- Détails de la commande -->
                            <div class="col-md-6">
                                <div class="order-details">
                                    <div class="mb-2">
                                        <strong>Produit:</strong> {{ $order->product->name }}
                                    </div>
                                    @foreach($order->details as $detail)
                                        <div class="detail-container">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <i class="fas fa-map-marker-alt"></i>
                                                    <strong>{{ optional($detail->destination)->name ?? 'N/A' }}</strong>
                                                    <span class="text-muted ms-2">
                                                        <i class="fas fa-user"></i>
                                                        {{ optional($detail->supplier)->name ?? 'N/A' }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <span class="badge bg-info">
                                                        <i class="fas fa-truck"></i>
                                                        {{ $detail->trips_count ?? 0 }} voyages
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="quantity-info">
                                                <div class="quantity-item">
                                                    <i class="fas fa-box"></i>
                                                    <span>Quantité: {{ number_format($detail->total_tonnage, 2) }} T</span>
                                                </div>
                                                <div class="quantity-item">
                                                    <i class="fas fa-check"></i>
                                                    <span>Livré: {{ number_format($detail->delivered_quantity ?? 0, 2) }} T</span>
                                                </div>
                                                <div class="quantity-item">
                                                    <i class="fas fa-clock"></i>
                                                    <span>Restant: {{ number_format($detail->remaining_quantity, 2) }} T</span>
                                                </div>
                                            </div>

                                            <div class="price-info">
                                                <span>Prix unitaire: {{ $detail->formatted_unit_price }}</span>
                                                <strong>Total: {{ $detail->formatted_total_amount }}</strong>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <!-- Totaux et actions -->
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="text-muted">Tonnage total</div>
                                    <strong>{{ $order->total_tonnage }}</strong>
                                </div>
                                <div class="mb-3">
                                    <div class="text-muted">Montant total</div>
                                    <strong>{{ $order->formatted_total_amount }}</strong>
                                </div>
                                <div class="mb-3">
                                    <div class="text-muted">Montant payé</div>
                                    <strong>{{ $order->formatted_paid_amount }}</strong>
                                </div>
                                <div class="mb-3">
                                    <div class="text-muted">Reste à payer</div>
                                    <strong>{{ $order->formatted_remaining_amount }}</strong>
                                </div>
                                <div class="d-grid gap-2">
                                    <a href="{{ route('accountant.cement-orders.show', ['cement_order' => $order->id]) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i> Voir les détails
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="alert alert-info">
                    Aucun bon de commande trouvé.
                </div>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    <div class="d-flex justify-content-center">
        {{ $orders->links() }}
    </div>
</div>
@endsection
