<?php $__env->startSection('title', 'Créer un utilisateur'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .wizard-container {
        max-width: 800px;
        margin: 2rem auto;
        background: white;
        border-radius: 20px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        overflow: hidden;
    }

    .wizard-header {
        background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
        color: white;
        padding: 2rem;
        text-align: center;
    }

    .wizard-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .wizard-subtitle {
        opacity: 0.9;
        font-size: 1.1rem;
    }

    /* Stepper */
    .step-progress {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 2rem 0;
        padding: 0 2rem;
    }

    .step-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        flex: 1;
        max-width: 150px;
    }

    .step-number {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #e5e7eb;
        color: #6b7280;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
        position: relative;
        z-index: 2;
    }

    .step-item.active .step-number {
        background: #4f46e5;
        color: white;
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
    }

    .step-item.completed .step-number {
        background: #10b981;
        color: white;
    }

    .step-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: #6b7280;
        text-align: center;
        line-height: 1.3;
    }

    .step-item.active .step-title {
        color: #4f46e5;
    }

    .step-item.completed .step-title {
        color: #10b981;
    }

    .step-connector {
        position: absolute;
        top: 25px;
        left: 50%;
        right: -50%;
        height: 2px;
        background: #e5e7eb;
        z-index: 1;
    }

    .step-item:last-child .step-connector {
        display: none;
    }

    .step-item.completed .step-connector {
        background: #10b981;
    }

    /* Contenu du wizard */
    .wizard-content {
        padding: 3rem;
    }

    .step-content {
        display: none;
    }

    .step-content.active {
        display: block;
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .step-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .step-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin: 0 auto 1rem;
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
    }

    .step-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }

    .step-description {
        color: #6b7280;
        font-size: 1rem;
    }

    /* Formulaires */
    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    .form-control.is-invalid {
        border-color: #ef4444;
    }

    .invalid-feedback {
        color: #ef4444;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Sélection de rôles */
    .roles-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .role-card {
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }

    .role-card:hover {
        border-color: #4f46e5;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
    }

    .role-card.selected {
        border-color: #4f46e5;
        background: #f0f9ff;
    }

    .role-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .role-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        margin-right: 1rem;
    }

    .role-name {
        font-weight: 600;
        color: #1f2937;
        margin: 0;
    }

    .role-description {
        color: #6b7280;
        font-size: 0.875rem;
        margin: 0;
    }

    /* Boutons */
    .btn {
        padding: 0.75rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        font-size: 1rem;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
    }

    .btn-secondary {
        background: #6b7280;
        color: white;
    }

    .btn-secondary:hover {
        background: #4b5563;
        transform: translateY(-2px);
    }

    .btn-success {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
    }

    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    }

    /* Navigation des étapes */
    .step-navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 3rem;
        padding-top: 2rem;
        border-top: 1px solid #e5e7eb;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .wizard-container {
            margin: 1rem;
            border-radius: 15px;
        }

        .wizard-content {
            padding: 2rem 1.5rem;
        }

        .step-progress {
            padding: 0 1rem;
        }

        .step-item {
            max-width: 100px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .step-title {
            font-size: 0.75rem;
        }

        .roles-grid {
            grid-template-columns: 1fr;
        }

        .step-navigation {
            flex-direction: column;
            gap: 1rem;
        }

        .btn {
            width: 100%;
            justify-content: center;
        }
    }
</style>
<?php $__env->stopSection(); ?>
    }

    .form-step.prev {
        animation: slideOutLeft 0.5s ease-out;
    }

    .form-step.next {
        animation: slideInRight 0.5s ease-out;
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(50px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slideOutLeft {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(-50px);
        }
    }

    /* Header moderne avec glassmorphism */
    .header-section {
        background: var(--glass-bg);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--card-shadow);
    }

    .breadcrumb-modern {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius);
        padding: 1rem 1.5rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .breadcrumb-modern .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        font-size: 1.2em;
        color: #6c757d;
    }

    .breadcrumb-modern a {
        color: #495057;
        text-decoration: none;
        transition: var(--transition);
    }

    .breadcrumb-modern a:hover {
        color: #007bff;
        transform: translateX(2px);
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .page-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-size: 1.1rem;
        margin-bottom: 0;
    }

    .text-gradient {
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Styles pour les étapes du formulaire */
    .form-card {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius);
        overflow: hidden;
    }

    .step-header {
        text-align: center;
        padding: 3rem 2rem 2rem;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .step-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 2rem;
        color: white;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .step-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: white;
        margin-bottom: 0.5rem;
    }

    .step-description {
        color: rgba(255, 255, 255, 0.8);
        font-size: 1rem;
        margin: 0;
    }

    .step-content {
        padding: 3rem 2rem;
        min-height: 400px;
    }

    .step-navigation {
        padding: 2rem;
        background: rgba(255, 255, 255, 0.05);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    /* Groupes de formulaire modernes */
    .form-group-modern {
        position: relative;
    }

    .input-group-modern {
        position: relative;
        display: flex;
        align-items: stretch;
    }

    .input-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
        color: #6c757d;
        font-size: 1.1rem;
    }

    .form-control-modern {
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius);
        padding: 1rem 1rem 1rem 3rem;
        font-size: 1rem;
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        color: white;
    }

    .form-control-modern::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    .form-control-modern:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
    }

    .form-floating > .form-control-modern:focus ~ label,
    .form-floating > .form-control-modern:not(:placeholder-shown) ~ label {
        color: #007bff;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    }

    .form-floating > label {
        color: rgba(255, 255, 255, 0.8);
        padding-left: 3rem;
    }

    .form-hint {
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.875rem;
    }

    /* Aperçu en temps réel */
    .live-preview-card {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-top: 2rem;
        text-align: center;
        backdrop-filter: blur(10px);
    }

    .preview-header {
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 1rem;
        font-size: 0.9rem;
    }

    .preview-content {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .preview-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: var(--secondary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin-bottom: 1rem;
        transition: var(--transition);
    }

    .preview-name {
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
    }

    .preview-email {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.875rem;
        margin: 0;
    }

    /* Section sécurité */
    .security-tips {
        margin-bottom: 2rem;
    }

    .tip-card {
        background: rgba(255, 193, 7, 0.1);
        border: 1px solid rgba(255, 193, 7, 0.3);
        border-radius: var(--border-radius);
        padding: 1rem;
        display: flex;
        align-items: flex-start;
        gap: 1rem;
    }

    .tip-card i {
        color: #ffc107;
        font-size: 1.5rem;
        margin-top: 0.25rem;
    }

    .tip-content strong {
        color: #ffc107;
        display: block;
        margin-bottom: 0.5rem;
    }

    .tip-content p {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        font-size: 0.9rem;
        line-height: 1.4;
    }

    /* Champs de mot de passe améliorés */
    .password-field {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.6);
        cursor: pointer;
        z-index: 10;
        transition: var(--transition);
        font-size: 1.1rem;
    }

    .password-toggle:hover {
        color: #007bff;
        transform: translateY(-50%) scale(1.1);
    }

    .password-strength-container {
        margin-top: 0.75rem;
    }

    .password-strength {
        height: 6px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }

    .password-strength::after {
        content: '';
        display: block;
        height: 100%;
        border-radius: 3px;
        transition: var(--transition);
        width: 0%;
    }

    .password-strength.weak::after {
        width: 25%;
        background: linear-gradient(90deg, #dc3545, #ff6b7a);
    }

    .password-strength.medium::after {
        width: 60%;
        background: linear-gradient(90deg, #ffc107, #ffda6a);
    }

    .password-strength.strong::after {
        width: 100%;
        background: linear-gradient(90deg, #28a745, #5cb85c);
    }

    .strength-text {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.7);
        text-align: center;
    }

    .password-match {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .password-match.match {
        color: #28a745;
    }

    .password-match.no-match {
        color: #dc3545;
    }

    .password-requirements {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .password-requirements h6 {
        color: white;
        margin-bottom: 1rem;
        font-size: 1rem;
    }

    .requirements-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .requirements-list li {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.5rem 0;
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
        transition: var(--transition);
    }

    .requirements-list li i {
        width: 16px;
        text-align: center;
        color: #dc3545;
        transition: var(--transition);
    }

    .requirements-list li.valid i {
        color: #28a745;
    }

    .requirements-list li.valid {
        color: rgba(255, 255, 255, 0.9);
    }

    /* Cards modernes */
    .animate-card {
        animation: slideInUp 0.6s ease-out;
        transition: var(--transition);
    }

    .animate-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--card-hover-shadow);
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Sélection de rôles moderne */
    .roles-selection {
        padding: 1rem 0;
    }

    .roles-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .role-card-modern {
        position: relative;
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
    }

    .role-card-modern:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .role-checkbox {
        position: absolute;
        opacity: 0;
        pointer-events: none;
    }

    .role-label-modern {
        display: block;
        padding: 1.5rem;
        cursor: pointer;
        transition: var(--transition);
        margin: 0;
        height: 100%;
    }

    .role-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .role-icon-modern {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.3rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .role-level {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .level-critique {
        background: rgba(220, 53, 69, 0.2);
        color: #ff6b7a;
        border: 1px solid rgba(220, 53, 69, 0.3);
    }

    .level-élevé {
        background: rgba(0, 123, 255, 0.2);
        color: #4dabf7;
        border: 1px solid rgba(0, 123, 255, 0.3);
    }

    .level-moyen {
        background: rgba(255, 193, 7, 0.2);
        color: #ffd43b;
        border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .level-faible {
        background: rgba(40, 167, 69, 0.2);
        color: #51cf66;
        border: 1px solid rgba(40, 167, 69, 0.3);
    }

    .level-personnalisé {
        background: rgba(108, 117, 125, 0.2);
        color: #adb5bd;
        border: 1px solid rgba(108, 117, 125, 0.3);
    }

    .role-body {
        margin-bottom: 1.5rem;
    }

    .role-name-modern {
        font-size: 1.1rem;
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
    }

    .role-description-modern {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.7);
        line-height: 1.4;
        margin: 0;
    }

    .role-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .role-check-modern {
        width: 28px;
        height: 28px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: var(--transition);
        color: transparent;
    }

    .selection-text {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.6);
        font-weight: 500;
    }

    .role-checkbox:checked + .role-label-modern {
        background: rgba(0, 123, 255, 0.1);
        border-color: #007bff;
    }

    .role-checkbox:checked + .role-label-modern .role-check-modern {
        background: #007bff;
        border-color: #007bff;
        color: white;
        transform: scale(1.1);
    }

    .role-checkbox:checked + .role-label-modern .selection-text {
        color: #007bff;
    }

    .role-checkbox:checked + .role-label-modern .role-name-modern {
        color: #007bff;
    }

    /* Aperçu des rôles sélectionnés */
    .selected-roles-preview {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .selected-roles-preview h6 {
        color: white;
        margin-bottom: 1rem;
        font-size: 1rem;
    }

    .selected-roles-container {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .selected-roles-container .badge {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        border-radius: 20px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .no-selection {
        color: rgba(255, 255, 255, 0.5);
        font-style: italic;
    }

    /* Section de confirmation */
    .confirmation-summary {
        padding: 1rem 0;
    }

    .summary-card {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        backdrop-filter: blur(10px);
    }

    .summary-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .summary-header i {
        font-size: 1.5rem;
        color: #28a745;
    }

    .summary-header h5 {
        color: white;
        margin: 0;
        font-weight: 600;
    }

    .user-summary {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--border-radius);
    }

    .summary-avatar {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background: var(--success-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.8rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .summary-info h6 {
        color: white;
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 1.2rem;
    }

    .summary-info p {
        color: rgba(255, 255, 255, 0.7);
        margin: 0;
    }

    .summary-details {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--border-radius);
    }

    .detail-label {
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
    }

    .detail-value {
        color: white;
        font-weight: 600;
    }

    .summary-roles {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .creation-notice {
        margin-top: 1rem;
    }

    .notice-card {
        background: rgba(23, 162, 184, 0.1);
        border: 1px solid rgba(23, 162, 184, 0.3);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        display: flex;
        gap: 1rem;
    }

    .notice-card i {
        color: #17a2b8;
        font-size: 1.5rem;
        margin-top: 0.25rem;
    }

    .notice-content h6 {
        color: #17a2b8;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .notice-content ul {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        padding-left: 1.5rem;
    }

    .notice-content li {
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    /* Boutons modernes */
    .btn {
        border-radius: var(--border-radius);
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
        text-transform: none;
        letter-spacing: 0.5px;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .btn-primary {
        background: var(--primary-gradient);
        color: white;
    }

    .btn-success {
        background: var(--success-gradient);
        color: white;
    }

    .btn-outline-secondary {
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.8);
        background: transparent;
        backdrop-filter: blur(10px);
    }

    .btn-outline-secondary:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border-color: rgba(255, 255, 255, 0.5);
    }

    .btn-outline-light {
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }

    .btn-outline-light:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
    }

    /* Bouton flottant de retour */
    .floating-back-btn {
        position: fixed;
        top: 2rem;
        left: 2rem;
        z-index: 1000;
    }

    .btn-floating {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
    }

    /* Couleurs spécifiques */
    .bg-purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .container-fluid {
            margin: 1rem;
            padding: 1rem;
        }

        .page-title {
            font-size: 2rem;
        }

        .creation-stepper {
            flex-direction: column;
            gap: 1rem;
        }

        .step {
            max-width: none;
        }

        .step-connector {
            display: none;
        }

        .step-circle {
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
        }

        .step-content {
            padding: 2rem 1rem;
            min-height: auto;
        }

        .roles-grid {
            grid-template-columns: 1fr;
        }

        .step-navigation {
            flex-direction: column;
            gap: 1rem;
        }

        .btn {
            width: 100%;
        }

        .floating-back-btn {
            top: 1rem;
            left: 1rem;
        }

        .user-summary {
            flex-direction: column;
            text-align: center;
        }

        .detail-row {
            flex-direction: column;
            gap: 0.5rem;
            text-align: center;
        }
    }

    @media (max-width: 576px) {
        .step-header {
            padding: 2rem 1rem 1rem;
        }

        .step-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }

        .step-title {
            font-size: 1.5rem;
        }

        .form-control-modern {
            padding: 0.875rem 0.875rem 0.875rem 2.5rem;
        }

        .input-icon {
            left: 0.75rem;
            font-size: 1rem;
        }

        .form-floating > label {
            padding-left: 2.5rem;
        }
    }

    /* Animations d'entrée */
    .animate-card {
        animation: slideInUp 0.8s ease-out;
    }

    .form-step {
        animation: fadeInScale 0.6s ease-out;
    }

    @keyframes fadeInScale {
        from {
            opacity: 0;
            transform: scale(0.95);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    /* États de validation */
    .is-invalid {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }

    .invalid-feedback {
        color: #ff6b7a;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .invalid-feedback::before {
        content: '⚠';
        font-size: 1rem;
    }

    /* Effets de focus améliorés */
    .form-control-modern:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1), 0 4px 15px rgba(0, 123, 255, 0.2);
    }

    /* Transitions fluides pour les étapes */
    .form-step {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .form-step:not(.active) {
        pointer-events: none;
    }

    /* Amélioration de l'accessibilité */
    .btn:focus,
    .form-control-modern:focus,
    .role-label-modern:focus {
        outline: 2px solid #007bff;
        outline-offset: 2px;
    }

    /* Indicateurs de progression */
    .step.active .step-circle {
        animation: pulse-glow 2s infinite;
    }

    @keyframes pulse-glow {
        0%, 100% {
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        50% {
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6), 0 0 20px rgba(102, 126, 234, 0.3);
        }
    }

    /* Champs de mot de passe */
    .password-field {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        z-index: 10;
        transition: var(--transition);
    }

    .password-toggle:hover {
        color: #007bff;
    }

    .password-strength {
        height: 4px;
        background: #e9ecef;
        border-radius: 2px;
        margin-top: 0.5rem;
        overflow: hidden;
    }

    .password-strength::after {
        content: '';
        display: block;
        height: 100%;
        border-radius: 2px;
        transition: var(--transition);
    }

    .password-strength.weak::after {
        width: 33%;
        background: #dc3545;
    }

    .password-strength.medium::after {
        width: 66%;
        background: #ffc107;
    }

    .password-strength.strong::after {
        width: 100%;
        background: #28a745;
    }

    .password-match {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .password-match.match {
        color: #28a745;
    }

    .password-match.no-match {
        color: #dc3545;
    }

    .password-requirements {
        margin-top: 1rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: var(--border-radius);
        border-left: 4px solid #007bff;
    }

    /* Cartes de rôles */
    .roles-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .role-card {
        position: relative;
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: var(--transition);
        background: #fff;
        border: 2px solid #e9ecef;
    }

    .role-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border-color: #007bff;
    }

    .role-checkbox {
        position: absolute;
        opacity: 0;
        pointer-events: none;
    }

    .role-label {
        display: flex;
        align-items: center;
        padding: 1rem;
        cursor: pointer;
        transition: var(--transition);
        margin: 0;
    }

    .role-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .role-info {
        flex: 1;
    }

    .role-name {
        font-weight: 600;
        font-size: 1rem;
        color: #212529;
        margin-bottom: 0.25rem;
    }

    .role-description {
        font-size: 0.875rem;
        color: #6c757d;
        line-height: 1.4;
    }

    .role-check {
        width: 24px;
        height: 24px;
        border: 2px solid #dee2e6;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: var(--transition);
        color: transparent;
        flex-shrink: 0;
    }

    .role-checkbox:checked + .role-label {
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border-color: #007bff;
    }

    .role-checkbox:checked + .role-label .role-check {
        background: #007bff;
        border-color: #007bff;
        color: white;
        transform: scale(1.1);
    }

    .role-checkbox:checked + .role-label .role-name {
        color: #007bff;
    }

    /* Couleurs spécifiques pour les rôles */
    .bg-purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    /* Boutons modernes */
    .btn-modern {
        border-radius: var(--border-radius);
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-modern:hover::before {
        left: 100%;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .btn-primary.btn-modern {
        background: var(--primary-gradient);
    }

    .btn-outline-secondary.btn-modern {
        border: 2px solid #6c757d;
        color: #6c757d;
        background: transparent;
    }

    .btn-outline-secondary.btn-modern:hover {
        background: #6c757d;
        color: white;
    }

    /* Actions du formulaire */
    .form-actions {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 2px solid #e9ecef;
    }

    /* Panneau d'aide */
    .help-section {
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e9ecef;
    }

    .help-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .help-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }

    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        line-height: 1.5;
        margin: 0;
    }

    /* Aperçu utilisateur */
    .user-preview {
        padding: 1rem 0;
    }

    .preview-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin: 0 auto 1rem;
        transition: var(--transition);
    }

    .preview-avatar:hover {
        transform: scale(1.1);
    }

    .preview-name {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #212529;
    }

    .preview-email {
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    .preview-roles .badge {
        margin: 0.25rem;
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
        border-radius: 20px;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }

        .roles-container {
            grid-template-columns: 1fr;
        }

        .form-actions .d-flex {
            flex-direction: column;
            gap: 1rem;
        }

        .btn-modern {
            width: 100%;
        }
    }

    /* Animations d'entrée */
    .form-section {
        animation: fadeInUp 0.6s ease-out;
        animation-fill-mode: both;
    }

    .form-section:nth-child(1) { animation-delay: 0.1s; }
    .form-section:nth-child(2) { animation-delay: 0.2s; }
    .form-section:nth-child(3) { animation-delay: 0.3s; }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="wizard-container">
    <!-- En-tête du wizard -->
    <div class="wizard-header">
        <h1 class="wizard-title">
            <i class="fas fa-user-plus me-2"></i>Créer un utilisateur
        </h1>
        <p class="wizard-subtitle">Suivez les étapes pour ajouter un nouveau membre à votre équipe</p>
    </div>

    <!-- Indicateur de progression -->
    <div class="step-progress">
        <div class="step-item active" data-step="1">
            <div class="step-number">1</div>
            <div class="step-title">Informations<br>personnelles</div>
            <div class="step-connector"></div>
        </div>
        <div class="step-item" data-step="2">
            <div class="step-number">2</div>
            <div class="step-title">Sécurité</div>
            <div class="step-connector"></div>
        </div>
        <div class="step-item" data-step="3">
            <div class="step-number">3</div>
            <div class="step-title">Rôles</div>
            <div class="step-connector"></div>
        </div>
        <div class="step-item" data-step="4">
            <div class="step-number">4</div>
            <div class="step-title">Confirmation</div>
        </div>
    </div>

    <!-- Contenu du formulaire -->
    <form method="POST" action="<?php echo e(route('admin.users.store')); ?>" id="userForm">
        <?php echo csrf_field(); ?>

        <!-- Étape 1: Informations personnelles -->
        <div class="step-content active" id="step-1">
            <div class="step-header">
                <div class="step-icon">
                    <i class="fas fa-user"></i>
                </div>
                <h2 class="step-title">Informations personnelles</h2>
                <p class="step-description">Saisissez les informations de base de l'utilisateur</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="name" class="form-label">Nom complet *</label>
                        <input type="text"
                               class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="name"
                               name="name"
                               value="<?php echo e(old('name')); ?>"
                               required>
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="email" class="form-label">Adresse email *</label>
                        <input type="email"
                               class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="email"
                               name="email"
                               value="<?php echo e(old('email')); ?>"
                               required>
                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
            </div>

            <div class="step-navigation">
                <button type="button" class="btn btn-secondary" disabled>
                    <i class="fas fa-arrow-left me-2"></i>Précédent
                </button>
                <button type="button" class="btn btn-primary" onclick="nextStep(2)">
                    Suivant<i class="fas fa-arrow-right ms-2"></i>
                </button>
            </div>
        </div>

        <!-- Étape 2: Sécurité -->
        <div class="step-content" id="step-2">
            <div class="step-header">
                <div class="step-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h2 class="step-title">Sécurité et accès</h2>
                <p class="step-description">Définissez les paramètres de sécurité du compte</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="password" class="form-label">Mot de passe *</label>
                        <input type="password"
                               class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="password"
                               name="password"
                               required>
                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="password_confirmation" class="form-label">Confirmer le mot de passe *</label>
                        <input type="password"
                               class="form-control"
                               id="password_confirmation"
                               name="password_confirmation"
                               required>
                    </div>
                </div>
            </div>

            <div class="step-navigation">
                <button type="button" class="btn btn-secondary" onclick="prevStep(1)">
                    <i class="fas fa-arrow-left me-2"></i>Précédent
                </button>
                <button type="button" class="btn btn-primary" onclick="nextStep(3)">
                    Suivant<i class="fas fa-arrow-right ms-2"></i>
                </button>
            </div>
        </div>

        <!-- Étape 3: Rôles -->
        <div class="step-content" id="step-3">
            <div class="step-header">
                <div class="step-icon">
                    <i class="fas fa-user-tag"></i>
                </div>
                <h2 class="step-title">Rôles et permissions</h2>
                <p class="step-description">Sélectionnez les rôles à attribuer à l'utilisateur</p>
            </div>

            <div class="roles-grid">
                <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="role-card" data-role="<?php echo e($role->name); ?>">
                    <div class="role-header">
                        <div class="role-icon" style="background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <div>
                            <h5 class="role-name"><?php echo e($role->name); ?></h5>
                            <p class="role-description"><?php echo e($role->description ?? 'Rôle ' . $role->name); ?></p>
                        </div>
                    </div>
                    <input type="checkbox" name="roles[]" value="<?php echo e($role->name); ?>" class="d-none role-checkbox">
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <div class="step-navigation">
                <button type="button" class="btn btn-secondary" onclick="prevStep(2)">
                    <i class="fas fa-arrow-left me-2"></i>Précédent
                </button>
                <button type="button" class="btn btn-primary" onclick="nextStep(4)">
                    Suivant<i class="fas fa-arrow-right ms-2"></i>
                </button>
            </div>
        </div>

        <!-- Étape 4: Confirmation -->
        <div class="step-content" id="step-4">
            <div class="step-header">
                <div class="step-icon">
                    <i class="fas fa-check"></i>
                </div>
                <h2 class="step-title">Confirmation</h2>
                <p class="step-description">Vérifiez les informations avant de créer l'utilisateur</p>
            </div>

            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Résumé de l'utilisateur</h5>
                            <div class="row">
                                <div class="col-sm-6">
                                    <strong>Nom :</strong> <span id="summary-name">-</span>
                                </div>
                                <div class="col-sm-6">
                                    <strong>Email :</strong> <span id="summary-email">-</span>
                                </div>
                            </div>
                            <div class="mt-3">
                                <strong>Rôles :</strong> <span id="summary-roles">Aucun rôle sélectionné</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="step-navigation">
                <button type="button" class="btn btn-secondary" onclick="prevStep(2)">
                    <i class="fas fa-arrow-left me-2"></i>Précédent
                </button>
                <button type="button" class="btn btn-primary" onclick="nextStep(4)">
                    Suivant<i class="fas fa-arrow-right ms-2"></i>
                </button>
            </div>
        </div>

        <!-- Étape 4: Confirmation et création -->
        <div class="step-content" id="step-4">
            <div class="step-header">
                <div class="step-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="step-title">Confirmation et création</h3>
                <p class="step-description">Vérifiez les informations avant de créer l'utilisateur</p>
            </div>

            <div class="confirmation-summary">
                <div class="summary-card">
                    <div class="summary-header">
                        <i class="fas fa-user-check"></i>
                        <h5>Récapitulatif de l'utilisateur</h5>
                    </div>

                    <div class="summary-content">
                        <div class="user-summary">
                            <div class="summary-avatar" id="summary-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="summary-info">
                                <h6 class="summary-name" id="summary-name">Nom de l'utilisateur</h6>
                                <p class="summary-email" id="summary-email"><EMAIL></p>
                            </div>
                        </div>

                        <div class="summary-details">
                            <div class="detail-row">
                                <span class="detail-label">
                                    <i class="fas fa-shield-alt me-2"></i>Mot de passe
                                </span>
                                <span class="detail-value">
                                    <i class="fas fa-check-circle text-success"></i> Configuré
                                </span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">
                                    <i class="fas fa-user-tag me-2"></i>Rôles assignés
                                </span>
                                <div class="detail-value">
                                    <div class="summary-roles" id="summary-roles">
                                        <span class="badge bg-light text-muted">Aucun rôle</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="creation-notice">
                    <div class="notice-card">
                        <i class="fas fa-info-circle"></i>
                        <div class="notice-content">
                            <h6>Informations importantes</h6>
                            <ul>
                                <li>L'utilisateur recevra un email de bienvenue</li>
                                <li>Il pourra modifier son mot de passe lors de sa première connexion</li>
                                <li>Les permissions seront appliquées immédiatement</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="step-navigation">
                <button type="button" class="btn btn-secondary" onclick="prevStep(3)">
                    <i class="fas fa-arrow-left me-2"></i>Précédent
                </button>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-user-plus me-2"></i>Créer l'utilisateur
                </button>
            </div>
        </div>

    </form>
</div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    let currentStep = 1;
    const totalSteps = 4;

    // Navigation entre les étapes
    function nextStep(step) {
        if (validateCurrentStep()) {
            showStep(step);
        }
    }

    function prevStep(step) {
        showStep(step);
    }

    function showStep(step) {
        // Masquer toutes les étapes
        document.querySelectorAll('.step-content').forEach(el => {
            el.classList.remove('active');
        });

        // Masquer tous les indicateurs d'étapes
        document.querySelectorAll('.step-item').forEach(el => {
            el.classList.remove('active', 'completed');
        });

        // Afficher l'étape courante
        document.getElementById(`step-${step}`).classList.add('active');

        // Mettre à jour les indicateurs
        for (let i = 1; i <= totalSteps; i++) {
            const stepElement = document.querySelector(`[data-step="${i}"]`);
            if (i < step) {
                stepElement.classList.add('completed');
            } else if (i === step) {
                stepElement.classList.add('active');
            }
        }

        currentStep = step;

        // Mettre à jour le résumé si on est à l'étape 4
        if (step === 4) {
            updateSummary();
        }
    }

    // Validation des étapes
    function validateCurrentStep() {
        const currentStepElement = document.getElementById(`step-${currentStep}`);
        const requiredFields = currentStepElement.querySelectorAll('input[required], select[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        // Validation spécifique pour l'étape 2 (mots de passe)
        if (currentStep === 2) {
            const password = document.getElementById('password').value;
            const passwordConfirm = document.getElementById('password_confirmation').value;

            if (password !== passwordConfirm) {
                document.getElementById('password_confirmation').classList.add('is-invalid');
                isValid = false;
            } else {
                document.getElementById('password_confirmation').classList.remove('is-invalid');
            }
        }

        return isValid;
    }

    // Mise à jour du résumé
    function updateSummary() {
        document.getElementById('summary-name').textContent = document.getElementById('name').value || '-';
        document.getElementById('summary-email').textContent = document.getElementById('email').value || '-';

        const selectedRoles = Array.from(document.querySelectorAll('input[name="roles[]"]:checked'))
            .map(checkbox => checkbox.value);
        document.getElementById('summary-roles').textContent = selectedRoles.length > 0
            ? selectedRoles.join(', ')
            : 'Aucun rôle sélectionné';
    }

    // Gestion des cartes de rôles
    document.addEventListener('DOMContentLoaded', function() {
        // Sélection des rôles
        document.querySelectorAll('.role-card').forEach(card => {
            card.addEventListener('click', function() {
                const checkbox = this.querySelector('.role-checkbox');
                checkbox.checked = !checkbox.checked;
                this.classList.toggle('selected', checkbox.checked);
            });
        });

        // Validation en temps réel
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('blur', function() {
                if (this.hasAttribute('required') && !this.value.trim()) {
                    this.classList.add('is-invalid');
                } else {
                    this.classList.remove('is-invalid');
                }
            });
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/admin/users/create.blade.php ENDPATH**/ ?>