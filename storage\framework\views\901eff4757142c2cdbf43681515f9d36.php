<?php $__env->startSection('title', 'Ajouter un utilisateur'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        --info-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        --card-hover-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        --border-radius: 16px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        --glass-bg: rgba(255, 255, 255, 0.25);
        --glass-border: rgba(255, 255, 255, 0.18);
    }

    /* Background avec effet glassmorphism */
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        position: relative;
    }

    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
        z-index: -1;
    }

    .container-fluid {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius);
        margin: 2rem;
        padding: 2rem;
        box-shadow: var(--card-shadow);
    }

    /* Stepper moderne et intuitif */
    .creation-stepper {
        display: flex;
        justify-content: center;
        margin-bottom: 3rem;
        position: relative;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        flex: 1;
        max-width: 200px;
    }

    .step-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: bold;
        color: white;
        background: #dee2e6;
        transition: var(--transition);
        position: relative;
        z-index: 2;
        border: 4px solid white;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .step.active .step-circle {
        background: var(--primary-gradient);
        transform: scale(1.1);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    .step.completed .step-circle {
        background: var(--success-gradient);
        animation: pulse 0.6s ease-out;
    }

    .step-label {
        margin-top: 1rem;
        font-weight: 600;
        color: #6c757d;
        text-align: center;
        font-size: 0.9rem;
        transition: var(--transition);
    }

    .step.active .step-label {
        color: #495057;
        transform: scale(1.05);
    }

    .step-connector {
        position: absolute;
        top: 30px;
        left: 50%;
        right: -50%;
        height: 4px;
        background: #dee2e6;
        z-index: 1;
        border-radius: 2px;
        transition: var(--transition);
    }

    .step.completed .step-connector {
        background: var(--success-gradient);
    }

    .step:last-child .step-connector {
        display: none;
    }

    @keyframes pulse {
        0% { transform: scale(1.1); }
        50% { transform: scale(1.2); }
        100% { transform: scale(1.1); }
    }

    /* Étapes du formulaire */
    .form-step {
        display: none;
        animation: slideInRight 0.5s ease-out;
    }

    .form-step.active {
        display: block;
    }

    .form-step.prev {
        animation: slideOutLeft 0.5s ease-out;
    }

    .form-step.next {
        animation: slideInRight 0.5s ease-out;
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(50px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slideOutLeft {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(-50px);
        }
    }

    /* Header moderne avec glassmorphism */
    .header-section {
        background: var(--glass-bg);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--card-shadow);
    }

    .breadcrumb-modern {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius);
        padding: 1rem 1.5rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .breadcrumb-modern .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        font-size: 1.2em;
        color: #6c757d;
    }

    .breadcrumb-modern a {
        color: #495057;
        text-decoration: none;
        transition: var(--transition);
    }

    .breadcrumb-modern a:hover {
        color: #007bff;
        transform: translateX(2px);
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .page-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-size: 1.1rem;
        margin-bottom: 0;
    }

    .text-gradient {
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Styles pour les étapes du formulaire */
    .form-card {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius);
        overflow: hidden;
    }

    .step-header {
        text-align: center;
        padding: 3rem 2rem 2rem;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .step-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 2rem;
        color: white;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .step-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: white;
        margin-bottom: 0.5rem;
    }

    .step-description {
        color: rgba(255, 255, 255, 0.8);
        font-size: 1rem;
        margin: 0;
    }

    .step-content {
        padding: 3rem 2rem;
        min-height: 400px;
    }

    .step-navigation {
        padding: 2rem;
        background: rgba(255, 255, 255, 0.05);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    /* Groupes de formulaire modernes */
    .form-group-modern {
        position: relative;
    }

    .input-group-modern {
        position: relative;
        display: flex;
        align-items: stretch;
    }

    .input-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
        color: #6c757d;
        font-size: 1.1rem;
    }

    .form-control-modern {
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius);
        padding: 1rem 1rem 1rem 3rem;
        font-size: 1rem;
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        color: white;
    }

    .form-control-modern::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    .form-control-modern:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
    }

    .form-floating > .form-control-modern:focus ~ label,
    .form-floating > .form-control-modern:not(:placeholder-shown) ~ label {
        color: #007bff;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    }

    .form-floating > label {
        color: rgba(255, 255, 255, 0.8);
        padding-left: 3rem;
    }

    .form-hint {
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.875rem;
    }

    /* Aperçu en temps réel */
    .live-preview-card {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-top: 2rem;
        text-align: center;
        backdrop-filter: blur(10px);
    }

    .preview-header {
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 1rem;
        font-size: 0.9rem;
    }

    .preview-content {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .preview-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: var(--secondary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin-bottom: 1rem;
        transition: var(--transition);
    }

    .preview-name {
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
    }

    .preview-email {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.875rem;
        margin: 0;
    }

    /* Section sécurité */
    .security-tips {
        margin-bottom: 2rem;
    }

    .tip-card {
        background: rgba(255, 193, 7, 0.1);
        border: 1px solid rgba(255, 193, 7, 0.3);
        border-radius: var(--border-radius);
        padding: 1rem;
        display: flex;
        align-items: flex-start;
        gap: 1rem;
    }

    .tip-card i {
        color: #ffc107;
        font-size: 1.5rem;
        margin-top: 0.25rem;
    }

    .tip-content strong {
        color: #ffc107;
        display: block;
        margin-bottom: 0.5rem;
    }

    .tip-content p {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        font-size: 0.9rem;
        line-height: 1.4;
    }

    /* Champs de mot de passe améliorés */
    .password-field {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.6);
        cursor: pointer;
        z-index: 10;
        transition: var(--transition);
        font-size: 1.1rem;
    }

    .password-toggle:hover {
        color: #007bff;
        transform: translateY(-50%) scale(1.1);
    }

    .password-strength-container {
        margin-top: 0.75rem;
    }

    .password-strength {
        height: 6px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }

    .password-strength::after {
        content: '';
        display: block;
        height: 100%;
        border-radius: 3px;
        transition: var(--transition);
        width: 0%;
    }

    .password-strength.weak::after {
        width: 25%;
        background: linear-gradient(90deg, #dc3545, #ff6b7a);
    }

    .password-strength.medium::after {
        width: 60%;
        background: linear-gradient(90deg, #ffc107, #ffda6a);
    }

    .password-strength.strong::after {
        width: 100%;
        background: linear-gradient(90deg, #28a745, #5cb85c);
    }

    .strength-text {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.7);
        text-align: center;
    }

    .password-match {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .password-match.match {
        color: #28a745;
    }

    .password-match.no-match {
        color: #dc3545;
    }

    .password-requirements {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .password-requirements h6 {
        color: white;
        margin-bottom: 1rem;
        font-size: 1rem;
    }

    .requirements-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .requirements-list li {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.5rem 0;
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
        transition: var(--transition);
    }

    .requirements-list li i {
        width: 16px;
        text-align: center;
        color: #dc3545;
        transition: var(--transition);
    }

    .requirements-list li.valid i {
        color: #28a745;
    }

    .requirements-list li.valid {
        color: rgba(255, 255, 255, 0.9);
    }

    /* Cards modernes */
    .animate-card {
        animation: slideInUp 0.6s ease-out;
        transition: var(--transition);
    }

    .animate-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--card-hover-shadow);
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Sélection de rôles moderne */
    .roles-selection {
        padding: 1rem 0;
    }

    .roles-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .role-card-modern {
        position: relative;
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
    }

    .role-card-modern:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .role-checkbox {
        position: absolute;
        opacity: 0;
        pointer-events: none;
    }

    .role-label-modern {
        display: block;
        padding: 1.5rem;
        cursor: pointer;
        transition: var(--transition);
        margin: 0;
        height: 100%;
    }

    .role-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .role-icon-modern {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.3rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .role-level {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .level-critique {
        background: rgba(220, 53, 69, 0.2);
        color: #ff6b7a;
        border: 1px solid rgba(220, 53, 69, 0.3);
    }

    .level-élevé {
        background: rgba(0, 123, 255, 0.2);
        color: #4dabf7;
        border: 1px solid rgba(0, 123, 255, 0.3);
    }

    .level-moyen {
        background: rgba(255, 193, 7, 0.2);
        color: #ffd43b;
        border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .level-faible {
        background: rgba(40, 167, 69, 0.2);
        color: #51cf66;
        border: 1px solid rgba(40, 167, 69, 0.3);
    }

    .level-personnalisé {
        background: rgba(108, 117, 125, 0.2);
        color: #adb5bd;
        border: 1px solid rgba(108, 117, 125, 0.3);
    }

    .role-body {
        margin-bottom: 1.5rem;
    }

    .role-name-modern {
        font-size: 1.1rem;
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
    }

    .role-description-modern {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.7);
        line-height: 1.4;
        margin: 0;
    }

    .role-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .role-check-modern {
        width: 28px;
        height: 28px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: var(--transition);
        color: transparent;
    }

    .selection-text {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.6);
        font-weight: 500;
    }

    .role-checkbox:checked + .role-label-modern {
        background: rgba(0, 123, 255, 0.1);
        border-color: #007bff;
    }

    .role-checkbox:checked + .role-label-modern .role-check-modern {
        background: #007bff;
        border-color: #007bff;
        color: white;
        transform: scale(1.1);
    }

    .role-checkbox:checked + .role-label-modern .selection-text {
        color: #007bff;
    }

    .role-checkbox:checked + .role-label-modern .role-name-modern {
        color: #007bff;
    }

    /* Aperçu des rôles sélectionnés */
    .selected-roles-preview {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .selected-roles-preview h6 {
        color: white;
        margin-bottom: 1rem;
        font-size: 1rem;
    }

    .selected-roles-container {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .selected-roles-container .badge {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        border-radius: 20px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .no-selection {
        color: rgba(255, 255, 255, 0.5);
        font-style: italic;
    }

    /* Section de confirmation */
    .confirmation-summary {
        padding: 1rem 0;
    }

    .summary-card {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        backdrop-filter: blur(10px);
    }

    .summary-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .summary-header i {
        font-size: 1.5rem;
        color: #28a745;
    }

    .summary-header h5 {
        color: white;
        margin: 0;
        font-weight: 600;
    }

    .user-summary {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--border-radius);
    }

    .summary-avatar {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background: var(--success-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.8rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .summary-info h6 {
        color: white;
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 1.2rem;
    }

    .summary-info p {
        color: rgba(255, 255, 255, 0.7);
        margin: 0;
    }

    .summary-details {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--border-radius);
    }

    .detail-label {
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
    }

    .detail-value {
        color: white;
        font-weight: 600;
    }

    .summary-roles {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .creation-notice {
        margin-top: 1rem;
    }

    .notice-card {
        background: rgba(23, 162, 184, 0.1);
        border: 1px solid rgba(23, 162, 184, 0.3);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        display: flex;
        gap: 1rem;
    }

    .notice-card i {
        color: #17a2b8;
        font-size: 1.5rem;
        margin-top: 0.25rem;
    }

    .notice-content h6 {
        color: #17a2b8;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .notice-content ul {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        padding-left: 1.5rem;
    }

    .notice-content li {
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    /* Boutons modernes */
    .btn {
        border-radius: var(--border-radius);
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
        text-transform: none;
        letter-spacing: 0.5px;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .btn-primary {
        background: var(--primary-gradient);
        color: white;
    }

    .btn-success {
        background: var(--success-gradient);
        color: white;
    }

    .btn-outline-secondary {
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.8);
        background: transparent;
        backdrop-filter: blur(10px);
    }

    .btn-outline-secondary:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border-color: rgba(255, 255, 255, 0.5);
    }

    .btn-outline-light {
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }

    .btn-outline-light:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
    }

    /* Bouton flottant de retour */
    .floating-back-btn {
        position: fixed;
        top: 2rem;
        left: 2rem;
        z-index: 1000;
    }

    .btn-floating {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
    }

    /* Couleurs spécifiques */
    .bg-purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .container-fluid {
            margin: 1rem;
            padding: 1rem;
        }

        .page-title {
            font-size: 2rem;
        }

        .creation-stepper {
            flex-direction: column;
            gap: 1rem;
        }

        .step {
            max-width: none;
        }

        .step-connector {
            display: none;
        }

        .step-circle {
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
        }

        .step-content {
            padding: 2rem 1rem;
            min-height: auto;
        }

        .roles-grid {
            grid-template-columns: 1fr;
        }

        .step-navigation {
            flex-direction: column;
            gap: 1rem;
        }

        .btn {
            width: 100%;
        }

        .floating-back-btn {
            top: 1rem;
            left: 1rem;
        }

        .user-summary {
            flex-direction: column;
            text-align: center;
        }

        .detail-row {
            flex-direction: column;
            gap: 0.5rem;
            text-align: center;
        }
    }

    @media (max-width: 576px) {
        .step-header {
            padding: 2rem 1rem 1rem;
        }

        .step-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }

        .step-title {
            font-size: 1.5rem;
        }

        .form-control-modern {
            padding: 0.875rem 0.875rem 0.875rem 2.5rem;
        }

        .input-icon {
            left: 0.75rem;
            font-size: 1rem;
        }

        .form-floating > label {
            padding-left: 2.5rem;
        }
    }

    /* Animations d'entrée */
    .animate-card {
        animation: slideInUp 0.8s ease-out;
    }

    .form-step {
        animation: fadeInScale 0.6s ease-out;
    }

    @keyframes fadeInScale {
        from {
            opacity: 0;
            transform: scale(0.95);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    /* États de validation */
    .is-invalid {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }

    .invalid-feedback {
        color: #ff6b7a;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .invalid-feedback::before {
        content: '⚠';
        font-size: 1rem;
    }

    /* Effets de focus améliorés */
    .form-control-modern:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1), 0 4px 15px rgba(0, 123, 255, 0.2);
    }

    /* Transitions fluides pour les étapes */
    .form-step {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .form-step:not(.active) {
        pointer-events: none;
    }

    /* Amélioration de l'accessibilité */
    .btn:focus,
    .form-control-modern:focus,
    .role-label-modern:focus {
        outline: 2px solid #007bff;
        outline-offset: 2px;
    }

    /* Indicateurs de progression */
    .step.active .step-circle {
        animation: pulse-glow 2s infinite;
    }

    @keyframes pulse-glow {
        0%, 100% {
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        50% {
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6), 0 0 20px rgba(102, 126, 234, 0.3);
        }
    }

    /* Champs de mot de passe */
    .password-field {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        z-index: 10;
        transition: var(--transition);
    }

    .password-toggle:hover {
        color: #007bff;
    }

    .password-strength {
        height: 4px;
        background: #e9ecef;
        border-radius: 2px;
        margin-top: 0.5rem;
        overflow: hidden;
    }

    .password-strength::after {
        content: '';
        display: block;
        height: 100%;
        border-radius: 2px;
        transition: var(--transition);
    }

    .password-strength.weak::after {
        width: 33%;
        background: #dc3545;
    }

    .password-strength.medium::after {
        width: 66%;
        background: #ffc107;
    }

    .password-strength.strong::after {
        width: 100%;
        background: #28a745;
    }

    .password-match {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .password-match.match {
        color: #28a745;
    }

    .password-match.no-match {
        color: #dc3545;
    }

    .password-requirements {
        margin-top: 1rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: var(--border-radius);
        border-left: 4px solid #007bff;
    }

    /* Cartes de rôles */
    .roles-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .role-card {
        position: relative;
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: var(--transition);
        background: #fff;
        border: 2px solid #e9ecef;
    }

    .role-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border-color: #007bff;
    }

    .role-checkbox {
        position: absolute;
        opacity: 0;
        pointer-events: none;
    }

    .role-label {
        display: flex;
        align-items: center;
        padding: 1rem;
        cursor: pointer;
        transition: var(--transition);
        margin: 0;
    }

    .role-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .role-info {
        flex: 1;
    }

    .role-name {
        font-weight: 600;
        font-size: 1rem;
        color: #212529;
        margin-bottom: 0.25rem;
    }

    .role-description {
        font-size: 0.875rem;
        color: #6c757d;
        line-height: 1.4;
    }

    .role-check {
        width: 24px;
        height: 24px;
        border: 2px solid #dee2e6;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: var(--transition);
        color: transparent;
        flex-shrink: 0;
    }

    .role-checkbox:checked + .role-label {
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border-color: #007bff;
    }

    .role-checkbox:checked + .role-label .role-check {
        background: #007bff;
        border-color: #007bff;
        color: white;
        transform: scale(1.1);
    }

    .role-checkbox:checked + .role-label .role-name {
        color: #007bff;
    }

    /* Couleurs spécifiques pour les rôles */
    .bg-purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    /* Boutons modernes */
    .btn-modern {
        border-radius: var(--border-radius);
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-modern:hover::before {
        left: 100%;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .btn-primary.btn-modern {
        background: var(--primary-gradient);
    }

    .btn-outline-secondary.btn-modern {
        border: 2px solid #6c757d;
        color: #6c757d;
        background: transparent;
    }

    .btn-outline-secondary.btn-modern:hover {
        background: #6c757d;
        color: white;
    }

    /* Actions du formulaire */
    .form-actions {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 2px solid #e9ecef;
    }

    /* Panneau d'aide */
    .help-section {
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e9ecef;
    }

    .help-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .help-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }

    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        line-height: 1.5;
        margin: 0;
    }

    /* Aperçu utilisateur */
    .user-preview {
        padding: 1rem 0;
    }

    .preview-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin: 0 auto 1rem;
        transition: var(--transition);
    }

    .preview-avatar:hover {
        transform: scale(1.1);
    }

    .preview-name {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #212529;
    }

    .preview-email {
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    .preview-roles .badge {
        margin: 0.25rem;
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
        border-radius: 20px;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }

        .roles-container {
            grid-template-columns: 1fr;
        }

        .form-actions .d-flex {
            flex-direction: column;
            gap: 1rem;
        }

        .btn-modern {
            width: 100%;
        }
    }

    /* Animations d'entrée */
    .form-section {
        animation: fadeInUp 0.6s ease-out;
        animation-fill-mode: both;
    }

    .form-section:nth-child(1) { animation-delay: 0.1s; }
    .form-section:nth-child(2) { animation-delay: 0.2s; }
    .form-section:nth-child(3) { animation-delay: 0.3s; }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header avec breadcrumb -->
    <div class="header-section mb-4">
        <nav aria-label="breadcrumb" class="mb-3">
            <ol class="breadcrumb breadcrumb-modern">
                <li class="breadcrumb-item">
                    <a href="<?php echo e(route('admin.dashboard')); ?>">
                        <i class="fas fa-home me-1"></i>Tableau de bord
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="<?php echo e(route('admin.users.index')); ?>">
                        <i class="fas fa-users me-1"></i>Utilisateurs
                    </a>
                </li>
                <li class="breadcrumb-item active">
                    <i class="fas fa-user-plus me-1"></i>Nouvel utilisateur
                </li>
            </ol>
        </nav>

        <div class="text-center">
            <h1 class="page-title text-gradient mb-2">
                <i class="fas fa-user-plus me-3"></i>Créer un nouvel utilisateur
            </h1>
            <p class="page-subtitle mb-4">Suivez les étapes pour ajouter un nouveau membre à votre équipe</p>

            <!-- Stepper intuitif -->
            <div class="creation-stepper">
                <div class="step active" data-step="1">
                    <div class="step-circle">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="step-label">Informations<br>personnelles</div>
                    <div class="step-connector"></div>
                </div>
                <div class="step" data-step="2">
                    <div class="step-circle">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="step-label">Sécurité &<br>Accès</div>
                    <div class="step-connector"></div>
                </div>
                <div class="step" data-step="3">
                    <div class="step-circle">
                        <i class="fas fa-user-tag"></i>
                    </div>
                    <div class="step-label">Rôles &<br>Permissions</div>
                    <div class="step-connector"></div>
                </div>
                <div class="step" data-step="4">
                    <div class="step-circle">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="step-label">Confirmation &<br>Création</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire multi-étapes -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card form-card shadow-lg border-0 animate-card">
                <div class="card-body p-0">
                    <form id="userForm" action="<?php echo e(route('admin.users.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>

                        <!-- Étape 1: Informations personnelles -->
                        <div class="form-step active" id="step-1">
                            <div class="step-header">
                                <div class="step-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                                <h3 class="step-title">Informations personnelles</h3>
                                <p class="step-description">Commençons par les informations de base de l'utilisateur</p>
                            </div>

                            <div class="step-content">
                                <div class="row justify-content-center">
                                    <div class="col-md-8">
                                        <div class="form-group-modern mb-4">
                                            <div class="input-group-modern">
                                                <div class="input-icon">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <div class="form-floating">
                                                    <input type="text"
                                                           class="form-control form-control-modern <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           id="name"
                                                           name="name"
                                                           value="<?php echo e(old('name')); ?>"
                                                           placeholder="Nom complet"
                                                           required>
                                                    <label for="name">Nom complet</label>
                                                </div>
                                            </div>
                                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            <div class="form-hint">
                                                <i class="fas fa-info-circle me-1"></i>
                                                <small>Entrez le nom complet de l'utilisateur</small>
                                            </div>
                                        </div>

                                        <div class="form-group-modern mb-4">
                                            <div class="input-group-modern">
                                                <div class="input-icon">
                                                    <i class="fas fa-envelope"></i>
                                                </div>
                                                <div class="form-floating">
                                                    <input type="email"
                                                           class="form-control form-control-modern <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           id="email"
                                                           name="email"
                                                           value="<?php echo e(old('email')); ?>"
                                                           placeholder="Adresse email"
                                                           required>
                                                    <label for="email">Adresse email</label>
                                                </div>
                                            </div>
                                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            <div class="form-hint">
                                                <i class="fas fa-info-circle me-1"></i>
                                                <small>Cette adresse sera utilisée pour la connexion</small>
                                            </div>
                                        </div>

                                        <div class="live-preview-card">
                                            <div class="preview-header">
                                                <i class="fas fa-eye me-2"></i>Aperçu en temps réel
                                            </div>
                                            <div class="preview-content">
                                                <div class="preview-avatar" id="previewAvatar">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <h6 class="preview-name" id="previewName">Nom de l'utilisateur</h6>
                                                <p class="preview-email" id="previewEmail"><EMAIL></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="step-navigation">
                                <button type="button" class="btn btn-outline-secondary" disabled>
                                    <i class="fas fa-arrow-left me-2"></i>Précédent
                                </button>
                                <button type="button" class="btn btn-primary btn-next" onclick="nextStep(2)">
                                    Suivant<i class="fas fa-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Étape 2: Sécurité et accès -->
                        <div class="form-step" id="step-2">
                            <div class="step-header">
                                <div class="step-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <h3 class="step-title">Sécurité et accès</h3>
                                <p class="step-description">Définissez un mot de passe sécurisé pour l'utilisateur</p>
                            </div>

                            <div class="step-content">
                                <div class="row justify-content-center">
                                    <div class="col-md-8">
                                        <div class="security-tips mb-4">
                                            <div class="tip-card">
                                                <i class="fas fa-lightbulb"></i>
                                                <div class="tip-content">
                                                    <strong>Conseil de sécurité :</strong>
                                                    <p>Utilisez un mot de passe fort avec au moins 8 caractères, incluant des majuscules, minuscules, chiffres et symboles.</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group-modern mb-4">
                                            <div class="input-group-modern">
                                                <div class="input-icon">
                                                    <i class="fas fa-lock"></i>
                                                </div>
                                                <div class="form-floating password-field">
                                                    <input type="password"
                                                           class="form-control form-control-modern <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           id="password"
                                                           name="password"
                                                           placeholder="Mot de passe"
                                                           required>
                                                    <label for="password">Mot de passe</label>
                                                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                                        <i class="fas fa-eye" id="password-icon"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            <div class="password-strength-container">
                                                <div class="password-strength" id="password-strength"></div>
                                                <div class="strength-text" id="strength-text">Tapez votre mot de passe</div>
                                            </div>
                                        </div>

                                        <div class="form-group-modern mb-4">
                                            <div class="input-group-modern">
                                                <div class="input-icon">
                                                    <i class="fas fa-lock"></i>
                                                </div>
                                                <div class="form-floating password-field">
                                                    <input type="password"
                                                           class="form-control form-control-modern"
                                                           id="password_confirmation"
                                                           name="password_confirmation"
                                                           placeholder="Confirmer le mot de passe"
                                                           required>
                                                    <label for="password_confirmation">Confirmer le mot de passe</label>
                                                    <button type="button" class="password-toggle" onclick="togglePassword('password_confirmation')">
                                                        <i class="fas fa-eye" id="password_confirmation-icon"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="password-match" id="password-match"></div>
                                        </div>

                                        <div class="password-requirements">
                                            <h6><i class="fas fa-check-circle me-2"></i>Exigences du mot de passe :</h6>
                                            <ul class="requirements-list">
                                                <li id="req-length"><i class="fas fa-times"></i> Au moins 8 caractères</li>
                                                <li id="req-lowercase"><i class="fas fa-times"></i> Une lettre minuscule</li>
                                                <li id="req-uppercase"><i class="fas fa-times"></i> Une lettre majuscule</li>
                                                <li id="req-number"><i class="fas fa-times"></i> Un chiffre</li>
                                                <li id="req-special"><i class="fas fa-times"></i> Un caractère spécial</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="step-navigation">
                                <button type="button" class="btn btn-outline-secondary" onclick="prevStep(1)">
                                    <i class="fas fa-arrow-left me-2"></i>Précédent
                                </button>
                                <button type="button" class="btn btn-primary btn-next" onclick="nextStep(3)">
                                    Suivant<i class="fas fa-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Étape 3: Rôles et permissions -->
                        <div class="form-step" id="step-3">
                            <div class="step-header">
                                <div class="step-icon">
                                    <i class="fas fa-user-tag"></i>
                                </div>
                                <h3 class="step-title">Rôles et permissions</h3>
                                <p class="step-description">Sélectionnez les rôles appropriés pour définir les accès de l'utilisateur</p>
                            </div>

                            <div class="step-content">
                                <div class="roles-selection">
                                    <div class="roles-grid">
                                        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $roleConfig = [
                                                    'admin' => ['color' => 'danger', 'icon' => 'user-shield', 'name' => 'Administrateur', 'description' => 'Accès complet au système', 'level' => 'Critique'],
                                                    'accountant' => ['color' => 'primary', 'icon' => 'calculator', 'name' => 'Comptable', 'description' => 'Gestion financière et comptabilité', 'level' => 'Élevé'],
                                                    'cement_manager' => ['color' => 'success', 'icon' => 'industry', 'name' => 'Gestionnaire Ciment', 'description' => 'Gestion des produits ciment', 'level' => 'Moyen'],
                                                    'iron_manager' => ['color' => 'info', 'icon' => 'hammer', 'name' => 'Gestionnaire Fer', 'description' => 'Gestion des produits fer', 'level' => 'Moyen'],
                                                    'cashier' => ['color' => 'warning', 'icon' => 'cash-register', 'name' => 'Caissier', 'description' => 'Gestion des transactions', 'level' => 'Moyen'],
                                                    'customer_service' => ['color' => 'secondary', 'icon' => 'headset', 'name' => 'Service Client', 'description' => 'Support et service clientèle', 'level' => 'Faible'],
                                                    'customer' => ['color' => 'dark', 'icon' => 'user', 'name' => 'Client', 'description' => 'Accès client standard', 'level' => 'Faible'],
                                                    'driver' => ['color' => 'purple', 'icon' => 'truck', 'name' => 'Chauffeur', 'description' => 'Gestion des livraisons', 'level' => 'Faible'],
                                                ];
                                                $config = $roleConfig[$role->name] ?? ['color' => 'dark', 'icon' => 'user', 'name' => ucfirst($role->name), 'description' => 'Rôle personnalisé', 'level' => 'Personnalisé'];
                                            ?>
                                            <div class="role-card-modern" data-role="<?php echo e($role->name); ?>">
                                                <input class="role-checkbox"
                                                       type="checkbox"
                                                       name="roles[]"
                                                       value="<?php echo e($role->name); ?>"
                                                       id="role_<?php echo e($role->id); ?>"
                                                       <?php echo e(in_array($role->name, old('roles', [])) ? 'checked' : ''); ?>>
                                                <label class="role-label-modern" for="role_<?php echo e($role->id); ?>">
                                                    <div class="role-header">
                                                        <div class="role-icon-modern bg-<?php echo e($config['color']); ?>">
                                                            <i class="fas fa-<?php echo e($config['icon']); ?>"></i>
                                                        </div>
                                                        <div class="role-level level-<?php echo e(strtolower($config['level'])); ?>">
                                                            <?php echo e($config['level']); ?>

                                                        </div>
                                                    </div>
                                                    <div class="role-body">
                                                        <h6 class="role-name-modern"><?php echo e($config['name']); ?></h6>
                                                        <p class="role-description-modern"><?php echo e($config['description']); ?></p>
                                                    </div>
                                                    <div class="role-footer">
                                                        <div class="role-check-modern">
                                                            <i class="fas fa-check"></i>
                                                        </div>
                                                        <span class="selection-text">Sélectionner</span>
                                                    </div>
                                                </label>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                    <?php $__errorArgs = ['roles'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="alert alert-danger mt-3">
                                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo e($message); ?>

                                        </div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="selected-roles-preview">
                                    <h6><i class="fas fa-eye me-2"></i>Rôles sélectionnés :</h6>
                                    <div class="selected-roles-container" id="selectedRolesPreview">
                                        <span class="no-selection">Aucun rôle sélectionné</span>
                                    </div>
                                </div>
                            </div>

                            <div class="step-navigation">
                                <button type="button" class="btn btn-outline-secondary" onclick="prevStep(2)">
                                    <i class="fas fa-arrow-left me-2"></i>Précédent
                                </button>
                                <button type="button" class="btn btn-primary btn-next" onclick="nextStep(4)">
                                    Suivant<i class="fas fa-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Étape 4: Confirmation et création -->
                        <div class="form-step" id="step-4">
                            <div class="step-header">
                                <div class="step-icon">
                                    <i class="fas fa-check"></i>
                                </div>
                                <h3 class="step-title">Confirmation et création</h3>
                                <p class="step-description">Vérifiez les informations avant de créer l'utilisateur</p>
                            </div>

                            <div class="step-content">
                                <div class="confirmation-summary">
                                    <div class="summary-card">
                                        <div class="summary-header">
                                            <i class="fas fa-user-check"></i>
                                            <h5>Récapitulatif de l'utilisateur</h5>
                                        </div>

                                        <div class="summary-content">
                                            <div class="user-summary">
                                                <div class="summary-avatar" id="summaryAvatar">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <div class="summary-info">
                                                    <h6 class="summary-name" id="summaryName">Nom de l'utilisateur</h6>
                                                    <p class="summary-email" id="summaryEmail"><EMAIL></p>
                                                </div>
                                            </div>

                                            <div class="summary-details">
                                                <div class="detail-row">
                                                    <span class="detail-label">
                                                        <i class="fas fa-shield-alt me-2"></i>Mot de passe
                                                    </span>
                                                    <span class="detail-value">
                                                        <i class="fas fa-check-circle text-success"></i> Configuré
                                                    </span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="detail-label">
                                                        <i class="fas fa-user-tag me-2"></i>Rôles assignés
                                                    </span>
                                                    <div class="detail-value">
                                                        <div class="summary-roles" id="summaryRoles">
                                                            <span class="badge bg-light text-muted">Aucun rôle</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="creation-notice">
                                        <div class="notice-card">
                                            <i class="fas fa-info-circle"></i>
                                            <div class="notice-content">
                                                <h6>Informations importantes</h6>
                                                <ul>
                                                    <li>L'utilisateur recevra un email de bienvenue</li>
                                                    <li>Il pourra modifier son mot de passe lors de sa première connexion</li>
                                                    <li>Les permissions seront appliquées immédiatement</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="step-navigation">
                                <button type="button" class="btn btn-outline-secondary" onclick="prevStep(3)">
                                    <i class="fas fa-arrow-left me-2"></i>Précédent
                                </button>
                                <button type="submit" class="btn btn-success btn-create" id="submitBtn">
                                    <i class="fas fa-user-plus me-2"></i>Créer l'utilisateur
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bouton de retour flottant -->
    <div class="floating-back-btn">
        <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-outline-light btn-floating" title="Retour à la liste">
            <i class="fas fa-arrow-left"></i>
        </a>
    </div>
</div>

                        <!-- Section sécurité -->
                        <div class="form-section mb-4">
                            <h6 class="section-title">
                                <i class="fas fa-shield-alt me-2 text-warning"></i>Sécurité et accès
                            </h6>
                            <div class="section-divider"></div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-floating password-field">
                                        <input type="password"
                                               class="form-control form-control-modern <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="password"
                                               name="password"
                                               placeholder="Mot de passe"
                                               required>
                                        <label for="password">
                                            <i class="fas fa-lock me-2"></i>Mot de passe
                                        </label>
                                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                            <i class="fas fa-eye" id="password-icon"></i>
                                        </button>
                                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <div class="password-strength" id="password-strength"></div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-floating password-field">
                                        <input type="password"
                                               class="form-control form-control-modern"
                                               id="password_confirmation"
                                               name="password_confirmation"
                                               placeholder="Confirmer le mot de passe"
                                               required>
                                        <label for="password_confirmation">
                                            <i class="fas fa-lock me-2"></i>Confirmer le mot de passe
                                        </label>
                                        <button type="button" class="password-toggle" onclick="togglePassword('password_confirmation')">
                                            <i class="fas fa-eye" id="password_confirmation-icon"></i>
                                        </button>
                                        <div class="password-match" id="password-match"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="password-requirements">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Le mot de passe doit contenir au moins 8 caractères
                                </small>
                            </div>
                        </div>

                        <!-- Section rôles -->
                        <div class="form-section mb-4">
                            <h6 class="section-title">
                                <i class="fas fa-user-tag me-2 text-success"></i>Rôles et permissions
                            </h6>
                            <div class="section-divider"></div>

                            <div class="roles-container">
                                <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $roleConfig = [
                                            'admin' => ['color' => 'danger', 'icon' => 'user-shield', 'name' => 'Administrateur', 'description' => 'Accès complet au système'],
                                            'accountant' => ['color' => 'primary', 'icon' => 'calculator', 'name' => 'Comptable', 'description' => 'Gestion financière et comptabilité'],
                                            'cement_manager' => ['color' => 'success', 'icon' => 'industry', 'name' => 'Gestionnaire Ciment', 'description' => 'Gestion des produits ciment'],
                                            'iron_manager' => ['color' => 'info', 'icon' => 'hammer', 'name' => 'Gestionnaire Fer', 'description' => 'Gestion des produits fer'],
                                            'cashier' => ['color' => 'warning', 'icon' => 'cash-register', 'name' => 'Caissier', 'description' => 'Gestion des transactions'],
                                            'customer_service' => ['color' => 'secondary', 'icon' => 'headset', 'name' => 'Service Client', 'description' => 'Support et service clientèle'],
                                            'customer' => ['color' => 'dark', 'icon' => 'user', 'name' => 'Client', 'description' => 'Accès client standard'],
                                            'driver' => ['color' => 'purple', 'icon' => 'truck', 'name' => 'Chauffeur', 'description' => 'Gestion des livraisons'],
                                        ];
                                        $config = $roleConfig[$role->name] ?? ['color' => 'dark', 'icon' => 'user', 'name' => ucfirst($role->name), 'description' => 'Rôle personnalisé'];
                                    ?>
                                    <div class="role-card" data-role="<?php echo e($role->name); ?>">
                                        <input class="role-checkbox"
                                               type="checkbox"
                                               name="roles[]"
                                               value="<?php echo e($role->name); ?>"
                                               id="role_<?php echo e($role->id); ?>"
                                               <?php echo e(in_array($role->name, old('roles', [])) ? 'checked' : ''); ?>>
                                        <label class="role-label" for="role_<?php echo e($role->id); ?>">
                                            <div class="role-icon bg-<?php echo e($config['color']); ?>">
                                                <i class="fas fa-<?php echo e($config['icon']); ?>"></i>
                                            </div>
                                            <div class="role-info">
                                                <div class="role-name"><?php echo e($config['name']); ?></div>
                                                <div class="role-description"><?php echo e($config['description']); ?></div>
                                            </div>
                                            <div class="role-check">
                                                <i class="fas fa-check"></i>
                                            </div>
                                        </label>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php $__errorArgs = ['roles'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger mt-2">
                                    <i class="fas fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="form-actions">
                            <div class="d-flex justify-content-between align-items-center">
                                <button type="button" class="btn btn-outline-secondary btn-modern" onclick="resetForm()">
                                    <i class="fas fa-undo me-2"></i>Réinitialiser
                                </button>
                                <button type="submit" class="btn btn-primary btn-modern" id="submitBtn">
                                    <i class="fas fa-user-plus me-2"></i>Créer l'utilisateur
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Panneau d'aide -->
        <div class="col-lg-4">
            <div class="card help-card shadow-sm border-0 animate-card">
                <div class="card-header bg-gradient-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Guide de création
                    </h6>
                </div>
                <div class="card-body">
                    <div class="help-section">
                        <h6 class="help-title">
                            <i class="fas fa-user text-primary me-2"></i>Informations personnelles
                        </h6>
                        <p class="help-text">Saisissez le nom complet et l'adresse email de l'utilisateur. L'email servira d'identifiant de connexion.</p>
                    </div>

                    <div class="help-section">
                        <h6 class="help-title">
                            <i class="fas fa-shield-alt text-warning me-2"></i>Sécurité
                        </h6>
                        <p class="help-text">Choisissez un mot de passe sécurisé d'au moins 8 caractères. L'utilisateur pourra le modifier après sa première connexion.</p>
                    </div>

                    <div class="help-section">
                        <h6 class="help-title">
                            <i class="fas fa-user-tag text-success me-2"></i>Rôles
                        </h6>
                        <p class="help-text">Sélectionnez un ou plusieurs rôles selon les responsabilités de l'utilisateur. Chaque rôle donne accès à des fonctionnalités spécifiques.</p>
                    </div>

                    <div class="help-section">
                        <div class="alert alert-info border-0">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Conseil :</strong> Commencez par attribuer les rôles minimaux nécessaires. Vous pourrez toujours les modifier plus tard.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aperçu utilisateur -->
            <div class="card preview-card shadow-sm border-0 animate-card mt-4">
                <div class="card-header bg-gradient-secondary text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>Aperçu utilisateur
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="user-preview">
                        <div class="preview-avatar" id="previewAvatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <h6 class="preview-name" id="previewName">Nom de l'utilisateur</h6>
                        <p class="preview-email text-muted" id="previewEmail"><EMAIL></p>
                        <div class="preview-roles" id="previewRoles">
                            <span class="badge bg-light text-muted">Aucun rôle sélectionné</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Variables globales
    let currentStep = 1;
    const totalSteps = 4;

    // Éléments du formulaire
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const passwordConfirmInput = document.getElementById('password_confirmation');
    const roleCheckboxes = document.querySelectorAll('input[name="roles[]"]');

    // Éléments d'aperçu
    const previewName = document.getElementById('previewName');
    const previewEmail = document.getElementById('previewEmail');
    const previewAvatar = document.getElementById('previewAvatar');

    // Éléments de résumé
    const summaryName = document.getElementById('summaryName');
    const summaryEmail = document.getElementById('summaryEmail');
    const summaryAvatar = document.getElementById('summaryAvatar');
    const summaryRoles = document.getElementById('summaryRoles');
    const selectedRolesPreview = document.getElementById('selectedRolesPreview');

    // Configuration des rôles
    const roleConfig = {
        'admin': { color: 'danger', icon: 'user-shield', name: 'Administrateur' },
        'accountant': { color: 'primary', icon: 'calculator', name: 'Comptable' },
        'cement_manager': { color: 'success', icon: 'industry', name: 'Gest. Ciment' },
        'iron_manager': { color: 'info', icon: 'hammer', name: 'Gest. Fer' },
        'cashier': { color: 'warning', icon: 'cash-register', name: 'Caissier' },
        'customer_service': { color: 'secondary', icon: 'headset', name: 'Service Client' },
        'customer': { color: 'dark', icon: 'user', name: 'Client' },
        'driver': { color: 'purple', icon: 'truck', name: 'Chauffeur' }
    };

    // Navigation entre les étapes
    window.nextStep = function(step) {
        if (validateCurrentStep()) {
            showStep(step);
        }
    };

    window.prevStep = function(step) {
        showStep(step);
    };

    function showStep(step) {
        // Masquer toutes les étapes
        document.querySelectorAll('.form-step').forEach(stepEl => {
            stepEl.classList.remove('active');
        });

        // Afficher l'étape courante
        document.getElementById(`step-${step}`).classList.add('active');

        // Mettre à jour le stepper
        updateStepper(step);

        currentStep = step;

        // Mettre à jour le résumé si on est à l'étape 4
        if (step === 4) {
            updateSummary();
        }
    }

    function updateStepper(activeStep) {
        document.querySelectorAll('.step').forEach((stepEl, index) => {
            const stepNumber = index + 1;
            stepEl.classList.remove('active', 'completed');

            if (stepNumber === activeStep) {
                stepEl.classList.add('active');
            } else if (stepNumber < activeStep) {
                stepEl.classList.add('completed');
            }
        });
    }

    function validateCurrentStep() {
        switch (currentStep) {
            case 1:
                return validateStep1();
            case 2:
                return validateStep2();
            case 3:
                return validateStep3();
            default:
                return true;
        }
    }

    function validateStep1() {
        const name = nameInput.value.trim();
        const email = emailInput.value.trim();

        if (!name) {
            showError('Veuillez saisir le nom complet.');
            nameInput.focus();
            return false;
        }

        if (!email || !isValidEmail(email)) {
            showError('Veuillez saisir une adresse email valide.');
            emailInput.focus();
            return false;
        }

        return true;
    }

    function validateStep2() {
        const password = passwordInput.value;
        const confirmPassword = passwordConfirmInput.value;

        if (!password || password.length < 8) {
            showError('Le mot de passe doit contenir au moins 8 caractères.');
            passwordInput.focus();
            return false;
        }

        if (password !== confirmPassword) {
            showError('Les mots de passe ne correspondent pas.');
            passwordConfirmInput.focus();
            return false;
        }

        return true;
    }

    function validateStep3() {
        const selectedRoles = Array.from(roleCheckboxes).filter(cb => cb.checked);

        if (selectedRoles.length === 0) {
            showError('Veuillez sélectionner au moins un rôle.');
            return false;
        }

        return true;
    }

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function showError(message) {
        // Créer une notification d'erreur temporaire
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger position-fixed';
        errorDiv.style.cssText = 'top: 2rem; right: 2rem; z-index: 9999; min-width: 300px;';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${message}`;

        document.body.appendChild(errorDiv);

        setTimeout(() => {
            errorDiv.remove();
        }, 4000);
    }

    // Mise à jour des aperçus en temps réel
    function updatePreview() {
        // Nom
        const nameValue = nameInput.value.trim();
        if (previewName) previewName.textContent = nameValue || 'Nom de l\'utilisateur';
        if (summaryName) summaryName.textContent = nameValue || 'Nom de l\'utilisateur';

        // Email
        const emailValue = emailInput.value.trim();
        if (previewEmail) previewEmail.textContent = emailValue || '<EMAIL>';
        if (summaryEmail) summaryEmail.textContent = emailValue || '<EMAIL>';

        // Avatar avec initiales
        const avatarContent = nameValue ?
            nameValue.split(' ').map(word => word.charAt(0)).join('').substring(0, 2).toUpperCase() :
            '<i class="fas fa-user"></i>';

        if (previewAvatar) previewAvatar.innerHTML = avatarContent;
        if (summaryAvatar) summaryAvatar.innerHTML = avatarContent;
    }

    function updateRolesPreview() {
        const selectedRoles = Array.from(roleCheckboxes)
            .filter(checkbox => checkbox.checked)
            .map(checkbox => {
                const roleName = checkbox.value;
                const config = roleConfig[roleName] || { color: 'dark', name: roleName };
                return {
                    name: config.name,
                    color: config.color,
                    value: roleName
                };
            });

        // Mise à jour de l'aperçu dans l'étape 3
        if (selectedRolesPreview) {
            if (selectedRoles.length > 0) {
                selectedRolesPreview.innerHTML = selectedRoles
                    .map(role => `<span class="badge bg-${role.color}">${role.name}</span>`)
                    .join(' ');
            } else {
                selectedRolesPreview.innerHTML = '<span class="no-selection">Aucun rôle sélectionné</span>';
            }
        }

        // Mise à jour du résumé
        if (summaryRoles) {
            if (selectedRoles.length > 0) {
                summaryRoles.innerHTML = selectedRoles
                    .map(role => `<span class="badge bg-${role.color}">${role.name}</span>`)
                    .join(' ');
            } else {
                summaryRoles.innerHTML = '<span class="badge bg-light text-muted">Aucun rôle</span>';
            }
        }
    }

    function updateSummary() {
        updatePreview();
        updateRolesPreview();
    }

    // Validation du mot de passe en temps réel
    function checkPasswordStrength(password) {
        const strengthIndicator = document.getElementById('password-strength');
        const strengthText = document.getElementById('strength-text');
        const requirements = {
            length: password.length >= 8,
            lowercase: /[a-z]/.test(password),
            uppercase: /[A-Z]/.test(password),
            number: /[0-9]/.test(password),
            special: /[^a-zA-Z0-9]/.test(password)
        };

        // Mise à jour des exigences visuelles
        updateRequirement('req-length', requirements.length);
        updateRequirement('req-lowercase', requirements.lowercase);
        updateRequirement('req-uppercase', requirements.uppercase);
        updateRequirement('req-number', requirements.number);
        updateRequirement('req-special', requirements.special);

        // Calcul de la force
        const validCount = Object.values(requirements).filter(Boolean).length;

        strengthIndicator.className = 'password-strength';
        if (password.length === 0) {
            strengthText.textContent = 'Tapez votre mot de passe';
        } else if (validCount < 2) {
            strengthIndicator.classList.add('weak');
            strengthText.textContent = 'Mot de passe faible';
        } else if (validCount < 4) {
            strengthIndicator.classList.add('medium');
            strengthText.textContent = 'Mot de passe moyen';
        } else {
            strengthIndicator.classList.add('strong');
            strengthText.textContent = 'Mot de passe fort';
        }
    }

    function updateRequirement(id, isValid) {
        const element = document.getElementById(id);
        if (element) {
            const icon = element.querySelector('i');
            if (isValid) {
                element.classList.add('valid');
                icon.className = 'fas fa-check';
            } else {
                element.classList.remove('valid');
                icon.className = 'fas fa-times';
            }
        }
    }

    // Vérification de la correspondance des mots de passe
    function checkPasswordMatch() {
        const matchIndicator = document.getElementById('password-match');
        const password = passwordInput.value;
        const confirmPassword = passwordConfirmInput.value;

        if (!matchIndicator) return;

        if (confirmPassword === '') {
            matchIndicator.textContent = '';
            matchIndicator.className = 'password-match';
            return;
        }

        if (password === confirmPassword) {
            matchIndicator.innerHTML = '<i class="fas fa-check me-1"></i>Les mots de passe correspondent';
            matchIndicator.className = 'password-match match';
        } else {
            matchIndicator.innerHTML = '<i class="fas fa-times me-1"></i>Les mots de passe ne correspondent pas';
            matchIndicator.className = 'password-match no-match';
        }
    }

    // Fonction pour basculer la visibilité du mot de passe
    window.togglePassword = function(fieldId) {
        const field = document.getElementById(fieldId);
        const icon = document.getElementById(fieldId + '-icon');

        if (field.type === 'password') {
            field.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            field.type = 'password';
            icon.className = 'fas fa-eye';
        }
    };

    // Event listeners
    if (nameInput) {
        nameInput.addEventListener('input', updatePreview);
    }

    if (emailInput) {
        emailInput.addEventListener('input', updatePreview);
    }

    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            checkPasswordStrength(this.value);
            checkPasswordMatch();
        });
    }

    if (passwordConfirmInput) {
        passwordConfirmInput.addEventListener('input', checkPasswordMatch);
    }

    // Event listeners pour les rôles
    roleCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateRolesPreview);
    });

    // Navigation au clavier
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && e.ctrlKey) {
            // Ctrl+Enter pour passer à l'étape suivante
            if (currentStep < totalSteps) {
                nextStep(currentStep + 1);
            } else {
                // Soumettre le formulaire
                document.querySelector('form').submit();
            }
        } else if (e.key === 'Escape') {
            // Échap pour revenir à l'étape précédente
            if (currentStep > 1) {
                prevStep(currentStep - 1);
            }
        }
    });

    // Animation d'entrée pour les cartes de rôles
    const roleCards = document.querySelectorAll('.role-card-modern');
    roleCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate-card');
    });

    // Initialisation
    updatePreview();
    updateRolesPreview();

    // Afficher la première étape
    showStep(1);

    // Animation de chargement terminée
    setTimeout(() => {
        document.body.classList.add('loaded');
    }, 500);
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin_minimal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/admin/users/create.blade.php ENDPATH**/ ?>