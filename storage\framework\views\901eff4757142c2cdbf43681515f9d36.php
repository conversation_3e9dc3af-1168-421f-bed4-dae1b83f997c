<?php $__env->startSection('title', 'Ajouter un utilisateur'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        --info-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        --card-hover-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        --border-radius: 12px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Header et breadcrumb */
    .breadcrumb-modern {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: var(--border-radius);
        padding: 1rem 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .breadcrumb-modern .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        font-size: 1.2em;
        color: #6c757d;
    }

    .breadcrumb-modern a {
        color: #495057;
        text-decoration: none;
        transition: var(--transition);
    }

    .breadcrumb-modern a:hover {
        color: #007bff;
        transform: translateX(2px);
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .page-subtitle {
        color: #6c757d;
        font-size: 1.1rem;
        margin-bottom: 0;
    }

    .text-gradient {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Cards modernes */
    .animate-card {
        animation: slideInUp 0.6s ease-out;
        transition: var(--transition);
    }

    .animate-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--card-hover-shadow);
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .form-card .card-header {
        background: var(--primary-gradient) !important;
        border: none;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }

    .help-card .card-header {
        background: var(--info-gradient) !important;
        border: none;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }

    .preview-card .card-header {
        background: var(--secondary-gradient) !important;
        border: none;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }

    /* Sections du formulaire */
    .form-section {
        position: relative;
        padding: 1.5rem 0;
    }

    .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .section-divider {
        height: 2px;
        background: linear-gradient(90deg, #007bff, transparent);
        margin-bottom: 1.5rem;
        border-radius: 1px;
    }

    /* Champs de formulaire modernes */
    .form-control-modern {
        border: 2px solid #e9ecef;
        border-radius: var(--border-radius);
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: var(--transition);
        background: #fff;
    }

    .form-control-modern:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
        transform: translateY(-2px);
    }

    .form-floating > .form-control-modern:focus ~ label,
    .form-floating > .form-control-modern:not(:placeholder-shown) ~ label {
        color: #007bff;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    }

    .form-hint {
        margin-top: 0.25rem;
    }

    /* Champs de mot de passe */
    .password-field {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        z-index: 10;
        transition: var(--transition);
    }

    .password-toggle:hover {
        color: #007bff;
    }

    .password-strength {
        height: 4px;
        background: #e9ecef;
        border-radius: 2px;
        margin-top: 0.5rem;
        overflow: hidden;
    }

    .password-strength::after {
        content: '';
        display: block;
        height: 100%;
        border-radius: 2px;
        transition: var(--transition);
    }

    .password-strength.weak::after {
        width: 33%;
        background: #dc3545;
    }

    .password-strength.medium::after {
        width: 66%;
        background: #ffc107;
    }

    .password-strength.strong::after {
        width: 100%;
        background: #28a745;
    }

    .password-match {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .password-match.match {
        color: #28a745;
    }

    .password-match.no-match {
        color: #dc3545;
    }

    .password-requirements {
        margin-top: 1rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: var(--border-radius);
        border-left: 4px solid #007bff;
    }

    /* Cartes de rôles */
    .roles-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .role-card {
        position: relative;
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: var(--transition);
        background: #fff;
        border: 2px solid #e9ecef;
    }

    .role-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border-color: #007bff;
    }

    .role-checkbox {
        position: absolute;
        opacity: 0;
        pointer-events: none;
    }

    .role-label {
        display: flex;
        align-items: center;
        padding: 1rem;
        cursor: pointer;
        transition: var(--transition);
        margin: 0;
    }

    .role-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .role-info {
        flex: 1;
    }

    .role-name {
        font-weight: 600;
        font-size: 1rem;
        color: #212529;
        margin-bottom: 0.25rem;
    }

    .role-description {
        font-size: 0.875rem;
        color: #6c757d;
        line-height: 1.4;
    }

    .role-check {
        width: 24px;
        height: 24px;
        border: 2px solid #dee2e6;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: var(--transition);
        color: transparent;
        flex-shrink: 0;
    }

    .role-checkbox:checked + .role-label {
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border-color: #007bff;
    }

    .role-checkbox:checked + .role-label .role-check {
        background: #007bff;
        border-color: #007bff;
        color: white;
        transform: scale(1.1);
    }

    .role-checkbox:checked + .role-label .role-name {
        color: #007bff;
    }

    /* Couleurs spécifiques pour les rôles */
    .bg-purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    /* Boutons modernes */
    .btn-modern {
        border-radius: var(--border-radius);
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-modern:hover::before {
        left: 100%;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .btn-primary.btn-modern {
        background: var(--primary-gradient);
    }

    .btn-outline-secondary.btn-modern {
        border: 2px solid #6c757d;
        color: #6c757d;
        background: transparent;
    }

    .btn-outline-secondary.btn-modern:hover {
        background: #6c757d;
        color: white;
    }

    /* Actions du formulaire */
    .form-actions {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 2px solid #e9ecef;
    }

    /* Panneau d'aide */
    .help-section {
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e9ecef;
    }

    .help-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .help-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }

    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        line-height: 1.5;
        margin: 0;
    }

    /* Aperçu utilisateur */
    .user-preview {
        padding: 1rem 0;
    }

    .preview-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin: 0 auto 1rem;
        transition: var(--transition);
    }

    .preview-avatar:hover {
        transform: scale(1.1);
    }

    .preview-name {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #212529;
    }

    .preview-email {
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    .preview-roles .badge {
        margin: 0.25rem;
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
        border-radius: 20px;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }

        .roles-container {
            grid-template-columns: 1fr;
        }

        .form-actions .d-flex {
            flex-direction: column;
            gap: 1rem;
        }

        .btn-modern {
            width: 100%;
        }
    }

    /* Animations d'entrée */
    .form-section {
        animation: fadeInUp 0.6s ease-out;
        animation-fill-mode: both;
    }

    .form-section:nth-child(1) { animation-delay: 0.1s; }
    .form-section:nth-child(2) { animation-delay: 0.2s; }
    .form-section:nth-child(3) { animation-delay: 0.3s; }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header avec breadcrumb -->
    <div class="header-section mb-4">
        <nav aria-label="breadcrumb" class="mb-3">
            <ol class="breadcrumb breadcrumb-modern">
                <li class="breadcrumb-item">
                    <a href="<?php echo e(route('admin.dashboard')); ?>">
                        <i class="fas fa-home me-1"></i>Tableau de bord
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="<?php echo e(route('admin.users.index')); ?>">
                        <i class="fas fa-users me-1"></i>Utilisateurs
                    </a>
                </li>
                <li class="breadcrumb-item active">
                    <i class="fas fa-user-plus me-1"></i>Nouvel utilisateur
                </li>
            </ol>
        </nav>

        <div class="d-flex justify-content-between align-items-center">
            <div class="page-title-section">
                <h1 class="page-title text-gradient">
                    <i class="fas fa-user-plus me-3"></i>Créer un nouvel utilisateur
                </h1>
                <p class="page-subtitle">Ajoutez un nouveau membre à votre équipe avec les rôles appropriés</p>
            </div>
            <div class="header-actions">
                <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-outline-secondary btn-modern">
                    <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Formulaire principal -->
        <div class="col-lg-8">
            <div class="card form-card shadow-sm border-0 animate-card">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-edit me-2"></i>Informations utilisateur
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form id="userForm" action="<?php echo e(route('admin.users.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>

                        <!-- Section informations personnelles -->
                        <div class="form-section mb-4">
                            <h6 class="section-title">
                                <i class="fas fa-user me-2 text-primary"></i>Informations personnelles
                            </h6>
                            <div class="section-divider"></div>

                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <div class="form-floating">
                                        <input type="text"
                                               class="form-control form-control-modern <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="name"
                                               name="name"
                                               value="<?php echo e(old('name')); ?>"
                                               placeholder="Nom complet"
                                               required>
                                        <label for="name">
                                            <i class="fas fa-user me-2"></i>Nom complet
                                        </label>
                                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <div class="form-hint">
                                            <small class="text-muted">Entrez le nom complet de l'utilisateur</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-12 mb-3">
                                    <div class="form-floating">
                                        <input type="email"
                                               class="form-control form-control-modern <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="email"
                                               name="email"
                                               value="<?php echo e(old('email')); ?>"
                                               placeholder="Adresse email"
                                               required>
                                        <label for="email">
                                            <i class="fas fa-envelope me-2"></i>Adresse email
                                        </label>
                                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <div class="form-hint">
                                            <small class="text-muted">Cette adresse sera utilisée pour la connexion</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Section sécurité -->
                        <div class="form-section mb-4">
                            <h6 class="section-title">
                                <i class="fas fa-shield-alt me-2 text-warning"></i>Sécurité et accès
                            </h6>
                            <div class="section-divider"></div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-floating password-field">
                                        <input type="password"
                                               class="form-control form-control-modern <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="password"
                                               name="password"
                                               placeholder="Mot de passe"
                                               required>
                                        <label for="password">
                                            <i class="fas fa-lock me-2"></i>Mot de passe
                                        </label>
                                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                            <i class="fas fa-eye" id="password-icon"></i>
                                        </button>
                                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <div class="password-strength" id="password-strength"></div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="form-floating password-field">
                                        <input type="password"
                                               class="form-control form-control-modern"
                                               id="password_confirmation"
                                               name="password_confirmation"
                                               placeholder="Confirmer le mot de passe"
                                               required>
                                        <label for="password_confirmation">
                                            <i class="fas fa-lock me-2"></i>Confirmer le mot de passe
                                        </label>
                                        <button type="button" class="password-toggle" onclick="togglePassword('password_confirmation')">
                                            <i class="fas fa-eye" id="password_confirmation-icon"></i>
                                        </button>
                                        <div class="password-match" id="password-match"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="password-requirements">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Le mot de passe doit contenir au moins 8 caractères
                                </small>
                            </div>
                        </div>

                        <!-- Section rôles -->
                        <div class="form-section mb-4">
                            <h6 class="section-title">
                                <i class="fas fa-user-tag me-2 text-success"></i>Rôles et permissions
                            </h6>
                            <div class="section-divider"></div>

                            <div class="roles-container">
                                <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $roleConfig = [
                                            'admin' => ['color' => 'danger', 'icon' => 'user-shield', 'name' => 'Administrateur', 'description' => 'Accès complet au système'],
                                            'accountant' => ['color' => 'primary', 'icon' => 'calculator', 'name' => 'Comptable', 'description' => 'Gestion financière et comptabilité'],
                                            'cement_manager' => ['color' => 'success', 'icon' => 'industry', 'name' => 'Gestionnaire Ciment', 'description' => 'Gestion des produits ciment'],
                                            'iron_manager' => ['color' => 'info', 'icon' => 'hammer', 'name' => 'Gestionnaire Fer', 'description' => 'Gestion des produits fer'],
                                            'cashier' => ['color' => 'warning', 'icon' => 'cash-register', 'name' => 'Caissier', 'description' => 'Gestion des transactions'],
                                            'customer_service' => ['color' => 'secondary', 'icon' => 'headset', 'name' => 'Service Client', 'description' => 'Support et service clientèle'],
                                            'customer' => ['color' => 'dark', 'icon' => 'user', 'name' => 'Client', 'description' => 'Accès client standard'],
                                            'driver' => ['color' => 'purple', 'icon' => 'truck', 'name' => 'Chauffeur', 'description' => 'Gestion des livraisons'],
                                        ];
                                        $config = $roleConfig[$role->name] ?? ['color' => 'dark', 'icon' => 'user', 'name' => ucfirst($role->name), 'description' => 'Rôle personnalisé'];
                                    ?>
                                    <div class="role-card" data-role="<?php echo e($role->name); ?>">
                                        <input class="role-checkbox"
                                               type="checkbox"
                                               name="roles[]"
                                               value="<?php echo e($role->name); ?>"
                                               id="role_<?php echo e($role->id); ?>"
                                               <?php echo e(in_array($role->name, old('roles', [])) ? 'checked' : ''); ?>>
                                        <label class="role-label" for="role_<?php echo e($role->id); ?>">
                                            <div class="role-icon bg-<?php echo e($config['color']); ?>">
                                                <i class="fas fa-<?php echo e($config['icon']); ?>"></i>
                                            </div>
                                            <div class="role-info">
                                                <div class="role-name"><?php echo e($config['name']); ?></div>
                                                <div class="role-description"><?php echo e($config['description']); ?></div>
                                            </div>
                                            <div class="role-check">
                                                <i class="fas fa-check"></i>
                                            </div>
                                        </label>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php $__errorArgs = ['roles'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger mt-2">
                                    <i class="fas fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="form-actions">
                            <div class="d-flex justify-content-between align-items-center">
                                <button type="button" class="btn btn-outline-secondary btn-modern" onclick="resetForm()">
                                    <i class="fas fa-undo me-2"></i>Réinitialiser
                                </button>
                                <button type="submit" class="btn btn-primary btn-modern" id="submitBtn">
                                    <i class="fas fa-user-plus me-2"></i>Créer l'utilisateur
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Panneau d'aide -->
        <div class="col-lg-4">
            <div class="card help-card shadow-sm border-0 animate-card">
                <div class="card-header bg-gradient-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Guide de création
                    </h6>
                </div>
                <div class="card-body">
                    <div class="help-section">
                        <h6 class="help-title">
                            <i class="fas fa-user text-primary me-2"></i>Informations personnelles
                        </h6>
                        <p class="help-text">Saisissez le nom complet et l'adresse email de l'utilisateur. L'email servira d'identifiant de connexion.</p>
                    </div>

                    <div class="help-section">
                        <h6 class="help-title">
                            <i class="fas fa-shield-alt text-warning me-2"></i>Sécurité
                        </h6>
                        <p class="help-text">Choisissez un mot de passe sécurisé d'au moins 8 caractères. L'utilisateur pourra le modifier après sa première connexion.</p>
                    </div>

                    <div class="help-section">
                        <h6 class="help-title">
                            <i class="fas fa-user-tag text-success me-2"></i>Rôles
                        </h6>
                        <p class="help-text">Sélectionnez un ou plusieurs rôles selon les responsabilités de l'utilisateur. Chaque rôle donne accès à des fonctionnalités spécifiques.</p>
                    </div>

                    <div class="help-section">
                        <div class="alert alert-info border-0">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Conseil :</strong> Commencez par attribuer les rôles minimaux nécessaires. Vous pourrez toujours les modifier plus tard.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aperçu utilisateur -->
            <div class="card preview-card shadow-sm border-0 animate-card mt-4">
                <div class="card-header bg-gradient-secondary text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>Aperçu utilisateur
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="user-preview">
                        <div class="preview-avatar" id="previewAvatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <h6 class="preview-name" id="previewName">Nom de l'utilisateur</h6>
                        <p class="preview-email text-muted" id="previewEmail"><EMAIL></p>
                        <div class="preview-roles" id="previewRoles">
                            <span class="badge bg-light text-muted">Aucun rôle sélectionné</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Éléments du formulaire
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const passwordConfirmInput = document.getElementById('password_confirmation');
    const roleCheckboxes = document.querySelectorAll('input[name="roles[]"]');

    // Éléments d'aperçu
    const previewName = document.getElementById('previewName');
    const previewEmail = document.getElementById('previewEmail');
    const previewRoles = document.getElementById('previewRoles');
    const previewAvatar = document.getElementById('previewAvatar');

    // Configuration des rôles
    const roleConfig = {
        'admin': { color: 'danger', icon: 'user-shield', name: 'Administrateur' },
        'accountant': { color: 'primary', icon: 'calculator', name: 'Comptable' },
        'cement_manager': { color: 'success', icon: 'industry', name: 'Gest. Ciment' },
        'iron_manager': { color: 'info', icon: 'hammer', name: 'Gest. Fer' },
        'cashier': { color: 'warning', icon: 'cash-register', name: 'Caissier' },
        'customer_service': { color: 'secondary', icon: 'headset', name: 'Service Client' },
        'customer': { color: 'dark', icon: 'user', name: 'Client' },
        'driver': { color: 'purple', icon: 'truck', name: 'Chauffeur' }
    };

    // Mise à jour de l'aperçu en temps réel
    function updatePreview() {
        // Nom
        const nameValue = nameInput.value.trim();
        previewName.textContent = nameValue || 'Nom de l\'utilisateur';

        // Email
        const emailValue = emailInput.value.trim();
        previewEmail.textContent = emailValue || '<EMAIL>';

        // Avatar avec initiales
        if (nameValue) {
            const initials = nameValue.split(' ').map(word => word.charAt(0)).join('').substring(0, 2).toUpperCase();
            previewAvatar.innerHTML = initials;
        } else {
            previewAvatar.innerHTML = '<i class="fas fa-user"></i>';
        }

        // Rôles
        const selectedRoles = Array.from(roleCheckboxes)
            .filter(checkbox => checkbox.checked)
            .map(checkbox => {
                const roleName = checkbox.value;
                const config = roleConfig[roleName] || { color: 'dark', name: roleName };
                return `<span class="badge bg-${config.color}">${config.name}</span>`;
            });

        if (selectedRoles.length > 0) {
            previewRoles.innerHTML = selectedRoles.join(' ');
        } else {
            previewRoles.innerHTML = '<span class="badge bg-light text-muted">Aucun rôle sélectionné</span>';
        }
    }

    // Écouteurs d'événements pour la mise à jour de l'aperçu
    nameInput.addEventListener('input', updatePreview);
    emailInput.addEventListener('input', updatePreview);
    roleCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updatePreview);
    });

    // Validation du mot de passe en temps réel
    function checkPasswordStrength(password) {
        const strengthIndicator = document.getElementById('password-strength');
        let strength = 0;

        if (password.length >= 8) strength++;
        if (password.match(/[a-z]/)) strength++;
        if (password.match(/[A-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        if (password.match(/[^a-zA-Z0-9]/)) strength++;

        strengthIndicator.className = 'password-strength';
        if (strength < 2) {
            strengthIndicator.classList.add('weak');
        } else if (strength < 4) {
            strengthIndicator.classList.add('medium');
        } else {
            strengthIndicator.classList.add('strong');
        }
    }

    // Vérification de la correspondance des mots de passe
    function checkPasswordMatch() {
        const matchIndicator = document.getElementById('password-match');
        const password = passwordInput.value;
        const confirmPassword = passwordConfirmInput.value;

        if (confirmPassword === '') {
            matchIndicator.textContent = '';
            matchIndicator.className = 'password-match';
            return;
        }

        if (password === confirmPassword) {
            matchIndicator.textContent = '✓ Les mots de passe correspondent';
            matchIndicator.className = 'password-match match';
        } else {
            matchIndicator.textContent = '✗ Les mots de passe ne correspondent pas';
            matchIndicator.className = 'password-match no-match';
        }
    }

    // Écouteurs pour la validation des mots de passe
    passwordInput.addEventListener('input', function() {
        checkPasswordStrength(this.value);
        checkPasswordMatch();
    });

    passwordConfirmInput.addEventListener('input', checkPasswordMatch);

    // Fonction pour basculer la visibilité du mot de passe
    window.togglePassword = function(fieldId) {
        const field = document.getElementById(fieldId);
        const icon = document.getElementById(fieldId + '-icon');

        if (field.type === 'password') {
            field.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            field.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    };

    // Fonction pour réinitialiser le formulaire
    window.resetForm = function() {
        if (confirm('Êtes-vous sûr de vouloir réinitialiser le formulaire ?')) {
            document.getElementById('userForm').reset();
            updatePreview();

            // Réinitialiser les indicateurs de mot de passe
            document.getElementById('password-strength').className = 'password-strength';
            document.getElementById('password-match').textContent = '';
            document.getElementById('password-match').className = 'password-match';
        }
    };

    // Validation du formulaire avant soumission
    document.getElementById('userForm').addEventListener('submit', function(e) {
        const submitBtn = document.getElementById('submitBtn');
        const selectedRoles = Array.from(roleCheckboxes).filter(cb => cb.checked);

        // Vérifier qu'au moins un rôle est sélectionné
        if (selectedRoles.length === 0) {
            e.preventDefault();
            alert('Veuillez sélectionner au moins un rôle pour l\'utilisateur.');
            return;
        }

        // Vérifier la correspondance des mots de passe
        if (passwordInput.value !== passwordConfirmInput.value) {
            e.preventDefault();
            alert('Les mots de passe ne correspondent pas.');
            return;
        }

        // Animation du bouton de soumission
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Création en cours...';
        submitBtn.disabled = true;
    });

    // Animation d'entrée pour les cartes de rôles
    const roleCards = document.querySelectorAll('.role-card');
    roleCards.forEach((card, index) => {
        card.style.animationDelay = `${0.1 + (index * 0.05)}s`;
        card.style.animation = 'fadeInUp 0.6s ease-out both';
    });

    // Initialiser l'aperçu
    updatePreview();
});
</script>
<?php $__env->stopSection(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin_minimal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/admin/users/create.blade.php ENDPATH**/ ?>