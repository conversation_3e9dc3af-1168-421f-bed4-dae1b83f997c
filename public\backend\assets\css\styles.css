/* Variables */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* Base styles */
body {
    margin: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #f8f9fa;
}

/* Sidebar Layout */
#layoutSidenav {
    display: flex;
}

#layoutSidenav_nav {
    flex-basis: 225px;
    flex-shrink: 0;
    transition: transform .15s ease-in-out;
    z-index: 1038;
    transform: translateX(0);
}

#layoutSidenav_content {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-width: 0;
    flex-grow: 1;
    min-height: calc(100vh - 56px);
    margin-left: -225px;
    margin-left: 0;
}

.sb-sidenav-toggled #layoutSidenav_nav {
    transform: translateX(-225px);
}

.sb-sidenav-toggled #layoutSidenav_content {
    margin-left: -225px;
}

/* Navigation */
.sb-topnav {
    padding-left: 0;
    height: 56px;
    z-index: 1039;
}

.sb-nav-fixed #layoutSidenav #layoutSidenav_nav {
    width: 225px;
    height: 100vh;
    z-index: 1038;
}

.sb-nav-fixed #layoutSidenav #layoutSidenav_nav .sb-sidenav {
    padding-top: 56px;
}

.sb-nav-fixed #layoutSidenav #layoutSidenav_nav .sb-sidenav .sb-sidenav-menu {
    overflow-y: auto;
}

.sb-nav-fixed #layoutSidenav #layoutSidenav_content {
    padding-left: 225px;
    top: 56px;
}

/* Sidebar Navigation */
.sb-sidenav {
    display: flex;
    flex-direction: column;
    height: 100%;
    flex-wrap: nowrap;
}

.sb-sidenav .sb-sidenav-menu {
    flex-grow: 1;
}

.sb-sidenav .sb-sidenav-menu .nav {
    flex-direction: column;
    flex-wrap: nowrap;
}

.sb-sidenav .sb-sidenav-menu .nav .sb-sidenav-menu-heading {
    padding: 1.75rem 1rem 0.75rem;
    font-size: 0.75rem;
    font-weight: bold;
    text-transform: uppercase;
}

.sb-sidenav .sb-sidenav-menu .nav .nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    position: relative;
}

.sb-sidenav .sb-sidenav-menu .nav .nav-link .sb-nav-link-icon {
    font-size: 0.9rem;
    padding-right: 0.5rem;
}

.sb-sidenav .sb-sidenav-menu .nav .nav-link .sb-sidenav-collapse-arrow {
    display: inline-block;
    margin-left: auto;
    transition: transform .15s ease;
}

.sb-sidenav .sb-sidenav-menu .nav .nav-link.collapsed .sb-sidenav-collapse-arrow {
    transform: rotate(-90deg);
}

.sb-sidenav .sb-sidenav-menu .nav .sb-sidenav-menu-nested {
    margin-left: 1.5rem;
    flex-direction: column;
}

.sb-sidenav .sb-sidenav-footer {
    padding: 0.75rem;
    flex-shrink: 0;
}

/* Dark Theme */
.sb-sidenav-dark {
    background-color: #212529;
    color: rgba(255, 255, 255, 0.5);
}

.sb-sidenav-dark .sb-sidenav-menu .sb-sidenav-menu-heading {
    color: rgba(255, 255, 255, 0.25);
}

.sb-sidenav-dark .sb-sidenav-menu .nav-link {
    color: rgba(255, 255, 255, 0.5);
}

.sb-sidenav-dark .sb-sidenav-menu .nav-link:hover {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
}

.sb-sidenav-dark .sb-sidenav-menu .nav-link.active {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
}

.sb-sidenav-dark .sb-sidenav-footer {
    background-color: #343a40;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #layoutSidenav #layoutSidenav_nav {
        transform: translateX(-225px);
    }
    
    #layoutSidenav #layoutSidenav_content {
        margin-left: -225px;
    }
    
    .sb-sidenav-toggled #layoutSidenav #layoutSidenav_nav {
        transform: translateX(0);
    }
    
    .sb-sidenav-toggled #layoutSidenav #layoutSidenav_content {
        margin-left: 0;
    }
}

/* Card Styles */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1rem 1.25rem;
    font-weight: 500;
}

.card-header i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

/* Stats Cards */
.stats-card {
    border-radius: 0.5rem;
    padding: 1.5rem;
    height: 100%;
}

.stats-card .icon {
    font-size: 2rem;
    opacity: 0.8;
}

.stats-card .title {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.stats-card .value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0;
}

/* Table Styles */
.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
}

.table td {
    vertical-align: middle;
    padding: 1rem;
}

/* Badge Styles */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
    letter-spacing: 0.03em;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        margin-bottom: 1rem;
    }
}

/* Animation */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom styles for statistics cards */
.bg-gradient-primary {
    background: linear-gradient(45deg, #4e73df 0%, #224abe 100%);
}

.bg-gradient-success {
    background: linear-gradient(45deg, #1cc88a 0%, #13855c 100%);
}

.bg-gradient-warning {
    background: linear-gradient(45deg, #f6c23e 0%, #dda20a 100%);
}

.bg-gradient-danger {
    background: linear-gradient(45deg, #e74a3b 0%, #be2617 100%);
}

/* Enhanced card styles */
.card-stats {
    overflow: hidden;
    position: relative;
}

.card-stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
}

.card-stats .icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    background: rgba(255,255,255,0.1);
}

/* Enhanced table styles */
.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,0.075);
    transition: background-color 0.2s ease;
}

.table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

/* Improved badge styles */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
    letter-spacing: 0.03em;
    border-radius: 0.25rem;
}

.badge.bg-success {
    background-color: #1cc88a !important;
}

.badge.bg-warning {
    background-color: #f6c23e !important;
    color: #000 !important;
}

.badge.bg-danger {
    background-color: #e74a3b !important;
}

/* Card hover effects */
.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
}

/* Improved button styles */
.btn {
    font-weight: 500;
    letter-spacing: 0.03em;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

/* Navigation improvements */
.navbar-brand {
    font-weight: 700;
    letter-spacing: 0.05em;
}

.nav-link {
    font-weight: 500;
    transition: all 0.2s ease;
}

/* Footer enhancements */
footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

footer a {
    color: #4e73df;
    text-decoration: none;
    transition: color 0.2s ease;
}

footer a:hover {
    color: #2e59d9;
    text-decoration: underline;
}
