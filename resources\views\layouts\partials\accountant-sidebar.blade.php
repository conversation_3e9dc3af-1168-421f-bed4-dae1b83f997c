<!-- Main Sidebar Container -->
<aside class="main-sidebar sidebar-dark-primary elevation-4">
    <!-- Brand Logo -->
    <a href="{{ route('accountant.dashboard') }}" class="brand-link">
        <img src="{{ asset('images/logo.png') }}" alt="Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
        <span class="brand-text font-weight-light">GRADIS</span>
    </a>

    <!-- Sidebar -->
    <div class="sidebar">
        <!-- Sidebar user panel (optional) -->
        <div class="user-panel mt-3 pb-3 mb-3 d-flex">
            <div class="image">
                <img src="{{ asset('images/user.png') }}" class="img-circle elevation-2" alt="User Image">
            </div>
            <div class="info">
                <a href="#" class="d-block">{{ Auth::user()->name }}</a>
            </div>
        </div>

        <!-- Sidebar Menu -->
        <nav class="mt-2">
            <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                <!-- Tableau de bord -->
                <li class="nav-item">
                    <a href="{{ route('accountant.dashboard') }}" class="nav-link {{ request()->routeIs('accountant.dashboard') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-tachometer-alt"></i>
                        <p>Tableau de bord</p>
                    </a>
                </li>

                <!-- Bons de commande -->
                <li class="nav-item">
                    <a href="{{ route('accountant.cement-orders.index') }}" 
                       class="nav-link {{ request()->routeIs('accountant.cement-orders.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-file-invoice"></i>
                        <p>
                            Bons de commande
                            @php
                                $pendingOrders = \App\Models\CementOrder::where('status', 'pending')->count();
                            @endphp
                            @if($pendingOrders > 0)
                                <span class="badge badge-warning right">{{ $pendingOrders }}</span>
                            @endif
                        </p>
                    </a>
                </li>

                <!-- Destinations -->
                <li class="nav-item">
                    <a href="{{ route('accountant.destinations.index') }}" class="nav-link {{ request()->routeIs('accountant.destinations.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-map-marker-alt"></i>
                        <p>Destinations</p>
                    </a>
                </li>

                <!-- Statistiques -->
                <li class="nav-item">
                    <a href="{{ route('accountant.cement-orders.stats') }}" class="nav-link {{ request()->routeIs('accountant.cement-orders.stats') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-chart-bar"></i>
                        <p>Statistiques</p>
                    </a>
                </li>

                <!-- Paramètres -->
                <li class="nav-item">
                    <a href="{{ route('accountant.settings.index') }}" class="nav-link {{ request()->routeIs('accountant.settings.*') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-cog"></i>
                        <p>Paramètres</p>
                    </a>
                </li>

                <!-- Déconnexion -->
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                        <i class="nav-icon fas fa-sign-out-alt"></i>
                        <p>Déconnexion</p>
                    </a>
                </li>
            </ul>
        </nav>
        <!-- /.sidebar-menu -->
    </div>
    <!-- /.sidebar -->
</aside>

<form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
    @csrf
</form>
