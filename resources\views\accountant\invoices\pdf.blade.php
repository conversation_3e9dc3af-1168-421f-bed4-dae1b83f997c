<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Facture #{{ $order->number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 20px;
        }
        .invoice-title {
            font-size: 24px;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .invoice-date {
            color: #7f8c8d;
            margin-bottom: 30px;
        }
        .addresses {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .address-block {
            width: 45%;
        }
        .address-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            color: #2c3e50;
        }
        .text-end {
            text-align: right;
        }
        .totals {
            width: 35%;
            margin-left: auto;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #ddd;
        }
        .total-row.final {
            font-weight: bold;
            border-bottom: 2px solid #2c3e50;
        }
        .notes {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .payment-info {
            margin-top: 30px;
            padding: 15px;
            background-color: #e8f4f8;
            border-radius: 4px;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            color: #7f8c8d;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <img src="{{ asset('images/logo.png') }}" alt="Logo" class="logo">
        <div class="invoice-title">Facture #{{ $order->number }}</div>
        <div class="invoice-date">Émise le {{ $order->created_at->format('d/m/Y') }}</div>
    </div>

    <div class="addresses">
        <div class="address-block">
            <div class="address-title">De</div>
            <div>GRADIS</div>
            <div>123 Rue du Commerce</div>
            <div>75001 Paris</div>
            <div>France</div>
            <div><EMAIL></div>
        </div>
        <div class="address-block">
            <div class="address-title">À</div>
            <div>{{ $order->user->name }}</div>
            <div>{{ $order->user->address ?? 'Adresse non spécifiée' }}</div>
            <div>{{ $order->user->city ?? 'Ville non spécifiée' }}</div>
            <div>{{ $order->user->email }}</div>
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th>Description</th>
                <th class="text-end">Quantité</th>
                <th class="text-end">Prix unitaire</th>
                <th class="text-end">Total</th>
            </tr>
        </thead>
        <tbody>
            @foreach($order->items as $item)
            <tr>
                <td>{{ $item->product->name }}</td>
                <td class="text-end">{{ $item->quantity }}</td>
                <td class="text-end">{{ number_format($item->unit_price, 0, ',', ' ') }} FCFA</td>
                <td class="text-end">{{ number_format($item->total, 0, ',', ' ') }} FCFA</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="totals">
        <div class="total-row">
            <span>Sous-total</span>
            <span>{{ number_format($order->subtotal, 0, ',', ' ') }} FCFA</span>
        </div>
        @if($order->tax > 0)
        <div class="total-row">
            <span>TVA ({{ $order->tax_rate }}%)</span>
            <span>{{ number_format($order->tax, 0, ',', ' ') }} FCFA</span>
        </div>
        @endif
        @if($order->discount > 0)
        <div class="total-row">
            <span>Remise</span>
            <span>-{{ number_format($order->discount, 0, ',', ' ') }} FCFA</span>
        </div>
        @endif
        <div class="total-row final">
            <span>Total</span>
            <span>{{ number_format($order->total, 0, ',', ' ') }} FCFA</span>
        </div>
    </div>

    @if($order->notes)
    <div class="notes">
        <div class="address-title">Notes</div>
        <div>{{ $order->notes }}</div>
    </div>
    @endif

    <div class="payment-info">
        <div class="address-title">Informations de paiement</div>
        <div>Méthode de paiement : {{ $order->payment_method }}</div>
        <div>Statut du paiement : {{ $order->payment_status }}</div>
        @if($order->payment_date)
        <div>Date de paiement : {{ $order->payment_date->format('d/m/Y H:i') }}</div>
        @endif
    </div>

    <div class="footer">
        <p>Merci de votre confiance !</p>
        <p>© {{ date('Y') }} GRADIS. Tous droits réservés.</p>
    </div>
</body>
</html>
