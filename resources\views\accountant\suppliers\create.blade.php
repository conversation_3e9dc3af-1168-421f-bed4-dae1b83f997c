@extends('layouts.accountant')

@section('title', 'Nouveau fournisseur')

@push('styles')
<style>
    .modern-supplier-form {
        background: #f8f9fa;
        min-height: 100vh;
        padding: 2rem 0;
    }

    .form-container {
        max-width: 1000px;
        margin: 0 auto;
    }

    .form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        border: 1px solid #e9ecef;
    }

    .form-header {
        background: white;
        padding: 2rem;
        text-align: center;
        color: #495057;
        border-bottom: 3px solid #e9ecef;
        position: relative;
    }

    .form-header h1 {
        font-size: 2rem;
        font-weight: 600;
        margin: 0;
        color: #495057;
    }

    .form-header .subtitle {
        font-size: 1rem;
        color: #6c757d;
        margin-top: 0.5rem;
    }

    .form-body {
        padding: 3rem;
    }

    .input-group-modern {
        position: relative;
        margin-bottom: 2rem;
    }

    .input-icon {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 1.2rem;
        z-index: 3;
        transition: all 0.3s ease;
    }

    .form-control-modern {
        border: 2px solid #e1e5e9;
        border-radius: 12px;
        padding: 15px 15px 15px 50px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8f9fa;
        height: auto;
    }

    .form-control-modern:focus {
        border-color: #4facfe;
        box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        background: white;
        transform: translateY(-2px);
    }

    .form-control-modern:focus + .input-icon {
        color: #4facfe;
        transform: translateY(-50%) scale(1.1);
    }

    .form-label-modern {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
        display: flex;
        align-items: center;
    }

    .label-icon {
        margin-right: 8px;
        font-size: 1rem;
    }

    .required-star {
        color: #e53e3e;
        margin-left: 4px;
    }

    .switch-container {
        background: #f7fafc;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 20px;
        transition: all 0.3s ease;
    }

    .switch-container:hover {
        border-color: #4facfe;
        background: #edf2f7;
    }

    .form-switch-modern .form-check-input {
        width: 3rem;
        height: 1.5rem;
        border-radius: 1rem;
        background-color: #cbd5e0;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .form-switch-modern .form-check-input:checked {
        background-color: #48bb78;
        border-color: #48bb78;
    }

    .form-switch-modern .form-check-input:focus {
        box-shadow: 0 0 0 0.2rem rgba(72, 187, 120, 0.25);
    }

    .btn-modern {
        padding: 12px 30px;
        border-radius: 10px;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn-primary-modern {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
    }

    .btn-primary-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(79, 172, 254, 0.6);
        color: white;
    }

    .btn-secondary-modern {
        background: #e2e8f0;
        color: #4a5568;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-secondary-modern:hover {
        background: #cbd5e0;
        transform: translateY(-2px);
        color: #2d3748;
    }

    .btn-back {
        background: #6c757d;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .btn-back:hover {
        background: #5a6268;
        color: white;
        transform: translateX(-3px);
    }

    .alert-modern {
        border: none;
        border-radius: 12px;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
        color: #742a2a;
        border-left: 4px solid #e53e3e;
    }

    .section-divider {
        height: 2px;
        background: linear-gradient(90deg, transparent 0%, #e2e8f0 50%, transparent 100%);
        margin: 2rem 0;
    }

    .icon-company { color: #4299e1; }
    .icon-person { color: #48bb78; }
    .icon-email { color: #ed8936; }
    .icon-phone { color: #9f7aea; }
    .icon-address { color: #38b2ac; }
    .icon-status { color: #f56565; }
</style>
@endpush

@section('content')
<div class="modern-supplier-form">
    <div class="container-fluid">
        <div class="form-container">
            <a href="{{ route('accountant.suppliers.index') }}" class="btn-back">
                <i class="fas fa-arrow-left me-2"></i>Retour
            </a>

            <div class="form-card">
                <div class="form-header">
                    <h1><i class="fas fa-industry me-3"></i>Nouveau Fournisseur</h1>
                    <p class="subtitle">Ajoutez un nouveau partenaire commercial à votre réseau</p>
                </div>

                <div class="form-body">
                    @if ($errors->any())
                        <div class="alert-modern">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Erreurs de validation</strong>
                            </div>
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('accountant.suppliers.store') }}" method="POST">
                        @csrf

                        <div class="row">
                            <!-- Informations de base -->
                            <div class="col-12">
                                <h4 class="text-primary mb-3">
                                    <i class="fas fa-info-circle me-2"></i>Informations de base
                                </h4>
                            </div>

                            <!-- Nom de l'entreprise -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="name" class="form-label-modern">
                                        <i class="fas fa-building label-icon icon-company"></i>
                                        Nom de l'entreprise
                                        <span class="required-star">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control-modern @error('name') is-invalid @enderror"
                                           id="name"
                                           name="name"
                                           value="{{ old('name') }}"
                                           placeholder="Ex: GRANUTOGO SARL"
                                           required>
                                    <i class="fas fa-building input-icon icon-company"></i>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Personne de contact -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="contact_person" class="form-label-modern">
                                        <i class="fas fa-user label-icon icon-person"></i>
                                        Personne de contact
                                    </label>
                                    <input type="text"
                                           class="form-control-modern @error('contact_person') is-invalid @enderror"
                                           id="contact_person"
                                           name="contact_person"
                                           value="{{ old('contact_person') }}"
                                           placeholder="Ex: Jean Dupont">
                                    <i class="fas fa-user input-icon icon-person"></i>
                                    @error('contact_person')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="section-divider"></div>
                                <h4 class="text-primary mb-3">
                                    <i class="fas fa-address-book me-2"></i>Coordonnées
                                </h4>
                            </div>

                            <!-- Email -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="email" class="form-label-modern">
                                        <i class="fas fa-envelope label-icon icon-email"></i>
                                        Adresse email
                                    </label>
                                    <input type="email"
                                           class="form-control-modern @error('email') is-invalid @enderror"
                                           id="email"
                                           name="email"
                                           value="{{ old('email') }}"
                                           placeholder="<EMAIL>">
                                    <i class="fas fa-envelope input-icon icon-email"></i>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Téléphone -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="phone" class="form-label-modern">
                                        <i class="fas fa-phone label-icon icon-phone"></i>
                                        Numéro de téléphone
                                    </label>
                                    <input type="tel"
                                           class="form-control-modern @error('phone') is-invalid @enderror"
                                           id="phone"
                                           name="phone"
                                           value="{{ old('phone') }}"
                                           placeholder="+228 XX XX XX XX">
                                    <i class="fas fa-phone input-icon icon-phone"></i>
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Adresse -->
                            <div class="col-md-12">
                                <div class="input-group-modern">
                                    <label for="address" class="form-label-modern">
                                        <i class="fas fa-map-marker-alt label-icon icon-address"></i>
                                        Adresse complète
                                    </label>
                                    <textarea class="form-control-modern @error('address') is-invalid @enderror"
                                              id="address"
                                              name="address"
                                              rows="3"
                                              placeholder="Adresse complète de l'entreprise...">{{ old('address') }}</textarea>
                                    <i class="fas fa-map-marker-alt input-icon icon-address"></i>
                                    @error('address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="section-divider"></div>
                                <h4 class="text-primary mb-3">
                                    <i class="fas fa-cog me-2"></i>Configuration
                                </h4>
                            </div>

                            <!-- Statut -->
                            <div class="col-md-6">
                                <div class="switch-container">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div>
                                            <label class="form-label-modern mb-1">
                                                <i class="fas fa-toggle-on label-icon icon-status"></i>
                                                Statut du fournisseur
                                            </label>
                                            <p class="text-muted small mb-0">
                                                Activez ce fournisseur pour qu'il apparaisse dans les listes de sélection
                                            </p>
                                        </div>
                                        <div class="form-check form-switch form-switch-modern">
                                            <input type="hidden" name="is_active" value="0">
                                            <input class="form-check-input"
                                                   type="checkbox"
                                                   id="is_active"
                                                   name="is_active"
                                                   value="1"
                                                   {{ old('is_active', '1') == '1' ? 'checked' : '' }}>
                                            <label class="form-check-label fw-bold" for="is_active">
                                                <span class="text-success">Actif</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="section-divider"></div>

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="{{ route('accountant.suppliers.index') }}" class="btn btn-secondary-modern">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-primary-modern">
                                <i class="fas fa-save me-2"></i>Enregistrer le fournisseur
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation d'entrée pour les champs
    const inputs = document.querySelectorAll('.form-control-modern');
    inputs.forEach((input, index) => {
        input.style.opacity = '0';
        input.style.transform = 'translateY(20px)';
        setTimeout(() => {
            input.style.transition = 'all 0.5s ease';
            input.style.opacity = '1';
            input.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Validation en temps réel
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');
    const phoneInput = document.getElementById('phone');

    nameInput.addEventListener('input', function() {
        if (this.value.length < 2) {
            this.style.borderColor = '#e53e3e';
        } else {
            this.style.borderColor = '#48bb78';
        }
    });

    emailInput.addEventListener('input', function() {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (this.value && !emailRegex.test(this.value)) {
            this.style.borderColor = '#e53e3e';
        } else if (this.value) {
            this.style.borderColor = '#48bb78';
        } else {
            this.style.borderColor = '#e1e5e9';
        }
    });

    phoneInput.addEventListener('input', function() {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
        if (this.value && !phoneRegex.test(this.value)) {
            this.style.borderColor = '#e53e3e';
        } else if (this.value) {
            this.style.borderColor = '#48bb78';
        } else {
            this.style.borderColor = '#e1e5e9';
        }
    });

    // Animation du switch
    const switchInput = document.getElementById('is_active');
    const switchLabel = switchInput.nextElementSibling.querySelector('span');

    switchInput.addEventListener('change', function() {
        if (this.checked) {
            switchLabel.textContent = 'Actif';
            switchLabel.className = 'text-success';
        } else {
            switchLabel.textContent = 'Inactif';
            switchLabel.className = 'text-danger';
        }
    });
});
</script>
@endsection
