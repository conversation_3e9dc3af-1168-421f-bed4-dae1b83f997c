@extends('layouts.cashier')

@section('styles')
<link rel="stylesheet" href="{{ asset('css/cashier-dashboard-modern.css') }}">
<link rel="stylesheet" href="{{ asset('css/cashier-payments-responsive.css') }}">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
@endsection

@section('title', 'Gestion des Paiements')

@section('content')
<div class="container-fluid">
    <!-- En-tête de page avec titre -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-money-bill-wave"></i>
            <span>Gestion des Paiements</span>
        </h1>
    </div>
    
    <!-- Statistiques des paiements -->
    <div class="stats-container">
        <div class="stat-card card-effect">
            <div class="stat-title">Total Paiements</div>
            <div class="stat-value">{{ $payments->total() }}</div>
            <div class="stat-trend positive">
                <i class="fas fa-arrow-up"></i>
                <span>+3.2% depuis le mois dernier</span>
            </div>
            <div class="stat-icon">
                <i class="fas fa-receipt"></i>
            </div>
        </div>
        
        <div class="stat-card card-effect">
            <div class="stat-title">Montant Total</div>
            <div class="stat-value">{{ number_format($payments->sum('amount'), 0, ',', ' ') }} FCFA</div>
            <div class="stat-trend positive">
                <i class="fas fa-arrow-up"></i>
                <span>+4.7% depuis le mois dernier</span>
            </div>
            <div class="stat-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
        </div>
        
        <div class="stat-card card-effect">
            <div class="stat-title">Paiement Moyen</div>
            <div class="stat-value">{{ number_format($payments->avg('amount') ?? 0, 0, ',', ' ') }} FCFA</div>
            <div class="stat-trend positive">
                <i class="fas fa-arrow-up"></i>
                <span>+1.8% depuis le mois dernier</span>
            </div>
            <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
            </div>
        </div>
        
        <div class="stat-card card-effect">
            <div class="stat-title">Clients Uniques</div>
            <div class="stat-value">{{ $payments->pluck('sale.customer_name')->unique()->count() }}</div>
            <div class="stat-trend positive">
                <i class="fas fa-arrow-up"></i>
                <span>+5.3% depuis le mois dernier</span>
            </div>
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
        </div>
    </div>
    
    <!-- Section de filtrage -->
    <div class="filter-section">
        <div class="filter-group">
            <span class="filter-label">Filtrer par méthode:</span>
            <button class="filter-button active" data-filter="all">
                <i class="fas fa-list"></i> Toutes
            </button>
            <button class="filter-button" data-filter="cash">
                <i class="fas fa-money-bill"></i> Espèces
            </button>
            <button class="filter-button" data-filter="bank_transfer">
                <i class="fas fa-university"></i> Virement
            </button>
            <button class="filter-button" data-filter="check">
                <i class="fas fa-money-check"></i> Chèque
            </button>
            <button class="filter-button" data-filter="mobile_money">
                <i class="fas fa-mobile-alt"></i> Mobile Money
            </button>
        </div>
        
        <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" id="paymentSearch" placeholder="Rechercher un paiement..." class="form-control">
        </div>
    </div>

    <!-- Tableau des paiements -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">
                <i class="fas fa-list"></i>
                <span>Liste des Paiements</span>
            </h2>
            <div class="card-actions">
                <button class="export-button" id="exportButton">
                    <i class="fas fa-file-export"></i> Exporter
                </button>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th>Référence</th>
                        <th>Date</th>
                        <th>Méthode</th>
                        <th>Client</th>
                        <th>Montant</th>
                        <th>Caissier</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($payments as $payment)
                        @php
                            // Formatage de la référence du paiement
                            $reference = 'PAY-' . str_pad($payment->id, 6, '0', STR_PAD_LEFT);
                            
                            // Déterminer la classe pour la méthode de paiement
                            $methodClass = '';
                            $methodIcon = '';
                            $methodText = '';
                            
                            switch($payment->payment_method) {
                                case 'cash':
                                    $methodClass = 'cash';
                                    $methodIcon = 'money-bill-wave';
                                    $methodText = 'Espèces';
                                    break;
                                case 'bank_transfer':
                                    $methodClass = 'bank_transfer';
                                    $methodIcon = 'university';
                                    $methodText = 'Virement';
                                    break;
                                case 'check':
                                    $methodClass = 'check';
                                    $methodIcon = 'money-check';
                                    $methodText = 'Chèque';
                                    break;
                                case 'mobile_money':
                                    $methodClass = 'mobile_money';
                                    $methodIcon = 'mobile-alt';
                                    $methodText = 'Mobile Money';
                                    break;
                                default:
                                    $methodClass = 'other';
                                    $methodIcon = 'credit-card';
                                    $methodText = ucfirst($payment->payment_method);
                            }
                        @endphp
                        <tr data-payment-method="{{ $payment->payment_method }}">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="me-2">
                                        <i class="fas fa-receipt text-primary"></i>
                                    </div>
                                    <span class="fw-medium">#{{ $reference }}</span>
                                </div>
                            </td>
                            <td>{{ $payment->payment_date->format('d/m/Y H:i') }}</td>
                            <td>
                                <span class="payment-method-badge {{ $methodClass }}">
                                    <i class="fas fa-{{ $methodIcon }}"></i>
                                    {{ $methodText }}
                                </span>
                            </td>
                            <td>
                                @if($payment->sale)
                                    <div class="d-flex align-items-center">
                                        <div class="me-2">
                                            <i class="fas fa-user text-secondary"></i>
                                        </div>
                                        <span>{{ $payment->sale->customer_name ?? 'Client non défini' }}</span>
                                    </div>
                                @else
                                    <span class="text-muted">N/A</span>
                                @endif
                            </td>
                            <td>
                                <span class="amount-indicator">{{ number_format($payment->amount, 0, ',', ' ') }} FCFA</span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="me-2">
                                        <i class="fas fa-user-tie text-info"></i>
                                    </div>
                                    <span>{{ $payment->cashier->name }}</span>
                                </div>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="{{ route('cashier.payments.show', $payment) }}" class="action-button view" title="Voir les détails">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('cashier.payments.receipt', $payment) }}" class="action-button print" title="Imprimer le reçu">
                                        <i class="fas fa-print"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7">
                                <div class="empty-state">
                                    <i class="fas fa-search"></i>
                                    <h4>Aucun paiement trouvé</h4>
                                    <p>Aucun paiement ne correspond à vos critères de recherche ou aucun paiement n'a encore été enregistré.</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        @if($payments->hasPages())
            <div class="card-footer bg-white d-flex justify-content-between align-items-center">
                <div>
                    <p class="mb-0">
                        Affichage de 
                        <span class="fw-semibold">{{ $payments->firstItem() ?? 0 }}</span>
                        à 
                        <span class="fw-semibold">{{ $payments->lastItem() ?? 0 }}</span>
                        sur
                        <span class="fw-semibold">{{ $payments->total() }}</span>
                        paiements
                    </p>
                </div>
                <div class="pagination-container">
                    {{ $payments->withQueryString()->links() }}
                </div>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Gestion des filtres
        const filterButtons = document.querySelectorAll('.filter-button[data-filter]');
        const tableRows = document.querySelectorAll('.modern-table tbody tr');
        const searchInput = document.getElementById('paymentSearch');
        
        // Fonction pour filtrer les lignes du tableau
        function filterTable() {
            const activeFilter = document.querySelector('.filter-button[data-filter].active').dataset.filter;
            const searchTerm = searchInput.value.toLowerCase();
            
            tableRows.forEach(row => {
                const paymentMethod = row.dataset.paymentMethod;
                const rowContent = row.textContent.toLowerCase();
                
                const methodMatch = activeFilter === 'all' || activeFilter === paymentMethod;
                const searchMatch = rowContent.includes(searchTerm);
                
                row.style.display = methodMatch && searchMatch ? '' : 'none';
            });
            
            // Vérifier si aucun résultat n'est affiché
            checkEmptyResults();
        }
        
        // Vérifier si aucun résultat n'est affiché après filtrage
        function checkEmptyResults() {
            const visibleRows = document.querySelectorAll('.modern-table tbody tr[style=""], .modern-table tbody tr[style="display: "]');
            const emptyMessageRow = document.querySelector('.modern-table tbody .empty-message-row');
            
            if (visibleRows.length === 0 && !emptyMessageRow) {
                const tbody = document.querySelector('.modern-table tbody');
                const newRow = document.createElement('tr');
                newRow.className = 'empty-message-row';
                newRow.innerHTML = `
                    <td colspan="7">
                        <div class="empty-state">
                            <i class="fas fa-filter"></i>
                            <h4>Aucun résultat</h4>
                            <p>Aucun paiement ne correspond à vos critères de filtrage.</p>
                        </div>
                    </td>
                `;
                tbody.appendChild(newRow);
            } else if (visibleRows.length > 0 && emptyMessageRow) {
                emptyMessageRow.remove();
            }
        }
        
        // Gestion des clics sur les boutons de filtre
        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                filterButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                filterTable();
            });
        });
        
        // Gestion de la recherche
        searchInput.addEventListener('input', filterTable);
        
        // Gestion de l'exportation
        document.getElementById('exportButton').addEventListener('click', function() {
            // Simuler une exportation
            const notification = document.createElement('div');
            notification.style.position = 'fixed';
            notification.style.bottom = '20px';
            notification.style.right = '20px';
            notification.style.backgroundColor = '#4CAF50';
            notification.style.color = 'white';
            notification.style.padding = '15px 20px';
            notification.style.borderRadius = '5px';
            notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
            notification.style.zIndex = '9999';
            notification.style.opacity = '0';
            notification.style.transition = 'opacity 0.5s ease';
            notification.innerHTML = '<i class="fas fa-check-circle me-2"></i> Exportation réussie ! Le fichier a été téléchargé.';
            
            document.body.appendChild(notification);
            
            // Afficher puis masquer la notification
            setTimeout(() => { notification.style.opacity = '1'; }, 100);
            setTimeout(() => { notification.style.opacity = '0'; }, 3000);
            setTimeout(() => { document.body.removeChild(notification); }, 3500);
        });
        
        // Animation au survol des lignes
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.classList.add('highlight');
            });
            
            row.addEventListener('mouseleave', function() {
                this.classList.remove('highlight');
            });
        });
    });
</script>
@endpush
