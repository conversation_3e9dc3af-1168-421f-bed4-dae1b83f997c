// Script pour gérer la navigation entre les étapes
$(document).ready(function() {
    console.log('Script de navigation entre les étapes chargé');
    
    // Variables globales
    var currentStep = 1;
    var totalSteps = 3;
    
    // Fonction pour passer à l'étape suivante
    function nextStep() {
        console.log('Passage à l\'étape suivante');
        
        // Vérifier si une carte est sélectionnée à l'étape 1
        if (currentStep === 1) {
            var selectedCard = $('.product-card.selected');
            if (selectedCard.length === 0) {
                alert('Veuillez sélectionner un produit avant de continuer.');
                return false;
            }
        }
        
        // Masquer l'étape actuelle
        $('#step' + currentStep).removeClass('active');
        $('.progress-step:nth-child(' + currentStep + ')').removeClass('active');
        
        // Passer à l'étape suivante
        currentStep++;
        
        // Afficher la nouvelle étape
        $('#step' + currentStep).addClass('active');
        $('.progress-step:nth-child(' + currentStep + ')').addClass('active');
        
        // Afficher/masquer les boutons selon l'étape
        if (currentStep > 1) {
            $('#prevBtn').show();
        }
        
        if (currentStep === totalSteps) {
            $('#nextBtn').hide();
            $('#submitBtn').show();
        }
        
        return true;
    }
    
    // Fonction pour revenir à l'étape précédente
    function prevStep() {
        console.log('Retour à l\'étape précédente');
        
        // Masquer l'étape actuelle
        $('#step' + currentStep).removeClass('active');
        $('.progress-step:nth-child(' + currentStep + ')').removeClass('active');
        
        // Revenir à l'étape précédente
        currentStep--;
        
        // Afficher la nouvelle étape
        $('#step' + currentStep).addClass('active');
        $('.progress-step:nth-child(' + currentStep + ')').addClass('active');
        
        // Masquer le bouton précédent si on est à la première étape
        if (currentStep === 1) {
            $('#prevBtn').hide();
        }
        
        // Afficher le bouton suivant et masquer le bouton de soumission
        $('#nextBtn').show();
        $('#submitBtn').hide();
        
        return true;
    }
    
    // Ajouter les gestionnaires d'événements aux boutons de navigation
    $('#nextBtn').on('click', function(e) {
        e.preventDefault();
        nextStep();
    });
    
    $('#prevBtn').on('click', function(e) {
        e.preventDefault();
        prevStep();
    });
});
