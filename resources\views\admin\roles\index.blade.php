@extends('layouts.admin_minimal')

@section('title', 'Gestion des rôles')

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/apexcharts@3.41.0/dist/apexcharts.min.css" rel="stylesheet">
<style>
.role-card {
    transition: all 0.3s ease;
    border-left: 4px solid;
}

.role-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.permission-badge {
    font-size: 0.8rem;
    padding: 0.3rem 0.5rem;
    margin: 0.2rem;
    border-radius: 1rem;
    display: inline-block;
    transition: all 0.2s ease;
}

.permission-badge:hover {
    transform: scale(1.1);
}

.activity-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.activity-very-active { background-color: #10B981; }
.activity-active { background-color: #3B82F6; }
.activity-moderately-active { background-color: #F59E0B; }
.activity-rarely-used { background-color: #EF4444; }
.activity-inactive { background-color: #6B7280; }

.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.permission-group {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.chart-container {
    height: 300px;
    margin-bottom: 2rem;
}

.role-actions .btn {
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
}

.role-card:hover .role-actions .btn {
    opacity: 1;
    transform: translateX(0);
}

.security-score {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    color: white;
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Gestion des rôles</h1>
        <div>
            <button type="button" class="btn btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#analysisModal">
                <i class="fas fa-chart-line"></i>
                <span class="ms-1">Analyse</span>
            </button>
            <a href="{{ route('admin.roles.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                Nouveau rôle
            </a>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card pulse">
                <h6 class="text-uppercase mb-2">Total des rôles</h6>
                <h2 class="mb-0">{{ $roleStats['total_roles'] }}</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h6 class="text-uppercase mb-2">Utilisateurs assignés</h6>
                <h2 class="mb-0">{{ $roleStats['total_users'] }}</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h6 class="text-uppercase mb-2">Rôle le plus utilisé</h6>
                <h2 class="mb-0">{{ $roleStats['most_used']->name }}</h2>
                <small>{{ $roleStats['most_used']->users_count }} utilisateurs</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h6 class="text-uppercase mb-2">Rôle le moins utilisé</h6>
                <h2 class="mb-0">{{ $roleStats['least_used']->name }}</h2>
                <small>{{ $roleStats['least_used']->users_count }} utilisateurs</small>
            </div>
        </div>
    </div>

    <!-- Graphiques -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Distribution des permissions</h6>
                </div>
                <div class="card-body">
                    <div id="permissionDistributionChart" class="chart-container"></div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Activité des rôles</h6>
                </div>
                <div class="card-body">
                    <div id="roleActivityChart" class="chart-container"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des rôles -->
    <div class="row">
        @foreach($roles as $role)
            <div class="col-md-6 mb-4">
                <div class="card shadow role-card" style="border-left-color: {{ $role->color ?? '#4e73df' }}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h5 class="card-title mb-0">
                                    <span class="activity-indicator activity-{{ $role->activity_level }}"></span>
                                    {{ $role->name }}
                                </h5>
                                <small class="text-muted">
                                    {{ $role->users_count }} utilisateur(s) assigné(s)
                                </small>
                            </div>
                            <div class="security-score" style="background-color: {{ $role->security_color }}">
                                {{ $role->security_score ?? '?' }}
                            </div>
                        </div>

                        <p class="card-text">{{ $role->description }}</p>

                        <div class="permission-preview mb-3">
                            @foreach($role->permissions->take(5) as $permission)
                                <span class="permission-badge bg-info">{{ $permission->name }}</span>
                            @endforeach
                            @if($role->permissions->count() > 5)
                                <span class="permission-badge bg-secondary">
                                    +{{ $role->permissions->count() - 5 }} plus
                                </span>
                            @endif
                        </div>

                        <div class="role-actions">
                            <a href="{{ route('admin.roles.edit', $role) }}" 
                               class="btn btn-primary btn-sm me-2">
                                <i class="fas fa-edit"></i>
                                <span class="ms-1">Modifier</span>
                            </a>
                            <button type="button" 
                                    class="btn btn-info btn-sm me-2"
                                    onclick="analyzeRole({{ $role->id }})">
                                <i class="fas fa-search"></i>
                                <span class="ms-1">Analyser</span>
                            </button>
                            @if($role->name !== 'admin')
                                <form action="{{ route('admin.roles.destroy', $role) }}" 
                                      method="POST" 
                                      class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" 
                                            class="btn btn-danger btn-sm"
                                            onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce rôle ?')">
                                        <i class="fas fa-trash"></i>
                                        <span class="ms-1">Supprimer</span>
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light fixed-bottom">
    <div class="container text-center">
        <span class="text-muted"> {{ date('Y') }} GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>

<!-- Modal d'analyse -->
<div class="modal fade" id="analysisModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Analyse des rôles</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-body">
                                <h6>Distribution des permissions par groupe</h6>
                                <div id="permissionGroupChart" style="height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-body">
                                <h6>Niveaux d'activité</h6>
                                <div id="activityLevelChart" style="height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <h6>Recommandations</h6>
                                <div id="recommendations">
                                    <!-- Les recommandations seront chargées dynamiquement -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/apexcharts@3.41.0/dist/apexcharts.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configuration des graphiques
    const permissionDistribution = new ApexCharts(document.querySelector("#permissionDistributionChart"), {
        series: @json($roleStats['permission_distribution']->pluck('permissions_count')),
        labels: @json($roleStats['permission_distribution']->pluck('name')),
        chart: {
            type: 'donut',
            height: 300
        },
        plotOptions: {
            pie: {
                donut: {
                    size: '70%'
                }
            }
        },
        legend: {
            position: 'bottom'
        }
    });
    permissionDistribution.render();

    // Graphique d'activité des rôles
    const roleActivity = @json($roles->groupBy('activity_level')->map->count());
    const activityChart = new ApexCharts(document.querySelector("#roleActivityChart"), {
        series: Object.values(roleActivity),
        labels: Object.keys(roleActivity).map(key => key.replace('_', ' ').toUpperCase()),
        chart: {
            type: 'polarArea',
            height: 300
        },
        stroke: {
            colors: ['#fff']
        },
        fill: {
            opacity: 0.8
        }
    });
    activityChart.render();

    // Fonction d'analyse de rôle
    window.analyzeRole = function(roleId) {
        fetch(`/admin/roles/${roleId}/analyze`)
            .then(response => response.json())
            .then(data => {
                // Afficher les résultats de l'analyse
                Swal.fire({
                    title: 'Analyse du rôle',
                    html: `
                        <div class="text-start">
                            <p><strong>Score de sécurité:</strong> ${data.security_score}/100</p>
                            <p><strong>Conflits de permissions:</strong> ${data.permission_conflicts.length} détectés</p>
                            <p><strong>Permissions inutilisées:</strong> ${data.unused_permissions.length} trouvées</p>
                            <h6 class="mt-4">Recommandations:</h6>
                            <ul>
                                ${data.suggested_optimizations.map(opt => `<li>${opt}</li>`).join('')}
                            </ul>
                        </div>
                    `,
                    icon: 'info',
                    width: '600px'
                });
            });
    };
});
</script>
@endpush
