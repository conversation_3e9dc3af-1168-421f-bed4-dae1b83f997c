@extends('layouts.cashier')

@section('title', 'Commandes de ciment')

@push('styles')
<style>
    .order-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    .order-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .badge-pending {
        background-color: #fbbf24;
        color: #92400e;
    }
    .badge-validated {
        background-color: #34d399;
        color: #065f46;
    }
    .badge-converted {
        background-color: #60a5fa;
        color: #1e40af;
    }
    .order-details {
        font-size: 0.9rem;
    }
    .order-product {
        padding: 0.5rem;
        margin: 0.5rem 0;
        background-color: #f8fafc;
        border-radius: 0.375rem;
        border: 1px solid #e2e8f0;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- En-tête -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Commandes de ciment</h1>
    </div>

    @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger">
            {{ session('error') }}
        </div>
    @endif

    <div class="row">
        @forelse($orders as $order)
            <div class="col-12 mb-4">
                <div class="card order-card">
                    <div class="card-body">
                        <div class="row">
                            <!-- En-tête -->
                            <div class="col-md-3">
                                <h5 class="card-title mb-1">
                                    <i class="fas fa-file-invoice"></i> 
                                    Commande #{{ $order->reference }}
                                </h5>
                                <div class="text-muted mb-2">
                                    <i class="fas fa-calendar"></i> {{ $order->created_at->format('d/m/Y H:i') }}
                                </div>
                            </div>

                            <!-- Détails des produits -->
                            <div class="col-md-6">
                                <div class="order-details">
                                    @foreach($order->details as $detail)
                                        <div class="order-product">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <strong>
                                                    @if($order->product)
                                                        {{ $order->product->name }}
                                                        <span class="text-muted">({{ $order->product->unit }})</span>
                                                    @else
                                                        Produit non défini
                                                    @endif
                                                </strong>
                                                <div>
                                                    @if($detail->tripAssignments && $detail->tripAssignments->where('status', 'converted_to_sale')->count() === $detail->tripAssignments->count() && $detail->tripAssignments->count() > 0)
                                                        <span class="badge badge-converted">Converti</span>
                                                    @elseif($detail->tripAssignments && $detail->tripAssignments->count() > 0)
                                                        <span class="badge badge-validated">Validé</span>
                                                    @else
                                                        <span class="badge badge-pending">En attente</span>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="d-flex justify-content-between text-muted">
                                                <span>Quantité: {{ number_format($detail->quantity, 2) }} {{ $order->product ? $order->product->unit : '' }}</span>
                                                <span>Prix unitaire: {{ number_format($detail->unit_price, 0, ',', ' ') }} FCFA</span>
                                            </div>
                                            <div class="d-flex justify-content-between text-muted">
                                                <span>Ville: {{ $detail->city ? $detail->city->name : 'Non définie' }}</span>
                                                <span>Total: {{ number_format($detail->unit_price * $detail->quantity, 0, ',', ' ') }} FCFA</span>
                                            </div>
                                            <div class="text-muted">
                                                <div class="mb-2">
                                                    <i class="fas fa-user"></i> Client:
                                                    <strong>{{ $detail->customer ? $detail->customer->name : 'Non défini' }}</strong>
                                                </div>
                                                <div>
                                                    <strong>Affectations:</strong>
                                                    @if($detail->tripAssignments)
                                                        @php
                                                            $convertedCount = $detail->tripAssignments->where('status', 'converted_to_sale')->count();
                                                            $totalCount = $detail->tripAssignments->count();
                                                            $pendingCount = $totalCount - $convertedCount;
                                                        @endphp
                                                        @if($totalCount > 0)
                                                            @if($pendingCount > 0)
                                                                <span class="text-success">{{ $pendingCount }} à convertir</span>
                                                            @endif
                                                            @if($convertedCount > 0)
                                                                @if($pendingCount > 0), @endif
                                                                <span class="text-info">{{ $convertedCount }} convertie(s)</span>
                                                            @endif
                                                        @else
                                                            <span class="text-muted">Aucune affectation</span>
                                                        @endif
                                                    @else
                                                        <span class="text-muted">Aucune affectation</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="col-md-3">
                                <div class="text-end">
                                    <a href="{{ route('cashier.cement-orders.show', $order) }}" 
                                       class="btn btn-primary btn-sm">
                                        <i class="fas fa-exchange-alt"></i> Gérer les conversions
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucune commande trouvée
                </div>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    <div class="d-flex justify-content-center mt-4">
        {{ $orders->withQueryString()->links() }}
    </div>
</div>
@endsection
