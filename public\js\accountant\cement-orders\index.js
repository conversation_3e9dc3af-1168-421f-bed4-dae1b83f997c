// Fonction pour initialiser les graphiques
function initCharts() {
    // Récupérer les statistiques
    fetch('/accountant/cement-orders-stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const monthlyData = data.data.monthly;
                const statusData = data.data.status;

                // Graphique mensuel
                const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
                new Chart(monthlyCtx, {
                    type: 'line',
                    data: {
                        labels: monthlyData.map(item => {
                            const months = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'];
                            return months[item.month - 1];
                        }),
                        datasets: [{
                            label: 'Nombre de commandes',
                            data: monthlyData.map(item => item.total_orders),
                            borderColor: '#4e73df',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });

                // Graphique des statuts
                const statusCtx = document.getElementById('statusChart').getContext('2d');
                new Chart(statusCtx, {
                    type: 'pie',
                    data: {
                        labels: statusData.map(item => {
                            const labels = {
                                'pending': 'En attente',
                                'approved': 'Approuvé',
                                'rejected': 'Rejeté'
                            };
                            return labels[item.status];
                        }),
                        datasets: [{
                            data: statusData.map(item => item.count),
                            backgroundColor: ['#f6c23e', '#1cc88a', '#e74a3b']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }
        })
        .catch(error => {
            console.error('Erreur lors de la récupération des statistiques:', error);
        });
}

// Initialiser les graphiques au chargement de la page
document.addEventListener('DOMContentLoaded', initCharts);
