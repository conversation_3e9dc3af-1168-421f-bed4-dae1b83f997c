@extends('layouts.accountant')

@section('content')
<div class="container-fluid">
    <!-- En-tête de la page -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-primary">
            <i class="fas fa-truck me-2"></i>Ajouter un véhicule
        </h1>
        <a href="{{ route('accountant.trucks.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Retour à la liste
        </a>
    </div>

    <!-- Formulaire d'ajout -->
    <div class="card shadow-lg border-0 rounded-lg mb-4">
        <div class="card-header bg-gradient-primary text-white py-3">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-info-circle me-2"></i>Informations du véhicule
            </h6>
        </div>
        <div class="card-body bg-light">
            @if($errors->any())
                <div class="alert alert-danger border-left-danger shadow-sm">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form action="{{ route('accountant.trucks.store') }}" method="POST">
                @csrf
                <div class="row g-4">
                    <!-- Immatriculation -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="registration_number" class="form-label fw-bold text-primary">
                                <i class="fas fa-fingerprint me-1"></i>Immatriculation
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg @error('registration_number') is-invalid @enderror" 
                                   id="registration_number" 
                                   name="registration_number" 
                                   value="{{ old('registration_number') }}"
                                   placeholder="Entrez l'immatriculation"
                                   required>
                            @error('registration_number')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Marque -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="brand" class="form-label fw-bold text-primary">
                                <i class="fas fa-trademark me-1"></i>Marque
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg @error('brand') is-invalid @enderror" 
                                   id="brand" 
                                   name="brand" 
                                   value="{{ old('brand') }}"
                                   placeholder="Entrez la marque"
                                   required>
                            @error('brand')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Modèle -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="model" class="form-label fw-bold text-primary">
                                <i class="fas fa-truck-moving me-1"></i>Modèle
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg @error('model') is-invalid @enderror" 
                                   id="model" 
                                   name="model" 
                                   value="{{ old('model') }}"
                                   placeholder="Entrez le modèle"
                                   required>
                            @error('model')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Capacité -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="truck_capacity_id" class="form-label fw-bold text-primary">
                                <i class="fas fa-weight me-1"></i>Capacité
                            </label>
                            <select class="form-control form-control-lg @error('truck_capacity_id') is-invalid @enderror" 
                                    id="truck_capacity_id" 
                                    name="truck_capacity_id"
                                    required>
                                <option value="">Sélectionnez une capacité</option>
                                @foreach($capacities as $capacity)
                                    <option value="{{ $capacity->id }}" {{ old('truck_capacity_id') == $capacity->id ? 'selected' : '' }}>
                                        {{ number_format($capacity->capacity, 2) }} tonnes
                                    </option>
                                @endforeach
                            </select>
                            @error('truck_capacity_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Année -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="year" class="form-label fw-bold text-primary">
                                <i class="fas fa-calendar-alt me-1"></i>Année
                            </label>
                            <input type="number" 
                                   class="form-control form-control-lg @error('year') is-invalid @enderror" 
                                   id="year" 
                                   name="year" 
                                   value="{{ old('year') }}"
                                   min="1900"
                                   max="{{ date('Y') + 1 }}"
                                   placeholder="Entrez l'année"
                                   required>
                            @error('year')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Statut -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="status" class="form-label fw-bold text-primary">
                                <i class="fas fa-traffic-light me-1"></i>Statut
                            </label>
                            <select class="form-control form-control-lg @error('status') is-invalid @enderror" 
                                    id="status" 
                                    name="status" 
                                    style="height: 48px;"
                                    required>
                                <option value="">Sélectionner un statut</option>
                                <option value="available" {{ old('status') == 'available' ? 'selected' : '' }}>
                                    <i class="fas fa-check-circle"></i> Disponible
                                </option>
                                <option value="maintenance" {{ old('status') == 'maintenance' ? 'selected' : '' }}>
                                    <i class="fas fa-tools"></i> En maintenance
                                </option>
                                <option value="busy" {{ old('status') == 'busy' ? 'selected' : '' }}>
                                    <i class="fas fa-clock"></i> Occupé
                                </option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="col-12">
                        <div class="form-group">
                            <label for="notes" class="form-label fw-bold text-primary">
                                <i class="fas fa-sticky-note me-1"></i>Notes
                            </label>
                            <textarea class="form-control form-control-lg @error('notes') is-invalid @enderror" 
                                      id="notes" 
                                      name="notes" 
                                      rows="3"
                                      placeholder="Ajoutez des notes supplémentaires ici...">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Boutons -->
                    <div class="col-12 text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-lg px-5 me-3">
                            <i class="fas fa-save me-2"></i>Enregistrer
                        </button>
                        <a href="{{ route('accountant.trucks.index') }}" class="btn btn-outline-secondary btn-lg px-5">
                            <i class="fas fa-times me-2"></i>Annuler
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
