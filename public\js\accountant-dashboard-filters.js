/**
 * Script pour gérer les filtres de période AJAX dans le tableau de bord comptable
 */

document.addEventListener('DOMContentLoaded', function() {
    initPeriodFilters();
});

/**
 * Initialise les filtres de période avec animations et effets interactifs
 */
function initPeriodFilters() {
    const periodFilters = document.querySelectorAll('.period-filter');
    if (!periodFilters.length) return;
    
    console.log('Initialisation des filtres de période');
    
    // Ajouter la classe active au filtre par défaut (Tous)
    const defaultFilter = document.querySelector('.period-filter[data-period="all"]');
    if (defaultFilter) defaultFilter.classList.add('active');
    
    // Ajouter des effets de survol et de clic aux filtres
    periodFilters.forEach(filter => {
        // Effet de survol
        filter.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.classList.add('hover');
            }
        });
        
        filter.addEventListener('mouseleave', function() {
            this.classList.remove('hover');
        });
        
        // Effet de clic et filtrage AJAX
        filter.addEventListener('click', function() {
            // Supprimer la classe active de tous les filtres
            periodFilters.forEach(f => f.classList.remove('active'));
            
            // Ajouter la classe active au filtre cliqué
            this.classList.add('active');
            
            // Ajouter un effet d'onde au clic
            createRippleEffect(this);
            
            // Récupérer la période sélectionnée
            const period = this.getAttribute('data-period');
            
            // Afficher un indicateur de chargement
            const dashboardContent = document.querySelector('.dashboard-content');
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'loading-overlay fadeIn';
            loadingOverlay.innerHTML = `
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <p class="mt-2">Mise à jour des données...</p>
            `;
            dashboardContent.appendChild(loadingOverlay);
            
            // Mettre à jour les données via AJAX
            fetch(`/accountant/dashboard/data?period=${period}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erreur lors de la récupération des données');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Mettre à jour les statistiques
                    updateStatistics(data.statistics);
                    
                    // Mettre à jour les graphiques
                    updateCharts(data.chartData);
                    
                    // Mettre à jour les tableaux
                    updateTables(data.tablesData);
                    
                    // Afficher une notification de succès
                    showNotification(`Données mises à jour pour la période: ${getPeriodLabel(period)}`, 'success');
                } else {
                    showNotification('Erreur lors de la mise à jour des données', 'error');
                }
                
                // Supprimer l'indicateur de chargement avec animation
                loadingOverlay.classList.remove('fadeIn');
                loadingOverlay.classList.add('fadeOut');
                setTimeout(() => {
                    loadingOverlay.remove();
                }, 500);
            })
            .catch(error => {
                console.error('Erreur AJAX:', error);
                showNotification('Erreur lors de la mise à jour des données', 'error');
                
                // Supprimer l'indicateur de chargement en cas d'erreur
                if (document.querySelector('.loading-overlay')) {
                    document.querySelector('.loading-overlay').remove();
                }
            });
        });
    });
}

/**
 * Met à jour les statistiques avec les nouvelles données
 */
function updateStatistics(statistics) {
    console.log('Mise à jour des statistiques:', statistics);
    
    // Mettre à jour les compteurs avec animation
    if (statistics.totalSales !== undefined) {
        updateCounter('totalSales', statistics.totalSales);
    }
    if (statistics.totalRevenue !== undefined) {
        updateCounter('totalRevenue', statistics.totalRevenue);
    }
    if (statistics.totalPayments !== undefined) {
        updateCounter('totalPayments', statistics.totalPayments);
    }
    if (statistics.pendingPayments !== undefined) {
        updateCounter('pendingPayments', statistics.pendingPayments);
    }
    if (statistics.recoveryRate !== undefined) {
        updateCounter('recoveryRate', statistics.recoveryRate);
    }
    
    // Mise à jour des statistiques des bons de ciment
    if (statistics.total_cement_orders !== undefined) {
        const totalOrdersElement = document.querySelector('.total-orders');
        if (totalOrdersElement) {
            totalOrdersElement.textContent = statistics.total_cement_orders;
            totalOrdersElement.setAttribute('data-count', statistics.total_cement_orders);
        }
    }
    
    // Mise à jour des tonnages
    if (statistics.validated_cement_tonnage !== undefined) {
        const validatedTonnageElement = document.querySelector('.validated-tonnage');
        if (validatedTonnageElement) {
            validatedTonnageElement.textContent = statistics.validated_cement_tonnage;
            validatedTonnageElement.setAttribute('data-count', statistics.validated_cement_tonnage);
        }
    }
    
    if (statistics.rejected_cement_tonnage !== undefined) {
        const rejectedTonnageElement = document.querySelector('.rejected-tonnage');
        if (rejectedTonnageElement) {
            rejectedTonnageElement.textContent = statistics.rejected_cement_tonnage;
            rejectedTonnageElement.setAttribute('data-count', statistics.rejected_cement_tonnage);
        }
    }
    
    if (statistics.pending_orders !== undefined) {
        const pendingOrdersElement = document.querySelector('.pending-orders');
        if (pendingOrdersElement) {
            pendingOrdersElement.textContent = statistics.pending_orders;
            pendingOrdersElement.setAttribute('data-count', statistics.pending_orders);
        }
    }
    
    // Ajouter des logs pour le débogage
    console.log('Éléments mis à jour:');
    console.log('Total des bons:', document.querySelector('.total-orders')?.textContent);
    console.log('Tonnage validé:', document.querySelector('.validated-tonnage')?.textContent);
    console.log('Tonnage rejeté:', document.querySelector('.rejected-tonnage')?.textContent);
    console.log('Bons en attente:', document.querySelector('.pending-orders')?.textContent);
}

/**
 * Met à jour un compteur avec animation
 */
function updateCounter(id, newValue) {
    const counterElement = document.querySelector(`[data-counter="${id}"]`);
    if (!counterElement) return;
    
    // Récupérer la valeur actuelle
    const currentValue = parseInt(counterElement.textContent.replace(/[^\d]/g, '')) || 0;
    
    // Animer le compteur
    animateValue(counterElement, currentValue, newValue, 1000);
}

/**
 * Anime la valeur d'un élément de currentVal à newVal pendant la durée spécifiée
 */
function animateValue(element, start, end, duration) {
    let startTimestamp = null;
    const step = (timestamp) => {
        if (!startTimestamp) startTimestamp = timestamp;
        const progress = Math.min((timestamp - startTimestamp) / duration, 1);
        const value = Math.floor(progress * (end - start) + start);
        element.textContent = formatNumber(value);
        if (progress < 1) {
            window.requestAnimationFrame(step);
        } else {
            // Ajouter une classe pour l'animation de fin
            element.classList.add('counter-updated');
            setTimeout(() => {
                element.classList.remove('counter-updated');
            }, 1000);
        }
    };
    window.requestAnimationFrame(step);
}

/**
 * Formate un nombre avec des séparateurs de milliers
 */
function formatNumber(number) {
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ");
}

/**
 * Met à jour les graphiques avec les nouvelles données
 */
function updateCharts(chartData) {
    // Mettre à jour le graphique des ventes mensuelles
    if (chartData.monthlySales && window.ordersChart) {
        window.ordersChart.data.labels = chartData.monthlySales.labels;
        window.ordersChart.data.datasets[0].data = chartData.monthlySales.data;
        window.ordersChart.update();
    }
    
    // Mettre à jour le graphique des statuts de paiement
    if (chartData.paymentStatus && window.paymentStatusChart) {
        window.paymentStatusChart.data.labels = chartData.paymentStatus.labels;
        window.paymentStatusChart.data.datasets[0].data = chartData.paymentStatus.data;
        window.paymentStatusChart.update();
    }
    
    // Mettre à jour le taux de recouvrement
    if (chartData.recoveryRate) {
        updateRecoveryRate(chartData.recoveryRate.rate);
    }
}

/**
 * Met à jour le taux de recouvrement
 */
function updateRecoveryRate(rate) {
    const rateElement = document.querySelector('.recovery-rate-value');
    if (!rateElement) return;
    
    // Animer le taux de recouvrement
    animateValue(rateElement, parseInt(rateElement.textContent) || 0, rate, 1000);
    
    // Mettre à jour la barre de progression
    const progressBar = document.querySelector('.recovery-rate-progress');
    if (progressBar) {
        progressBar.style.width = `${rate}%`;
        
        // Changer la couleur en fonction du taux
        if (rate < 30) {
            progressBar.className = 'recovery-rate-progress bg-danger';
        } else if (rate < 70) {
            progressBar.className = 'recovery-rate-progress bg-warning';
        } else {
            progressBar.className = 'recovery-rate-progress bg-success';
        }
    }
}

/**
 * Met à jour les tableaux avec les nouvelles données
 */
function updateTables(tablesData) {
    // Mettre à jour le tableau des ventes récentes
    if (tablesData.recentSales) {
        updateRecentSalesTable(tablesData.recentSales);
    }
    
    // Mettre à jour le tableau des paiements récents
    if (tablesData.recentPayments) {
        updateRecentPaymentsTable(tablesData.recentPayments);
    }
}

/**
 * Met à jour le tableau des ventes récentes
 */
function updateRecentSalesTable(sales) {
    const tableBody = document.querySelector('#recentSalesTable tbody');
    if (!tableBody) return;
    
    // Vider le tableau
    tableBody.innerHTML = '';
    
    // Ajouter les nouvelles lignes
    if (sales.length === 0) {
        // Afficher un message si aucune vente
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="6" class="text-center">Aucune vente pour cette période</td>`;
        tableBody.appendChild(emptyRow);
    } else {
        sales.forEach(sale => {
            const row = document.createElement('tr');
            
            // Déterminer la classe du badge en fonction du statut de paiement
            let badgeClass = 'badge-secondary';
            if (sale.payment_status === 'paid') badgeClass = 'badge-success';
            else if (sale.payment_status === 'partial') badgeClass = 'badge-warning';
            else if (sale.payment_status === 'unpaid') badgeClass = 'badge-danger';
            
            // Formater la date
            const date = new Date(sale.created_at);
            const formattedDate = `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
            
            row.innerHTML = `
                <td>#${sale.invoice_number || 'SALE-' + sale.id}</td>
                <td>${sale.customer ? sale.customer.name : 'Client inconnu'}</td>
                <td>${sale.total_amount.toLocaleString()} F</td>
                <td><span class="badge ${badgeClass}">${sale.payment_status}</span></td>
                <td>${formattedDate}</td>
                <td>
                    <a href="/accountant/sales/${sale.id}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye"></i>
                    </a>
                </td>
            `;
            
            // Ajouter un effet d'animation à la nouvelle ligne
            row.classList.add('fadeIn');
            row.style.animationDelay = `${0.1}s`;
            
            tableBody.appendChild(row);
        });
    }
}

/**
 * Met à jour le tableau des paiements récents
 */
function updateRecentPaymentsTable(payments) {
    const tableBody = document.querySelector('#recentPaymentsTable tbody');
    if (!tableBody) return;
    
    // Vider le tableau
    tableBody.innerHTML = '';
    
    // Ajouter les nouvelles lignes
    if (payments.length === 0) {
        // Afficher un message si aucun paiement
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="5" class="text-center">Aucun paiement pour cette période</td>`;
        tableBody.appendChild(emptyRow);
    } else {
        payments.forEach(payment => {
            const row = document.createElement('tr');
            
            // Formater la date
            const date = new Date(payment.created_at);
            const formattedDate = `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
            
            row.innerHTML = `
                <td>#${payment.id}</td>
                <td>${payment.sale ? '#' + (payment.sale.invoice_number || 'SALE-' + payment.sale_id) : 'Vente inconnue'}</td>
                <td>${payment.amount.toLocaleString()} F</td>
                <td>${payment.payment_method || 'Non spécifié'}</td>
                <td>${formattedDate}</td>
            `;
            
            // Ajouter un effet d'animation à la nouvelle ligne
            row.classList.add('fadeIn');
            row.style.animationDelay = `${0.1}s`;
            
            tableBody.appendChild(row);
        });
    }
}

/**
 * Retourne le libelé d'une période à partir de son code
 */
function getPeriodLabel(period) {
    const labels = {
        'today': "Aujourd'hui",
        'week': 'Cette semaine',
        'month': 'Ce mois',
        'quarter': 'Ce trimestre',
        'year': 'Cette année',
        'all': 'Toutes les périodes'
    };
    
    return labels[period] || period;
}

/**
 * Crée un effet d'onde au clic sur un élément
 */
function createRippleEffect(element) {
    const ripple = document.createElement('span');
    ripple.classList.add('ripple-effect');
    
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    
    ripple.style.width = ripple.style.height = `${size}px`;
    
    element.addEventListener('click', function(e) {
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.left = `${x}px`;
        ripple.style.top = `${y}px`;
    }, { once: true });
    
    element.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * Affiche une notification
 */
function showNotification(message, type = 'info') {
    // Vérifier si la fonction existe déjà dans le contexte global
    if (window.showNotification && typeof window.showNotification === 'function') {
        window.showNotification(message, type);
        return;
    }
    
    const notification = document.createElement('div');
    notification.className = `notification notification-${type} fadeIn`;
    notification.innerHTML = `
        <div class="notification-icon">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
        </div>
        <div class="notification-content">
            ${message}
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.remove('fadeIn');
        notification.classList.add('fadeOut');
        
        setTimeout(() => {
            notification.remove();
        }, 500);
    }, 3000);
}
