class ProductForm {
    constructor() {
        this.initializeElements();
        this.setupEventListeners();
        this.updateDisplay();
    }

    initializeElements() {
        this.elements = {
            categorySelect: document.getElementById('category_id'),
            regionalPricing: document.getElementById('regional-pricing'),
            ironSpecifications: document.getElementById('iron-specifications'),
            unitSelect: document.getElementById('unit'),
            basePriceInput: document.getElementById('base_price')
        };
    }

    setupEventListeners() {
        if (this.elements.categorySelect) {
            this.elements.categorySelect.addEventListener('change', () => {
                console.log('Changement de catégorie détecté');
                this.updateDisplay();
            });
        }
    }

    getCurrentCategory() {
        if (!this.elements.categorySelect) return null;
        
        const selectedOption = this.elements.categorySelect.options[this.elements.categorySelect.selectedIndex];
        if (!selectedOption || !selectedOption.value) return null;

        const categoryText = selectedOption.textContent.trim().toLowerCase();
        console.log('Catégorie sélectionnée:', categoryText);

        return categoryText;
    }

    updateDisplay() {
        const categoryName = this.getCurrentCategory();
        if (!categoryName) return;

        const isCement = categoryName.includes('ciment');
        const isIron = categoryName.includes('fer');

        console.log('Est ciment:', isCement);
        console.log('Est fer:', isIron);

        // Afficher/masquer les sections
        if (this.elements.regionalPricing) {
            this.elements.regionalPricing.style.display = isCement ? 'block' : 'none';
        }
        if (this.elements.ironSpecifications) {
            this.elements.ironSpecifications.style.display = isIron ? 'block' : 'none';
        }

        // Gérer l'affichage du prix de base
        if (this.elements.basePriceInput) {
            const basePriceContainer = this.elements.basePriceInput.closest('.col-md-4');
            if (basePriceContainer) {
                basePriceContainer.style.display = isIron ? 'none' : 'block';
                this.elements.basePriceInput.required = !isIron;
            }
        }

        // Gérer la validation des champs du fer
        const diameterSelect = document.getElementById('diameter');
        const lengthInput = document.getElementById('length');
        const unitPriceInput = document.getElementById('unit_price');

        if (diameterSelect && lengthInput && unitPriceInput) {
            diameterSelect.required = isIron;
            lengthInput.required = isIron;
            unitPriceInput.required = isIron;
        }
    }
}

// Initialiser quand le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
    console.log('Initialisation du formulaire');
    window.productForm = new ProductForm();
});
