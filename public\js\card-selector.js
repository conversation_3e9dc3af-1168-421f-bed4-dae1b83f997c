// Script simple pour la sélection des cartes de produits
$(document).ready(function() {
    console.log('Script de sélection des cartes chargé');
    
    // Gestionnaire d'événements pour les cartes de produits
    $('.product-card').on('click', function() {
        console.log('Carte cliquée:', $(this).data('supply-id'));
        
        // Retirer la sélection précédente
        $('.product-card').removeClass('selected');
        
        // Ajouter la sélection à la carte cliquée
        $(this).addClass('selected');
        
        // Récupérer l'ID du bon sélectionné
        var supplyId = $(this).data('supply-id');
        $('#hiddenSupplyId').val(supplyId);
        
        // Activer le bouton suivant
        $('#nextBtn').prop('disabled', false);
        
        console.log('Carte sélectionnée avec succès, ID:', supplyId);
    });
});
