/* Styles additionnels pour le tableau de bord comptable moderne */

/* Effet de survol avec classe pour JavaScript */
.card-hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
    z-index: 10 !important;
}

/* Filtres de période */
.period-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.period-filter {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    background-color: rgba(30, 136, 229, 0.1);
    color: #1E88E5;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.period-filter:hover {
    background-color: rgba(30, 136, 229, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.period-filter.active {
    background-color: #1E88E5;
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.period-filter.hover {
    background-color: rgba(30, 136, 229, 0.15);
}

/* Effet d'onde au clic */
.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Indicateur de chargement */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(3px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 100;
    border-radius: 1rem;
}

/* Animation pour les compteurs */
.updating {
    animation: pulse 0.5s ease-in-out;
    color: #1E88E5;
    font-weight: bold;
}

/* Styles pour les notifications améliorées */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-width: 350px;
    display: flex;
    align-items: flex-start;
    transform: translateX(400px);
    transition: transform 0.3s ease, opacity 0.3s ease;
    backdrop-filter: blur(10px);
    border-left: 4px solid transparent;
}

.notification.show {
    transform: translateX(0);
}

.notification.fadeOut {
    opacity: 0;
    transform: translateX(400px);
}

.notification-icon {
    margin-right: 15px;
    font-size: 20px;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.notification-message {
    color: #6c757d;
    font-size: 14px;
}

.notification-close {
    cursor: pointer;
    font-size: 16px;
    margin-left: 10px;
    color: #adb5bd;
    transition: color 0.2s ease;
}

.notification-close:hover {
    color: #495057;
}

.notification-success {
    border-left-color: var(--success-color);
}

.notification-success .notification-icon {
    color: var(--success-color);
}

.notification-warning {
    border-left-color: var(--warning-color);
}

.notification-warning .notification-icon {
    color: var(--warning-color);
}

.notification-error {
    border-left-color: var(--danger-color);
}

.notification-error .notification-icon {
    color: var(--danger-color);
}

.notification-info {
    border-left-color: var(--primary-color);
}

.notification-info .notification-icon {
    color: var(--primary-color);
}

/* Styles pour les tableaux interactifs */
.data-table tbody tr {
    cursor: pointer;
    transition: all 0.2s ease;
}

.data-table tbody tr:hover {
    background-color: rgba(30, 136, 229, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.data-table tbody tr.selected-row {
    background-color: rgba(30, 136, 229, 0.1);
    border-left: 3px solid #1E88E5;
}

/* Styles pour le chargement */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-overlay.fadeOut {
    opacity: 0;
    transition: opacity 0.5s ease;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(30, 136, 229, 0.2);
    border-top: 3px solid #1E88E5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

.loading-text {
    color: #1E88E5;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Styles pour la modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(3px);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
}

.modal-overlay.fadeOut {
    opacity: 0;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    transform: translateY(20px);
    transition: transform 0.3s ease;
}

.modal-overlay.show .modal-content {
    transform: translateY(0);
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(to right, #1E88E5, #0D47A1);
    color: white;
}

.modal-title {
    font-weight: 600;
    margin: 0;
    color: white;
}

.modal-close {
    color: white;
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.modal-close:hover {
    opacity: 1;
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.modal-btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-btn-primary {
    background-color: #1E88E5;
    color: white;
    border: none;
}

.modal-btn-primary:hover {
    background-color: #0D47A1;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.modal-btn-secondary {
    background-color: #f8f9fa;
    color: #495057;
    border: 1px solid #e9ecef;
}

.modal-btn-secondary:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
}

/* Styles pour la légende des graphiques */
.chart-legend {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
}

.legend-item {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    border-radius: 20px;
    background-color: rgba(0, 0, 0, 0.03);
    transition: all 0.2s ease;
    cursor: pointer;
    opacity: 0.8;
}

.legend-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

.legend-item.inactive {
    opacity: 0.4;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.legend-label {
    font-size: 13px;
    color: #495057;
}

/* Curseur pointer pour les éléments interactifs */
.cursor-pointer {
    cursor: pointer;
}

/* Amélioration des filtres de période */
.period-filters {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.period-filter {
    padding: 8px 16px;
    background-color: rgba(30, 136, 229, 0.1);
    color: #1E88E5;
    border-radius: 20px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.period-filter:hover {
    background-color: rgba(30, 136, 229, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.period-filter.active {
    background-color: #1E88E5;
    color: white;
    box-shadow: 0 4px 10px rgba(30, 136, 229, 0.3);
}

/* Responsive - Petits écrans */
@media (max-width: 768px) {
    .period-filters {
        justify-content: flex-start;
        margin-top: 10px;
        overflow-x: auto;
        padding-bottom: 5px;
        -webkit-overflow-scrolling: touch;
    }
    
    .period-filter {
        white-space: nowrap;
        flex-shrink: 0;
    }
    
    .notification {
        max-width: 90%;
        left: 5%;
        right: 5%;
    }
    
    .modal-content {
        width: 95%;
    }
}

/* Animation de pulsation pour les éléments importants */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(30, 136, 229, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(30, 136, 229, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(30, 136, 229, 0);
    }
}

/* Effet de rotation 3D sur les cartes */
.card-3d {
    transition: transform 0.5s ease, box-shadow 0.5s ease;
    transform-style: preserve-3d;
}

.card-3d:hover {
    transform: perspective(1000px) rotateX(5deg) rotateY(5deg) translateZ(10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}
