@extends($layout ?? 'layouts.accountant')

@section('title', 'Bons de commande')

@php
use Illuminate\Support\Facades\Log;
@endphp

@push('styles')
<style>
    .order-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    .order-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .badge-pending {
        background-color: #fbbf24;
        color: #92400e;
    }
    .badge-approved {
        background-color: #34d399;
        color: #065f46;
    }
    .badge-rejected {
        background-color: #f87171;
        color: #991b1b;
    }
    .order-details {
        font-size: 0.9rem;
    }
    .order-product {
        padding: 0.5rem;
        margin: 0.5rem 0;
        background-color: #f8fafc;
        border-radius: 0.375rem;
        border: 1px solid #e2e8f0;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- En-tête -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Bons de commande</h1>
        @if(auth()->user()->hasRole('accountant'))
            <a href="{{ route('accountant.cement-orders.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouveau bon de commande
            </a>
        @endif
    </div>

    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">En attente</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Approuvées</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['approved'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Rejetées</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['rejected'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filtres</h6>
        </div>
        <div class="card-body">
            <form action="{{ $filterRoute }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">Statut</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">Tous les statuts</option>
                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>En attente</option>
                        <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Approuvé</option>
                        <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Rejeté</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_start" class="form-label">Date début</label>
                    <input type="date" class="form-control" id="date_start" name="date_start" value="{{ request('date_start') }}">
                </div>
                <div class="col-md-3">
                    <label for="date_end" class="form-label">Date fin</label>
                    <input type="date" class="form-control" id="date_end" name="date_end" value="{{ request('date_end') }}">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Filtrer
                    </button>
                    <a href="{{ $filterRoute }}" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des bons -->
    <div class="row">
        @forelse($orders as $order)
            <div class="col-12 mb-4">
                <div class="card order-card">
                    <div class="card-body">
                        <div class="row">
                            <!-- En-tête -->
                            <div class="col-md-3">
                                <h5 class="card-title mb-1">
                                    <i class="fas fa-file-invoice"></i> {{ $order->reference }}
                                </h5>
                                <div class="text-muted mb-2">
                                    <i class="fas fa-calendar"></i> 
                                    {{ \Carbon\Carbon::parse($order->created_at)->format('d/m/Y H:i') }}
                                </div>
                                <div class="mb-2">
                                    @if($order->status === 'pending')
                                        <span class="badge badge-pending">En attente</span>
                                    @elseif($order->status === 'approved')
                                        <span class="badge badge-approved">Approuvé</span>
                                    @else
                                        <span class="badge badge-rejected">Rejeté</span>
                                    @endif
                                </div>
                            </div>

                            <!-- Détails des produits -->
                            <div class="col-md-6">
                                <div class="order-details">
                                    @forelse($order->details as $detail)
                                        <div class="order-product">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <strong>{{ optional($detail->product)->name ?? 'N/A' }}</strong>
                                                <span class="text-muted">
                                                    @if($order->customer)
                                                        Client: {{ $order->customer->name }}
                                                    @else
                                                        Client non défini
                                                    @endif
                                                </span>
                                                <span class="badge bg-info">{{ $detail->trips_count ?? 0 }} voyages</span>
                                            </div>
                                            <div class="d-flex justify-content-between text-muted">
                                                <span>{{ optional($detail->truck_capacity)->name ?? 'N/A' }}</span>
                                                <span>{{ number_format(($detail->tonnage ?? 0) * ($detail->trips_count ?? 0), 2) }} T</span>
                                            </div>
                                        </div>
                                    @empty
                                        <div class="alert alert-info">
                                            Aucun détail disponible pour cette commande (ID: {{ $order->id }})
                                        </div>
                                    @endforelse
                                </div>
                            </div>

                            <!-- Totaux et Actions -->
                            <div class="col-md-3">
                                <div class="text-end">
                                    <div class="mb-2">
                                        <strong>Total Tonnage:</strong><br>
                                        {{ number_format($order->total_tonnage ?? 0, 2) }} T
                                    </div>
                                    <div class="mb-2">
                                        <strong>Total Voyages:</strong><br>
                                        {{ $order->total_trips ?? 0 }}
                                    </div>
                                    <div class="mt-3">
                                        @if(auth()->user()->hasRole('accountant'))
                                            <a href="{{ route('accountant.cement-orders.show', $order->id) }}" 
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> Détails
                                            </a>
                                        @else
                                            <a href="{{ route('cement-manager.cement-orders.show', $order->id) }}" 
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> Détails
                                            </a>
                                            @if($order->status === 'pending')
                                                <a href="{{ route('cement-manager.cement-orders.validate-form', $order->id) }}" 
                                                   class="btn btn-sm btn-success">
                                                    <i class="fas fa-check"></i> Valider le bon
                                                </a>
                                            @endif
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Aucun bon de commande trouvé
                </div>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    <div class="d-flex justify-content-center mt-4">
        {{ $orders->withQueryString()->links() }}
    </div>
</div>
@endsection
