@extends('layouts.accountant')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/modern-sales-view.css') }}">
<style>
    .status-badge {
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }
    .status-paid { background-color: #e0f7e6; color: #0c6b26; }
    .status-partial { background-color: #fff5e0; color: #956800; }
    .status-unpaid { background-color: #ffebee; color: #b71c1c; }
    
    .payment-progress {
        height: 10px;
        border-radius: 10px;
        background-color: #f0f0f0;
        overflow: hidden;
        margin: 5px 0;
    }
    
    .payment-progress-bar {
        height: 100%;
        background: linear-gradient(to right, #4cc2ff, #066ee8);
    }
    
    .stats-card {
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        margin-bottom: 25px;
        border-left: 4px solid #066ee8;
        background: white;
        transition: transform 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-3px);
    }
    
    .filter-section {
        background: #f9fbfd;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
</style>
@endpush

@section('title', 'Gestion des Ventes')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>Gestion des Ventes
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Statistiques des ventes -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h6 class="text-uppercase text-muted mb-2">Ventes Totales</h6>
                                <div class="d-flex align-items-center">
                                    <h3 class="mb-0 me-2">{{ $totalSales ?? 0 }}</h3>
                                    <small class="text-muted">factures</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h6 class="text-uppercase text-muted mb-2">Montant Total</h6>
                                <div class="d-flex align-items-center">
                                    <h3 class="mb-0 me-2">{{ number_format($totalAmount ?? 0) }}</h3>
                                    <small class="text-muted">FCFA</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h6 class="text-uppercase text-muted mb-2">Montant Payé</h6>
                                <div class="d-flex align-items-center">
                                    <h3 class="mb-0 me-2">{{ number_format($paidAmount ?? 0) }}</h3>
                                    <small class="text-muted">FCFA</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h6 class="text-uppercase text-muted mb-2">À Recouvrer</h6>
                                <div class="d-flex align-items-center">
                                    <h3 class="mb-0 me-2">{{ number_format($pendingAmount ?? 0) }}</h3>
                                    <small class="text-muted">FCFA</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Filtres -->
                    <div class="filter-section mt-4">
                        <form action="{{ route('accountant.sales.index') }}" method="GET" class="row align-items-end">
                            <div class="col-md-3 mb-3">
                                <label for="payment_status" class="form-label">Statut de paiement</label>
                                <select id="payment_status" name="payment_status" class="form-select">
                                    <option value="">Tous les statuts</option>
                                    <option value="paid" {{ request('payment_status') == 'paid' ? 'selected' : '' }}>Payé</option>
                                    <option value="partial" {{ request('payment_status') == 'partial' ? 'selected' : '' }}>Partiel</option>
                                    <option value="pending" {{ request('payment_status') == 'pending' ? 'selected' : '' }}>En attente</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="start_date" class="form-label">Date début</label>
                                <input type="date" id="start_date" name="start_date" value="{{ request('start_date') }}" class="form-control">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="end_date" class="form-label">Date fin</label>
                                <input type="date" id="end_date" name="end_date" value="{{ request('end_date') }}" class="form-control">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="customer_id" class="form-label">Client</label>
                                <select id="customer_id" name="customer_id" class="form-select">
                                    <option value="">Tous les clients</option>
                                    @foreach($customers as $customer)
                                        <option value="{{ $customer->id }}" {{ request('customer_id') == $customer->id ? 'selected' : '' }}>
                                            {{ $customer->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-12">
                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter me-2"></i>Filtrer
                                    </button>
                                    <a href="{{ route('accountant.sales.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo me-2"></i>Réinitialiser
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Actions -->
                    <div class="action-buttons mb-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Liste des Ventes</h5>
                        </div>
                    </div>

                    <!-- Tableau des ventes -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Référence</th>
                                    <th>Date</th>
                                    <th>Client</th>
                                    <th>Montant Total</th>
                                    <th>Statut</th>
                                    <th>Progression</th>
                                    @if(request('payment_status') !== 'partial')
                                        <th>Actions</th>
                                    @endif
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($sales as $sale)
                                    <tr>
                                        <td>#{{ $sale->id }}</td>
                                        <td>{{ $sale->created_at->format('d/m/Y') }}</td>
                                        <td>{{ $sale->customer->name ?? $sale->customer_name ?? 'N/A' }}</td>
                                        <td>{{ number_format($sale->total_amount) }} FCFA</td>
                                        <td>
                                            @if($sale->payment_status == 'paid')
                                                <span class="status-badge status-paid">Payé</span>
                                            @elseif($sale->payment_status == 'partial')
                                                <span class="status-badge status-partial">Partiel</span>
                                            @else
                                                <span class="status-badge status-unpaid">En attente</span>
                                            @endif
                                        </td>
                                        <td class="col-2">
                                            @php
                                                $paidAmount = $sale->payments->sum('amount') ?? 0;
                                                $percentage = $sale->total_amount > 0 ? round(($paidAmount / $sale->total_amount) * 100) : 0;
                                            @endphp
                                            <div class="payment-progress">
                                                <div class="payment-progress-bar" style="width: {{ $percentage }}%"></div>
                                            </div>
                                            <small class="text-muted">{{ $percentage }}% payé</small>
                                        </td>
                                        @if(request('payment_status') !== 'partial')
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ route('accountant.sales.show', $sale->id) }}" class="btn btn-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('accountant.sales.edit', $sale->id) }}" class="btn btn-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteSaleModal{{ $sale->id }}">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>

                                                <!-- Modal de suppression -->
                                                <div class="modal fade" id="deleteSaleModal{{ $sale->id }}" tabindex="-1" aria-labelledby="deleteSaleModalLabel{{ $sale->id }}" aria-hidden="true">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="deleteSaleModalLabel{{ $sale->id }}">Confirmation de suppression</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                Êtes-vous sûr de vouloir supprimer la vente #{{ $sale->id }} ?
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                                                <form action="{{ route('accountant.sales.destroy', $sale->id) }}" method="POST" class="d-inline">
                                                                    @csrf
                                                                    @method('DELETE')
                                                                    <button type="submit" class="btn btn-danger">Supprimer</button>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        @endif
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="{{ request('payment_status') === 'partial' ? '6' : '7' }}" class="text-center py-4">
                                            <div class="empty-state">
                                                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">Aucune vente trouvée.</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-end mt-3">
                        {{ $sales->appends(request()->input())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Animation pour les cartes de statistiques
        const statCards = document.querySelectorAll('.stats-card');
        statCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 8px 16px rgba(0,0,0,0.1)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.08)';
            });
        });
    });
</script>
@endpush
