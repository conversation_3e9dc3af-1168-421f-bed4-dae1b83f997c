<?php
// Script de débogage pour afficher les données de la table supplies

require_once __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;

// Récupérer les 10 premiers enregistrements de la table supplies
$supplies = DB::table('supplies')
    ->select('id', 'reference', 'created_by', 'status', 'created_at')
    ->orderBy('id', 'desc')
    ->limit(10)
    ->get();

echo "<h1>Données de la table supplies</h1>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>ID</th><th>Référence</th><th>Créé par (ID)</th><th>Statut</th><th>Date de création</th></tr>";

foreach ($supplies as $supply) {
    echo "<tr>";
    echo "<td>{$supply->id}</td>";
    echo "<td>{$supply->reference}</td>";
    echo "<td>{$supply->created_by}</td>";
    echo "<td>{$supply->status}</td>";
    echo "<td>{$supply->created_at}</td>";
    echo "</tr>";
}

echo "</table>";

// Compter les approvisionnements par utilisateur
$suppliesByUser = DB::table('supplies')
    ->select('created_by', DB::raw('count(*) as total'))
    ->groupBy('created_by')
    ->get();

echo "<h2>Nombre d'approvisionnements par utilisateur</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>ID Utilisateur</th><th>Nombre d'approvisionnements</th></tr>";

foreach ($suppliesByUser as $item) {
    echo "<tr>";
    echo "<td>{$item->created_by}</td>";
    echo "<td>{$item->total}</td>";
    echo "</tr>";
}

echo "</table>";

// Récupérer les informations sur les utilisateurs comptables
$accountants = DB::table('users')
    ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
    ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
    ->where('roles.name', 'accountant')
    ->select('users.id', 'users.name', 'users.email')
    ->get();

echo "<h2>Utilisateurs avec le rôle 'accountant'</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>ID</th><th>Nom</th><th>Email</th></tr>";

foreach ($accountants as $accountant) {
    echo "<tr>";
    echo "<td>{$accountant->id}</td>";
    echo "<td>{$accountant->name}</td>";
    echo "<td>{$accountant->email}</td>";
    echo "</tr>";
}

echo "</table>";
