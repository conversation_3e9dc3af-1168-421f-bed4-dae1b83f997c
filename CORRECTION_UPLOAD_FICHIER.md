# Correction du problème d'upload de fichier

## Problème identifié

L'erreur `422 (Unprocessable Content)` avec le message "La facture d'approvisionnement est requise pour le ciment" indiquait que le fichier de facture n'était pas correctement envoyé au serveur.

### Analyse du problème

Dans les logs, on pouvait voir :
```json
{
  "supplier_id":"3",
  "category_id":"1", 
  "product_id":"1",
  "reference":"Bond N000256",
  "date":"2025-07-26",
  "expected_delivery_date":null,
  "notes":"Acquisition de ciments",
  "cities":[...],
  "region_id":"1"
}
```

**Problème** : Aucun champ `invoice_file` n'était présent dans la requête.

### Cause racine

La méthode `handleSubmit()` utilisait :
1. **JSON.stringify()** pour sérialiser les données
2. **Content-Type: application/json** dans les headers
3. Un objet JavaScript simple au lieu de FormData

Cette approche ne permet **pas** d'envoyer des fichiers !

## Solution implémentée

### ✅ **Remplacement de la logique d'envoi**

**Avant** (incorrect pour les fichiers) :
```javascript
const data = {
    supplier_id: formData.get('supplier_id'),
    // ... autres champs
};

const response = await fetch(form.action, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': this.token,
        'Accept': 'application/json'
    },
    body: JSON.stringify(data)
});
```

**Après** (correct pour les fichiers) :
```javascript
const formData = new FormData();
formData.append('supplier_id', document.getElementById('supplier_id').value);
// ... autres champs

// Gestion conditionnelle du fichier
if (invoiceFile && invoiceFile.files[0]) {
    formData.append('invoice_file', invoiceFile.files[0]);
}

const response = await fetch(form.action, {
    method: 'POST',
    headers: {
        'X-CSRF-TOKEN': this.token,
        'Accept': 'application/json'
        // Pas de Content-Type pour FormData !
    },
    body: formData
});
```

### 🔧 **Modifications apportées**

1. **Utilisation de FormData** au lieu d'un objet JavaScript
2. **Suppression du Content-Type** (le navigateur le définit automatiquement pour multipart/form-data)
3. **Ajout conditionnel du fichier** selon la catégorie
4. **Gestion correcte des tableaux** pour les villes avec la syntaxe `cities[0][id]`

### 📋 **Gestion conditionnelle**

```javascript
// Ajouter conditionnellement la date de livraison ou le fichier de facture
const expectedDeliveryDate = document.getElementById('expected_delivery_date');
const invoiceFile = document.getElementById('invoice_file');

if (expectedDeliveryDate && expectedDeliveryDate.value) {
    formData.append('expected_delivery_date', expectedDeliveryDate.value);
}

if (invoiceFile && invoiceFile.files[0]) {
    formData.append('invoice_file', invoiceFile.files[0]);
}
```

## Test de la correction

### Pour tester la catégorie Ciment :
1. Aller sur `http://127.0.0.1:8000/accountant/supplies/create`
2. Sélectionner "Ciment" dans la catégorie
3. Remplir tous les champs requis
4. **Sélectionner un fichier** via le champ "Facture d'approvisionnement"
5. Ajouter au moins une ville
6. Soumettre le formulaire

### Résultat attendu :
- ✅ Le fichier est correctement envoyé
- ✅ La validation passe
- ✅ L'approvisionnement est créé avec succès
- ✅ Le fichier est stocké dans `storage/app/public/invoices/`

### Pour tester les autres catégories :
1. Sélectionner une catégorie autre que "Ciment"
2. Remplir la date de livraison prévue
3. Soumettre le formulaire
4. ✅ Doit fonctionner normalement

## Points techniques importants

### FormData vs JSON
- **FormData** : Requis pour les fichiers, gère automatiquement multipart/form-data
- **JSON** : Ne peut pas transporter de fichiers binaires

### Headers HTTP
- **Avec FormData** : Ne pas définir Content-Type (auto-détection)
- **Avec JSON** : Définir Content-Type: application/json

### Validation côté serveur
La validation conditionnelle fonctionne maintenant correctement :
- **Ciment** : `invoice_file` requis
- **Autres** : `expected_delivery_date` requis

La correction est maintenant complète et fonctionnelle ! 🎉
