/* App Styles */
body {
    font-family: 'Poppins', sans-serif;
    background-color: #f8f9fa;
}

/* Table Styles */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    border-top: none;
}

.table td {
    vertical-align: middle;
}

/* Card Styles */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* Button Styles */
.btn {
    font-weight: 500;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
}

/* Alert Styles */
.alert {
    margin-bottom: 1rem;
}

/* Modal Styles */
.modal-content {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Form Styles */
.form-control {
    border: 1px solid #ced4da;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Badge Styles */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
}

/* Pagination Styles */
.pagination {
    margin-bottom: 0;
}

.page-link {
    padding: 0.375rem 0.75rem;
}

/* Custom Utility Classes */
.gap-2 {
    gap: 0.5rem;
}

.ps-3 {
    padding-left: 1rem;
}

.pe-3 {
    padding-right: 1rem;
}
