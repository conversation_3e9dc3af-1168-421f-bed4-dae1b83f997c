@extends('layouts.admin_minimal')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title m-0">Détails du Véhicule</h3>
                    <a href="{{ route('accountant.trucks.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour à la liste
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-25">Immatriculation</th>
                                    <td class="fw-bold">{{ $truck->registration_number }}</td>
                                </tr>
                                <tr>
                                    <th>Marque</th>
                                    <td>{{ $truck->brand }}</td>
                                </tr>
                                <tr>
                                    <th>Modèle</th>
                                    <td>{{ $truck->model }}</td>
                                </tr>
                                <tr>
                                    <th>Capacité</th>
                                    <td class="fw-bold">
                                        {{ $truck->capacity ? number_format($truck->capacity->capacity, 1) . ' tonnes' : 'N/A' }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Année</th>
                                    <td>{{ $truck->year }}</td>
                                </tr>
                                <tr>
                                    <th>Statut</th>
                                    <td>
                                        @php
                                            $statusClass = match($truck->status) {
                                                'available' => 'success',
                                                'maintenance' => 'warning',
                                                'busy' => 'info',
                                                default => 'secondary'
                                            };
                                            $statusLabel = match($truck->status) {
                                                'available' => 'Disponible',
                                                'maintenance' => 'En maintenance',
                                                'busy' => 'En mission',
                                                default => 'Inconnu'
                                            };
                                        @endphp
                                        <span class="badge bg-{{ $statusClass }}">
                                            {{ $statusLabel }}
                                        </span>
                                    </td>
                                </tr>
                                @if($truck->notes)
                                <tr>
                                    <th>Notes</th>
                                    <td>{{ $truck->notes }}</td>
                                </tr>
                                @endif
                                <tr>
                                    <th>Date de création</th>
                                    <td>{{ $truck->created_at->format('d/m/Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <th>Dernière modification</th>
                                    <td>{{ $truck->updated_at->format('d/m/Y H:i') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light fixed-bottom">
    <div class="container text-center">
        <span class="text-muted"> {{ date('Y') }} GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>
@endsection
