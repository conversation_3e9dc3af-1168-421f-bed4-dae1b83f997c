<?php
// Script pour analyser et corriger les erreurs de syntaxe dans show.blade.php
$filePath = 'c:/xampp/htdocs/gradis/resources/views/cashier/sales/show.blade.php';
$content = file_get_contents($filePath);

// Fonction pour trouver toutes les occurrences d'une chaîne dans un texte
function findAllOccurrences($haystack, $needle) {
    $positions = [];
    $pos = 0;
    while (($pos = strpos($haystack, $needle, $pos)) !== false) {
        $positions[] = $pos;
        $pos += strlen($needle);
    }
    return $positions;
}

// Compter les @if et @endif
$ifCount = substr_count($content, '@if');
$elseifCount = substr_count($content, '@elseif');
$endifCount = substr_count($content, '@endif');

echo "Analyse du fichier:\n";
echo "Nombre de @if: $ifCount\n";
echo "Nombre de @elseif: $elseifCount\n";
echo "Nombre de @endif: $endifCount\n";

// Vérifier si le nombre de @if correspond au nombre de @endif
if ($ifCount > $endifCount) {
    echo "ERREUR: Il manque " . ($ifCount - $endifCount) . " @endif\n";
} elseif ($ifCount < $endifCount) {
    echo "ERREUR: Il y a " . ($endifCount - $ifCount) . " @endif en trop\n";
    
    // Rechercher tous les @endif
    $lines = explode("\n", $content);
    $lineNumbers = [];
    
    foreach ($lines as $i => $line) {
        if (strpos($line, '@endif') !== false) {
            $lineNumbers[] = $i + 1;
        }
    }
    
    echo "Les @endif se trouvent aux lignes: " . implode(', ', $lineNumbers) . "\n";
    
    // Créer une version temporaire du fichier sans le dernier @endif
    $newContent = $content;
    $endifPositions = findAllOccurrences($content, '@endif');
    
    // Supprimer le dernier @endif
    if (!empty($endifPositions)) {
        $lastEndifPos = $endifPositions[count($endifPositions) - 1];
        $newContent = substr_replace($newContent, '', $lastEndifPos, 6);
        file_put_contents($filePath . '.fixed', $newContent);
        echo "Une version corrigée a été créée dans " . $filePath . ".fixed\n";
        echo "Vérifiez cette version avant de remplacer le fichier original.\n";
    }
} else {
    echo "Le nombre de @if et @endif est équilibré.\n";
}

// Rechercher les @if sans @endif correspondant (analyse plus avancée)
$lines = explode("\n", $content);
$stack = [];
$errors = [];

foreach ($lines as $i => $line) {
    $lineNumber = $i + 1;
    
    // Détecter les @if
    if (preg_match('/@if/', $line)) {
        $stack[] = ['type' => 'if', 'line' => $lineNumber];
    }
    // Détecter les @elseif
    else if (preg_match('/@elseif/', $line)) {
        if (empty($stack) || end($stack)['type'] !== 'if') {
            $errors[] = "Ligne $lineNumber: @elseif sans @if correspondant";
        }
    }
    // Détecter les @endif
    else if (preg_match('/@endif/', $line)) {
        if (empty($stack)) {
            $errors[] = "Ligne $lineNumber: @endif sans @if correspondant";
        } else {
            array_pop($stack);
        }
    }
}

if (!empty($stack)) {
    foreach ($stack as $item) {
        $errors[] = "Ligne {$item['line']}: @if sans @endif correspondant";
    }
}

if (!empty($errors)) {
    echo "\nErreurs détectées:\n";
    foreach ($errors as $error) {
        echo "- $error\n";
    }
}

// Rechercher les @endif isolés (qui ne sont pas précédés par un @if, @elseif, @else)
$suspiciousEndifs = [];
for ($i = 0; $i < count($lines); $i++) {
    if (preg_match('/@endif/', $lines[$i])) {
        // Vérifier si cette ligne est isolée (pas associée à une structure if)
        $isolated = true;
        for ($j = $i - 1; $j >= max(0, $i - 20); $j--) {
            if (preg_match('/@if|@elseif|@else/', $lines[$j])) {
                $isolated = false;
                break;
            }
        }
        if ($isolated) {
            $suspiciousEndifs[] = $i + 1;
        }
    }
}

if (!empty($suspiciousEndifs)) {
    echo "\n@endif suspects (potentiellement isolés) aux lignes: " . implode(', ', $suspiciousEndifs) . "\n";
    
    // Créer une version corrigée en supprimant le premier @endif suspect
    $newContent = $content;
    $lines = explode("\n", $newContent);
    $lineToRemove = $suspiciousEndifs[0] - 1;
    $lines[$lineToRemove] = str_replace('@endif', '<!-- @endif supprimé -->', $lines[$lineToRemove]);
    $newContent = implode("\n", $lines);
    
    file_put_contents($filePath, $newContent);
    echo "\nCorrection appliquée: @endif suspect à la ligne {$suspiciousEndifs[0]} a été supprimé.\n";
}
?>
