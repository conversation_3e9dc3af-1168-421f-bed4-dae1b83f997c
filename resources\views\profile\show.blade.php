@extends('layouts.admin_minimal')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-white py-3">
                    <h1 class="h3 mb-0">Mon Profil</h1>
                </div>

                <div class="card-body">
                    <div class="text-center mb-4">
                        @if($user->avatar)
                            <img src="{{ Storage::url($user->avatar) }}" 
                                 alt="Avatar" 
                                 class="rounded-circle mb-3"
                                 width="150" 
                                 height="150"
                                 style="object-fit: cover;">
                        @else
                            <div class="avatar-placeholder mb-3">
                                <i class="fas fa-user-circle"></i>
                            </div>
                        @endif
                        <h2 class="h4 mb-0">{{ $user->name }}</h2>
                        <p class="text-muted">{{ $user->roles->pluck('name')->implode(', ') }}</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h3 class="h5 mb-2">Informations personnelles</h3>
                            <dl class="row">
                                <dt class="col-sm-4">Nom</dt>
                                <dd class="col-sm-8">{{ $user->name }}</dd>

                                <dt class="col-sm-4">Email</dt>
                                <dd class="col-sm-8">{{ $user->email }}</dd>

                                <dt class="col-sm-4">Téléphone</dt>
                                <dd class="col-sm-8">{{ $user->phone ?? 'Non renseigné' }}</dd>
                            </dl>
                        </div>

                        <div class="col-md-6 mb-3">
                            <h3 class="h5 mb-2">Informations du compte</h3>
                            <dl class="row">
                                <dt class="col-sm-4">Statut</dt>
                                <dd class="col-sm-8">
                                    @if($user->is_active)
                                        <span class="badge bg-success">Actif</span>
                                    @else
                                        <span class="badge bg-danger">Inactif</span>
                                    @endif
                                </dd>

                                <dt class="col-sm-4">Membre depuis</dt>
                                <dd class="col-sm-8">{{ $user->created_at->format('d/m/Y') }}</dd>

                                <dt class="col-sm-4">Dernière mise à jour</dt>
                                <dd class="col-sm-8">{{ $user->updated_at->format('d/m/Y H:i') }}</dd>
                            </dl>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <a href="{{ route('profile.edit') }}" class="btn btn-primary me-2">
                            <i class="fas fa-edit me-1"></i>Modifier le profil
                        </a>
                        <a href="{{ route('profile.password') }}" class="btn btn-secondary">
                            <i class="fas fa-key me-1"></i>Changer le mot de passe
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-placeholder {
    width: 150px;
    height: 150px;
    background-color: #e9ecef;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: #adb5bd;
}

dl {
    margin-bottom: 0;
}

dt {
    font-weight: 600;
    color: #495057;
}

dd {
    margin-bottom: .5rem;
}
</style>
@endsection
