:root {
    --primary-color: #2563eb;
    --secondary-color: #475569;
    --success-color: #16a34a;
    --warning-color: #ca8a04;
    --danger-color: #dc2626;
    --light-color: #f1f5f9;
    --dark-color: #0f172a;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: #f8fafc;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 250px;
    background: var(--dark-color);
    color: white;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 4px 0 10px rgba(0,0,0,0.1);
    overflow-y: auto;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-header img {
    height: 50px;
    width: auto;
}

.nav-item {
    position: relative;
}

.nav-heading {
    padding: 1rem 1.5rem 0.5rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: rgba(255,255,255,0.4);
}

.nav-link {
    padding: 12px 20px;
    color: rgba(255,255,255,0.8) !important;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-link:hover {
    color: white !important;
    background: rgba(255,255,255,0.1);
}

.nav-link.active {
    color: white !important;
    background: rgba(255,255,255,0.2);
}

.nav-link i {
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    margin-left: 250px;
    min-height: 100vh;
    transition: all 0.3s ease;
    padding: 1rem;
    margin-top: 60px;
}

.main-content.expanded {
    margin-left: 70px;
}

/* Top Navbar */
.top-navbar {
    position: fixed;
    top: 0;
    right: 0;
    left: 250px;
    height: 60px;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1030;
    padding: 0 1.5rem;
    transition: all 0.3s ease;
}

.navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

#sidebarToggle {
    background: none;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
    padding: 0.5rem;
    font-size: 1.25rem;
}

#sidebarToggle:hover {
    color: var(--primary-color);
}

.profile-menu {
    position: relative;
}

.profile-toggle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    cursor: pointer;
    border-radius: 0.5rem;
}

.profile-image {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.profile-info {
    display: none;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.7rem;
}

/* Cards et tables */
.card {
    border: none;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    margin-bottom: 1rem;
}

.card-header {
    background-color: white;
    border-bottom: 1px solid rgba(0,0,0,0.125);
    padding: 1rem 1.25rem;
}

.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    background-color: var(--light-color);
    color: var(--secondary-color);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.05em;
}

.table td {
    vertical-align: middle;
}

.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 70px;
    }

    .sidebar.expanded {
        width: 250px;
    }

    .main-content {
        margin-left: 70px;
    }

    .top-navbar {
        left: 70px;
    }

    .profile-info {
        display: none;
    }

    .table-responsive {
        margin: 0;
    }
}
