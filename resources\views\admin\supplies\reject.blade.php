@extends('layouts.minimal')

@section('title', 'Rejet de l\'approvisionnement')

@section('content')
<div class="container mt-4">
    <div class="card">
        <div class="card-header">
            <h4>Rejet de l'approvisionnement #{{ $supply->reference }}</h4>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                Veuillez indiquer la raison du rejet de cet approvisionnement.
            </div>
            
            <dl class="row">
                <dt class="col-sm-3">Référence</dt>
                <dd class="col-sm-9">{{ $supply->reference }}</dd>
                
                <dt class="col-sm-3">Fournisseur</dt>
                <dd class="col-sm-9">{{ $supply->supplier->name }}</dd>
                
                <dt class="col-sm-3">Montant</dt>
                <dd class="col-sm-9">{{ number_format($supply->total_amount, 0, ',', ' ') }} FCFA</dd>
                
                <dt class="col-sm-3">Date</dt>
                <dd class="col-sm-9">{{ $supply->created_at->format('d/m/Y H:i') }}</dd>
            </dl>

            <div class="table-responsive mb-4">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Produit</th>
                            <th>Quantité</th>
                            <th>Prix unitaire</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($supply->items as $item)
                            <tr>
                                <td>{{ $item->product->name }}</td>
                                <td>{{ $item->quantity }}</td>
                                <td>{{ number_format($item->unit_price, 0, ',', ' ') }} FCFA</td>
                                <td>{{ number_format($item->quantity * $item->unit_price, 0, ',', ' ') }} FCFA</td>
                            </tr>
                        @endforeach
                    </tbody>
                    <tfoot>
                        <tr>
                            <th colspan="3" class="text-end">Total</th>
                            <th>{{ number_format($supply->total_amount, 0, ',', ' ') }} FCFA</th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <form action="{{ route('admin.supplies.rejectSupply', $supply) }}" method="POST">
                @csrf
                <div class="form-group mb-3">
                    <label for="rejection_reason" class="form-label">Raison du rejet</label>
                    <textarea name="rejection_reason" 
                              id="rejection_reason" 
                              class="form-control @error('rejection_reason') is-invalid @enderror" 
                              rows="3" 
                              required>{{ old('rejection_reason') }}</textarea>
                    @error('rejection_reason')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.supplies.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times"></i> Rejeter l'approvisionnement
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
