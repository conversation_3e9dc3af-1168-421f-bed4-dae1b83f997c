/**
 * GRADIS - Styles modernes pour les rapports comptables
 * Design system avancé avec glassmorphism et animations fluides
 * 2025 MOMK-Solutions
 */

/* Variables CSS globales */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #48bb78 0%, #38b2ac 100%);
    --warning-gradient: linear-gradient(135deg, #ed8936 0%, #f6ad55 100%);
    --danger-gradient: linear-gradient(135deg, #f56565 0%, #fc8181 100%);
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
    --shadow-medium: 0 15px 35px rgba(31, 38, 135, 0.2);
    --shadow-heavy: 0 25px 50px rgba(31, 38, 135, 0.15);
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;
    --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-medium: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Effets glassmorphism */
.glass-effect {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-light);
}

.glass-effect-strong {
    background: rgba(255, 255, 255, 0.35);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.25);
    box-shadow: var(--shadow-medium);
}

/* Animations personnalisées */
@keyframes float-gentle {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.3); }
    50% { box-shadow: 0 0 30px rgba(102, 126, 234, 0.6); }
}

@keyframes slide-in-bottom {
    0% {
        transform: translateY(100px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes scale-in {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Classes d'animation */
.animate-float {
    animation: float-gentle 6s ease-in-out infinite;
}

.animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
}

.animate-slide-in-bottom {
    animation: slide-in-bottom 0.6s ease-out;
}

.animate-scale-in {
    animation: scale-in 0.5s ease-out;
}

.animate-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

/* Boutons modernes avec effets */
.btn-modern {
    position: relative;
    overflow: hidden;
    border: none;
    border-radius: var(--border-radius-lg);
    padding: 12px 24px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition-medium);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-modern:active {
    transform: translateY(0);
}

.btn-primary-modern {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-success-modern {
    background: var(--success-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(72, 187, 120, 0.4);
}

.btn-warning-modern {
    background: var(--warning-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(237, 137, 54, 0.4);
}

.btn-danger-modern {
    background: var(--danger-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(245, 101, 101, 0.4);
}

/* Cartes avec effets avancés */
.card-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
    transition: var(--transition-medium);
    overflow: hidden;
    position: relative;
}

.card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--primary-gradient);
}

.card-modern:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-heavy);
}

.card-modern .card-header {
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px;
}

.card-modern .card-body {
    padding: 20px;
}

/* Indicateurs de statut modernes */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: var(--border-radius-lg);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.status-indicator::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.status-success {
    background: rgba(72, 187, 120, 0.15);
    color: #2f855a;
    border: 1px solid rgba(72, 187, 120, 0.3);
}

.status-warning {
    background: rgba(237, 137, 54, 0.15);
    color: #c05621;
    border: 1px solid rgba(237, 137, 54, 0.3);
}

.status-danger {
    background: rgba(245, 101, 101, 0.15);
    color: #c53030;
    border: 1px solid rgba(245, 101, 101, 0.3);
}

.status-info {
    background: rgba(102, 126, 234, 0.15);
    color: #5a67d8;
    border: 1px solid rgba(102, 126, 234, 0.3);
}

/* Tableaux modernes */
.table-modern {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.table-modern thead th {
    background: rgba(102, 126, 234, 0.1);
    border: none;
    padding: 16px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.8rem;
    color: #4a5568;
}

.table-modern tbody td {
    border: none;
    padding: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition-fast);
}

.table-modern tbody tr:hover td {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.table-modern tbody tr:last-child td {
    border-bottom: none;
}

/* Formulaires modernes */
.form-modern .form-control {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    padding: 12px 16px;
    transition: var(--transition-fast);
    color: #2d3748;
}

.form-modern .form-control:focus {
    background: rgba(255, 255, 255, 0.4);
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.form-modern .form-label {
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 8px;
}

/* Modals modernes */
.modal-modern .modal-content {
    background: var(--glass-bg);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-heavy);
}

.modal-modern .modal-header {
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
}

.modal-modern .modal-body {
    padding: 30px;
}

/* Tooltips personnalisés */
.tooltip-modern {
    position: relative;
    cursor: help;
}

.tooltip-modern::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: var(--border-radius-md);
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: var(--transition-fast);
    z-index: 1000;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.tooltip-modern:hover::after {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
}

/* Responsive design */
@media (max-width: 768px) {
    .card-modern {
        margin: 10px;
        border-radius: var(--border-radius-md);
    }
    
    .btn-modern {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .table-modern thead th,
    .table-modern tbody td {
        padding: 12px 8px;
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .card-modern .card-header,
    .card-modern .card-body {
        padding: 15px;
    }
    
    .btn-modern {
        padding: 8px 16px;
        font-size: 0.8rem;
    }
}
