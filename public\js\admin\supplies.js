// Fonction pour gérer les erreurs AJAX
function handleAjaxError(error) {
    console.error('Erreur AJAX:', error);
    Swal.fire({
        icon: 'error',
        title: 'Erreur !',
        text: 'Une erreur est survenue lors de la communication avec le serveur.'
    });
}

// Attendre que le DOM soit chargé
document.addEventListener('DOMContentLoaded', function() {
    // S'assurer que SweetAlert2 est disponible
    if (typeof Swal === 'undefined') {
        console.error('SweetAlert2 n\'est pas chargé');
        return;
    }

    // Gestion de la validation
    document.querySelectorAll('.validate-supply').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const url = this.dataset.validateUrl;
            const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            Swal.fire({
                title: 'Êtes-vous sûr ?',
                text: "Voulez-vous vraiment valider cet approvisionnement ?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Oui, valider !',
                cancelButtonText: 'Annuler'
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;
                    
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_token';
                    csrfInput.value = token;
                    form.appendChild(csrfInput);
                    
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });

    // Gestion du rejet
    document.querySelectorAll('.reject-supply').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const url = this.dataset.rejectUrl;
            const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            Swal.fire({
                title: 'Rejet de l\'approvisionnement',
                text: "Veuillez indiquer la raison du rejet",
                input: 'textarea',
                inputLabel: 'Raison du rejet',
                inputPlaceholder: 'Saisissez la raison du rejet...',
                inputAttributes: {
                    'aria-label': 'Raison du rejet'
                },
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Rejeter',
                cancelButtonText: 'Annuler',
                inputValidator: (value) => {
                    if (!value) {
                        return 'Vous devez saisir une raison pour le rejet!';
                    }
                    return null;
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = url;

                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_token';
                    csrfInput.value = token;
                    form.appendChild(csrfInput);

                    const reasonInput = document.createElement('input');
                    reasonInput.type = 'hidden';
                    reasonInput.name = 'rejection_reason';
                    reasonInput.value = result.value;
                    form.appendChild(reasonInput);

                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
});
