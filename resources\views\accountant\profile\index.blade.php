@extends('layouts.accountant')

@section('title', 'Mon profil')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-xl-4">
            <!-- Carte profil -->
            <div class="card">
                <div class="card-body">
                    <div class="text-center">
                        <div class="dropdown float-end">
                            <a class="text-body dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i data-feather="more-horizontal"></i>
                            </a>
                            <div class="dropdown-menu dropdown-menu-end">
                                <a class="dropdown-item" href="{{ route('accountant.profile.edit') }}">
                                    <i data-feather="edit-2" class="icon-dual icon-xs me-2"></i> Modifier
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item text-danger" href="#" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                    <i data-feather="log-out" class="icon-dual icon-xs me-2"></i> Déconnexion
                                </a>
                            </div>
                        </div>
                        <div>
                            <img src="{{ auth()->user()->avatar_url ?? asset('assets/images/users/default-avatar.jpg') }}" 
                                 class="avatar-lg rounded-circle img-thumbnail" 
                                 alt="photo de profil">
                        </div>
                        <div class="mt-3">
                            <h4 class="mb-1">{{ auth()->user()->name }}</h4>
                            <p class="text-muted mb-2">Comptable</p>
                        </div>
                    </div>

                    <div class="mt-3 pt-2 border-top">
                        <h4 class="mb-3 fs-15">Informations de contact</h4>
                        <div class="table-responsive">
                            <table class="table table-borderless mb-0 text-muted">
                                <tbody>
                                    <tr>
                                        <th scope="row">Email</th>
                                        <td>{{ auth()->user()->email }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Téléphone</th>
                                        <td>{{ auth()->user()->phone ?? 'Non renseigné' }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Adresse</th>
                                        <td>{{ auth()->user()->address ?? 'Non renseignée' }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Ville</th>
                                        <td>{{ auth()->user()->city ?? 'Non renseignée' }}</td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Pays</th>
                                        <td>{{ auth()->user()->country ?? 'Non renseigné' }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- fin carte profil -->
        </div>

        <div class="col-xl-8">
            <!-- Statistiques -->
            <div class="row">
                <div class="col-sm-4">
                    <div class="card tilebox-one">
                        <div class="card-body">
                            <i data-feather="file-text" class="icon-dual"></i>
                            <h6 class="text-uppercase mt-0">Factures traitées</h6>
                            <h2 class="my-2">{{ auth()->user()->invoices_count ?? 0 }}</h2>
                            <p class="mb-0 text-muted">
                                <span class="text-success me-2">
                                    <i data-feather="trending-up" class="icon-xs"></i> +7.8%
                                </span>
                                <span class="text-nowrap">Depuis le mois dernier</span>
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-sm-4">
                    <div class="card tilebox-one">
                        <div class="card-body">
                            <i data-feather="credit-card" class="icon-dual"></i>
                            <h6 class="text-uppercase mt-0">Paiements traités</h6>
                            <h2 class="my-2">{{ auth()->user()->payments_count ?? 0 }}</h2>
                            <p class="mb-0 text-muted">
                                <span class="text-success me-2">
                                    <i data-feather="trending-up" class="icon-xs"></i> +5.4%
                                </span>
                                <span class="text-nowrap">Depuis le mois dernier</span>
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-sm-4">
                    <div class="card tilebox-one">
                        <div class="card-body">
                            <i data-feather="check-circle" class="icon-dual"></i>
                            <h6 class="text-uppercase mt-0">Tâches complétées</h6>
                            <h2 class="my-2">{{ auth()->user()->tasks_count ?? 0 }}</h2>
                            <p class="mb-0 text-muted">
                                <span class="text-success me-2">
                                    <i data-feather="trending-up" class="icon-xs"></i> +12.5%
                                </span>
                                <span class="text-nowrap">Depuis le mois dernier</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activités récentes -->
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Activités récentes</h4>

                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Action</th>
                                    <th>Détails</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse(auth()->user()->activities()->latest()->take(5)->get() ?? [] as $activity)
                                    <tr>
                                        <td>{{ $activity->created_at->format('d/m/Y H:i') }}</td>
                                        <td>{{ $activity->action }}</td>
                                        <td>{{ $activity->details }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="3" class="text-center">Aucune activité récente</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Formulaire de déconnexion -->
<form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
    @csrf
</form>

@push('scripts')
<script>
    // Initialiser les icônes Feather
    feather.replace();
</script>
@endpush
