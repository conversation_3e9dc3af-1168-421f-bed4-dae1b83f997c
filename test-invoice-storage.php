<?php
/**
 * Script de test pour vérifier le stockage des factures de ciment
 * dans le dossier public/uploads/avatars/documents
 */

echo "<h2>Test du stockage des factures de ciment</h2>";

// Vérifier que le dossier existe
$documentsPath = __DIR__ . '/public/uploads/avatars/documents';
echo "<p><strong>Chemin du dossier :</strong> " . $documentsPath . "</p>";

if (file_exists($documentsPath)) {
    echo "<p style='color: green;'>✓ Le dossier existe</p>";
    
    // Lister les fichiers dans le dossier
    $files = scandir($documentsPath);
    $files = array_filter($files, function($file) {
        return !in_array($file, ['.', '..']);
    });
    
    echo "<p><strong>Fichiers dans le dossier :</strong></p>";
    if (empty($files)) {
        echo "<p style='color: orange;'>Aucun fichier trouvé</p>";
    } else {
        echo "<ul>";
        foreach ($files as $file) {
            $filePath = $documentsPath . '/' . $file;
            $fileSize = filesize($filePath);
            $fileDate = date('Y-m-d H:i:s', filemtime($filePath));
            echo "<li><strong>$file</strong> - " . number_format($fileSize / 1024, 2) . " KB - $fileDate</li>";
        }
        echo "</ul>";
    }
    
    // Vérifier les permissions
    $permissions = substr(sprintf('%o', fileperms($documentsPath)), -4);
    echo "<p><strong>Permissions du dossier :</strong> $permissions</p>";
    
    if (is_writable($documentsPath)) {
        echo "<p style='color: green;'>✓ Le dossier est accessible en écriture</p>";
    } else {
        echo "<p style='color: red;'>✗ Le dossier n'est pas accessible en écriture</p>";
    }
    
} else {
    echo "<p style='color: red;'>✗ Le dossier n'existe pas</p>";
    echo "<p>Tentative de création du dossier...</p>";
    
    if (mkdir($documentsPath, 0755, true)) {
        echo "<p style='color: green;'>✓ Dossier créé avec succès</p>";
    } else {
        echo "<p style='color: red;'>✗ Impossible de créer le dossier</p>";
    }
}

// Vérifier l'accès web au dossier
$webPath = '/uploads/avatars/documents/';
echo "<p><strong>Chemin web :</strong> " . $webPath . "</p>";

// Afficher les informations sur le serveur web
echo "<h3>Informations du serveur</h3>";
echo "<p><strong>Document Root :</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Script Name :</strong> " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p><strong>Server Software :</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";

echo "<h3>Instructions</h3>";
echo "<p>1. Allez sur la page de création d'approvisionnement : <a href='/accountant/supplies/create' target='_blank'>Créer un approvisionnement</a></p>";
echo "<p>2. Sélectionnez la catégorie 'Ciment'</p>";
echo "<p>3. Remplissez le formulaire et uploadez une facture</p>";
echo "<p>4. Vérifiez que le fichier apparaît dans ce dossier</p>";
echo "<p>5. Consultez l'approvisionnement créé pour vérifier l'affichage de la facture</p>";
?>
