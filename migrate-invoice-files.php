<?php
/**
 * Script de migration des fichiers de factures existants
 * De storage/app/public/invoices vers public/uploads/avatars/documents
 */

require_once __DIR__ . '/vendor/autoload.php';

// Charger l'application Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Supply;
use Illuminate\Support\Facades\DB;

echo "<h2>Migration des fichiers de factures</h2>";

// Chemins
$oldPath = storage_path('app/public/invoices');
$newPath = public_path('uploads/avatars/documents');

echo "<p><strong>Ancien dossier :</strong> $oldPath</p>";
echo "<p><strong>Nouveau dossier :</strong> $newPath</p>";

// Créer le nouveau dossier s'il n'existe pas
if (!file_exists($newPath)) {
    if (mkdir($newPath, 0755, true)) {
        echo "<p style='color: green;'>✓ Nouveau dossier créé</p>";
    } else {
        echo "<p style='color: red;'>✗ Impossible de créer le nouveau dossier</p>";
        exit;
    }
} else {
    echo "<p style='color: green;'>✓ Le nouveau dossier existe déjà</p>";
}

// Récupérer tous les approvisionnements avec des fichiers de factures
$supplies = Supply::whereNotNull('invoice_file')->get();

echo "<p><strong>Approvisionnements avec factures trouvés :</strong> " . $supplies->count() . "</p>";

if ($supplies->count() === 0) {
    echo "<p style='color: orange;'>Aucun approvisionnement avec facture trouvé.</p>";
    exit;
}

$migrated = 0;
$errors = 0;

echo "<h3>Détails de la migration :</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Référence</th><th>Ancien chemin</th><th>Nouveau chemin</th><th>Statut</th></tr>";

foreach ($supplies as $supply) {
    $oldFilePath = $oldPath . '/' . basename($supply->invoice_file);
    $newFilePath = $newPath . '/' . basename($supply->invoice_file);
    $newDbPath = 'uploads/avatars/documents/' . basename($supply->invoice_file);
    
    echo "<tr>";
    echo "<td>{$supply->id}</td>";
    echo "<td>{$supply->reference}</td>";
    echo "<td>" . basename($supply->invoice_file) . "</td>";
    echo "<td>$newDbPath</td>";
    
    // Vérifier si l'ancien fichier existe
    if (file_exists($oldFilePath)) {
        // Copier le fichier vers le nouveau dossier
        if (copy($oldFilePath, $newFilePath)) {
            // Mettre à jour le chemin dans la base de données
            try {
                $supply->update(['invoice_file' => $newDbPath]);
                echo "<td style='color: green;'>✓ Migré</td>";
                $migrated++;
            } catch (Exception $e) {
                echo "<td style='color: red;'>✗ Erreur DB: " . $e->getMessage() . "</td>";
                $errors++;
            }
        } else {
            echo "<td style='color: red;'>✗ Erreur copie</td>";
            $errors++;
        }
    } else {
        echo "<td style='color: orange;'>⚠ Fichier introuvable</td>";
        $errors++;
    }
    
    echo "</tr>";
}

echo "</table>";

echo "<h3>Résumé de la migration :</h3>";
echo "<p><strong>Fichiers migrés avec succès :</strong> $migrated</p>";
echo "<p><strong>Erreurs :</strong> $errors</p>";

if ($migrated > 0) {
    echo "<p style='color: green;'>✓ Migration terminée avec succès !</p>";
    echo "<p><strong>Actions recommandées :</strong></p>";
    echo "<ul>";
    echo "<li>Vérifiez que les factures s'affichent correctement dans l'interface</li>";
    echo "<li>Testez le téléchargement des factures</li>";
    echo "<li>Une fois confirmé, vous pouvez supprimer les anciens fichiers dans storage/app/public/invoices</li>";
    echo "</ul>";
} else {
    echo "<p style='color: orange;'>Aucun fichier n'a été migré.</p>";
}

// Afficher les fichiers dans les deux dossiers pour comparaison
echo "<h3>Contenu des dossiers :</h3>";

echo "<h4>Ancien dossier (storage/app/public/invoices) :</h4>";
if (file_exists($oldPath)) {
    $oldFiles = array_diff(scandir($oldPath), ['.', '..']);
    if (empty($oldFiles)) {
        echo "<p>Aucun fichier</p>";
    } else {
        echo "<ul>";
        foreach ($oldFiles as $file) {
            echo "<li>$file</li>";
        }
        echo "</ul>";
    }
} else {
    echo "<p>Dossier inexistant</p>";
}

echo "<h4>Nouveau dossier (public/uploads/avatars/documents) :</h4>";
$newFiles = array_diff(scandir($newPath), ['.', '..']);
if (empty($newFiles)) {
    echo "<p>Aucun fichier</p>";
} else {
    echo "<ul>";
    foreach ($newFiles as $file) {
        echo "<li>$file</li>";
    }
    echo "</ul>";
}
?>
