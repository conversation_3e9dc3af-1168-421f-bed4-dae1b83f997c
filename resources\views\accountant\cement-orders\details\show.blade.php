@extends('layouts.accountant')

@section('content')
<div class="container-fluid">
    <!-- En-tête de la page -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Détails de la commande #{{ $detail->id }}</h1>
        <a href="{{ route('accountant.cement-orders.show', $detail->cement_order_id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Retour
        </a>
    </div>

    <div class="row">
        <!-- Informations générales -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Tonnage Total
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $detail->formatted_total_tonnage }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-weight-hanging fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Prix Unitaire
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $detail->formatted_unit_price }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Montant Total
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $detail->formatted_total_amount }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Détails de la commande -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informations de la commande</h6>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th>Destination</th>
                            <td>{{ optional($detail->destination)->name }}</td>
                        </tr>
                        <tr>
                            <th>Client</th>
                            <td>{{ optional($detail->customer)->name }}</td>
                        </tr>
                        <tr>
                            <th>Nombre de voyages</th>
                            <td>{{ $detail->trips_count }}</td>
                        </tr>
                        <tr>
                            <th>Tonnage par voyage</th>
                            <td>{{ $detail->tonnage_per_trip }} T</td>
                        </tr>
                        <tr>
                            <th>Quantité restante</th>
                            <td>{{ $detail->formatted_remaining_quantity }}</td>
                        </tr>
                        <tr>
                            <th>Statut</th>
                            <td>
                                <span class="badge bg-{{ $detail->status_color }}">
                                    {{ $detail->status_label }}
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Liste des affectations -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Affectations de véhicules</h6>
                    <a href="{{ route('accountant.cement-orders.details.assign-truck', ['cement_order' => $cement_order->id, 'detail' => $detail->id]) }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-truck me-2"></i>Nouvelle affectation
                    </a>
                </div>
                <div class="card-body">
                    @if($detail->tripAssignments->isNotEmpty())
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Véhicule</th>
                                        <th>Chauffeur</th>
                                        <th>Voyage</th>
                                        <th>Tonnage</th>
                                        <th>Début</th>
                                        <th>Fin</th>
                                        <th>Statut</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($detail->tripAssignments as $assignment)
                                        <tr>
                                            <td>{{ optional($assignment->truck)->registration_number }}</td>
                                            <td>{{ optional($assignment->driver)->first_name }} {{ optional($assignment->driver)->last_name }}</td>
                                            <td>{{ $assignment->trip_number }}</td>
                                            <td>{{ number_format($assignment->tonnage, 2) }} T</td>
                                            <td>{{ $assignment->start_date->format('d/m/Y H:i') }}</td>
                                            <td>{{ $assignment->end_date->format('d/m/Y H:i') }}</td>
                                            <td>
                                                <span class="badge bg-{{ $assignment->status_color }}">
                                                    {{ $assignment->status_label }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-3">
                            <p class="text-muted mb-0">Aucune affectation pour le moment</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
