/* Styles pour la vue des ventes en attente de paiement */
:root {
    --primary-color: #1E88E5;
    --primary-dark: #0D47A1;
    --primary-light: #64B5F6;
    --secondary-color: #475569;
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --danger-color: #F44336;
    --info-color: #03A9F4;
    --light-color: #f1f5f9;
    --dark-color: #0f172a;
    --text-light: #64748b;
    --card-radius: 0.75rem;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --transition: all 0.3s ease;
}

/* Styles généraux */
body {
    background-color: #f8fafc;
    font-family: 'Inter', sans-serif;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.page-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
}

.page-title i {
    color: var(--primary-color);
}

.page-subtitle {
    color: var(--text-light);
    font-size: 1rem;
    margin-top: 0.25rem;
}

.breadcrumb-container {
    background: white;
    padding: 0.75rem 1.25rem;
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
}

.breadcrumb {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    color: var(--text-light);
    font-size: 0.875rem;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb-item a:hover {
    color: var(--primary-dark);
}

.breadcrumb-item + .breadcrumb-item::before {
    content: '/';
    margin: 0 0.5rem;
    color: var(--text-light);
}

.breadcrumb-item.active {
    color: var(--dark-color);
    font-weight: 500;
}

/* Carte principale */
.main-card {
    background-color: white;
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    border: none;
    margin-bottom: 2rem;
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 1.25rem 1.5rem;
    border-bottom: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header-title {
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-body {
    padding: 1.5rem;
}

/* Barre de recherche */
.search-container {
    position: relative;
    max-width: 350px;
}

.search-input {
    border: 1px solid #e2e8f0;
    border-radius: 2rem;
    padding: 0.6rem 1rem 0.6rem 2.5rem;
    width: 100%;
    transition: var(--transition);
    background-color: white;
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 136, 229, 0.25);
    outline: none;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

.search-button {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 1.5rem;
    padding: 0.4rem 1rem;
    font-size: 0.875rem;
    transition: var(--transition);
}

.search-button:hover {
    background-color: var(--primary-dark);
}

/* Tableau moderne */
.modern-table-container {
    overflow-x: auto;
    border-radius: var(--card-radius);
}

.modern-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.modern-table th {
    background-color: #f8fafc;
    color: var(--secondary-color);
    font-weight: 600;
    text-align: left;
    padding: 1rem;
    border-bottom: 2px solid #e2e8f0;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.modern-table td {
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    vertical-align: middle;
    color: var(--dark-color);
    font-size: 0.95rem;
}

.modern-table tr:last-child td {
    border-bottom: none;
}

.modern-table tr:hover td {
    background-color: #f1f5f9;
}

/* Badges de statut */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    border-radius: 2rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.pending {
    background-color: rgba(255, 152, 0, 0.15);
    color: #f57c00;
}

.status-badge.partial {
    background-color: rgba(3, 169, 244, 0.15);
    color: #0288d1;
}

/* Boutons d'action */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
    text-decoration: none;
    border: none;
    cursor: pointer;
}

.action-button.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.action-button.primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-dark));
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Message vide */
.empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
}

.empty-state-icon {
    font-size: 3rem;
    color: var(--text-light);
    margin-bottom: 1rem;
}

.empty-state-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.empty-state-message {
    color: var(--text-light);
    max-width: 400px;
    margin: 0 auto;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 1.5rem;
}

.pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 0.25rem;
}

.pagination .page-item .page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    background-color: white;
    color: var(--dark-color);
    font-weight: 500;
    transition: var(--transition);
}

.pagination .page-item .page-link:hover {
    background-color: #f1f5f9;
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .page-item.disabled .page-link {
    color: var(--text-light);
    pointer-events: none;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.fade-in-delay-1 {
    animation-delay: 0.1s;
}

.fade-in-delay-2 {
    animation-delay: 0.2s;
}

.fade-in-delay-3 {
    animation-delay: 0.3s;
}

/* Responsive */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .search-container {
        width: 100%;
        max-width: none;
    }
    
    .card-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}
