/**
 * Styles additionnels pour le tableau de bord comptable
 * Ajout des filtres de période et animations
 */

/* Styles améliorés pour l'en-tête du tableau de bord */
.dashboard-header {
    position: relative;
    background: linear-gradient(135deg, #1E88E5 0%, #0D47A1 100%);
    border-radius: 1.5rem;
    padding: 2rem 0;
    margin-bottom: 2rem;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(13, 71, 161, 0.2);
    transition: all 0.5s ease;
}

.dashboard-header:hover {
    box-shadow: 0 15px 40px rgba(13, 71, 161, 0.3);
    transform: translateY(-5px);
}

.header-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.header-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.header-circle.circle-1 {
    width: 300px;
    height: 300px;
    top: -150px;
    right: -100px;
    animation: float 8s ease-in-out infinite;
}

.header-circle.circle-2 {
    width: 200px;
    height: 200px;
    bottom: -100px;
    left: 10%;
    animation: float 10s ease-in-out infinite reverse;
}

.header-circle.circle-3 {
    width: 150px;
    height: 150px;
    top: 20%;
    left: 30%;
    animation: float 7s ease-in-out infinite 1s;
    background: rgba(255, 255, 255, 0.05);
}

.header-wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50px;
    background: url('data:image/svg+xml;utf8,<svg viewBox="0 0 1200 120" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="%23FFFFFF"/><path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="%23FFFFFF"/><path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="%23FFFFFF"/></svg>');
    background-size: cover;
    background-position: center;
}

.header-glow {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 40px;
    background: rgba(255, 255, 255, 0.3);
    filter: blur(30px);
    border-radius: 50%;
}

.header-content {
    position: relative;
    z-index: 2;
    color: white;
    padding: 1rem 0;
}

.date-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
    border-radius: 2rem;
    padding: 0.5rem 1rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.date-badge i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.header-title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.header-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 1rem;
    margin-right: 1rem;
    font-size: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.header-icon:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.3);
}

/* Styles pour les cartes de statistiques */
.stat-card {
    padding: 0.6rem 0.85rem;
    margin-bottom: 0.5rem;
    height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-radius: 0.85rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.6);
    overflow: hidden;
    position: relative;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.stat-card:hover::before {
    transform: translateX(100%);
}

.stat-card-top {
    display: flex;
    align-items: center;
}

.stat-card-label {
    font-size: 0.65rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    opacity: 0.85;
    margin-bottom: 0.1rem;
    text-transform: uppercase;
}

.stat-card-icon {
    width: 32px;
    height: 32px;
    min-width: 32px;
    font-size: 0.85rem;
    margin-right: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1E88E5, #0D47A1);
    color: white;
    border-radius: 8px;
    box-shadow: 0 3px 8px rgba(13, 71, 161, 0.25);
    transition: all 0.3s ease;
}

.stat-card:hover .stat-card-icon {
    transform: scale(1.1);
    box-shadow: 0 4px 10px rgba(13, 71, 161, 0.35);
}

.stat-card.success .stat-card-icon {
    background: linear-gradient(135deg, #43A047, #2E7D32);
    box-shadow: 0 3px 8px rgba(46, 125, 50, 0.25);
}

.stat-card.danger .stat-card-icon {
    background: linear-gradient(135deg, #E53935, #C62828);
    box-shadow: 0 3px 8px rgba(198, 40, 40, 0.25);
}

.stat-card.warning .stat-card-icon {
    background: linear-gradient(135deg, #FB8C00, #EF6C00);
    box-shadow: 0 3px 8px rgba(239, 108, 0, 0.25);
}

.stat-card-value {
    font-size: 1.5rem;
    line-height: 1;
    font-weight: 700;
    margin-bottom: 0.1rem;
    background: linear-gradient(135deg, #1E88E5, #0D47A1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.stat-card:hover .stat-card-value {
    transform: scale(1.03);
}

.stat-card.success .stat-card-value {
    background: linear-gradient(135deg, #43A047, #2E7D32);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-card.danger .stat-card-value {
    background: linear-gradient(135deg, #E53935, #C62828);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-card.warning .stat-card-value {
    background: linear-gradient(135deg, #FB8C00, #EF6C00);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-card-trend {
    font-size: 0.6rem;
    padding: 0.15rem 0;
    display: flex;
    align-items: center;
    height: 18px;
    border-radius: 12px;
    background-color: rgba(30, 136, 229, 0.08);
    padding: 0 0.5rem;
    display: inline-flex;
    width: fit-content;
}

.stat-card-trend.primary {
    color: #1E88E5;
    background-color: rgba(30, 136, 229, 0.08);
}

.stat-card-trend.success {
    color: #43A047;
    background-color: rgba(67, 160, 71, 0.08);
}

.stat-card-trend.danger {
    color: #E53935;
    background-color: rgba(229, 57, 53, 0.08);
}

.stat-card-trend.warning {
    color: #FB8C00;
    background-color: rgba(251, 140, 0, 0.08);
}

.stat-card-trend i {
    margin-right: 0.25rem;
    font-size: 0.6rem;
}

.stat-card-bottom {
    margin-top: 0.3rem;
    padding-top: 0.15rem;
}

.stat-card-progress {
    height: 4px;
    margin-top: 0.15rem;
}

/* Animations */
@keyframes float {
    0% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(5deg);
    }
    100% {
        transform: translateY(0) rotate(0deg);
    }
}

.fadeInUp {
    animation: fadeInUp 0.8s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

/* Filtres de période */
.period-filters {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.period-filter {
    background: rgba(30, 136, 229, 0.1);
    border: none;
    border-radius: 20px;
    padding: 0.35rem 0.8rem;
    font-size: 0.85rem;
    color: #1E88E5;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    outline: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.period-filter:hover {
    background: rgba(30, 136, 229, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.period-filter.active {
    background: #1E88E5;
    color: white;
    font-weight: 500;
    box-shadow: 0 4px 10px rgba(30, 136, 229, 0.3);
}

.period-filter.hover {
    animation: pulse 1s infinite;
}

/* Effet d'onde au clic */
.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.7);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

/* Animation pour l'indicateur de chargement */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(3px);
    border-radius: var(--card-border-radius);
}

/* Animations */
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(30, 136, 229, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(30, 136, 229, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(30, 136, 229, 0);
    }
}

/* Animation pour les compteurs */
.counter-animation {
    position: relative;
    display: inline-block;
}

.counter-animation::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #1E88E5, transparent);
    animation: counterLine 2s ease-in-out infinite;
}

@keyframes counterLine {
    0% {
        width: 0;
        left: 0;
        opacity: 0;
    }
    50% {
        width: 100%;
        opacity: 1;
    }
    100% {
        width: 0;
        left: 100%;
        opacity: 0;
    }
}

/* Animations pour les éléments qui apparaissent */
.fadeIn {
    animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .period-filters {
        margin-bottom: 0.5rem;
    }
    
    .period-filter {
        padding: 0.25rem 0.6rem;
        font-size: 0.75rem;
    }
    
    .dashboard-card-actions {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .dashboard-card-actions .btn {
        margin-top: 0.5rem;
    }
}
