/* Tableau moderne avec design épuré */
.table-responsive {
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.table {
    width: 100%;
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: 1rem;
    border-top: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    color: var(--gray-color);
    background-color: rgba(0, 0, 0, 0.02);
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-top: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table tr:last-child td {
    border-bottom: none;
}

.table tr:hover {
    background-color: rgba(0, 0, 0, 0.01);
}

.table .invoice-link {
    font-weight: 600;
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

.table .invoice-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.table .customer-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.table .customer-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.85rem;
}

.table .customer-name {
    font-weight: 500;
    color: var(--dark-color);
}

.table .amount {
    font-weight: 600;
    color: var(--dark-color);
}

.table .date {
    color: var(--gray-color);
    font-size: 0.85rem;
}

/* Badges de statut */
.status-badge {
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.35rem;
}

.status-badge i {
    font-size: 0.7rem;
}

.status-badge.paid {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.status-badge.partial {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

.status-badge.unpaid {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
}

/* Barre de progression de paiement */
.payment-progress {
    height: 6px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.progress-bar {
    height: 100%;
    border-radius: 3px;
    transition: width 0.5s ease;
}

.progress-bar.paid {
    background: var(--gradient-success);
}

.progress-bar.partial {
    background: var(--gradient-warning);
}

.progress-bar.unpaid {
    background: var(--gradient-danger);
}

/* Actions rapides */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.quick-action {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    text-align: center;
    text-decoration: none;
    color: var(--dark-color);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.quick-action:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    color: var(--dark-color);
}

.quick-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-primary);
    z-index: 1;
}

.quick-action.primary::before { background: var(--gradient-primary); }
.quick-action.success::before { background: var(--gradient-success); }
.quick-action.warning::before { background: var(--gradient-warning); }
.quick-action.danger::before { background: var(--gradient-danger); }

.quick-action-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(30, 136, 229, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
    transition: var(--transition-normal);
}

.quick-action:hover .quick-action-icon {
    transform: scale(1.1);
    background: var(--primary-color);
    color: white;
}

.quick-action-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.quick-action-description {
    font-size: 0.85rem;
    color: var(--gray-color);
    margin-bottom: 0;
}

/* Activités récentes */
.activity-timeline {
    position: relative;
    padding-left: 2rem;
}

.activity-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 8px;
    width: 2px;
    height: 100%;
    background: rgba(0, 0, 0, 0.05);
}

.activity-item {
    position: relative;
    padding-bottom: 1.5rem;
}

.activity-item:last-child {
    padding-bottom: 0;
}

.activity-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -2rem;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    border: 3px solid white;
    box-shadow: 0 0 0 3px rgba(30, 136, 229, 0.2);
    z-index: 1;
}

.activity-item.success::before { background: var(--success-color); box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2); }
.activity-item.warning::before { background: var(--warning-color); box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.2); }
.activity-item.danger::before { background: var(--danger-color); box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.2); }

.activity-content {
    background: white;
    border-radius: var(--border-radius-md);
    padding: 1rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.activity-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--dark-color);
}

.activity-description {
    font-size: 0.85rem;
    color: var(--gray-color);
    margin-bottom: 0.5rem;
}

.activity-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: var(--gray-color);
}

.activity-time {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.activity-user {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.activity-user-avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-light);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.6rem;
    font-weight: 600;
}
