@extends('layouts.accountant')

@section('title', 'Gestion des Approvisionnements')

@push('styles')
<style>
    /* Variables de couleurs modernes et attrayantes */
    :root {
        --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
        --gradient-info: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.1);
        --shadow-hover: 0 15px 40px rgba(0, 0, 0, 0.15);
        --border-radius: 15px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Header avec dégradé moderne */
    .page-header {
        background: var(--gradient-primary);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        box-shadow: var(--shadow-soft);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .page-header-content {
        position: relative;
        z-index: 2;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    /* Bouton principal avec animation */
    .btn-create {
        background: var(--gradient-success);
        border: none;
        border-radius: 50px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        color: white;
        text-decoration: none;
        transition: var(--transition);
        box-shadow: 0 5px 15px rgba(79, 172, 254, 0.3);
        position: relative;
        overflow: hidden;
    }

    .btn-create::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-create:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
        color: white;
    }

    .btn-create:hover::before {
        left: 100%;
    }

    /* Cartes de statistiques */
    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-soft);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--gradient-primary);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-hover);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
    }

    .stat-icon.total { background: var(--gradient-primary); }
    .stat-icon.pending { background: var(--gradient-warning); }
    .stat-icon.validated { background: var(--gradient-success); }
    .stat-icon.rejected { background: var(--gradient-danger); }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #718096;
        font-weight: 500;
        margin: 0;
        margin-bottom: 0.5rem;
    }

    .stat-tonnage {
        background: rgba(255, 255, 255, 0.9);
        padding: 0.5rem 0.75rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        color: #4a5568;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
        transition: var(--transition);
    }

    .stat-tonnage:hover {
        background: rgba(255, 255, 255, 1);
        transform: scale(1.05);
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    }

    .stat-tonnage i {
        color: #667eea;
        font-size: 0.8rem;
        animation: bounce 2s infinite;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-3px);
        }
        60% {
            transform: translateY(-1px);
        }
    }

    /* Animation de compteur pour les tonnages */
    .stat-tonnage span {
        display: inline-block;
        animation: countUp 1.5s ease-out;
    }

    @keyframes countUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Effet de pulsation pour attirer l'attention */
    .stat-card:hover .stat-tonnage {
        animation: pulse 1s ease-in-out;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
        }
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- En-tête moderne avec dégradé -->
    <div class="page-header">
        <div class="page-header-content">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-3">
                <div>
                    <h1 class="page-title">📦 Approvisionnements</h1>
                    <p class="page-subtitle">Gérez vos approvisionnements avec style et efficacité</p>
                </div>
                <a href="{{ route('accountant.supplies.create') }}" class="btn-create">
                    <i class="fas fa-plus-circle me-2"></i>Nouvel Approvisionnement
                </a>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success border-0 rounded-3 shadow-sm">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
        </div>
    @endif

    <!-- Statistiques rapides -->
    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-icon total">
                <i class="fas fa-boxes"></i>
            </div>
            <div class="stat-value">{{ $supplies->total() }}</div>
            <p class="stat-label">Total des approvisionnements</p>
            <div class="stat-tonnage">
                <i class="fas fa-weight-hanging me-1"></i>
                <span>{{ number_format($stats['totalTonnage'] ?? 0, 2, ',', ' ') }} T</span>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon pending">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-value">{{ $supplies->where('status', 'pending')->count() }}</div>
            <p class="stat-label">En attente de validation</p>
            <div class="stat-tonnage">
                <i class="fas fa-weight-hanging me-1"></i>
                <span>{{ number_format($stats['pendingTonnage'] ?? 0, 2, ',', ' ') }} T</span>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon validated">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-value">{{ $supplies->where('status', 'validated')->count() }}</div>
            <p class="stat-label">Validés</p>
            <div class="stat-tonnage">
                <i class="fas fa-weight-hanging me-1"></i>
                <span>{{ number_format($stats['validatedTonnage'] ?? 0, 2, ',', ' ') }} T</span>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon rejected">
                <i class="fas fa-times-circle"></i>
            </div>
            <div class="stat-value">{{ $supplies->where('status', 'rejected')->count() }}</div>
            <p class="stat-label">Rejetés</p>
            <div class="stat-tonnage">
                <i class="fas fa-weight-hanging me-1"></i>
                <span>{{ number_format($stats['rejectedTonnage'] ?? 0, 2, ',', ' ') }} T</span>
            </div>
        </div>
    </div>

    <!-- Filtres et recherche modernes -->
    <div class="filter-card">
        <div class="filter-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="filter-title">
                    <i class="fas fa-filter me-2"></i>Filtres et recherche
                </div>
                <button type="button" class="btn-reset" id="resetFilters">
                    <i class="fas fa-undo me-1"></i>Réinitialiser
                </button>
            </div>
        </div>
        <div class="filter-body">
            <form method="GET" action="{{ route('accountant.supplies.index') }}" id="filterForm">
                <div class="filter-grid">
                    <div class="filter-group">
                        <label for="search" class="filter-label">
                            <i class="fas fa-search me-1"></i>Recherche
                        </label>
                        <input type="text" class="filter-input" id="search" name="search"
                               value="{{ request('search') }}" placeholder="Référence, fournisseur...">
                    </div>
                    <div class="filter-group">
                        <label for="status" class="filter-label">
                            <i class="fas fa-flag me-1"></i>Statut
                        </label>
                        <select class="filter-select" id="status" name="status">
                            <option value="">Tous les statuts</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>⏳ En attente</option>
                            <option value="validated" {{ request('status') == 'validated' ? 'selected' : '' }}>✅ Validé</option>
                            <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>❌ Rejeté</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="date_from" class="filter-label">
                            <i class="fas fa-calendar-alt me-1"></i>Date de début
                        </label>
                        <input type="date" class="filter-input" id="date_from" name="date_from"
                               value="{{ request('date_from') }}">
                    </div>
                    <div class="filter-group">
                        <label for="date_to" class="filter-label">
                            <i class="fas fa-calendar-check me-1"></i>Date de fin
                        </label>
                        <input type="date" class="filter-input" id="date_to" name="date_to"
                               value="{{ request('date_to') }}">
                    </div>
                </div>
                <div class="filter-actions">
                    <button type="submit" class="btn-filter">
                        <i class="fas fa-search me-2"></i>Appliquer les filtres
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Table moderne avec cartes -->
    <div class="supplies-container">
        @forelse($supplies as $supply)
            <div class="supply-card" data-status="{{ $supply->status }}">
                <div class="supply-card-header">
                    <div class="supply-reference">
                        <i class="fas fa-hashtag me-2"></i>
                        <strong>{{ $supply->reference }}</strong>
                    </div>
                    <div class="supply-status">
                        @if($supply->status === 'pending')
                            <span class="status-badge pending">
                                <i class="fas fa-clock me-1"></i>En attente
                            </span>
                        @elseif($supply->status === 'validated')
                            <span class="status-badge validated">
                                <i class="fas fa-check-circle me-1"></i>Validé
                            </span>
                        @else
                            <span class="status-badge rejected">
                                <i class="fas fa-times-circle me-1"></i>Rejeté
                            </span>
                        @endif
                    </div>
                </div>

                <div class="supply-card-body">
                    <div class="supply-info-grid">
                        <div class="supply-info-item">
                            <div class="info-icon supplier">
                                <i class="fas fa-industry"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">Fournisseur</div>
                                <div class="info-value">{{ $supply->supplier->name ?? $supply->supplier }}</div>
                            </div>
                        </div>

                        <div class="supply-info-item">
                            <div class="info-icon date">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">Date</div>
                                <div class="info-value">{{ $supply->created_at->format('d/m/Y') }}</div>
                            </div>
                        </div>

                        <div class="supply-info-item">
                            <div class="info-icon amount">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">Montant Total</div>
                                <div class="info-value amount-text">{{ number_format($supply->total_amount, 0, ',', ' ') }} FCFA</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="supply-card-footer">
                    <a href="{{ route('accountant.supplies.show', $supply) }}" class="btn-view-details">
                        <i class="fas fa-eye me-2"></i>Voir les détails
                        <i class="fas fa-arrow-right ms-2"></i>
                    </a>
                </div>
            </div>
        @empty
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-box-open"></i>
                </div>
                <h3 class="empty-title">Aucun approvisionnement trouvé</h3>
                <p class="empty-description">Il n'y a actuellement aucun approvisionnement correspondant à vos critères.</p>
                <a href="{{ route('accountant.supplies.create') }}" class="btn-create-first">
                    <i class="fas fa-plus me-2"></i>Créer le premier approvisionnement
                </a>
            </div>
        @endforelse
    </div>
            
            <!-- Pagination responsive -->
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-center gap-3 mt-4">
                <div class="text-muted small">
                    Affichage de {{ $supplies->firstItem() ?? 0 }} à {{ $supplies->lastItem() ?? 0 }} sur {{ $supplies->total() }} approvisionnements
                </div>
                <div class="d-flex justify-content-center w-100 w-md-auto">
                    {!! $supplies->links() !!}
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    /* Styles pour les filtres modernes */
    .filter-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-soft);
        margin-bottom: 2rem;
        overflow: hidden;
        transition: var(--transition);
    }

    .filter-header {
        background: var(--gradient-info);
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .filter-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2d3748;
        margin: 0;
    }

    .btn-reset {
        background: rgba(255, 255, 255, 0.9);
        border: none;
        border-radius: 25px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        color: #4a5568;
        transition: var(--transition);
    }

    .btn-reset:hover {
        background: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .filter-body {
        padding: 2rem;
    }

    .filter-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
    }

    .filter-label {
        font-weight: 600;
        color: #4a5568;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .filter-input, .filter-select {
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: var(--transition);
        background: white;
    }

    .filter-input:focus, .filter-select:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
    }

    .filter-actions {
        text-align: center;
    }

    .btn-filter {
        background: var(--gradient-primary);
        border: none;
        border-radius: 50px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        color: white;
        font-size: 1rem;
        transition: var(--transition);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    /* Styles pour les cartes d'approvisionnement */
    .supplies-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .supply-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-soft);
        transition: var(--transition);
        overflow: hidden;
        position: relative;
    }

    .supply-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-hover);
    }

    .supply-card-header {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding: 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .supply-reference {
        font-size: 1.1rem;
        font-weight: 700;
        color: #2d3748;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.85rem;
        display: flex;
        align-items: center;
    }

    .status-badge.pending {
        background: var(--gradient-warning);
        color: white;
    }

    .status-badge.validated {
        background: var(--gradient-success);
        color: white;
    }

    .status-badge.rejected {
        background: var(--gradient-danger);
        color: white;
    }

    .supply-card-body {
        padding: 1.5rem;
    }

    .supply-info-grid {
        display: grid;
        gap: 1rem;
    }

    .supply-info-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: #f8fafc;
        border-radius: 10px;
        transition: var(--transition);
    }

    .supply-info-item:hover {
        background: #e2e8f0;
        transform: translateX(5px);
    }

    .info-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        flex-shrink: 0;
    }

    .info-icon.supplier { background: var(--gradient-primary); }
    .info-icon.date { background: var(--gradient-info); }
    .info-icon.amount { background: var(--gradient-success); }

    .info-content {
        flex: 1;
    }

    .info-label {
        font-size: 0.8rem;
        color: #718096;
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .info-value {
        font-size: 1rem;
        font-weight: 600;
        color: #2d3748;
    }

    .amount-text {
        color: #38a169;
        font-size: 1.1rem;
    }

    .supply-card-footer {
        padding: 1.5rem;
        background: #f8fafc;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    .btn-view-details {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        background: var(--gradient-primary);
        color: white;
        text-decoration: none;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        font-weight: 600;
        transition: var(--transition);
    }

    .btn-view-details:hover {
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    /* État vide */
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-soft);
        grid-column: 1 / -1;
    }

    .empty-icon {
        font-size: 4rem;
        color: #cbd5e0;
        margin-bottom: 1.5rem;
    }

    .empty-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #4a5568;
        margin-bottom: 1rem;
    }

    .empty-description {
        color: #718096;
        margin-bottom: 2rem;
        font-size: 1.1rem;
    }

    .btn-create-first {
        background: var(--gradient-success);
        color: white;
        text-decoration: none;
        padding: 1rem 2rem;
        border-radius: 50px;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        transition: var(--transition);
        box-shadow: 0 5px 15px rgba(79, 172, 254, 0.3);
    }

    .btn-create-first:hover {
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
    }

    /* Pagination moderne */
    .pagination {
        margin-bottom: 0;
    }

    .pagination .page-link {
        border: none;
        border-radius: 10px;
        margin: 0 0.25rem;
        padding: 0.75rem 1rem;
        color: #4a5568;
        font-weight: 500;
        transition: var(--transition);
    }

    .pagination .page-link:hover {
        background: var(--gradient-primary);
        color: white;
        transform: translateY(-1px);
    }

    .pagination .page-item.active .page-link {
        background: var(--gradient-primary);
        border: none;
        box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .supplies-container {
            grid-template-columns: 1fr;
        }

        .filter-grid {
            grid-template-columns: 1fr;
        }

        .page-title {
            font-size: 2rem;
        }

        .stats-container {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 480px) {
        .stats-container {
            grid-template-columns: 1fr;
        }

        .supply-info-item {
            flex-direction: column;
            text-align: center;
        }
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Éléments du DOM
    const searchInput = document.getElementById('search');
    const statusSelect = document.getElementById('status');
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');
    const resetButton = document.getElementById('resetFilters');
    const filterForm = document.getElementById('filterForm');
    const suppliesContainer = document.querySelector('.supplies-container');

    // Animation d'entrée pour les cartes
    function animateCards() {
        const cards = document.querySelectorAll('.supply-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // Animation des compteurs de tonnage
    function animateTonnageCounters() {
        const tonnageElements = document.querySelectorAll('.stat-tonnage span');

        tonnageElements.forEach((element, index) => {
            const text = element.textContent;
            const value = parseFloat(text.replace(/[^\d,.-]/g, '').replace(',', '.'));

            if (!isNaN(value)) {
                element.textContent = '0,00 T';

                setTimeout(() => {
                    animateValue(element, 0, value, 1500, text);
                }, index * 200);
            }
        });
    }

    // Fonction d'animation de valeur
    function animateValue(element, start, end, duration, originalText) {
        const startTime = performance.now();

        function updateValue(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Utiliser une fonction d'easing pour un effet plus fluide
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = start + (end - start) * easeOutQuart;

            if (progress < 1) {
                element.textContent = currentValue.toLocaleString('fr-FR', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }) + ' T';
                requestAnimationFrame(updateValue);
            } else {
                element.textContent = originalText;
                element.style.color = '#38a169';
                setTimeout(() => {
                    element.style.color = '';
                }, 500);
            }
        }

        requestAnimationFrame(updateValue);
    }

    // Filtrage en temps réel
    function filterSupplies() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusFilter = statusSelect.value;
        const cards = document.querySelectorAll('.supply-card');

        cards.forEach(card => {
            const reference = card.querySelector('.supply-reference').textContent.toLowerCase();
            const supplier = card.querySelector('.info-value').textContent.toLowerCase();
            const status = card.dataset.status;

            const matchesSearch = reference.includes(searchTerm) || supplier.includes(searchTerm);
            const matchesStatus = !statusFilter || status === statusFilter;

            if (matchesSearch && matchesStatus) {
                card.style.display = 'block';
                card.style.animation = 'fadeIn 0.3s ease';
            } else {
                card.style.display = 'none';
            }
        });

        // Vérifier s'il y a des résultats
        const visibleCards = document.querySelectorAll('.supply-card[style*="display: block"], .supply-card:not([style*="display: none"])');
        const emptyState = document.querySelector('.empty-state');

        if (visibleCards.length === 0 && !emptyState) {
            showNoResults();
        } else if (visibleCards.length > 0 && emptyState) {
            hideNoResults();
        }
    }

    function showNoResults() {
        const emptyHTML = `
            <div class="empty-state temporary">
                <div class="empty-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3 class="empty-title">Aucun résultat trouvé</h3>
                <p class="empty-description">Aucun approvisionnement ne correspond à vos critères de recherche.</p>
                <button type="button" class="btn-create-first" onclick="clearFilters()">
                    <i class="fas fa-undo me-2"></i>Effacer les filtres
                </button>
            </div>
        `;
        suppliesContainer.insertAdjacentHTML('beforeend', emptyHTML);
    }

    function hideNoResults() {
        const tempEmptyState = document.querySelector('.empty-state.temporary');
        if (tempEmptyState) {
            tempEmptyState.remove();
        }
    }

    window.clearFilters = function() {
        searchInput.value = '';
        statusSelect.value = '';
        dateFromInput.value = '';
        dateToInput.value = '';
        filterSupplies();
        hideNoResults();
    };

    // Gestionnaires d'événements
    let searchTimer;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimer);
        searchTimer = setTimeout(filterSupplies, 300);
    });

    statusSelect.addEventListener('change', filterSupplies);

    // Réinitialisation des filtres
    resetButton.addEventListener('click', function(e) {
        e.preventDefault();
        clearFilters();
    });

    // Soumission du formulaire avec animation
    filterForm.addEventListener('submit', function(e) {
        const submitBtn = filterForm.querySelector('.btn-filter');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Recherche...';
        submitBtn.disabled = true;
    });

    // Animations au chargement
    animateCards();
    animateTonnageCounters();

    // Animation CSS pour fadeIn
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .supply-card {
            transition: all 0.3s ease;
        }

        .btn-filter:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }
    `;
    document.head.appendChild(style);

    // Effet de parallaxe léger sur les cartes
    window.addEventListener('scroll', function() {
        const cards = document.querySelectorAll('.supply-card');
        const scrolled = window.pageYOffset;

        cards.forEach((card, index) => {
            const rate = scrolled * -0.02;
            card.style.transform = `translateY(${rate}px)`;
        });
    });
});
</script>
@endpush
@endsection
