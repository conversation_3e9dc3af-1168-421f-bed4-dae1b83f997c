@extends('layouts.accountant')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Détails du bon de commande</h1>
        <div class="btn-group">
            <a href="{{ route('accountant.cement-orders.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour
            </a>
        </div>
    </div>

    <!-- Informations générales -->
    <div class="row g-3">
        <div class="col-md-6">
            <div class="card shadow h-100">
                <div class="card-header">
                    <h5 class="m-0 font-weight-bold text-primary">Informations générales</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="35%">Référence</th>
                            <td>{{ $cement_order->reference }}</td>
                        </tr>
                        <tr>
                            <th>Produit</th>
                            <td>{{ $cement_order->product->name }}</td>
                        </tr>
                        <tr>
                            <th>Créé par</th>
                            <td>{{ $cement_order->creator->name }}</td>
                        </tr>
                        <tr>
                            <th>Date de création</th>
                            <td>{{ $cement_order->created_at->format('d/m/Y H:i') }}</td>
                        </tr>
                        <tr>
                            <th>Statut</th>
                            <td>
                                @switch($cement_order->status)
                                    @case('pending')
                                        <span class="badge bg-warning text-dark">En attente</span>
                                        @break
                                    @case('validated')
                                        <span class="badge bg-success">Validé</span>
                                        @break
                                    @case('rejected')
                                        <span class="badge bg-danger">Rejeté</span>
                                        @break
                                    @default
                                        <span class="badge bg-secondary">{{ $cement_order->status }}</span>
                                @endswitch
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card shadow h-100">
                <div class="card-header">
                    <h5 class="m-0 font-weight-bold text-primary">Totaux</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-sm-6">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="text-xs text-uppercase mb-1">Total Tonnage</div>
                                    <div class="h5 mb-0">{{ $cement_order->formatted_total_tonnage }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="text-xs text-uppercase mb-1">Montant Total</div>
                                    <div class="h5 mb-0">{{ $cement_order->formatted_total_amount }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="text-xs text-uppercase mb-1">Montant Payé</div>
                                    <div class="h5 mb-0">{{ $cement_order->formatted_paid_amount }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="text-xs text-uppercase mb-1">Reste à Payer</div>
                                    <div class="h5 mb-0">{{ $cement_order->formatted_remaining_amount }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Détails de la commande -->
    <div class="card shadow mt-4">
        <div class="card-header">
            <h5 class="m-0 font-weight-bold text-primary">Détails des livraisons</h5>
        </div>
        <div class="card-body">
            @if($cement_order->details->isNotEmpty())
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="bg-primary text-white">
                            <tr>
                                <th class="text-center" style="width: 50px;">N°</th>
                                <th>Fournisseur</th>
                                <th>Destination</th>
                                <th class="text-center" style="width: 120px;">Tonnage</th>
                                <th class="text-center" style="width: 100px;">Tours</th>
                                <th class="text-center">Quantité</th>
                                <th class="text-center">Prix unitaire</th>
                                <th class="text-center">Montant total</th>
                                <th class="text-center" style="width: 100px;">État</th>
                                <th class="text-center" style="width: 100px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($cement_order->details as $index => $detail)
                                <tr>
                                    <td class="text-center">{{ $index + 1 }}</td>
                                    <td>
                                        <span class="text-primary">
                                            <i class="fas fa-industry me-1"></i>
                                            {{ $detail->supplier->name }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-success">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            {{ $detail->destination->name }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-success" style="font-size: 0.9em;">
                                            {{ $detail->tonnage_per_trip_formatted }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-info" style="font-size: 0.9em;">
                                            {{ $detail->number_of_trips }} tours
                                        </span>
                                    </td>
                                    <td class="text-center">{{ $detail->formatted_total_tonnage }}</td>
                                    <td class="text-center">{{ $detail->formatted_unit_price }}</td>
                                    <td class="text-center">{{ $detail->formatted_total_amount }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-{{ $detail->status_color }}">
                                            {{ $detail->status_label }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group">
                                            <a href="{{ route('accountant.cement-orders.details.show', ['cement_order' => $cement_order->id, 'detail' => $detail->id]) }}" 
                                               class="btn btn-sm btn-info" 
                                               title="Voir les détails">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('accountant.cement-orders.details.assign-truck', ['cement_order' => $cement_order->id, 'detail' => $detail->id]) }}" 
                                               class="btn btn-sm btn-primary" 
                                               title="Affecter un véhicule">
                                                <i class="fas fa-truck"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                        <tfoot class="bg-light">
                            <tr>
                                <td colspan="5" class="text-end"><strong>Total:</strong></td>
                                <td class="text-center"><strong>{{ $cement_order->formatted_total_tonnage }}</strong></td>
                                <td></td>
                                <td class="text-center"><strong>{{ $cement_order->formatted_total_amount }}</strong></td>
                                <td colspan="2"></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            @else
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Aucun détail trouvé pour ce bon de commande
                </div>
            @endif
        </div>
    </div>

    <!-- Liste des affectations -->
    <div class="card shadow mt-4">
        <div class="card-header">
            <h5 class="m-0 font-weight-bold text-primary">Liste des affectations</h5>
        </div>
        <div class="card-body">
            @php
                $allAssignments = collect();
                foreach($cement_order->details as $detail) {
                    $allAssignments = $allAssignments->concat($detail->tripAssignments);
                }
            @endphp

            @if($allAssignments->isNotEmpty())
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="bg-primary text-white">
                            <tr>
                                <th>Véhicule</th>
                                <th>Chauffeur</th>
                                <th>Voyage</th>
                                <th>Tonnage</th>
                                <th>Date début</th>
                                <th>Date fin</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($allAssignments as $assignment)
                                <tr>
                                    <td>{{ $assignment->truck->registration_number }}</td>
                                    <td>{{ $assignment->driver?->fullName ?? 'Non défini' }}</td>
                                    <td>{{ $assignment->trip_number }}</td>
                                    <td>{{ number_format($assignment->tonnage, 2) }} T</td>
                                    <td>{{ $assignment->start_date ? $assignment->start_date->format('d/m/Y H:i') : 'Non défini' }}</td>
                                    <td>{{ $assignment->end_date ? $assignment->end_date->format('d/m/Y H:i') : 'Non défini' }}</td>
                                    <td>
                                        <span class="badge bg-{{ $assignment->status === 'completed' ? 'success' : 'warning' }}">
                                            {{ $assignment->status === 'completed' ? 'Terminé' : 'En cours' }}
                                        </span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Aucune affectation trouvée pour ce bon de commande
                </div>
            @endif
        </div>
    </div>

    @if($cement_order->notes)
        <!-- Notes -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="m-0 font-weight-bold text-primary">Notes</h5>
            </div>
            <div class="card-body">
                {{ $cement_order->notes }}
            </div>
        </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Scripts pour la gestion des tableaux et autres fonctionnalités
        $('.datatable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/fr-FR.json',
            },
        });
    });
</script>
@endpush
