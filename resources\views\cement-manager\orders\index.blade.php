@extends('layouts.cement-manager')

@section('title', 'Bons de commande')

@section('content')
<style>
    .stats-section {
        margin-top: 50px !important;
    }
</style>

<div class="container-fluid">
    <!-- Statistiques -->
    <div class="row mb-4 stats-section">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100">
                <div class="card-body py-4">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-2">
                                En attente de confirmation
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_pending'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100">
                <div class="card-body py-4">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-2">
                                Confirmés
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_confirmed'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100">
                <div class="card-body py-4">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-2">
                                Livrés
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_delivered'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-truck-loading fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filtres</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('cement-manager.dashboard') }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">Statut</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">Tous les statuts</option>
                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>En attente de confirmation</option>
                        <option value="confirmed" {{ request('status') === 'confirmed' ? 'selected' : '' }}>Confirmés</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_start" class="form-label">Date début</label>
                    <input type="date" class="form-control" id="date_start" name="date_start" value="{{ request('date_start') }}">
                </div>
                <div class="col-md-3">
                    <label for="date_end" class="form-label">Date fin</label>
                    <input type="date" class="form-control" id="date_end" name="date_end" value="{{ request('date_end') }}">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Filtrer
                    </button>
                    <a href="{{ route('cement-manager.dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des commandes -->
    <div class="row">
        @forelse($orders as $order)
            <div class="col-12 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            Bon de commande #{{ $order->reference }}
                        </h6>
                        <span class="badge bg-{{ !$order->details->first()->creditSales->count() ? 'warning' : 'success' }}">
                            {{ !$order->details->first()->creditSales->count() ? 'En attente de confirmation' : 'Confirmé' }}
                        </span>
                    </div>
                    <div class="card-body">
                        @foreach($order->details as $detail)
                            <div class="border rounded p-3 mb-3">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <strong>Client:</strong> {{ $detail->customer?->name ?? 'Non défini' }}
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Destination:</strong> {{ $detail->destination?->name ?? 'Non définie' }}
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Produit:</strong> {{ $detail->product?->name ?? 'Non défini' }}
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <strong>Quantité totale:</strong> {{ number_format($detail->total_tonnage, 2) }} tonnes
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Prix unitaire:</strong> {{ number_format($detail->unit_price, 0, ',', ' ') }} FCFA
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Montant total:</strong> {{ number_format($detail->total_amount, 0, ',', ' ') }} FCFA
                                    </div>
                                </div>

                                <!-- Affectations -->
                                <div class="mt-3">
                                    <h6 class="font-weight-bold">Affectations</h6>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Camion</th>
                                                    <th>Chauffeur</th>
                                                    <th>Tonnage</th>
                                                    <th>Statut</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($detail->tripAssignments as $assignment)
                                                    <tr>
                                                        <td>{{ $assignment->truck->registration_number }}</td>
                                                        <td>{{ $assignment->driver?->fullName ?? 'Non défini' }}</td>
                                                        <td>{{ number_format($assignment->tonnage, 2) }} tonnes</td>
                                                        <td>
                                                            <span class="badge bg-{{ $assignment->status === 'completed' ? 'success' : 'warning' }}">
                                                                {{ $assignment->status === 'completed' ? 'Terminé' : 'En cours' }}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                @if(!$detail->creditSales->count() && $detail->tripAssignments->isNotEmpty())
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#confirmModal{{ $detail->id }}">
                                            <i class="fas fa-check"></i> Confirmer la livraison
                                        </button>
                                    </div>

                                    <!-- Modal de confirmation -->
                                    <div class="modal fade" id="confirmModal{{ $detail->id }}" tabindex="-1" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <form action="{{ route('cement-manager.assignments.confirm-delivery', ['assignment' => $detail->tripAssignments->first()->id]) }}" method="POST">
                                                    @csrf
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">Confirmer la livraison</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>Êtes-vous sûr de vouloir confirmer cette livraison ? Cette action :</p>
                                                        <ul>
                                                            <li>Créera une vente à crédit</li>
                                                            <li>Mettra à jour le stock</li>
                                                            <li>Libérera les camions et chauffeurs</li>
                                                        </ul>
                                                        <div class="mb-3">
                                                            <label for="notes" class="form-label">Notes (optionnel)</label>
                                                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                                        <button type="submit" class="btn btn-success">Confirmer</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Aucun bon de commande avec affectations trouvé
                </div>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    <div class="d-flex justify-content-center mt-4">
        {{ $orders->links() }}
    </div>
</div>
@endsection
