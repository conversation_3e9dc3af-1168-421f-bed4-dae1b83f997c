/**
 * GRADIS - Dashboard Comptable Avancé
 * Styles CSS pour l'interface avancée et interactive
 * 2025 MOMK-Solutions
 */

:root {
    /* Palette de couleurs avancée */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --danger-gradient: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
    --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    /* Couleurs modernes */
    --primary-modern: #6366f1;
    --secondary-modern: #8b5cf6;
    --success-modern: #10b981;
    --warning-modern: #f59e0b;
    --danger-modern: #ef4444;
    --info-modern: #06b6d4;
    
    /* Ombres avancées */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Animations */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Conteneur principal du dashboard avancé */
.advanced-dashboard {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

/* Header du dashboard avec effet glassmorphism */
.advanced-dashboard-header {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

.advanced-dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0.1;
    z-index: -1;
}

.advanced-dashboard-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.advanced-dashboard-subtitle {
    color: #64748b;
    font-size: 1.1rem;
    font-weight: 500;
}

/* Cartes de statistiques avancées */
.advanced-stat-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.advanced-stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-2xl);
    border-color: rgba(255, 255, 255, 0.4);
}

.advanced-stat-card:hover .advanced-stat-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.advanced-stat-card:hover .advanced-stat-value {
    color: var(--primary-modern);
    text-shadow: 0 0 10px rgba(99, 102, 241, 0.3);
}

.advanced-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transition: height var(--transition-normal);
}

.advanced-stat-card:hover::before {
    height: 8px;
}

.advanced-stat-card.success::before {
    background: var(--success-gradient);
}

.advanced-stat-card.warning::before {
    background: var(--warning-gradient);
}

.advanced-stat-card.danger::before {
    background: var(--danger-gradient);
}

.advanced-stat-card.info::before {
    background: var(--info-gradient);
}

.advanced-stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1rem;
    background: var(--primary-gradient);
    box-shadow: var(--shadow-md);
}

.advanced-stat-icon.success {
    background: var(--success-gradient);
}

.advanced-stat-icon.warning {
    background: var(--warning-gradient);
}

.advanced-stat-icon.danger {
    background: var(--danger-gradient);
}

.advanced-stat-icon.info {
    background: var(--info-gradient);
}

.advanced-stat-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.advanced-stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
    font-family: 'Inter', sans-serif;
}

.advanced-stat-trend {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    font-weight: 500;
}

.advanced-stat-trend.up {
    color: var(--success-modern);
}

.advanced-stat-trend.down {
    color: var(--danger-modern);
}

.advanced-stat-trend.neutral {
    color: var(--warning-modern);
}

.advanced-stat-trend i {
    margin-right: 0.25rem;
}

/* Graphiques avancés */
.advanced-chart-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.advanced-chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(99, 102, 241, 0.02) 0%, rgba(139, 92, 246, 0.02) 100%);
    pointer-events: none;
}

.advanced-chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;
}

.advanced-chart-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    display: flex;
    align-items: center;
}

.advanced-chart-title i {
    margin-right: 0.5rem;
    color: var(--primary-modern);
}

/* Filtres de période avancés */
.advanced-period-filters {
    display: flex;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.5);
    padding: 0.25rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.advanced-period-filter {
    padding: 0.5rem 1rem;
    border: none;
    background: transparent;
    border-radius: 8px;
    font-weight: 500;
    color: #64748b;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.875rem;
}

.advanced-period-filter:hover {
    background: rgba(255, 255, 255, 0.7);
    color: #1e293b;
}

.advanced-period-filter.active {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-md);
}

/* Zone de graphique */
.advanced-chart-area {
    position: relative;
    height: 400px;
    margin-top: 1rem;
}

.advanced-chart-area canvas {
    border-radius: 12px;
}

/* Indicateur de chargement */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(99, 102, 241, 0.2);
    border-left: 4px solid var(--primary-modern);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal de détails */
.detail-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease-in-out;
}

.modal-content {
    background: white;
    border-radius: 20px;
    max-width: 800px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: var(--shadow-2xl);
    animation: slideUp 0.3s ease-in-out;
}

.modal-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--primary-gradient);
    color: white;
}

.modal-header h3 {
    margin: 0;
    font-weight: 700;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: white;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background var(--transition-fast);
}

.close-modal:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 2rem;
    max-height: 60vh;
    overflow-y: auto;
}

/* Animations avancées */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(99, 102, 241, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(99, 102, 241, 0.8);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Classes d'animation */
.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
    animation: slideUp 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.4s ease-out;
}

.animate-bounce-in {
    animation: bounceIn 0.8s ease-out;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* Animations séquentielles */
.stagger-animation > * {
    opacity: 0;
    animation: slideUp 0.6s ease-out forwards;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }
.stagger-animation > *:nth-child(7) { animation-delay: 0.7s; }
.stagger-animation > *:nth-child(8) { animation-delay: 0.8s; }

/* Effets de particules et micro-interactions */
.particle-effect {
    position: relative;
    overflow: hidden;
}

.particle-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
    animation: particleFloat 10s ease-in-out infinite;
    pointer-events: none;
}

@keyframes particleFloat {
    0%, 100% {
        transform: translateX(0) translateY(0) rotate(0deg);
    }
    33% {
        transform: translateX(10px) translateY(-10px) rotate(120deg);
    }
    66% {
        transform: translateX(-5px) translateY(5px) rotate(240deg);
    }
}

/* Effets de ripple au clic */
.ripple-effect {
    position: relative;
    overflow: hidden;
}

.ripple-effect::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::after {
    width: 300px;
    height: 300px;
}

/* Effets de gradient animé */
.gradient-border {
    position: relative;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    animation: gradientShift 4s ease infinite;
    padding: 2px;
    border-radius: 12px;
}

.gradient-border > * {
    background: white;
    border-radius: 10px;
    position: relative;
    z-index: 1;
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Effets de typing pour les nombres */
.typing-effect {
    overflow: hidden;
    border-right: 2px solid var(--primary-modern);
    white-space: nowrap;
    animation: typing 2s steps(10, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink-caret {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: var(--primary-modern);
    }
}

/* Effets de morphing pour les icônes */
.morph-icon {
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.morph-icon:hover {
    transform: scale(1.2) rotate(360deg);
}

/* Effets de néon */
.neon-glow {
    text-shadow: 0 0 5px currentColor,
                 0 0 10px currentColor,
                 0 0 15px currentColor,
                 0 0 20px var(--primary-modern),
                 0 0 35px var(--primary-modern),
                 0 0 40px var(--primary-modern);
    animation: neonFlicker 2s ease-in-out infinite alternate;
}

@keyframes neonFlicker {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

/* Effets de parallax léger */
.parallax-element {
    transition: transform 0.1s ease-out;
}

/* Effets de glassmorphism avancé */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-effect:hover {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(25px) saturate(200%);
    border-color: rgba(255, 255, 255, 0.3);
}

/* Styles pour les exports et rapports */
.export-options {
    padding: 1.5rem;
}

.export-btn {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.export-btn:hover {
    border-color: var(--primary-modern);
    background: rgba(99, 102, 241, 0.05);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.export-btn i {
    font-size: 1.5rem;
    color: var(--primary-modern);
}

.export-btn span {
    font-weight: 600;
    color: #1e293b;
}

.export-btn small {
    color: #64748b;
    font-size: 0.75rem;
}

.predefined-reports {
    padding: 1.5rem;
}

.report-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 1rem;
    transition: all var(--transition-fast);
}

.report-item:hover {
    border-color: var(--primary-modern);
    background: rgba(99, 102, 241, 0.02);
}

.report-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
}

.report-info {
    flex: 1;
}

.report-title {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.report-description {
    font-size: 0.875rem;
    color: #64748b;
}

.generate-report {
    border-radius: 6px;
    font-weight: 500;
}

.print-options {
    padding: 1.5rem;
    background: rgba(248, 250, 252, 0.5);
    border-radius: 8px;
    margin-top: 1rem;
}

.print-controls {
    display: flex;
    gap: 0.5rem;
}

.print-controls .btn {
    border-radius: 8px;
    font-weight: 500;
}

/* Styles pour l'impression */
@media print {
    .no-print {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }

    .advanced-chart-container {
        break-inside: avoid;
        page-break-inside: avoid;
    }

    .advanced-stat-card {
        break-inside: avoid;
        page-break-inside: avoid;
        box-shadow: none;
        border: 1px solid #e2e8f0;
    }

    body {
        background: white !important;
    }

    .advanced-dashboard {
        background: white !important;
    }
}

/* Modal pour les rapports personnalisés */
.custom-report-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.custom-report-modal.active {
    opacity: 1;
    visibility: visible;
}

.custom-report-content {
    background: white;
    border-radius: 16px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-2xl);
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.custom-report-modal.active .custom-report-content {
    transform: scale(1);
}

.custom-report-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.custom-report-body {
    padding: 2rem;
}

.report-section {
    margin-bottom: 1.5rem;
}

.report-section h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background var(--transition-fast);
}

.checkbox-item:hover {
    background: rgba(99, 102, 241, 0.05);
}

/* Indicateur de progression pour les exports */
.export-progress {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--primary-modern);
    z-index: 10000;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
}

.export-progress.active {
    transform: translateX(0);
}

.progress-bar-container {
    width: 200px;
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.progress-bar-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 2px;
    transition: width var(--transition-normal);
}

/* Tooltip personnalisé */
.custom-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    z-index: 1000;
    pointer-events: none;
    animation: fadeIn 0.2s ease-in-out;
}

/* Styles pour le système de filtres avancés */
.filters-container {
    padding: 1.5rem;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(226, 232, 240, 0.5);
}

.filter-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #475569;
    margin-bottom: 0.5rem;
    display: block;
}

.advanced-filter {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    transition: all var(--transition-fast);
    background: white;
}

.advanced-filter:focus {
    border-color: var(--primary-modern);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
}

.filter-controls {
    display: flex;
    gap: 0.5rem;
}

.filter-controls .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all var(--transition-fast);
}

.filter-controls .btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.active-filters {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.filter-tag {
    background: var(--primary-gradient);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    animation: slideIn 0.3s ease-out;
}

.filter-tag .remove-filter {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    transition: background var(--transition-fast);
}

.filter-tag .remove-filter:hover {
    background: rgba(255, 255, 255, 0.2);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Indicateur de chargement pour les filtres */
.filter-loading {
    position: relative;
}

.filter-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid #e2e8f0;
    border-left: 2px solid var(--primary-modern);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Styles pour les widgets KPI avancés */
.mini-chart {
    margin-top: 1rem;
    height: 30px;
}

.progress-indicator {
    margin-top: 0.5rem;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    overflow: hidden;
}

.progress-indicator .progress-bar {
    height: 100%;
    background: var(--success-gradient);
    border-radius: 2px;
    transition: width 1s ease-in-out;
}

.circular-progress {
    width: 40px;
    height: 40px;
    margin-top: 0.5rem;
}

.circular-chart {
    width: 100%;
    height: 100%;
}

.circle-bg {
    fill: none;
    stroke: rgba(255, 255, 255, 0.3);
    stroke-width: 2;
}

.circle {
    fill: none;
    stroke: white;
    stroke-width: 2;
    stroke-linecap: round;
    animation: progress 1s ease-in-out forwards;
}

@keyframes progress {
    0% {
        stroke-dasharray: 0 100;
    }
}

.alert-indicator {
    display: flex;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.pulse-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
    animation: pulse 2s infinite;
}

.alert-indicator.high .pulse-dot {
    background: #ef4444;
}

.alert-indicator.medium .pulse-dot {
    background: #f59e0b;
}

.alert-indicator.low .pulse-dot {
    background: #10b981;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
}

/* Styles pour la liste des top produits */
.top-products-list {
    padding: 1rem 0;
}

.product-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.product-item:last-child {
    border-bottom: none;
}

.product-rank {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.875rem;
    margin-right: 1rem;
}

.product-info {
    flex: 1;
    margin-right: 1rem;
}

.product-name {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.product-stats {
    font-size: 0.875rem;
    color: #64748b;
}

.product-progress {
    width: 60px;
    height: 4px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    overflow: hidden;
}

.product-progress .progress-bar {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 2px;
    transition: width 1s ease-in-out;
}

.no-data {
    text-align: center;
    color: #64748b;
    font-style: italic;
    padding: 2rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .advanced-dashboard-title {
        font-size: 2rem;
    }

    .advanced-chart-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .advanced-period-filters {
        width: 100%;
        justify-content: center;
    }

    .advanced-chart-area {
        height: 300px;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .product-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .product-rank {
        margin-right: 0;
    }

    .product-progress {
        width: 100%;
    }
}
