// Script pour gérer la soumission du formulaire
$(document).ready(function() {
    console.log('Script de gestion de la soumission du formulaire chargé');
    
    // Intercepter la soumission du formulaire
    $('#saleForm').on('submit', function(e) {
        e.preventDefault(); // Empêcher la soumission normale du formulaire
        
        console.log('Soumission du formulaire interceptée');
        
        // Récupérer les données du formulaire
        var formData = $(this).serialize();
        
        // Afficher un indicateur de chargement
        Swal.fire({
            title: 'Traitement en cours...',
            text: 'Veuillez patienter pendant l\'enregistrement de la vente',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
        
        // Envoyer les données via AJAX
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                console.log('Réponse reçue:', response);
                
                // Fermer l'indicateur de chargement
                Swal.close();
                
                // Afficher un message de succès
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Vente enregistrée avec succès!',
                        text: response.message,
                        confirmButtonText: 'OK'
                    }).then((result) => {
                        // Rediriger vers la liste des ventes
                        window.location.href = '/cement-manager/sales';
                    });
                } else {
                    // Afficher un message d'erreur
                    Swal.fire({
                        icon: 'error',
                        title: 'Erreur',
                        text: response.message || 'Une erreur est survenue lors de l\'enregistrement de la vente',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('Erreur AJAX:', error);
                
                // Fermer l'indicateur de chargement
                Swal.close();
                
                // Afficher un message d'erreur
                var errorMessage = 'Une erreur est survenue lors de l\'enregistrement de la vente';
                
                // Tenter de récupérer le message d'erreur du serveur
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMessage = response.message;
                    } else if (response.error) {
                        errorMessage = response.error;
                    }
                } catch (e) {
                    console.error('Erreur lors du parsing de la réponse:', e);
                }
                
                Swal.fire({
                    icon: 'error',
                    title: 'Erreur',
                    text: errorMessage,
                    confirmButtonText: 'OK'
                });
            }
        });
    });
});
