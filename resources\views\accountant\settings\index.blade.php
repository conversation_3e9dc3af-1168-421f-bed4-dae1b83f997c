@extends('layouts.accountant')

@section('title', 'Paramètres')

@section('css')
<link href="{{ asset('assets/libs/mohithg-switchery/switchery.min.css') }}" rel="stylesheet" type="text/css" />
@endsection

@section('content')
<div class="container-fluid">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('accountant.dashboard') }}">Tableau de bord</a></li>
                        <li class="breadcrumb-item active">Paramètres</li>
                    </ol>
                </div>
                <h4 class="page-title">Paramètres</h4>
            </div>
        </div>
    </div>
    <!-- end page title -->

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <!-- Onglets -->
                    <ul class="nav nav-tabs nav-bordered mb-3" role="tablist">
                        <li class="nav-item" role="presentation">
                            <a href="#profile-tab" data-bs-toggle="tab" aria-expanded="true" class="nav-link active" aria-selected="true" role="tab">
                                <i class="mdi mdi-account me-1"></i> Profil
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a href="#security-tab" data-bs-toggle="tab" aria-expanded="false" class="nav-link" aria-selected="false" tabindex="-1" role="tab">
                                <i class="mdi mdi-security me-1"></i> Sécurité
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a href="#preferences-tab" data-bs-toggle="tab" aria-expanded="false" class="nav-link" aria-selected="false" tabindex="-1" role="tab">
                                <i class="mdi mdi-cog me-1"></i> Préférences
                            </a>
                        </li>
                    </ul>

                    <!-- Contenu des onglets -->
                    <div class="tab-content">
                        <!-- Profil -->
                        <div class="tab-pane show active" id="profile-tab" role="tabpanel">
                            <form action="{{ route('accountant.settings.profile.update') }}" method="POST" class="needs-validation" novalidate>
                                @csrf
                                @method('PUT')
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Nom complet</label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', auth()->user()->name) }}" required>
                                            @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">Adresse email</label>
                                            <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email', auth()->user()->email) }}" required>
                                            @error('email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">Téléphone</label>
                                            <input type="tel" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone', auth()->user()->phone) }}">
                                            @error('phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="text-end">
                                    <button type="submit" class="btn btn-success mt-2">
                                        <i class="mdi mdi-content-save me-1"></i> Enregistrer les modifications
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Sécurité -->
                        <div class="tab-pane" id="security-tab" role="tabpanel">
                            <form action="{{ route('accountant.settings.password.update') }}" method="POST" class="needs-validation" novalidate>
                                @csrf
                                @method('PUT')
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="current_password" class="form-label">Mot de passe actuel</label>
                                            <input type="password" class="form-control @error('current_password') is-invalid @enderror" id="current_password" name="current_password" required>
                                            @error('current_password')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password" class="form-label">Nouveau mot de passe</label>
                                            <input type="password" class="form-control @error('password') is-invalid @enderror" id="password" name="password" required>
                                            @error('password')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password_confirmation" class="form-label">Confirmer le nouveau mot de passe</label>
                                            <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-end">
                                    <button type="submit" class="btn btn-success mt-2">
                                        <i class="mdi mdi-content-save me-1"></i> Mettre à jour le mot de passe
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Préférences -->
                        <div class="tab-pane" id="preferences-tab" role="tabpanel">
                            <form action="{{ route('accountant.settings.preferences.update') }}" method="POST" class="needs-validation" novalidate>
                                @csrf
                                @method('PUT')
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="default_currency" class="form-label">Devise par défaut</label>
                                            <select class="form-select @error('settings.default_currency') is-invalid @enderror" id="default_currency" name="settings[default_currency]">
                                                <option value="XOF">FCFA (XOF)</option>
                                            </select>
                                            @error('settings.default_currency')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="tax_rate" class="form-label">Taux de TVA par défaut (%)</label>
                                            <input type="number" class="form-control @error('settings.tax_rate') is-invalid @enderror" id="tax_rate" name="settings[tax_rate]" value="{{ old('settings.tax_rate', $settings->where('key', 'tax_rate')->first()?->value ?? 18) }}" min="0" max="100" step="0.01">
                                            @error('settings.tax_rate')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="invoice_prefix" class="form-label">Préfixe des factures</label>
                                            <input type="text" class="form-control @error('settings.invoice_prefix') is-invalid @enderror" id="invoice_prefix" name="settings[invoice_prefix]" value="{{ old('settings.invoice_prefix', $settings->where('key', 'invoice_prefix')->first()?->value ?? 'FAC-') }}">
                                            @error('settings.invoice_prefix')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_terms" class="form-label">Conditions de paiement (jours)</label>
                                            <input type="number" class="form-control @error('settings.payment_terms') is-invalid @enderror" id="payment_terms" name="settings[payment_terms]" value="{{ old('settings.payment_terms', $settings->where('key', 'payment_terms')->first()?->value ?? 30) }}" min="0">
                                            @error('settings.payment_terms')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="text-end">
                                    <button type="submit" class="btn btn-success mt-2">
                                        <i class="mdi mdi-content-save me-1"></i> Enregistrer les préférences
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="{{ asset('assets/libs/mohithg-switchery/switchery.min.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Active l'onglet spécifié dans l'URL si présent
        var hash = window.location.hash;
        if (hash) {
            $('.nav-tabs a[href="' + hash + '"]').tab('show');
        }

        // Met à jour l'URL quand un onglet est sélectionné
        $('.nav-tabs a').on('shown.bs.tab', function (e) {
            window.location.hash = e.target.hash;
        });

        // Validation des formulaires
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function (form) {
            form.addEventListener('submit', function (event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    });
</script>
@endsection
