@extends('layouts.accountant')

@section('title', 'Détails du paiement')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="card-title mb-0">Détails du paiement #{{ $payment->reference }}</h4>
                        <a href="{{ route('accountant.payments.index') }}" class="btn btn-secondary">
                            <i data-feather="arrow-left"></i> Retour à la liste
                        </a>
                    </div>

                    <div class="row">
                        <!-- Informations du paiement -->
                        <div class="col-md-6">
                            <div class="card border">
                                <div class="card-body">
                                    <h5 class="card-title">Informations du paiement</h5>
                                    <div class="mt-3">
                                        <p class="mb-2">
                                            <strong>Référence:</strong> {{ $payment->reference }}
                                        </p>
                                        <p class="mb-2">
                                            <strong>Date:</strong> {{ $payment->created_at->format('d/m/Y H:i') }}
                                        </p>
                                        <p class="mb-2">
                                            <strong>Montant:</strong> 
                                            <span class="currency">{{ number_format($payment->amount, 0, ',', ' ') }}</span>
                                        </p>
                                        <p class="mb-2">
                                            <strong>Méthode:</strong> {{ $payment->method }}
                                        </p>
                                        <p class="mb-2">
                                            <strong>Statut:</strong>
                                            @if($payment->status === 'completed')
                                                <span class="badge bg-success">Complété</span>
                                            @elseif($payment->status === 'pending')
                                                <span class="badge bg-warning">En attente</span>
                                            @elseif($payment->status === 'failed')
                                                <span class="badge bg-danger">Échoué</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Informations du client -->
                        <div class="col-md-6">
                            <div class="card border">
                                <div class="card-body">
                                    <h5 class="card-title">Informations du client</h5>
                                    <div class="mt-3">
                                        <p class="mb-2">
                                            <strong>Nom:</strong> {{ $payment->order->user->name }}
                                        </p>
                                        <p class="mb-2">
                                            <strong>Email:</strong> {{ $payment->order->user->email }}
                                        </p>
                                        <p class="mb-2">
                                            <strong>Téléphone:</strong> {{ $payment->order->user->phone }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Détails de la commande -->
                    <div class="card border mt-4">
                        <div class="card-body">
                            <h5 class="card-title">Détails de la commande #{{ $payment->order->number }}</h5>
                            <div class="table-responsive mt-3">
                                <table class="table table-centered">
                                    <thead>
                                        <tr>
                                            <th>Produit</th>
                                            <th>Quantité</th>
                                            <th>Prix unitaire</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($payment->order->items as $item)
                                            <tr>
                                                <td>{{ $item->product->name }}</td>
                                                <td>{{ $item->quantity }}</td>
                                                <td class="currency">{{ number_format($item->unit_price, 0, ',', ' ') }}</td>
                                                <td class="currency">{{ number_format($item->total_price, 0, ',', ' ') }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td colspan="3" class="text-end"><strong>Sous-total</strong></td>
                                            <td class="currency"><strong>{{ number_format($payment->order->subtotal, 0, ',', ' ') }}</strong></td>
                                        </tr>
                                        @if($payment->order->tax > 0)
                                            <tr>
                                                <td colspan="3" class="text-end">TVA</td>
                                                <td class="currency">{{ number_format($payment->order->tax, 0, ',', ' ') }}</td>
                                            </tr>
                                        @endif
                                        @if($payment->order->discount > 0)
                                            <tr>
                                                <td colspan="3" class="text-end">Remise</td>
                                                <td class="currency">-{{ number_format($payment->order->discount, 0, ',', ' ') }}</td>
                                            </tr>
                                        @endif
                                        <tr>
                                            <td colspan="3" class="text-end"><strong>Total</strong></td>
                                            <td class="currency"><strong>{{ number_format($payment->order->total, 0, ',', ' ') }}</strong></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Initialiser les icônes Feather
    feather.replace();

    // Formatter les montants en FCFA
    document.querySelectorAll('.currency').forEach(element => {
        const amount = parseFloat(element.textContent.replace(/[^\d.-]/g, ''));
        element.textContent = new Intl.NumberFormat('fr-FR').format(amount) + ' FCFA';
    });
</script>
@endpush
