// Script renforcé pour supprimer le fond bleu dégradé définitivement

// Fonction pour supprimer le fond bleu avec une approche plus agressive
function removeBlueBackground() {
    // Créer une feuille de style pour remplacer tous les dégradés bleus
    if (!document.getElementById('override-blue-styles')) {
        const styleSheet = document.createElement('style');
        styleSheet.id = 'override-blue-styles';
        styleSheet.innerHTML = `
            html, body, body::before, body::after {
                background: white !important;
                background-color: white !important;
                background-image: none !important;
                background-attachment: unset !important;
                background-position: unset !important;
                background-repeat: unset !important;
                background-size: unset !important;
            }
            
            .main-card, .main-card::before, .main-card::after {
                background: white !important;
                background-color: white !important;
                background-image: none !important;
                color: #333 !important;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
                animation: none !important;
            }
            
            @keyframes cardGlow {
                0%, 50%, 100% {
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
                    background: white !important;
                    background-image: none !important;
                }
            }
            
            /* Assurer que les styles des colonnes Type et Validation restent intacts */
            .sales-table th:nth-child(6),
            .sales-table th:nth-child(8) {
                background-color: #1976D2 !important;
                color: white !important;
            }
            
            .sales-table td:nth-child(6),
            .sales-table td:nth-child(8),
            .type-column,
            .validation-column {
                background-color: rgba(25, 118, 210, 0.1) !important;
            }
        `;
        document.head.appendChild(styleSheet);
    }
    
    // Supprimer le fond bleu du body et html avec !important
    document.documentElement.style.cssText += 'background: white !important; background-image: none !important; background-color: white !important;';
    document.body.style.cssText += 'background: white !important; background-image: none !important; background-color: white !important;';
    
    // Supprimer tous les styles linéaires potentiels
    const allElements = document.querySelectorAll('*');
    allElements.forEach(el => {
        try {
            const style = window.getComputedStyle(el);
            if (style.backgroundImage.includes('linear-gradient')) {
                // Vérifier spécifiquement le dégradé bleu problématique
                if (style.backgroundImage.includes('rgb(30, 136, 229)') && 
                    style.backgroundImage.includes('rgb(13, 71, 161)')) {
                    const currentStyle = el.getAttribute('style') || '';
                    el.setAttribute('style', currentStyle + '; background: white !important; background-image: none !important;');
                }
            }
        } catch (e) {
            // Ignorer les erreurs potentielles
        }
    });
    
    // Supprimer le fond bleu de la carte principale
    const mainCard = document.querySelector('.main-card');
    if (mainCard) {
        mainCard.style.cssText += 'background: white !important; background-image: none !important; color: #333 !important; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important; animation: none !important;';
    }
    
    // Cibler spécifiquement les sections avec des filtres
    const filtersSection = document.querySelector('.filters-section');
    if (filtersSection) {
        filtersSection.style.cssText += 'background: white !important; background-image: none !important; color: #333 !important;';
        
        // Adapter les textes dans la section des filtres
        const filterHeadings = filtersSection.querySelectorAll('h5, h6');
        filterHeadings.forEach(heading => {
            heading.style.color = '#1976D2';
        });
    }
    
    // Adapter les textes au fond blanc
    const mainCardHeadings = document.querySelectorAll('.main-card h1, .main-card .text-white, .main-card .text-light');
    mainCardHeadings.forEach(heading => {
        heading.style.color = '#1976D2';
    });
    
    const mainCardParagraphs = document.querySelectorAll('.main-card p, .main-card .text-white-50, .main-card .text-light-50');
    mainCardParagraphs.forEach(paragraph => {
        paragraph.style.color = '#666';
    });
    
    // Vérifier si le CSS white-theme.css est chargé
    const cssLoaded = Array.from(document.styleSheets).some(sheet => {
        try {
            return sheet.href && sheet.href.includes('white-theme.css');
        } catch (e) {
            return false;
        }
    });
    
    // Si le CSS n'est pas chargé, l'injecter manuellement
    if (!cssLoaded && !document.getElementById('dynamic-white-theme')) {
        const link = document.createElement('link');
        link.id = 'dynamic-white-theme';
        link.rel = 'stylesheet';
        link.href = '/css/white-theme.css';
        document.head.appendChild(link);
        console.log('White theme CSS injected dynamically');
    }
    
    // Afficher des informations de débogage
    console.log('Background removal executed');
    console.log('CSS file loaded:', cssLoaded ? 'Yes' : 'No');
    
    // Vérifier le background actuel du body
    const bodyStyle = window.getComputedStyle(document.body);
    console.log('Current body background:', bodyStyle.background);
    console.log('Current body background-image:', bodyStyle.backgroundImage);
}

// Exécuter immédiatement
removeBlueBackground();

// Exécuter après le chargement du DOM
document.addEventListener('DOMContentLoaded', function() {
    removeBlueBackground();
    console.log('DOM loaded - Background removal executed');
});

// Exécuter après le chargement complet de la page
window.addEventListener('load', function() {
    removeBlueBackground();
    console.log('Page fully loaded - Background removal executed');
    
    // Afficher des informations de débogage dans la console
    console.log('Debug Info');
    console.log('JS loaded: Yes');
    
    const cssLoaded = Array.from(document.styleSheets).some(sheet => {
        try {
            return sheet.href && sheet.href.includes('white-theme.css');
        } catch (e) {
            return false;
        }
    });
    console.log('CSS loaded:', cssLoaded ? 'Yes' : 'No');
    
    const bodyStyle = window.getComputedStyle(document.body);
    console.log('Background:', bodyStyle.backgroundImage);
    
    console.log('CSS file:', cssLoaded ? 'Loaded' : 'Not loaded');
});

// Exécuter périodiquement pour s'assurer que le fond reste blanc
setInterval(removeBlueBackground, 500);

// Exécuter lorsque le DOM est modifié (pour les éléments ajoutés dynamiquement)
const observer = new MutationObserver(function(mutations) {
    removeBlueBackground();
});

// Observer les changements dans le document
observer.observe(document.documentElement, {
    childList: true,
    subtree: true
});
