$(document).ready(function() {
    // Gestion du retrait d'assignation
    $('[id^="removeAssignment"]').on('click', function() {
        const driverId = $(this).attr('id').replace('removeAssignment', '');
        const modal = $(this).closest('.modal');
        const form = modal.find('form');
        const select = modal.find('select');
        const submitButton = modal.find('button[type="submit"]');
        
        Swal.fire({
            title: 'Êtes-vous sûr ?',
            text: "Voulez-vous vraiment retirer l'assignation actuelle ?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Oui, retirer',
            cancelButtonText: 'Annuler'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '/accountant/driver-truck-assignments/unassign',
                    method: 'POST',
                    data: {
                        driver_id: driverId,
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            // Activer le formulaire
                            select.prop('disabled', false);
                            submitButton.prop('disabled', false);
                            
                            Swal.fire({
                                icon: 'success',
                                title: 'Succès',
                                text: response.message,
                                showConfirmButton: false,
                                timer: 1500
                            }).then(function() {
                                // Recharger la page pour mettre à jour les données
                                window.location.reload();
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Erreur',
                                text: response.message || 'Une erreur est survenue lors du retrait de l\'assignation.'
                            });
                        }
                    },
                    error: function(xhr) {
                        const message = xhr.responseJSON?.message || 'Une erreur est survenue lors du retrait de l\'assignation.';
                        Swal.fire({
                            icon: 'error',
                            title: 'Erreur',
                            text: message
                        });
                    }
                });
            }
        });
    });

    // Gestion de l'assignation
    $('.assign-vehicle-form').on('submit', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const submitButton = form.find('button[type="submit"]');
        const modalId = form.closest('.modal').attr('id');
        
        // Désactiver le bouton pendant la soumission
        submitButton.prop('disabled', true);
        
        $.ajax({
            url: form.attr('action'),
            method: 'POST',
            data: form.serialize(),
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    // Fermer le modal
                    $(`#${modalId}`).modal('hide');
                    
                    // Afficher le message de succès
                    Swal.fire({
                        icon: 'success',
                        title: 'Succès!',
                        text: response.message,
                        showConfirmButton: false,
                        timer: 1500
                    }).then(function() {
                        // Recharger la page pour mettre à jour les données
                        window.location.reload();
                    });
                } else {
                    // Réactiver le bouton en cas d'erreur
                    submitButton.prop('disabled', false);
                    
                    Swal.fire({
                        icon: 'error',
                        title: 'Erreur',
                        text: response.message || 'Une erreur est survenue lors de l\'assignation du véhicule.'
                    });
                }
            },
            error: function(xhr) {
                // Réactiver le bouton en cas d'erreur
                submitButton.prop('disabled', false);
                
                const message = xhr.responseJSON?.message || 'Une erreur est survenue lors de l\'assignation du véhicule.';
                Swal.fire({
                    icon: 'error',
                    title: 'Erreur',
                    text: message
                });
            },
            complete: function() {
                // S'assurer que le bouton est toujours réactivé
                submitButton.prop('disabled', false);
            }
        });
    });
});
