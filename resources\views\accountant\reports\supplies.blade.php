@extends('layouts.app')

@section('title', 'Rapport Stocks')

@section('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<style>
    :root {
        --primary-blue: #3b82f6;
        --primary-green: #10b981;
        --primary-orange: #f59e0b;
        --primary-purple: #8b5cf6;
        --primary-red: #ef4444;
        --text-dark: #1f2937;
        --text-light: #6b7280;
        --bg-light: #f8fafc;
        --border-light: #e5e7eb;
        --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .supplies-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 20px;
    }

    .dashboard-header {
        text-align: center;
        margin-bottom: 40px;
        padding: 30px 0;
    }

    .supplies-main-title {
        font-size: 3rem;
        font-weight: 800;
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-purple) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 10px;
        text-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .supplies-subtitle {
        font-size: 1.2rem;
        color: var(--text-light);
        font-weight: 500;
        margin: 0;
    }

    .enhanced-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: var(--shadow-large);
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .enhanced-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    .card-header-enhanced {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
        padding: 25px 30px;
        border-bottom: 1px solid rgba(59, 130, 246, 0.1);
        display: flex;
        justify-content: between;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;
    }

    .card-title-enhanced {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--text-dark);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.875rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 6px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-badge.info {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.2) 100%);
        color: var(--primary-blue);
        border: 1px solid rgba(59, 130, 246, 0.3);
    }

    .status-badge.success {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.2) 100%);
        color: var(--primary-green);
        border: 1px solid rgba(16, 185, 129, 0.3);
    }

    .status-badge.warning {
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.2) 100%);
        color: var(--primary-orange);
        border: 1px solid rgba(245, 158, 11, 0.3);
    }

    .status-badge.danger {
        background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.2) 100%);
        color: var(--primary-red);
        border: 1px solid rgba(239, 68, 68, 0.3);
    }

    .period-selector {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }

    .period-btn {
        padding: 10px 20px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        background: rgba(255, 255, 255, 0.7);
        color: var(--text-dark);
    }

    .period-btn:hover {
        background: rgba(59, 130, 246, 0.1);
        border-color: rgba(59, 130, 246, 0.3);
        color: var(--primary-blue);
        text-decoration: none;
        transform: translateY(-2px);
    }

    .period-btn.active {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-purple) 100%);
        color: white;
        border-color: transparent;
        box-shadow: var(--shadow-medium);
    }

    .chart-container {
        position: relative;
        height: 350px;
        padding: 20px;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 51, 234, 0.02) 100%);
        border-radius: 12px;
        border: 1px solid rgba(59, 130, 246, 0.1);
    }

    .table-enhanced {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: var(--shadow-soft);
        border: none;
    }

    .table-enhanced thead {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-purple) 100%);
    }

    .table-enhanced thead th {
        color: white;
        font-weight: 700;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        padding: 20px 15px;
        border: none;
        position: relative;
    }

    .table-enhanced tbody tr {
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .table-enhanced tbody tr:hover {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
        transform: scale(1.01);
    }

    .table-enhanced tbody td {
        padding: 18px 15px;
        vertical-align: middle;
        border: none;
        font-weight: 500;
    }

    .metric-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
        border-radius: 20px;
        padding: 25px;
        text-align: center;
        box-shadow: var(--shadow-medium);
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        height: 100%;
    }

    .metric-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-large);
    }

    .metric-value {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 8px;
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-purple) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .metric-label {
        font-size: 0.875rem;
        color: var(--text-light);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-enhanced {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-purple) 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        color: white;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-medium);
    }

    .btn-enhanced:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow-large);
        color: white;
        text-decoration: none;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: var(--text-light);
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    .empty-state h3 {
        font-size: 1.5rem;
        margin-bottom: 10px;
        color: var(--text-dark);
    }

    .empty-state p {
        font-size: 1rem;
        margin: 0;
    }

    @media (max-width: 768px) {
        .supplies-main-title {
            font-size: 2rem;
        }
        
        .card-header-enhanced {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .period-selector {
            width: 100%;
            justify-content: center;
        }
        
        .metric-value {
            font-size: 2rem;
        }
    }
</style>
@endsection

@section('content')
<div class="supplies-container">
    <!-- En-tête du tableau de bord -->
    <div class="dashboard-header animate__animated animate__fadeInDown">
        <h1 class="supplies-main-title">Rapport Stocks</h1>
        <p class="supplies-subtitle">Gestion et suivi des approvisionnements par période</p>
    </div>

    <div class="row">
        <div class="col-12">
            <!-- En-tête avec navigation -->
            <div class="enhanced-card animate__animated animate__fadeInDown" style="margin-bottom: 32px;">
                <div class="card-header-enhanced">
                    <h2 class="card-title-enhanced">
                        <i class="fas fa-boxes" style="color: var(--primary-blue);"></i>
                        Filtres et Navigation
                    </h2>
                    <div class="status-badge info">
                        <i class="fas fa-calendar-alt"></i>
                        Période: {{ ucfirst($period) }}
                    </div>
                </div>
                <div class="card-body" style="padding: 30px;">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="period-selector">
                                <a href="{{ route('accountant.reports.supplies', ['period' => 'today']) }}"
                                   class="period-btn {{ $period === 'today' ? 'active' : '' }}">
                                   Aujourd'hui
                                </a>
                                <a href="{{ route('accountant.reports.supplies', ['period' => 'week']) }}"
                                   class="period-btn {{ $period === 'week' ? 'active' : '' }}">
                                   Cette semaine
                                </a>
                                <a href="{{ route('accountant.reports.supplies', ['period' => 'month']) }}"
                                   class="period-btn {{ $period === 'month' ? 'active' : '' }}">
                                   Ce mois
                                </a>
                                <a href="{{ route('accountant.reports.supplies', ['period' => 'year']) }}"
                                   class="period-btn {{ $period === 'year' ? 'active' : '' }}">
                                   Cette année
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ route('accountant.reports.index') }}" class="btn-enhanced">
                                <i class="fas fa-arrow-left"></i>
                                Retour aux rapports
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Métriques principales -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="metric-card animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                        <div class="metric-value" id="totalSuppliesValue">{{ $supplies->sum('count') }}</div>
                        <div class="metric-label">
                            <i class="fas fa-boxes" style="margin-right: 5px;"></i>
                            Total Approvisionnements
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="metric-card animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                        <div class="metric-value" id="totalAmountValue">{{ number_format($supplies->sum('total'), 0, ',', ' ') }} FCFA</div>
                        <div class="metric-label">
                            <i class="fas fa-money-bill-wave" style="margin-right: 5px;"></i>
                            Montant Total
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="metric-card animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                        <div class="metric-value" id="totalTonnageValue">{{ number_format($supplies->sum('tonnage'), 2, ',', ' ') }} T</div>
                        <div class="metric-label">
                            <i class="fas fa-weight-hanging" style="margin-right: 5px;"></i>
                            Tonnage Total
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="metric-card animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                        <div class="metric-value" id="averageSupplyValue">{{ $supplies->count() > 0 ? number_format($supplies->sum('total') / $supplies->sum('count'), 0, ',', ' ') : 0 }} FCFA</div>
                        <div class="metric-label">
                            <i class="fas fa-calculator" style="margin-right: 5px;"></i>
                            Coût Moyen
                        </div>
                    </div>
                </div>
            </div>

            <!-- Graphique des approvisionnements -->
            <div class="enhanced-card animate__animated animate__fadeInLeft" style="margin-bottom: 32px; animation-delay: 0.5s;">
                <div class="card-header-enhanced">
                    <h3 class="card-title-enhanced">
                        <i class="fas fa-chart-area" style="color: var(--primary-blue);"></i>
                        Évolution des Approvisionnements
                    </h3>
                    <div class="status-badge info">
                        <i class="fas fa-eye"></i>
                        Graphique interactif
                    </div>
                </div>
                <div class="chart-container" style="height: 350px; padding: 20px; background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 51, 234, 0.02) 100%); border-radius: 12px; border: 1px solid rgba(59, 130, 246, 0.1);">
                    <canvas id="suppliesChart"></canvas>
                </div>
            </div>

            <!-- Tableau détaillé -->
            <div class="enhanced-card animate__animated animate__fadeInRight" style="animation-delay: 0.7s;">
                <div class="card-header-enhanced">
                    <h3 class="card-title-enhanced">
                        <i class="fas fa-table" style="color: var(--primary-blue);"></i>
                        Détails des Approvisionnements
                    </h3>
                    <div class="status-badge info">
                        <i class="fas fa-list"></i>
                        {{ $supplies->count() }} entrées
                    </div>
                </div>
                <div class="table-responsive" style="padding: 0;">
                    @if($supplies->count() > 0)
                        <table class="table table-enhanced mb-0">
                            <thead>
                                <tr>
                                    <th>
                                        <i class="fas fa-calendar-day" style="margin-right: 8px; color: rgba(255,255,255,0.8);"></i>
                                        Date
                                    </th>
                                    <th>
                                        <i class="fas fa-boxes" style="margin-right: 8px; color: rgba(255,255,255,0.8);"></i>
                                        Nombre d'approvisionnements
                                    </th>
                                    <th>
                                        <i class="fas fa-weight-hanging" style="margin-right: 8px; color: rgba(255,255,255,0.8);"></i>
                                        Tonnage
                                    </th>
                                    <th>
                                        <i class="fas fa-coins" style="margin-right: 8px; color: rgba(255,255,255,0.8);"></i>
                                        Montant total
                                    </th>
                                    <th>
                                        <i class="fas fa-chart-line" style="margin-right: 8px; color: rgba(255,255,255,0.8);"></i>
                                        Statut
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($supplies as $supply)
                                    <tr>
                                        <td>
                                            <strong>{{ \Carbon\Carbon::parse($supply->date)->format('d/m/Y') }}</strong>
                                            <br>
                                            <small style="color: var(--text-light);">{{ \Carbon\Carbon::parse($supply->date)->format('l') }}</small>
                                        </td>
                                        <td>
                                            <div class="status-badge info" style="font-size: 11px;">
                                                <i class="fas fa-boxes"></i>
                                                {{ $supply->count }} approvisionnements
                                            </div>
                                        </td>
                                        <td>
                                            <strong style="color: var(--primary-orange); font-size: 16px;">
                                                {{ number_format($supply->tonnage, 2, ',', ' ') }} T
                                            </strong>
                                        </td>
                                        <td>
                                            <strong style="color: var(--primary-green); font-size: 16px;">
                                                {{ number_format($supply->total, 0, ',', ' ') }} FCFA
                                            </strong>
                                        </td>
                                        <td>
                                            @if($supply->total > 1000000)
                                                <div class="status-badge success">
                                                    <i class="fas fa-arrow-up"></i>
                                                    Élevé
                                                </div>
                                            @elseif($supply->total > 500000)
                                                <div class="status-badge warning">
                                                    <i class="fas fa-minus"></i>
                                                    Moyen
                                                </div>
                                            @else
                                                <div class="status-badge info">
                                                    <i class="fas fa-arrow-down"></i>
                                                    Faible
                                                </div>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @else
                        <div class="empty-state">
                            <i class="fas fa-boxes"></i>
                            <h3>Aucun approvisionnement trouvé</h3>
                            <p>Il n'y a pas d'approvisionnements pour la période sélectionnée.</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Bouton de retour -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="text-center">
                        <a href="{{ route('accountant.reports.index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left"></i>
                            Retour aux rapports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Configuration des données pour les graphiques
    const suppliesData = {
        labels: @json($supplies->pluck('date')->map(function($date) { return \Carbon\Carbon::parse($date)->format('d/m'); })),
        counts: @json($supplies->pluck('count')),
        totals: @json($supplies->pluck('total')),
        tonnages: @json($supplies->pluck('tonnage')),
        totalSupplies: {{ $supplies->sum('count') }},
        totalAmount: {{ $supplies->sum('total') }},
        totalTonnage: {{ $supplies->sum('tonnage') }},
        averageCost: {{ $supplies->count() > 0 ? round($supplies->sum('total') / $supplies->sum('count'), 0) : 0 }},
        activeDays: {{ $supplies->count() }}
    };

    // Configuration du graphique principal
    const ctx = document.getElementById('suppliesChart').getContext('2d');
    const suppliesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: suppliesData.labels,
            datasets: [
                {
                    label: 'Montant (FCFA)',
                    data: suppliesData.totals,
                    borderColor: 'rgba(59, 130, 246, 1)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    yAxisID: 'y'
                },
                {
                    label: 'Nombre d\'approvisionnements',
                    data: suppliesData.counts,
                    borderColor: 'rgba(147, 51, 234, 1)',
                    backgroundColor: 'rgba(147, 51, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgba(147, 51, 234, 1)',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    yAxisID: 'y1'
                },
                {
                    label: 'Tonnage (T)',
                    data: suppliesData.tonnages,
                    borderColor: 'rgba(245, 158, 11, 1)',
                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: 'rgba(245, 158, 11, 1)',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    yAxisID: 'y2'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            plugins: {
                title: {
                    display: true,
                    text: 'Évolution des Approvisionnements',
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    color: '#1f2937'
                },
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            size: 12,
                            weight: '600'
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    titleColor: '#1f2937',
                    bodyColor: '#1f2937',
                    borderColor: 'rgba(59, 130, 246, 0.2)',
                    borderWidth: 1,
                    cornerRadius: 12,
                    displayColors: true,
                    callbacks: {
                        title: function(context) {
                            return 'Date: ' + context[0].label;
                        },
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.dataset.label === 'Montant (FCFA)') {
                                label += new Intl.NumberFormat('fr-FR').format(context.parsed.y) + ' FCFA';
                            } else if (context.dataset.label === 'Tonnage (T)') {
                                label += new Intl.NumberFormat('fr-FR', {minimumFractionDigits: 2}).format(context.parsed.y) + ' T';
                            } else {
                                label += context.parsed.y;
                            }
                            return label;
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Période',
                        font: {
                            size: 14,
                            weight: 'bold'
                        }
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Montant (FCFA)',
                        font: {
                            size: 12,
                            weight: 'bold'
                        },
                        color: 'rgba(59, 130, 246, 1)'
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        callback: function(value) {
                            return new Intl.NumberFormat('fr-FR').format(value) + ' FCFA';
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Nombre',
                        font: {
                            size: 12,
                            weight: 'bold'
                        },
                        color: 'rgba(147, 51, 234, 1)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                },
                y2: {
                    type: 'linear',
                    display: false,
                    position: 'right',
                }
            },
            elements: {
                point: {
                    hoverBackgroundColor: '#ffffff',
                    hoverBorderWidth: 3
                }
            }
        }
    });

    // Animation des compteurs
    function animateCounter(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);
        const timer = setInterval(() => {
            start += increment;
            if (start >= target) {
                element.textContent = target;
                clearInterval(timer);
            } else {
                element.textContent = Math.floor(start);
            }
        }, 16);
    }

    function animateCounterFCFA(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);
        const timer = setInterval(() => {
            start += increment;
            if (start >= target) {
                element.textContent = new Intl.NumberFormat('fr-FR').format(target) + ' FCFA';
                clearInterval(timer);
            } else {
                element.textContent = new Intl.NumberFormat('fr-FR').format(Math.floor(start)) + ' FCFA';
            }
        }, 16);
    }

    function animateCounterTonnage(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);
        const timer = setInterval(() => {
            start += increment;
            if (start >= target) {
                element.textContent = new Intl.NumberFormat('fr-FR', {minimumFractionDigits: 2}).format(target) + ' T';
                clearInterval(timer);
            } else {
                element.textContent = new Intl.NumberFormat('fr-FR', {minimumFractionDigits: 2}).format(start) + ' T';
            }
        }, 16);
    }

    // Initialisation au chargement de la page
    document.addEventListener('DOMContentLoaded', function() {
        // Animer les compteurs des cartes avec les vraies données
        const totalSuppliesCard = document.getElementById('totalSuppliesValue');
        const totalAmountCard = document.getElementById('totalAmountValue');
        const totalTonnageCard = document.getElementById('totalTonnageValue');
        const averageSupplyCard = document.getElementById('averageSupplyValue');

        if (totalSuppliesCard) {
            totalSuppliesCard.textContent = '0';
            animateCounter(totalSuppliesCard, suppliesData.totalSupplies);
        }

        if (totalAmountCard) {
            totalAmountCard.textContent = '0 FCFA';
            animateCounterFCFA(totalAmountCard, suppliesData.totalAmount);
        }

        if (totalTonnageCard) {
            totalTonnageCard.textContent = '0.00 T';
            animateCounterTonnage(totalTonnageCard, suppliesData.totalTonnage);
        }

        if (averageSupplyCard) {
            averageSupplyCard.textContent = '0 FCFA';
            animateCounterFCFA(averageSupplyCard, suppliesData.averageCost);
        }

        // Animation d'entrée pour les éléments
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observer tous les éléments animés
        document.querySelectorAll('.animate__animated').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'all 0.6s ease';
            observer.observe(el);
        });
    });
</script>
@endpush
