<?php
// Script de débogage simple pour vérifier la structure de la table supplies et les données

// Connexion à la base de données
$host = 'localhost';
$db   = 'gradis';
$user = 'root';
$pass = '';
$charset = 'utf8mb4';

$dsn = "mysql:host=$host;dbname=$db;charset=$charset";
$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

try {
    $pdo = new PDO($dsn, $user, $pass, $options);
    echo "<h1>Débogage de la table supplies</h1>";
    
    // Vérifier si la table existe
    $tables = $pdo->query("SHOW TABLES LIKE 'supplies'")->fetchAll();
    if (count($tables) === 0) {
        echo "<p style='color:red'>La table 'supplies' n'existe pas!</p>";
        exit;
    }
    
    echo "<p style='color:green'>La table 'supplies' existe.</p>";
    
    // Afficher la structure de la table
    echo "<h2>Structure de la table supplies</h2>";
    $columns = $pdo->query("SHOW COLUMNS FROM supplies")->fetchAll();
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Compter les enregistrements
    $count = $pdo->query("SELECT COUNT(*) FROM supplies")->fetchColumn();
    echo "<p>Nombre total d'enregistrements: <strong>$count</strong></p>";
    
    if ($count > 0) {
        // Afficher quelques enregistrements
        echo "<h2>Exemples d'enregistrements (10 premiers)</h2>";
        $supplies = $pdo->query("SELECT id, reference, created_by, status, created_at FROM supplies LIMIT 10")->fetchAll();
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Référence</th><th>Créé par</th><th>Statut</th><th>Date création</th></tr>";
        foreach ($supplies as $supply) {
            echo "<tr>";
            echo "<td>{$supply['id']}</td>";
            echo "<td>{$supply['reference']}</td>";
            echo "<td>{$supply['created_by']}</td>";
            echo "<td>{$supply['status']}</td>";
            echo "<td>{$supply['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Compter par utilisateur créateur
        echo "<h2>Nombre d'approvisionnements par utilisateur créateur</h2>";
        $byUser = $pdo->query("SELECT created_by, COUNT(*) as count FROM supplies GROUP BY created_by")->fetchAll();
        echo "<table border='1'>";
        echo "<tr><th>ID Utilisateur</th><th>Nombre d'approvisionnements</th></tr>";
        foreach ($byUser as $row) {
            echo "<tr>";
            echo "<td>{$row['created_by']}</td>";
            echo "<td>{$row['count']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Lister les comptables
    echo "<h2>Utilisateurs avec le rôle 'accountant'</h2>";
    $accountants = $pdo->query("
        SELECT u.id, u.name, u.email 
        FROM users u
        JOIN model_has_roles mhr ON u.id = mhr.model_id
        JOIN roles r ON mhr.role_id = r.id
        WHERE r.name = 'accountant'
    ")->fetchAll();
    
    if (count($accountants) > 0) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Nom</th><th>Email</th></tr>";
        foreach ($accountants as $accountant) {
            echo "<tr>";
            echo "<td>{$accountant['id']}</td>";
            echo "<td>{$accountant['name']}</td>";
            echo "<td>{$accountant['email']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Pour chaque comptable, afficher ses approvisionnements
        foreach ($accountants as $accountant) {
            $accountantId = $accountant['id'];
            $accountantName = $accountant['name'];
            
            echo "<h3>Approvisionnements créés par $accountantName (ID: $accountantId)</h3>";
            $accountantSupplies = $pdo->query("
                SELECT id, reference, status, created_at 
                FROM supplies 
                WHERE created_by = $accountantId
                LIMIT 10
            ")->fetchAll();
            
            if (count($accountantSupplies) > 0) {
                echo "<table border='1'>";
                echo "<tr><th>ID</th><th>Référence</th><th>Statut</th><th>Date création</th></tr>";
                foreach ($accountantSupplies as $supply) {
                    echo "<tr>";
                    echo "<td>{$supply['id']}</td>";
                    echo "<td>{$supply['reference']}</td>";
                    echo "<td>{$supply['status']}</td>";
                    echo "<td>{$supply['created_at']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>Aucun approvisionnement créé par ce comptable.</p>";
            }
        }
    } else {
        echo "<p>Aucun utilisateur avec le rôle 'accountant' trouvé.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color:red'>Erreur: {$e->getMessage()}</p>";
}
?>
