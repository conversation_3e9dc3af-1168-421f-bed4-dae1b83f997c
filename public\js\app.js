// App initialization
document.addEventListener('DOMContentLoaded', function() {
    console.log('App initialized');
    initializeTooltips();
    initializePopovers();
    setupAjaxDefaults();
});

// Initialize Bootstrap tooltips
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Initialize Bootstrap popovers
function initializePopovers() {
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

// Setup Ajax defaults
function setupAjaxDefaults() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
}

// Global error handler
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
});

// Global AJAX error handler
$(document).ajaxError(function(event, jqXHR, settings, error) {
    console.error('AJAX error:', error);
});

// Utility functions
function formatNumber(number) {
    return new Intl.NumberFormat('fr-FR').format(number);
}

function formatDate(date) {
    return new Date(date).toLocaleDateString('fr-FR');
}

function formatDateTime(date) {
    return new Date(date).toLocaleString('fr-FR');
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', { 
        style: 'currency', 
        currency: 'XOF'
    }).format(amount);
}

// Sweet Alert helpers
function showSuccess(message) {
    Swal.fire({
        icon: 'success',
        title: 'Succès',
        text: message,
        timer: 2000,
        showConfirmButton: false
    });
}

function showError(message) {
    Swal.fire({
        icon: 'error',
        title: 'Erreur',
        text: message
    });
}

function showConfirm(title, text) {
    return Swal.fire({
        title: title,
        text: text,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Oui',
        cancelButtonText: 'Non'
    });
}

// Form helpers
function clearForm(formId) {
    document.getElementById(formId).reset();
}

function serializeForm(formId) {
    return $('#' + formId).serialize();
}

function disableForm(formId) {
    $('#' + formId + ' :input').prop('disabled', true);
}

function enableForm(formId) {
    $('#' + formId + ' :input').prop('disabled', false);
}

// Table helpers
function updateTable(tableId, data) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const tbody = table.querySelector('tbody');
    if (!tbody) return;

    tbody.innerHTML = '';
    data.forEach(row => {
        const tr = document.createElement('tr');
        Object.values(row).forEach(value => {
            const td = document.createElement('td');
            td.textContent = value;
            tr.appendChild(td);
        });
        tbody.appendChild(tr);
    });
}
