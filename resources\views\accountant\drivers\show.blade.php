@extends('layouts.accountant')

@section('content')
<div class="content-wrapper">
    <!-- Main content -->
    <div class="content pt-4">
        <div class="container-fluid">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-3">
                <ol class="breadcrumb bg-light p-2">
                    <li class="breadcrumb-item"><a href="{{ route('accountant.dashboard') }}">Tableau de bord</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('accountant.drivers.index') }}">Chauffeurs</a></li>
                    <li class="breadcrumb-item active">{{ $driver->first_name }} {{ $driver->last_name }}</li>
                </ol>
            </nav>

            <!-- Page Heading -->
            <div class="d-sm-flex align-items-center justify-content-between mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-user-tie fa-sm"></i> 
                    {{ $driver->first_name }} {{ $driver->last_name }}
                </h1>
                <div>
                    <button type="button" 
                            class="btn btn-success btn-sm shadow-sm mr-2" 
                            data-bs-toggle="modal" 
                            data-bs-target="#assignVehicleModal">
                        <i class="fas fa-truck fa-sm"></i> Réassigner le véhicule
                    </button>
                    <a href="{{ route('accountant.drivers.edit', $driver) }}" class="btn btn-primary btn-sm shadow-sm mr-2">
                        <i class="fas fa-edit fa-sm"></i> Modifier
                    </a>
                    <a href="{{ route('accountant.drivers.index') }}" class="btn btn-secondary btn-sm shadow-sm">
                        <i class="fas fa-arrow-left fa-sm"></i> Retour
                    </a>
                </div>
            </div>

            <!-- Content Row -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-info-circle mr-1"></i> Informations du Chauffeur
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table">
                                        <tr>
                                            <th style="width: 40%;"><i class="fas fa-envelope text-gray-500 mr-2"></i>Email</th>
                                            <td>{{ $driver->email }}</td>
                                        </tr>
                                        <tr>
                                            <th><i class="fas fa-phone text-gray-500 mr-2"></i>Téléphone</th>
                                            <td>{{ $driver->phone }}</td>
                                        </tr>
                                        <tr>
                                            <th><i class="fas fa-map-marker-alt text-gray-500 mr-2"></i>Adresse</th>
                                            <td>{{ $driver->address }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table">
                                        <tr>
                                            <th style="width: 40%;"><i class="fas fa-id-card text-gray-500 mr-2"></i>N° Permis</th>
                                            <td>{{ $driver->license_number }}</td>
                                        </tr>
                                        <tr>
                                            <th><i class="fas fa-calendar text-gray-500 mr-2"></i>Expiration</th>
                                            <td>
                                                @if($driver->license_expiry)
                                                    @if($driver->license_expiry->isPast())
                                                        <span class="text-danger">
                                                            <i class="fas fa-exclamation-triangle"></i>
                                                            Expiré le {{ $driver->license_expiry->format('d/m/Y') }}
                                                        </span>
                                                    @elseif($driver->license_expiry->diffInMonths(now()) <= 3)
                                                        <span class="text-warning">
                                                            <i class="fas fa-exclamation-circle"></i>
                                                            Expire le {{ $driver->license_expiry->format('d/m/Y') }}
                                                        </span>
                                                    @else
                                                        <span class="text-success">
                                                            <i class="fas fa-check-circle"></i>
                                                            Valide jusqu'au {{ $driver->license_expiry->format('d/m/Y') }}
                                                        </span>
                                                    @endif
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th><i class="fas fa-clock text-gray-500 mr-2"></i>Statut</th>
                                            <td>
                                                <span class="badge badge-{{ $driver->status === 'available' ? 'success' : 'warning' }}">
                                                    {{ $driver->status === 'available' ? 'Disponible' : 'Occupé' }}
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Véhicule assigné -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-truck mr-1"></i> Véhicule Assigné
                            </h6>
                        </div>
                        <div class="card-body">
                            @if($driver->truck)
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table">
                                            <tr>
                                                <th style="width: 40%;"><i class="fas fa-hashtag text-gray-500 mr-2"></i>Immatriculation</th>
                                                <td>{{ $driver->truck->registration_number }}</td>
                                            </tr>
                                            <tr>
                                                <th><i class="fas fa-weight text-gray-500 mr-2"></i>Capacité</th>
                                                <td>
                                                    @if($driver->truck && $driver->truck->capacity_name)
                                                        {{ $driver->truck->capacity_name }} 
                                                        ({{ number_format($driver->truck->capacity_value, 1) }} {{ $driver->truck->capacity_unit }})
                                                    @else
                                                        Non définie
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <th><i class="fas fa-info-circle text-gray-500 mr-2"></i>Statut</th>
                                                <td>
                                                    <span class="badge badge-{{ $driver->truck->status === 'available' ? 'success' : 'warning' }}">
                                                        {{ $driver->truck->status === 'available' ? 'Disponible' : 'En service' }}
                                                    </span>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            @else
                                <div class="text-center py-5">
                                    <i class="fas fa-truck fa-3x text-gray-300 mb-3"></i>
                                    <p class="text-gray-500 mb-0">Aucun véhicule assigné</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Livraisons en cours -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-shipping-fast mr-1"></i> Livraisons en cours
                            </h6>
                        </div>
                        <div class="card-body">
                            @if($driver->deliveries && $driver->deliveries->isNotEmpty())
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Référence</th>
                                                <th>Client</th>
                                                <th>Date de livraison</th>
                                                <th>Statut</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($driver->deliveries as $delivery)
                                                <tr>
                                                    <td>{{ $delivery->reference }}</td>
                                                    <td>{{ $delivery->customer->name }}</td>
                                                    <td>{{ $delivery->delivery_date->format('d/m/Y') }}</td>
                                                    <td>
                                                        @switch($delivery->status)
                                                            @case('pending')
                                                                <span class="badge badge-warning">En attente</span>
                                                                @break
                                                            @case('in_progress')
                                                                <span class="badge badge-info">En cours</span>
                                                                @break
                                                            @case('completed')
                                                                <span class="badge badge-success">Terminée</span>
                                                                @break
                                                            @default
                                                                <span class="badge badge-secondary">{{ $delivery->status }}</span>
                                                        @endswitch
                                                    </td>
                                                    <td>
                                                        <a href="{{ route('accountant.deliveries.show', $delivery) }}" 
                                                           class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-5">
                                    <i class="fas fa-shipping-fast fa-3x text-gray-300 mb-3"></i>
                                    <p class="text-gray-500 mb-0">Aucune livraison en cours</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Notes et commentaires -->
                <div class="col-md-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-sticky-note mr-1"></i> Notes et Commentaires
                            </h6>
                        </div>
                        <div class="card-body">
                            @if($driver->notes)
                                <p class="mb-0">{{ $driver->notes }}</p>
                            @else
                                <div class="text-center py-5">
                                    <i class="fas fa-sticky-note fa-3x text-gray-300 mb-3"></i>
                                    <p class="text-gray-500 mb-0">Aucune note disponible</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'assignation de véhicule -->
<div class="modal fade" id="assignVehicleModal" tabindex="-1" role="dialog" aria-labelledby="assignVehicleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignVehicleModalLabel">
                    <i class="fas fa-truck mr-2"></i>Réassigner le véhicule
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="assignVehicleForm">
                    @csrf
                    <input type="hidden" name="driver_id" value="{{ $driver->id }}">
                    
                    <div class="form-group">
                        <label for="truck_id">Sélectionner un véhicule</label>
                        <select class="form-control" id="truck_id" name="truck_id" required>
                            <option value="">Choisir un véhicule...</option>
                            @foreach($availableTrucks as $truck)
                                <option value="{{ $truck->id }}">
                                    {{ $truck->registration_number }} 
                                    @if($truck->capacity_name)
                                        ({{ $truck->capacity_name }} - {{ number_format($truck->capacity_value, 1) }} {{ $truck->capacity_unit }})
                                    @else
                                        (Capacité non définie)
                                    @endif
                                </option>
                            @endforeach
                            @if($driver->truck)
                                <option value="{{ $driver->truck->id }}" selected>
                                    {{ $driver->truck->registration_number }} 
                                    @if($driver->truck->capacity_name)
                                        ({{ $driver->truck->capacity_name }} - {{ number_format($driver->truck->capacity_value, 1) }} {{ $driver->truck->capacity_unit }})
                                    @else
                                        (Capacité non définie)
                                    @endif
                                    - Actuellement assigné
                                </option>
                            @endif
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times mr-2"></i>Annuler
                </button>
                <button type="button" class="btn btn-success" id="confirmAssignment">
                    <i class="fas fa-check mr-2"></i>Confirmer
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Gérer la soumission du formulaire d'assignation
    $('#confirmAssignment').on('click', function() {
        const form = $('#assignVehicleForm');
        const formData = {
            driver_id: form.find('input[name="driver_id"]').val(),
            truck_id: form.find('select[name="truck_id"]').val(),
            _token: form.find('input[name="_token"]').val()
        };

        // Réinitialiser les messages d'erreur
        form.find('.is-invalid').removeClass('is-invalid');
        form.find('.invalid-feedback').empty();

        // Envoyer la requête AJAX
        $.ajax({
            url: '{{ route("accountant.drivers.reassign-vehicle") }}',
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    // Afficher un message de succès
                    Swal.fire({
                        icon: 'success',
                        title: 'Succès!',
                        text: response.message,
                        showConfirmButton: false,
                        timer: 1500
                    }).then(() => {
                        // Recharger la page pour afficher les changements
                        window.location.reload();
                    });
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    // Erreurs de validation
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(field => {
                        const input = form.find(`[name="${field}"]`);
                        input.addClass('is-invalid');
                        input.siblings('.invalid-feedback').text(errors[field][0]);
                    });
                } else {
                    // Autres erreurs
                    Swal.fire({
                        icon: 'error',
                        title: 'Erreur!',
                        text: 'Une erreur est survenue lors de la réassignation du véhicule.',
                    });
                }
            }
        });
    });
});
</script>
@endpush
