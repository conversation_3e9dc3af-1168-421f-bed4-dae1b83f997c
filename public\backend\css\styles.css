/* Variables et Reset */
:root {
    --primary-color: #4e73df;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
    --border-color: #e3e6f0;
    --primary-gradient: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    --success-gradient: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    --warning-gradient: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
    --danger-gradient: linear-gradient(135deg, #e74a3b 0%, #be2617 100%);
    --sidebar-width: 250px;
    --topbar-height: 70px;
    --card-border-radius: 0.75rem;
    --transition-speed: 0.15s;
}

/* Base Layout */
body {
    background-color: #f8f9fc;
    font-family: "Nuni<PERSON>", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--secondary-color);
}

/* Scrollbar personnalisé */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Topbar */
.sb-topnav {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
    height: var(--topbar-height);
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 0 1.5rem;
}

.sb-topnav .navbar-brand {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--primary-color);
    padding: 1.5rem 1rem;
    letter-spacing: -0.5px;
}

.sb-topnav .form-control {
    border-radius: 20px;
    padding-left: 1rem;
    border: 1px solid var(--border-color);
    font-size: 0.875rem;
}

.sb-topnav .btn-primary {
    border-radius: 20px;
    padding: 0.375rem 1rem;
}

/* Sidebar */
#layoutSidenav {
    display: flex;
}

#layoutSidenav_nav {
    flex-basis: var(--sidebar-width);
    flex-shrink: 0;
    transition: transform var(--transition-speed) ease-in-out;
    z-index: 1038;
    transform: translateX(0);
}

.sb-sidenav {
    display: flex;
    flex-direction: column;
    height: 100vh;
    flex-wrap: nowrap;
    background: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sb-sidenav-menu {
    flex-grow: 1;
    padding: 1.5rem 0;
    overflow-y: auto;
}

.sb-sidenav-menu .nav {
    flex-direction: column;
    flex-wrap: nowrap;
}

.sb-sidenav-menu .nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    font-size: 0.9rem;
    color: var(--secondary-color);
    transition: all var(--transition-speed) ease;
    position: relative;
    font-weight: 600;
    border-left: 3px solid transparent;
}

.sb-sidenav-menu .nav-link:hover {
    color: var(--primary-color);
    background: var(--light-color);
    border-left-color: var(--primary-color);
}

.sb-sidenav-menu .nav-link.active {
    color: var(--primary-color);
    background: var(--light-color);
    border-left-color: var(--primary-color);
}

.sb-sidenav-menu .nav-link .sb-nav-link-icon {
    margin-right: 1rem;
    color: inherit;
    font-size: 1rem;
    width: 1.25rem;
    text-align: center;
}

.sb-sidenav-menu-heading {
    padding: 1.5rem 1.5rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--secondary-color);
}

.sb-sidenav-footer {
    padding: 1.5rem;
    background: var(--light-color);
    font-size: 0.875rem;
    border-top: 1px solid var(--border-color);
}

/* Main Content */
#layoutSidenav_content {
    padding-left: var(--sidebar-width);
    padding-top: var(--topbar-height);
    min-width: 0;
    flex-grow: 1;
    background: #f8f9fc;
    min-height: 100vh;
}

.container-fluid {
    padding: 1.5rem 1.5rem 0;
}

/* Dashboard Cards */
.stats-card {
    border: none;
    border-radius: var(--card-border-radius);
    transition: all 0.3s ease;
    overflow: hidden;
    margin-bottom: 1.5rem;
    height: 100%;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 2rem rgba(0,0,0,0.15);
}

.stats-card .card-body {
    padding: 2rem;
}

.stats-card .icon-circle {
    height: 4rem;
    width: 4rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    background: rgba(255, 255, 255, 0.1);
    margin-left: 1rem;
    transition: all 0.3s ease;
}

.stats-card:hover .icon-circle {
    transform: scale(1.1);
}

.stats-card .display-6 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0.5rem 0;
}

.stats-card .trend {
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.stats-card .trend i {
    margin-right: 0.25rem;
}

/* Content Cards */
.card {
    border: none;
    border-radius: var(--card-border-radius);
    background: white;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.25);
}

.card-header {
    background: transparent;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h6 {
    margin: 0;
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--dark-color);
    display: flex;
    align-items: center;
}

.card-header h6 i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.card-body {
    padding: 1.5rem;
}

/* Tables */
.table-responsive {
    border-radius: var(--card-border-radius);
    overflow: hidden;
}

.table {
    margin: 0;
    white-space: nowrap;
}

.table th {
    font-weight: 700;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    background: var(--light-color);
    color: var(--dark-color);
    padding: 1rem 1.5rem;
    border-top: none;
    vertical-align: middle;
}

.table td {
    padding: 1rem 1.5rem;
    vertical-align: middle;
    border-color: var(--border-color);
    color: var(--secondary-color);
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background: var(--light-color);
}

/* Avatar et Badges */
.avatar-sm {
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    font-weight: 700;
    color: white;
    background: var(--primary-color);
    border-radius: 50%;
}

.badge {
    padding: 0.5em 1em;
    font-weight: 600;
    font-size: 0.75rem;
    border-radius: 20px;
}

/* Buttons */
.btn {
    padding: 0.5rem 1.25rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 20px;
    transition: all 0.2s;
}

.btn-primary {
    background: var(--primary-gradient);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
}

.btn-outline-primary {
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    border-color: transparent;
    transform: translateY(-1px);
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
}

/* Dropdown */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
    border-radius: 0.5rem;
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1.5rem;
    font-size: 0.875rem;
    color: var(--secondary-color);
    display: flex;
    align-items: center;
}

.dropdown-item i {
    margin-right: 0.5rem;
    width: 1rem;
    text-align: center;
}

.dropdown-item:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

/* Charts */
.chart-area, .chart-pie {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Responsive */
@media (max-width: 992px) {
    :root {
        --sidebar-width: 100%;
    }

    #layoutSidenav_nav {
        transform: translateX(-100%);
        position: fixed;
        height: calc(100vh - var(--topbar-height));
        top: var(--topbar-height);
    }

    #layoutSidenav_content {
        padding-left: 0;
    }

    .sb-sidenav-toggled #layoutSidenav_nav {
        transform: translateX(0);
    }

    .stats-card .card-body {
        padding: 1.5rem;
    }

    .stats-card .icon-circle {
        height: 3rem;
        width: 3rem;
        font-size: 1.25rem;
    }

    .stats-card .display-6 {
        font-size: 1.5rem;
    }

    .table-responsive {
        margin: 0 -1.5rem;
        width: calc(100% + 3rem);
    }
}
