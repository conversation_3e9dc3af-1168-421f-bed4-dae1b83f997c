/* Styles pour le tableau de bord des ventes */

/* Styles généraux */
body {
    font-family: 'Poppins', sans-serif;
    color: #2D3748;
    line-height: 1.6;
    background-color: #ffffff;
    min-height: 100vh;
}

/* Carte principale avec design moderne et épuré */
.main-card {
    background: white;
    color: #333;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: none;
    position: relative;
}

.container-fluid {
    padding: 2rem;
    max-width: 1600px;
    margin: 0 auto;
}

/* Animation de fade-in pour les éléments */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Cartes statistiques - style épuré et moderne */
.sales-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.25rem;
    margin-top: 1.5rem;
    animation: fadeIn 0.6s ease-out forwards;
}

.stat-card {
    background: white;
    border-radius: 0.5rem;
    padding: 1.25rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
    animation: fadeIn 0.5s ease-out forwards;
    animation-delay: calc(var(--animation-order, 0) * 0.1s);
    opacity: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 100px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-card:nth-child(1) { --animation-order: 1; }
.stat-card:nth-child(2) { --animation-order: 2; }
.stat-card:nth-child(3) { --animation-order: 3; }
.stat-card:nth-child(4) { --animation-order: 4; }

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.stat-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.stat-label {
    font-size: 0.85rem;
    color: #718096;
    font-weight: 500;
    margin-bottom: 0.5rem;
    letter-spacing: 0.01em;
}

.stat-value {
    font-size: 1.85rem;
    font-weight: 700;
    color: #2D3748;
    margin-bottom: 0.5rem;
    line-height: 1.2;
    letter-spacing: -0.01em;
}

.stat-trend {
    font-size: 0.75rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #718096;
    letter-spacing: 0.01em;
}

.stat-trend.positive {
    color: #38A169;
}

.stat-trend.negative {
    color: #E53E3E;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    opacity: 1;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-card:hover .stat-icon {
    transform: scale(1.05);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
}

/* Styles spécifiques pour chaque carte statistique */
.stat-total {
    border-left-color: #1E88E5;
}

.stat-total .stat-icon {
    background-color: #1E88E5;
}

.stat-revenue {
    border-left-color: #e74c3c;
}

.stat-revenue .stat-icon {
    background-color: #e74c3c;
}

.stat-discount {
    border-left-color: #2ecc71;
}

.stat-discount .stat-icon {
    background-color: #2ecc71;
}

.stat-customers {
    border-left-color: #f39c12;
}

.stat-customers .stat-icon {
    background-color: #f39c12;
}

/* Styles pour le tableau des ventes */
.sales-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    margin-top: 2rem;
    overflow: hidden;
    animation: fadeIn 0.8s ease-out forwards;
    animation-delay: 0.4s;
    opacity: 0;
    border: 1px solid rgba(226, 232, 240, 0.7);
}

.sales-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.sales-table th {
    background-color: rgba(25, 118, 210, 0.05);
    color: #1976D2;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: 1.25rem 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(226, 232, 240, 0.7);
}

.sales-table th i {
    margin-right: 0.5rem;
    opacity: 0.8;
}

.sales-table td {
    padding: 1.25rem 1rem;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    color: #2d3748;
    vertical-align: middle;
    transition: all 0.2s ease;
}

.sales-table tr {
    transition: all 0.3s ease;
}

.sales-table tr:hover {
    background-color: rgba(25, 118, 210, 0.02);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
}

.sales-table tr:hover td {
    border-bottom: 1px solid rgba(25, 118, 210, 0.1);
}

/* Badges et étiquettes */
.badge {
    font-weight: 700;
    padding: 0.6rem 1rem;
    border-radius: 50px;
    font-size: 0.75rem;
    display: inline-flex !important;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    z-index: 1;
    visibility: visible !important;
    opacity: 1 !important;
    margin-bottom: 0;
    white-space: nowrap;
    color: white;
}

.badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0.3), rgba(255,255,255,0));
    transition: all 0.4s ease;
    z-index: -1;
}

.badge i {
    font-size: 0.85rem;
}

.badge:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
    filter: brightness(1.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.badge:hover::before {
    left: 100%;
}

/* Styles spécifiques pour les badges dans le tableau */
.sales-table .badge {
    display: inline-flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.badge:active {
    transform: translateY(2px) scale(0.95);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Animation d'apparition pour les badges */
@keyframes badge-appear {
    0% { opacity: 0; transform: translateY(10px); }
    100% { opacity: 1; transform: translateY(0); }
}

.badge {
    animation: badge-appear 0.5s ease-out forwards;
}

.badge-direct-sale {
    background: linear-gradient(135deg, var(--direct-sale-color), #1565C0);
    color: white;
    position: relative;
    overflow: hidden;
}

.badge-direct-sale i {
    background: rgba(255, 255, 255, 0.15);
    padding: 0.35rem;
    border-radius: 50%;
    margin-right: 0.2rem;
}

.badge-discount-sale {
    background: linear-gradient(135deg, var(--discount-sale-color), #FF4081);
    color: white;
    position: relative;
    overflow: hidden;
}

.badge-discount-sale i {
    background: rgba(255, 255, 255, 0.15);
    padding: 0.35rem;
    border-radius: 50%;
    margin-right: 0.2rem;
    animation: pulse-percentage 2s infinite;
}

@keyframes pulse-percentage {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.badge-price-increase {
    background: linear-gradient(135deg, var(--price-increase-color), #FF8F00);
    color: white;
    position: relative;
    overflow: hidden;
}

.badge-price-increase i {
    background: rgba(255, 255, 255, 0.15);
    padding: 0.35rem;
    border-radius: 50%;
    margin-right: 0.2rem;
}

.badge-approved {
    background: linear-gradient(135deg, var(--approved-color), #00C853);
    color: white;
    position: relative;
    overflow: hidden;
    border-color: rgba(0, 200, 83, 0.5);
}

.badge-approved i {
    background: rgba(255, 255, 255, 0.15);
    padding: 0.35rem;
    border-radius: 50%;
    margin-right: 0.2rem;
}

.badge-pending {
    background: linear-gradient(135deg, var(--pending-validation-color), #FFB300);
    color: white;
    position: relative;
    overflow: hidden;
}

.badge-pending i {
    background: rgba(255, 255, 255, 0.15);
    padding: 0.35rem;
    border-radius: 50%;
    margin-right: 0.2rem;
    animation: pulse-clock 1.5s infinite;
}

@keyframes pulse-clock {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}

.badge-rejected {
    background: linear-gradient(135deg, var(--rejected-color), #D50000);
    color: white;
    position: relative;
    overflow: hidden;
}

.badge-rejected i {
    background: rgba(255, 255, 255, 0.15);
    padding: 0.35rem;
    border-radius: 50%;
    margin-right: 0.2rem;
}

/* Filtres */
.filter-pills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    animation: fadeIn 0.6s ease-out forwards;
    animation-delay: 0.2s;
    opacity: 0;
}

.filter-pill {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    border-radius: 50px;
    background-color: rgba(255, 255, 255, 0.95);
    color: #1E88E5;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(5px);
    border: none;
}

.filter-pill:hover {
    background-color: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.filter-pill.active {
    background: linear-gradient(135deg, #1E88E5, #1565C0);
    color: white;
    box-shadow: 0 4px 12px rgba(30, 136, 229, 0.3);
}

.filter-pill i {
    margin-right: 0.5rem;
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Barre de recherche */
.search-box {
    position: relative;
    margin-bottom: 1.5rem;
    animation: fadeIn 0.6s ease-out forwards;
    animation-delay: 0.3s;
    opacity: 0;
    max-width: 800px;
}

.search-box input {
    width: 100%;
    padding: 1rem 1.2rem 1rem 3rem;
    border-radius: 1rem;
    border: 1px solid rgba(226, 232, 240, 0.7);
    background-color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
    backdrop-filter: blur(5px);
}

.search-box input:focus {
    outline: none;
    border-color: #1976D2;
    box-shadow: 0 4px 15px rgba(25, 118, 210, 0.15);
    background-color: white;
}

.search-box i {
    position: absolute;
    left: 1.2rem;
    top: 50%;
    transform: translateY(-50%);
    color: #1976D2;
    font-size: 1.1rem;
}

/* Styles pour la légende */
.legend-section {
    animation: fadeIn 0.5s ease-out forwards;
}

.legend-card {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 1rem;
    padding: 1.25rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(226, 232, 240, 0.7);
    height: 100%;
    transition: all 0.3s ease;
}

.legend-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
    background-color: white;
}

.legend-desc {
    color: #718096;
    font-size: 0.9rem;
}

.toggle-legend {
    transition: all 0.3s ease;
}

.toggle-legend[aria-expanded="false"] .fa-chevron-up {
    transform: rotate(180deg);
}

/* Animations pour les sections */
.card-header, .card-body, .card-footer {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Responsive */
@media (max-width: 768px) {
    .sales-stats {
        grid-template-columns: 1fr;
    }
    
    .filter-pills {
        overflow-x: auto;
        flex-wrap: nowrap;
        padding-bottom: 0.5rem;
    }
    
    .legend-card {
        margin-bottom: 1rem;
    }
}
