<div class="card border-primary shadow mb-4" id="validation-section">
    <div class="card-header bg-primary text-white d-flex align-items-center justify-content-between">
        <span><i class="fas fa-exclamation-circle me-2"></i>Ventes à valider</span>
        <span class="badge bg-dark">{{ $pendingValidationSales->count() }} à valider</span>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover table-bordered m-0 align-middle">
                <thead class="table-primary">
                    <tr>
                        <th>#</th>
                        <th>Date</th>
                        <th>Client</th>
                        <th>Quantité</th>
                        <th>Prix unitaire</th>
                        <th>Remise/tonne</th>
                        <th>Montant total</th>
                        <th>Modifications</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($pendingValidationSales as $sale)
                        <tr>
                            <td><strong>{{ $sale->id }}</strong></td>
                            <td>{{ $sale->created_at->format('d/m/Y H:i') }}</td>
                            <td>{{ $sale->customer_name }}</td>
                            <td>{{ $sale->quantity }}</td>
                            <td>
                                @if($sale->price_modified)
                                    <span class="text-danger fw-bold">{{ number_format($sale->unit_price, 2, ',', ' ') }} FCFA</span>
                                    <br>
                                    <small class="text-muted">Original: {{ number_format($sale->original_price, 2, ',', ' ') }} FCFA</small>
                                @else
                                    {{ number_format($sale->unit_price, 2, ',', ' ') }} FCFA
                                @endif
                            </td>
                            <td>
                                @if($sale->discount_per_ton > 0)
                                    <span class="text-danger fw-bold">{{ number_format($sale->discount_per_ton, 2, ',', ' ') }} FCFA</span>
                                @else
                                    -
                                @endif
                            </td>
                            <td class="fw-bold">{{ number_format($sale->total_amount, 2, ',', ' ') }} FCFA</td>
                            <td>
                                @if($sale->price_modified)
                                    <span class="badge bg-warning text-dark">Prix modifié</span>
                                @elseif($sale->discount_per_ton > 0)
                                    <span class="badge bg-info">Remise</span>
                                @endif
                            </td>
                            <td class="text-center">
                                <form action="{{ route('admin.sales.validate-discount', $sale) }}" method="POST" class="d-inline">
                                    @csrf
                                    <input type="hidden" name="action" value="approve">
                                    <button type="submit" class="btn btn-outline-success btn-sm" title="Valider">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </form>
                                <form action="{{ route('admin.sales.validate-discount', $sale) }}" method="POST" class="d-inline ms-1">
                                    @csrf
                                    <input type="hidden" name="action" value="reject">
                                    <button type="submit" class="btn btn-outline-danger btn-sm" title="Rejeter">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="9" class="text-center text-muted">Aucune vente à valider</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
