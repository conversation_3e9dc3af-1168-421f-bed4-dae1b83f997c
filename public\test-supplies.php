<?php
// Configuration de la base de données
$dbConfig = [
    'host' => 'localhost',
    'db'   => 'gradis',
    'user' => 'root',
    'pass' => '',
];

// Fonction pour se connecter à la base de données
function connectDB($config) {
    try {
        $dsn = "mysql:host={$config['host']};dbname={$config['db']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['user'], $config['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        return $pdo;
    } catch (PDOException $e) {
        die("Erreur de connexion à la base de données: " . $e->getMessage());
    }
}

// Fonction pour afficher un tableau HTML à partir des données
function displayTable($data, $headers = null) {
    if (empty($data)) {
        echo "<p>Aucune donnée trouvée.</p>";
        return;
    }
    
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    
    // En-têtes
    echo "<tr style='background-color: #f2f2f2;'>";
    if ($headers) {
        foreach ($headers as $header) {
            echo "<th>" . htmlspecialchars($header) . "</th>";
        }
    } else {
        foreach (array_keys($data[0]) as $key) {
            echo "<th>" . htmlspecialchars($key) . "</th>";
        }
    }
    echo "</tr>";
    
    // Données
    foreach ($data as $row) {
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
        }
        echo "</tr>";
    }
    
    echo "</table>";
}

// Connexion à la base de données
$pdo = connectDB($dbConfig);

// Style CSS pour la page
echo "
<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h1, h2, h3 { color: #1E88E5; }
    table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
    th { background-color: #1E88E5; color: white; }
    th, td { padding: 8px; text-align: left; }
    tr:nth-child(even) { background-color: #f2f2f2; }
    .card { background: white; border-radius: 5px; padding: 15px; margin-bottom: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    .btn { background: #1E88E5; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; }
    .btn:hover { background: #1565C0; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
    .success { color: green; }
    .error { color: red; }
</style>
";

echo "<h1>Test des approvisionnements</h1>";

// 1. Vérifier si la table supplies existe
echo "<div class='card'>";
echo "<h2>1. Vérification de la table supplies</h2>";

$stmt = $pdo->query("SHOW TABLES LIKE 'supplies'");
$tableExists = $stmt->rowCount() > 0;

if ($tableExists) {
    echo "<p class='success'>✓ La table 'supplies' existe.</p>";
} else {
    echo "<p class='error'>✗ La table 'supplies' n'existe pas!</p>";
    die("La table n'existe pas, impossible de continuer.");
}

// 2. Afficher la structure de la table
echo "<h3>Structure de la table supplies</h3>";
$stmt = $pdo->query("DESCRIBE supplies");
$columns = $stmt->fetchAll();
displayTable($columns);

// Vérifier si la colonne created_by existe
$createdByExists = false;
foreach ($columns as $column) {
    if ($column['Field'] === 'created_by') {
        $createdByExists = true;
        break;
    }
}

if ($createdByExists) {
    echo "<p class='success'>✓ La colonne 'created_by' existe dans la table supplies.</p>";
} else {
    echo "<p class='error'>✗ La colonne 'created_by' n'existe pas dans la table supplies!</p>";
}
echo "</div>";

// 3. Afficher les données de la table
echo "<div class='card'>";
echo "<h2>2. Données de la table supplies</h2>";

$stmt = $pdo->query("SELECT COUNT(*) as count FROM supplies");
$count = $stmt->fetch()['count'];
echo "<p>Nombre total d'enregistrements: <strong>{$count}</strong></p>";

if ($count > 0) {
    echo "<h3>10 derniers approvisionnements</h3>";
    $stmt = $pdo->query("
        SELECT s.id, s.reference, s.created_by, u.name as created_by_name, s.status, s.created_at
        FROM supplies s
        LEFT JOIN users u ON s.created_by = u.id
        ORDER BY s.id DESC
        LIMIT 10
    ");
    $supplies = $stmt->fetchAll();
    displayTable($supplies);
    
    // Compter par utilisateur créateur
    echo "<h3>Nombre d'approvisionnements par utilisateur</h3>";
    $stmt = $pdo->query("
        SELECT s.created_by, u.name, COUNT(*) as count
        FROM supplies s
        LEFT JOIN users u ON s.created_by = u.id
        GROUP BY s.created_by, u.name
        ORDER BY count DESC
    ");
    $byUser = $stmt->fetchAll();
    displayTable($byUser);
}
echo "</div>";

// 4. Lister les comptables
echo "<div class='card'>";
echo "<h2>3. Utilisateurs avec le rôle 'accountant'</h2>";

$stmt = $pdo->query("
    SELECT u.id, u.name, u.email 
    FROM users u
    JOIN model_has_roles mhr ON u.id = mhr.model_id
    JOIN roles r ON mhr.role_id = r.id
    WHERE r.name = 'accountant'
");
$accountants = $stmt->fetchAll();

if (count($accountants) > 0) {
    displayTable($accountants);
    
    // Pour chaque comptable, vérifier ses approvisionnements
    foreach ($accountants as $accountant) {
        $accountantId = $accountant['id'];
        $accountantName = $accountant['name'];
        
        echo "<h3>Approvisionnements créés par {$accountantName} (ID: {$accountantId})</h3>";
        $stmt = $pdo->prepare("
            SELECT id, reference, status, created_at 
            FROM supplies 
            WHERE created_by = ?
            ORDER BY created_at DESC
            LIMIT 5
        ");
        $stmt->execute([$accountantId]);
        $accountantSupplies = $stmt->fetchAll();
        
        if (count($accountantSupplies) > 0) {
            displayTable($accountantSupplies);
            
            // Compter par statut
            $stmt = $pdo->prepare("
                SELECT status, COUNT(*) as count
                FROM supplies
                WHERE created_by = ?
                GROUP BY status
            ");
            $stmt->execute([$accountantId]);
            $byStatus = $stmt->fetchAll();
            
            echo "<h4>Répartition par statut</h4>";
            displayTable($byStatus);
        } else {
            echo "<p>Aucun approvisionnement créé par ce comptable.</p>";
        }
    }
} else {
    echo "<p>Aucun utilisateur avec le rôle 'accountant' trouvé.</p>";
}
echo "</div>";

// 5. Tester l'API getMySupplies
echo "<div class='card'>";
echo "<h2>4. Test de l'API getMySupplies</h2>";
echo "<p>Pour tester l'API, vous devez être connecté en tant que comptable. Cliquez sur le bouton ci-dessous:</p>";
echo "<button class='btn' onclick='testApi()'>Tester l'API getMySupplies</button>";
echo "<div id='api-result' style='margin-top: 15px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9;'>Le résultat s'affichera ici</div>";

echo "<script>
function testApi() {
    document.getElementById('api-result').innerHTML = 'Chargement...';
    
    fetch('/accountant/supplies/my-supplies')
        .then(response => {
            if (!response.ok) {
                throw new Error('Erreur réseau: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            document.getElementById('api-result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            document.getElementById('api-result').innerHTML = '<div class=\"error\">Erreur: ' + error.message + '</div>';
        });
}
</script>";
echo "</div>";

// 6. Vérifier les logs Laravel
echo "<div class='card'>";
echo "<h2>5. Derniers logs Laravel</h2>";
$logPath = __DIR__ . '/../storage/logs/laravel.log';

if (file_exists($logPath)) {
    $logContent = file_get_contents($logPath);
    $logLines = array_slice(explode("\n", $logContent), -50);
    echo "<pre style='max-height: 400px; overflow-y: auto;'>";
    foreach ($logLines as $line) {
        echo htmlspecialchars($line) . "\n";
    }
    echo "</pre>";
} else {
    echo "<p class='error'>Le fichier de log n'existe pas ou n'est pas accessible.</p>";
}
echo "</div>";
?>
