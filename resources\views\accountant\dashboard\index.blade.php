@extends('layouts.accountant')

@section('title', 'Tableau de bord')

@section('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="{{ asset('css/accountant-dashboard.css') }}?v={{ time() }}">
@endsection

@section('content')
<div class="dashboard-container">
    <!-- En-tête du tableau de bord avec animation -->
    <div class="dashboard-header animate__animated animate__fadeIn">
        <div class="header-content">
            <div class="header-title-section">
                <h1 class="header-title">Tableau de bord <span class="year-badge">2025</span></h1>
                <p class="header-subtitle">Gestion financière et suivi des approvisionnements</p>
            </div>
            <div class="header-icon animate__animated animate__pulse animate__infinite">
                <i class="fas fa-chart-pie"></i>
            </div>
        </div>
        <!-- Éléments décoratifs pour l'en-tête -->
        <div class="header-decoration">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
            <div class="decoration-wave"></div>
        </div>
    </div>

    <!-- Stats Overview avec design moderne -->
    <div class="stats-container">
        <div class="row g-4">
            <div class="col-xl-3 col-md-6" style="--animation-order: 0">
                <div class="stat-card primary-card">
                    <div class="card-body">
                        <div class="card-title">Ventes du mois</div>
                        <div class="card-value currency">0</div>
                        <i class="fas fa-shopping-cart card-icon"></i>
                    </div>
                    <div class="card-trend">
                        <span><i class="fas fa-chart-line"></i> +12.5% par rapport au mois dernier</span>
                    </div>
                    <div class="card-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6" style="--animation-order: 1">
                <div class="stat-card success-card">
                    <div class="card-body">
                        <div class="card-title">Commandes</div>
                        <div class="card-value">0</div>
                        <i class="fas fa-file-invoice card-icon"></i>
                    </div>
                    <div class="card-trend">
                        <span><i class="fas fa-plus-circle"></i> 0 nouvelles commandes</span>
                    </div>
                    <div class="card-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6" style="--animation-order: 2">
                <div class="stat-card warning-card">
                    <div class="card-body">
                        <div class="card-title">Clients actifs</div>
                        <div class="card-value">0</div>
                        <i class="fas fa-users card-icon"></i>
                    </div>
                    <div class="card-trend">
                        <span><i class="fas fa-user-plus"></i> 5 nouveaux ce mois</span>
                    </div>
                    <div class="card-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6" style="--animation-order: 3">
                <div class="stat-card danger-card">
                    <div class="card-body">
                        <div class="card-title">Chiffre d'affaires</div>
                        <div class="card-value currency">0</div>
                        <i class="fas fa-money-bill-wave card-icon"></i>
                    </div>
                    <div class="card-trend">
                        <span><i class="fas fa-chart-line"></i> +8.3% par rapport au mois dernier</span>
                    </div>
                    <div class="card-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques et statistiques -->
    <div class="row mt-4 mb-4">
        <div class="col-12 col-xl-8 mb-4">
            <div class="chart-card">
                <div class="card-header">
                    <div class="card-title">
                        <div class="card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3>Évolution des commandes</h3>
                    </div>
                    <div class="card-actions">
                        <button class="action-button" title="Télécharger">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
                <div class="chart-controls">
                    <button class="chart-toggle-btn active" data-index="0">Nombre de bons</button>
                    <button class="chart-toggle-btn" data-index="1">Montant (FCFA)</button>
                    <button class="chart-toggle-btn" data-index="2">Tonnage</button>
                </div>
                <div class="chart-container">
                    <canvas id="ordersChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Activités récentes -->
        <div class="col-12 col-xl-4 mb-4">
            <div class="activity-card">
                <div class="card-header">
                    <div class="card-title">
                        <div class="card-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3>Activités récentes</h3>
                    </div>
                </div>
                <div class="activities-container" id="recent-activities">
                    <!-- Les activités seront ajoutées dynamiquement ici -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="row">
        <!-- Recent Orders -->
        <div class="col-12 col-xl-8 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">Commandes récentes</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Client</th>
                                    <th>Montant</th>
                                    <th>Date</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Table content here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="col-12 col-xl-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">Activités récentes</h5>
                </div>
                <div class="card-body">
                    <div class="activity-feed">
                        <!-- Activity feed content here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .dashboard-container {
        padding: 1.5rem;
        margin: var(--topbar-height) 0 var(--footer-height) var(--sidebar-width);
        min-height: calc(100vh - var(--topbar-height) - var(--footer-height));
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }

    .stats-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    .stat-card {
        background: #fff;
        border-radius: 1rem;
        padding: 1.5rem;
        height: 100%;
        transition: all 0.3s ease;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 12px;
        margin-bottom: 1rem;
    }

    .stat-icon i {
        width: 24px;
        height: 24px;
    }

    .stat-card h4 {
        color: #6B7280;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .stat-card h2 {
        color: #111827;
        font-size: 1.5rem;
        font-weight: 600;
    }

    .stat-card p {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
    }

    .stat-card p i {
        width: 16px;
        height: 16px;
    }

    .card {
        border: none;
        border-radius: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .card-header {
        background-color: transparent;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1.25rem 1.5rem;
    }

    .card-title {
        color: #111827;
        font-weight: 600;
    }

    .table {
        margin-bottom: 0;
    }

    .table th {
        border-top: none;
        color: #6B7280;
        font-weight: 500;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
    }

    .table td {
        vertical-align: middle;
        color: #374151;
    }

    .activity-feed {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    @media (max-width: 1199.98px) {
        .dashboard-container {
            margin-left: 0;
        }
        
        .stats-container {
            padding: 0 0.5rem;
        }
    }

    @media (max-width: 767.98px) {
        .stats-container {
            padding: 0 0.25rem;
        }
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Initialisation des icônes
    if (typeof feather !== 'undefined') {
        feather.replace();
    }

    // Variables globales
    let ordersChart = null;
    let loadingTimeout = null;

    // Initialisation au chargement de la page
    document.addEventListener('DOMContentLoaded', function() {
        initOrdersChart();
        initCounters();
        
        // Gestion des boutons de contrôle du graphique
        const chartToggleBtns = document.querySelectorAll('.chart-toggle-btn');
        chartToggleBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const index = parseInt(this.getAttribute('data-index'));
                
                // Mettre à jour les classes actives
                chartToggleBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                // Afficher/masquer les datasets
                if (window.ordersChart) {
                    window.ordersChart.data.datasets.forEach((dataset, i) => {
                        dataset.hidden = i !== index;
                    });
                    
                    // Mettre à jour le graphique avec animation
                    window.ordersChart.update({
                        duration: 800,
                        easing: 'easeOutQuart'
                    });
                }
            });
        });
    });

    // Animation des compteurs
    function initCounters() {
        const counters = document.querySelectorAll('.card-value');
        counters.forEach(counter => {
            const target = parseInt(counter.innerText) || 0;
            let count = 0;
            const duration = 2000; // 2 secondes
            const frameRate = 30; // 30 FPS
            const totalFrames = duration / (1000 / frameRate);
            const increment = target / totalFrames;
            
            const updateCount = () => {
                if (count < target) {
                    count += increment;
                    counter.innerText = Math.floor(count);
                    requestAnimationFrame(updateCount);
                } else {
                    counter.innerText = target;
                }
            };
            
            updateCount();
        });
    }

    // Initialisation du graphique
    function initOrdersChart() {
        const ctx = document.getElementById('ordersChart').getContext('2d');
        
        // Créer des dégradés colorés plus vifs pour le fond du graphique
        const gradient1 = ctx.createLinearGradient(0, 0, 0, 300);
        gradient1.addColorStop(0, 'rgba(78, 99, 255, 0.5)');
        gradient1.addColorStop(0.5, 'rgba(78, 99, 255, 0.2)');
        gradient1.addColorStop(1, 'rgba(78, 99, 255, 0)');
        
        const gradient2 = ctx.createLinearGradient(0, 0, 0, 300);
        gradient2.addColorStop(0, 'rgba(147, 51, 234, 0.4)');
        gradient2.addColorStop(0.5, 'rgba(147, 51, 234, 0.15)');
        gradient2.addColorStop(1, 'rgba(147, 51, 234, 0)');
        
        const gradient3 = ctx.createLinearGradient(0, 0, 0, 300);
        gradient3.addColorStop(0, 'rgba(236, 72, 153, 0.4)');
        gradient3.addColorStop(0.5, 'rgba(236, 72, 153, 0.15)');
        gradient3.addColorStop(1, 'rgba(236, 72, 153, 0)');
        
        // Créer des dégradés plus vifs pour les lignes du graphique
        const lineGradient = ctx.createLinearGradient(0, 0, 600, 0);
        lineGradient.addColorStop(0, '#4f46e5');
        lineGradient.addColorStop(0.3, '#7c3aed');
        lineGradient.addColorStop(0.6, '#c026d3');
        lineGradient.addColorStop(1, '#db2777');
        
        window.ordersChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil'],
                datasets: [
                    {
                        label: 'Nombre de bons',
                        data: [12, 19, 15, 25, 22, 30, 35],
                        backgroundColor: gradient1,
                        borderColor: lineGradient,
                        borderWidth: 3,
                        pointBackgroundColor: '#ffffff',
                        pointBorderColor: '#4f46e5',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 10,
                        pointHoverBackgroundColor: '#ffffff',
                        pointHoverBorderColor: '#db2777',
                        pointHoverBorderWidth: 3,
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Montant total (FCFA)',
                        data: [1800000, 2500000, 2100000, 3200000, 2800000, 3800000, 4500000],
                        backgroundColor: gradient2,
                        borderColor: '#7c3aed',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        pointBackgroundColor: '#ffffff',
                        pointBorderColor: '#7c3aed',
                        pointBorderWidth: 2,
                        pointRadius: 5,
                        pointHoverRadius: 8,
                        pointHoverBackgroundColor: '#ffffff',
                        pointHoverBorderColor: '#7c3aed',
                        pointHoverBorderWidth: 3,
                        tension: 0.4,
                        fill: false,
                        hidden: true
                    },
                    {
                        label: 'Tonnage total',
                        data: [60, 95, 75, 125, 110, 150, 175],
                        backgroundColor: gradient3,
                        borderColor: '#c026d3',
                        borderWidth: 2,
                        borderDash: [3, 3],
                        pointBackgroundColor: '#ffffff',
                        pointBorderColor: '#c026d3',
                        pointBorderWidth: 2,
                        pointRadius: 5,
                        pointHoverRadius: 8,
                        pointHoverBackgroundColor: '#ffffff',
                        pointHoverBorderColor: '#c026d3',
                        pointHoverBorderWidth: 3,
                        tension: 0.4,
                        fill: false,
                        hidden: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                },
                interaction: {
                    mode: 'index',
                    intersect: false,
                    includeInvisible: true
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        align: 'end',
                        labels: {
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 15,
                            boxWidth: 8,
                            boxHeight: 8,
                            font: {
                                size: 12,
                                weight: '600'
                            }
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(255, 255, 255, 0.98)',
                        titleColor: '#111827',
                        bodyColor: '#4B5563',
                        borderColor: 'rgba(79, 70, 229, 0.3)',
                        borderWidth: 2,
                        padding: 16,
                        boxPadding: 10,
                        usePointStyle: true,
                        titleFont: {
                            size: 14,
                            weight: 'bold',
                            family: "'Poppins', sans-serif"
                        },
                        bodyFont: {
                            size: 13,
                            family: "'Poppins', sans-serif"
                        },
                        cornerRadius: 12,
                        caretSize: 8,
                        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                        callbacks: {
                            title: function(tooltipItems) {
                                return tooltipItems[0].label;
                            },
                            label: function(context) {
                                let label = context.dataset.label || '';
                                let value = context.parsed.y;
                                
                                if (label === 'Nombre de bons') {
                                    return '📊 ' + label + ': ' + value + ' bons';
                                } else if (label === 'Montant total (FCFA)') {
                                    return '💰 ' + label + ': ' + value.toLocaleString('fr-FR') + ' FCFA';
                                } else if (label === 'Tonnage total') {
                                    return '⚖️ ' + label + ': ' + value + ' T';
                                }
                                return label + ': ' + value;
                            },
                            labelPointStyle: function(context) {
                                const colors = ['#4f46e5', '#7c3aed', '#c026d3'];
                                return {
                                    pointStyle: 'circle',
                                    rotation: 0,
                                    backgroundColor: colors[context.datasetIndex % colors.length]
                                };
                            },
                            labelTextColor: function(context) {
                                const colors = ['#4f46e5', '#7c3aed', '#c026d3'];
                                return colors[context.datasetIndex % colors.length];
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false,
                            drawBorder: false
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#6B7280',
                            font: {
                                size: 12,
                                family: "'Poppins', sans-serif",
                                weight: '500'
                            },
                            padding: 12,
                            maxRotation: 0
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(243, 244, 246, 0.6)',
                            drawBorder: false,
                            lineWidth: 1.5,
                            tickLength: 8
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#6B7280',
                            font: {
                                size: 12,
                                family: "'Poppins', sans-serif",
                                weight: '500'
                            },
                            padding: 12,
                            stepSize: 5,
                            callback: function(value) {
                                return value;
                            }
                        },
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Simulation d'activités récentes
    function loadRecentActivities() {
        const container = document.getElementById('recent-activities');
        if (!container) return;
        
        const activities = [
            { type: 'Nouveau bon', status: 'success', message: 'Bon #12345 créé pour Client XYZ', time_ago: 'Il y a 10 min' },
            { type: 'Paiement', status: 'success', message: 'Paiement de 1,500,000 FCFA reçu', time_ago: 'Il y a 1h' },
            { type: 'Alerte', status: 'warning', message: 'Stock de ciment faible (15%)', time_ago: 'Il y a 3h' },
            { type: 'Erreur', status: 'danger', message: 'Échec de la validation du bon #12340', time_ago: 'Il y a 5h' }
        ];
        
        if (activities.length) {
            let html = '';
            activities.forEach((activity, index) => {
                let statusClass = 'primary';
                
                if (activity.status === 'success') {
                    statusClass = 'success';
                } else if (activity.status === 'danger') {
                    statusClass = 'danger';
                } else if (activity.status === 'warning') {
                    statusClass = 'warning';
                }
                
                html += `
                <div class="activity-item ${statusClass}" style="animation-delay: ${index * 0.1}s">
                    <div class="activity-icon">
                        <i class="fas fa-${activity.status === 'success' ? 'check-circle' : activity.status === 'danger' ? 'times-circle' : 'exclamation-circle'}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">
                            ${activity.type}
                            <span class="badge-status ${statusClass}">${activity.status === 'success' ? 'Succès' : activity.status === 'danger' ? 'Erreur' : 'Alerte'}</span>
                        </div>
                        <div class="activity-details">
                            <div class="activity-detail">
                                <i class="fas fa-info-circle"></i>
                                <span>${activity.message}</span>
                            </div>
                            <div class="activity-detail">
                                <i class="far fa-clock"></i>
                                <span>${activity.time_ago}</span>
                            </div>
                        </div>
                    </div>
                </div>
                `;
            });
            
            container.innerHTML = html;
        } else {
            container.innerHTML = '<div class="text-center py-4"><p class="text-muted">Aucune activité récente</p></div>';
        }
    }
    
    // Charger les activités récentes après un court délai
    setTimeout(loadRecentActivities, 500);
</script>
@endpush
