@extends('layouts.cement_manager')

@section('title', 'Historique des ventes')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Historique des ventes</h1>
        <a href="{{ route('cement-manager.sales.create') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> Nouvelle vente
        </a>
    </div>
    
    @if(isset($rejectedSales) && $rejectedSales->count() > 0)
    <div class="card border-danger shadow mb-4">
        <div class="card-header bg-danger text-white d-flex align-items-center justify-content-between">
            <span><i class="fas fa-exclamation-triangle me-2"></i>Ventes rejetées par l'administrateur</span>
            <span class="badge bg-dark">{{ $rejectedSales->count() }} rejet(s)</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-bordered m-0 align-middle">
                    <thead class="table-danger">
                        <tr>
                            <th>#</th>
                            <th>Date</th>
                            <th>Client</th>
                            <th>Quantité</th>
                            <th>Type</th>
                            <th>Raison du rejet</th>
                            <th class="text-center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($rejectedSales as $sale)
                            <tr>
                                <td><strong>{{ $sale->id }}</strong></td>
                                <td>{{ $sale->created_at->format('d/m/Y H:i') }}</td>
                                <td>{{ $sale->customer_name }}</td>
                                <td>{{ $sale->quantity }} T</td>
                                <td>
                                    @if(($sale->discount_per_ton ?? 0) > 0)
                                        <span class="badge bg-info">Remise</span>
                                    @elseif($sale->price_modified && $sale->original_price)
                                        <span class="badge bg-warning">Augmentation de prix</span>
                                    @else
                                        <span class="badge bg-secondary">Standard</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="text-danger">{{ Str::limit($sale->admin_note, 50) }}</span>
                                </td>
                                <td class="text-center">
                                    <a href="{{ route('cement-manager.sales.show', $sale) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> Détail
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    @endif

    <div class="card shadow">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Référence BC</th>
                            <th>Client</th>
                            <th>Ville</th>
                            <th>Quantité</th>
                            <th>Montant</th>
                            <th>Paiement</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($sales as $sale)
                            <tr>
                                <td>{{ $sale->created_at->format('d/m/Y H:i') }}</td>
                                <td>{{ $sale->supply->reference }}</td>
                                <td>
                                    {{ $sale->customer_name }}
                                    <br>
                                    <small class="text-muted">{{ $sale->customer_phone }}</small>
                                </td>
                                <td>{{ $sale->city->name }}</td>
                                <td>{{ number_format($sale->quantity, 2, ',', ' ') }} T</td>
                                <td>{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</td>
                                <td>
                                    @if($sale->payment_method === 'cash')
                                        <span class="badge bg-success">Comptant</span>
                                    @else
                                        <span class="badge bg-warning">Crédit</span>
                                    @endif
                                </td>
                                <td>
                                    @if($sale->status === 'completed')
                                        <span class="badge bg-success">Terminé</span>
                                    @elseif($sale->status === 'pending')
                                        <span class="badge bg-warning">En attente</span>
                                    @else
                                        <span class="badge bg-danger">Annulé</span>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('cement-manager.sales.show', $sale) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> Détail
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center">Aucune vente trouvée</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-end mt-3">
                {{ $sales->links() }}
            </div>
        </div>
    </div>
</div>
@endsection
