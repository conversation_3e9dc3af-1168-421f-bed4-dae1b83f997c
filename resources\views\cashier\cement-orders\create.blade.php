@extends('layouts.cashier')

@section('title', 'Nouveau bon de commande ciment')

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
<style>
    .select2-container--bootstrap4 .select2-selection--single {
        height: calc(2.25rem + 2px) !important;
    }
    /* Style pour les champs de sélection avec recherche */
    .select-group {
        display: flex;
        align-items: stretch;
        gap: 0;
    }
    .select-group .select2-container {
        flex: 1;
    }
    .select-group .select2-container .select2-selection {
        border: 1px solid #d1d3e2;
        border-radius: 0.35rem;
        height: 38px !important;
    }
    .select-group .btn-add-customer {
        background-color: #4e73df;
        color: white;
        border: none;
        margin-left: 5px;
        border-radius: 0.35rem;
        padding: 0.375rem 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .select-group .btn-add-customer:hover {
        background-color: #2e59d9;
    }
    /* Style pour le champ de recherche Select2 */
    .select2-container--default .select2-search--dropdown .select2-search__field {
        border: 1px solid #d1d3e2;
        border-radius: 0.35rem;
    }
    .select2-results__option {
        padding: 6px 12px;
    }
    /* Style pour rendre visible le contour du champ Ville */
    #city + .select2-container .select2-selection {
        border: 1px solid #d1d3e2 !important;
        border-radius: 0.35rem !important;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Nouveau bon de commande ciment</h1>
        <a href="{{ route('cashier.cement-orders.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour
        </a>
    </div>

    <div class="row">
        <!-- Formulaire d'ajout de produit -->
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Détails du produit</h6>
                </div>
                <div class="card-body">
                    <form id="productForm">
                        <!-- Sélection du produit -->
                        <div class="form-group">
                            <label for="product">Produit <span class="text-danger">*</span></label>
                            <select class="form-control select2" id="product" name="product" required>
                                <option value="">Sélectionner un produit</option>
                                @foreach($products as $product)
                                    <option value="{{ $product->id }}" data-unit="{{ $product->unit }}">
                                        {{ $product->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Sélection du client -->
                        <div class="form-group">
                            <label for="customer">Client <span class="text-danger">*</span></label>
                            <div class="select-group">
                                <select class="form-control select2" id="customer" name="customer" required>
                                    <option value="">Sélectionner un client</option>
                                    @foreach($customers as $customer)
                                        <option value="{{ $customer->id }}">{{ $customer->name }}</option>
                                    @endforeach
                                </select>
                                <button type="button" class="btn-add-customer" data-toggle="modal" data-target="#customerModal">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Sélection de la ville -->
                        <div class="form-group">
                            <label for="city">Ville de livraison <span class="text-danger">*</span></label>
                            <select class="form-control select2" id="city" name="city" required>
                                <option value="">Sélectionner une ville</option>
                                @foreach($cities as $city)
                                    <option value="{{ $city->id }}" data-region="{{ $city->region->name }}">
                                        {{ $city->name }} ({{ $city->region->name }})
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Quantité -->
                        <div class="form-group">
                            <label for="quantity">Quantité (en tonnes) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="quantity" name="quantity" step="0.01" min="0" required>
                        </div>

                        <!-- Capacité du camion -->
                        <div class="form-group">
                            <label for="capacity">Capacité du camion <span class="text-danger">*</span></label>
                            <select class="form-control select2" id="capacity" name="capacity" required>
                                <option value="">Sélectionner une capacité</option>
                                @foreach($truckCapacities as $capacity)
                                    <option value="{{ $capacity->id }}" data-tonnage="{{ $capacity->tonnage }}">
                                        {{ $capacity->tonnage }} tonnes
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Ajouter au bon
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Résumé du bon de commande -->
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Résumé du bon de commande</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive mb-4">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Client</th>
                                    <th>Ville</th>
                                    <th>Quantité</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="orderItems">
                                <!-- Les items seront ajoutés ici dynamiquement -->
                            </tbody>
                        </table>
                    </div>

                    <button type="button" id="saveOrder" class="btn btn-success">
                        <i class="fas fa-save"></i> Enregistrer le bon
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour ajouter un client -->
<div class="modal fade" id="customerModal" tabindex="-1" aria-labelledby="customerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customerModalLabel">Ajouter un nouveau client</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="customerForm">
                    <div class="form-group">
                        <label for="customerName">Nom complet</label>
                        <input type="text" class="form-control" id="customerName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="customerEmail">Email</label>
                        <input type="email" class="form-control" id="customerEmail" name="email">
                    </div>
                    <div class="form-group">
                        <label for="customerPhone">Téléphone</label>
                        <input type="tel" class="form-control" id="customerPhone" name="phone">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" id="saveCustomer">Enregistrer</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    // Variable globale pour stocker les items
    let orderItems = [];

    // Configuration de SweetAlert2 Toast
    const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true
    });

    $(document).ready(function() {
        // Initialisation de Select2
        $('.select2').select2({
            theme: 'bootstrap4'
        });

        // Gestionnaire pour le formulaire d'ajout de produit
        $('#productForm').on('submit', function(e) {
            e.preventDefault();

            try {
                // Récupération des valeurs sélectionnées
                const product = $('#product option:selected');
                const customer = $('#customer option:selected');
                const city = $('#city option:selected');
                const quantity = parseFloat($('#quantity').val());
                const capacity = $('#capacity option:selected');

                // Validation des données
                if (!product.val() || !customer.val() || !city.val() || !quantity || !capacity.val()) {
                    throw new Error('Veuillez remplir tous les champs obligatoires');
                }

                // Création de l'item
                const item = {
                    product_id: product.val(),
                    product_name: product.text(),
                    product_unit: product.data('unit'),
                    customer_id: customer.val(),
                    customer_name: customer.text(),
                    city_id: city.val(),
                    city_name: city.text(),
                    quantity: quantity,
                    capacity_id: capacity.val(),
                    tonnage_per_trip: capacity.data('tonnage'),
                    trips_count: Math.ceil(quantity / capacity.data('tonnage'))
                };

                // Ajout de l'item à la liste
                orderItems.push(item);

                // Mise à jour du tableau
                updateOrderItemsTable();

                // Réinitialisation du formulaire
                this.reset();
                $('.select2').val('').trigger('change');

                // Notification de succès
                Toast.fire({
                    icon: 'success',
                    title: 'Produit ajouté au bon de commande'
                });

            } catch (error) {
                Toast.fire({
                    icon: 'error',
                    title: error.message
                });
            }
        });

        // Gestionnaire pour la sauvegarde du bon de commande
        $('#saveOrder').on('click', async function() {
            try {
                if (orderItems.length === 0) {
                    throw new Error('Veuillez ajouter au moins un produit au bon de commande');
                }

                // Préparation des données
                const formData = {
                    details: orderItems.map(item => ({
                        product_id: item.product_id,
                        customer_id: item.customer_id,
                        city_id: item.city_id,
                        quantity: item.quantity,
                        trips_count: item.trips_count,
                        tonnage_per_trip: item.tonnage_per_trip,
                        capacity_id: item.capacity_id
                    }))
                };

                // Envoi de la requête
                const response = await fetch('{{ route("cashier.cement-orders.store") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.message || 'Une erreur est survenue');
                }

                // Redirection vers la liste des bons de commande
                window.location.href = '{{ route("cashier.cement-orders.index") }}';

            } catch (error) {
                Toast.fire({
                    icon: 'error',
                    title: error.message
                });
            }
        });

        // Gestionnaire pour la suppression d'un item
        $(document).on('click', '.delete-item', function() {
            const index = $(this).data('index');
            orderItems.splice(index, 1);
            updateOrderItemsTable();
        });

        // Gestionnaire pour l'ajout d'un nouveau client
        $('#saveCustomer').on('click', async function() {
            try {
                const formData = {
                    name: $('#customerName').val(),
                    email: $('#customerEmail').val(),
                    phone: $('#customerPhone').val()
                };

                const response = await fetch('{{ route("cashier.customers.store") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.message || 'Une erreur est survenue');
                }

                // Ajout du nouveau client à la liste
                const newOption = new Option(result.data.name, result.data.id, true, true);
                $('#customer').append(newOption).trigger('change');

                // Fermeture du modal
                $('#customerModal').modal('hide');

                // Réinitialisation du formulaire
                $('#customerForm')[0].reset();

                Toast.fire({
                    icon: 'success',
                    title: 'Client ajouté avec succès'
                });

            } catch (error) {
                Toast.fire({
                    icon: 'error',
                    title: error.message
                });
            }
        });
    });

    // Fonction pour mettre à jour le tableau des produits
    function updateOrderItemsTable() {
        const tbody = $('#orderItems');
        tbody.empty();

        orderItems.forEach((item, index) => {
            tbody.append(`
                <tr>
                    <td>${item.product_name}</td>
                    <td>${item.customer_name}</td>
                    <td>${item.city_name}</td>
                    <td>${item.quantity} ${item.product_unit}</td>
                    <td>
                        <button type="button" class="btn btn-danger btn-sm delete-item" data-index="${index}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `);
        });
    }
</script>
@endpush
