// Script pour gérer les fonctionnalités des champs de saisie et des cases à cocher
$(document).ready(function() {
    console.log('Script de gestion des contrôles du formulaire chargé');
    
    // Variables globales
    var quantityInput = document.getElementById('quantity');
    var remiseCheck = document.getElementById('remiseCheck');
    var modifierPrixCheck = document.getElementById('modifierPrixCheck');
    var remiseTonneInput = document.getElementById('remise_tonne');
    var nouveauPrixInput = document.getElementById('nouveau_prix');
    var remiseTonneGroup = document.getElementById('remiseTonneGroup');
    var remiseMontantGroup = document.getElementById('remiseMontantGroup');
    var nouveauPrixGroup = document.getElementById('nouveauPrixGroup');
    
    // Fonction pour convertir une chaîne en nombre flottant de manière sécurisée
    function safeParseFloat(value) {
        if (!value) return 0;
        // Remplacer la virgule par un point pour la conversion
        value = value.toString().replace(',', '.');
        var parsed = parseFloat(value);
        return isNaN(parsed) ? 0 : parsed;
    }
    
    // Fonction pour formater les nombres
    function formatNumber(number) {
        return number.toLocaleString('fr-FR');
    }
    
    // Fonction pour mettre à jour les calculs
    function updateCalculations() {
        console.log('updateCalculations called');
        
        // Récupérer les détails de la ville sélectionnée depuis la variable globale
        var selectedCityDetails = window.selectedCityDetails;
        
        // Vérifier si les données nécessaires sont disponibles
        if (!selectedCityDetails || !quantityInput || !quantityInput.value) {
            console.log('Données manquantes pour le calcul', { 
                selectedCityDetails: !!selectedCityDetails,
                quantityInput: !!quantityInput,
                quantityValue: quantityInput ? quantityInput.value : 'N/A'
            });
            return;
        }
        
        const quantity = safeParseFloat(quantityInput.value);
        let pricePerTon = selectedCityDetails.price;
        let remiseParTonne = 0;
        
        console.log('Valeurs initiales', { quantity, pricePerTon });
        
        // Si le prix est augmenté et qu'une valeur est entrée
        if (modifierPrixCheck && modifierPrixCheck.checked && nouveauPrixInput && nouveauPrixInput.value) {
            const augmentation = safeParseFloat(nouveauPrixInput.value);
            const prixOriginal = selectedCityDetails.price;
            
            // Ajouter l'augmentation au prix original
            pricePerTon = prixOriginal + augmentation;
            
            // Afficher le nouveau prix total
            const differencePrixEl = document.getElementById('difference_prix');
            
            if (differencePrixEl) {
                // Afficher le nouveau prix total
                differencePrixEl.value = formatNumber(pricePerTon);
                
                // Toujours en orange car c'est une augmentation
                differencePrixEl.classList.add('text-warning');
                differencePrixEl.classList.remove('text-success', 'text-info');
                
                // Mettre à jour le champ caché
                document.getElementById('hiddenNouveauPrix').value = augmentation;
            }
        }
        
        // Si une remise est appliquée et qu'une valeur est entrée
        if (remiseCheck && remiseCheck.checked && remiseTonneInput && remiseTonneInput.value) {
            remiseParTonne = safeParseFloat(remiseTonneInput.value);
            
            // Vérifier que la remise n'est pas supérieure au prix
            if (remiseParTonne > pricePerTon) {
                alert('La remise ne peut pas être supérieure au prix par tonne!');
                remiseTonneInput.value = pricePerTon;
                remiseParTonne = pricePerTon;
            }
            
            // Calculer le montant total de la remise
            const remiseTotale = remiseParTonne * quantity;
            
            // Afficher le montant de la remise
            const remiseMontantEl = document.getElementById('remise_montant');
            if (remiseMontantEl) {
                remiseMontantEl.value = formatNumber(remiseTotale);
            }
            
            // Mettre à jour le champ caché
            document.getElementById('hiddenDiscountPerTon').value = remiseParTonne;
        }
        
        // Calculer le prix unitaire final (après remise)
        const prixUnitaireFinal = pricePerTon - remiseParTonne;
        
        // Calculer le montant total
        const montantTotal = prixUnitaireFinal * quantity;
        
        // Mettre à jour les champs d'affichage
        const prixUnitaireEl = document.getElementById('prix_unitaire');
        const montantTotalEl = document.getElementById('montant_total');
        const totalAmountEl = document.getElementById('totalAmount');
        const requiredTripsEl = document.getElementById('requiredTrips');
        
        if (prixUnitaireEl) {
            prixUnitaireEl.value = formatNumber(prixUnitaireFinal);
        }
        
        if (montantTotalEl) {
            montantTotalEl.value = formatNumber(montantTotal);
        }
        
        // Mettre à jour le montant total affiché dans le résumé
        if (totalAmountEl) {
            totalAmountEl.textContent = formatNumber(montantTotal) + ' FCFA';
        }
        
        // Calculer et afficher le nombre de voyages nécessaires
        if (requiredTripsEl && selectedCityDetails && selectedCityDetails.vehicleCapacity) {
            const vehicleCapacity = selectedCityDetails.vehicleCapacity;
            const requiredTrips = Math.ceil(quantity / vehicleCapacity);
            requiredTripsEl.textContent = requiredTrips;
            console.log('Nombre de voyages nécessaires:', requiredTrips);
        }
        
        // Mettre à jour le champ caché pour la quantité
        document.getElementById('hiddenQuantity').value = quantity;
        
        console.log('Calculs mis à jour', {
            prixUnitaireFinal,
            montantTotal,
            remiseParTonne,
            quantity
        });
    }
    
    // Gestionnaires d'événements pour les cases à cocher
    if (remiseCheck) {
        remiseCheck.addEventListener('change', function() {
            if (this.checked) {
                // Désactiver l'autre option
                if (modifierPrixCheck) modifierPrixCheck.checked = false;
                
                // Afficher le groupe de remise par tonne
                if (remiseTonneGroup) remiseTonneGroup.style.display = 'block';
                if (remiseMontantGroup) remiseMontantGroup.style.display = 'block';
                
                // Masquer le groupe de nouveau prix
                if (nouveauPrixGroup) nouveauPrixGroup.style.display = 'none';
                
                // Réinitialiser le champ de nouveau prix
                if (nouveauPrixInput) nouveauPrixInput.value = '';
            } else {
                // Masquer les groupes de remise
                if (remiseTonneGroup) remiseTonneGroup.style.display = 'none';
                if (remiseMontantGroup) remiseMontantGroup.style.display = 'none';
                
                // Réinitialiser le champ de remise
                if (remiseTonneInput) remiseTonneInput.value = '';
            }
            
            // Mettre à jour les calculs
            updateCalculations();
        });
    }
    
    if (modifierPrixCheck) {
        modifierPrixCheck.addEventListener('change', function() {
            if (this.checked) {
                // Désactiver l'autre option
                if (remiseCheck) remiseCheck.checked = false;
                
                // Afficher le groupe de nouveau prix
                if (nouveauPrixGroup) nouveauPrixGroup.style.display = 'block';
                
                // Masquer les groupes de remise
                if (remiseTonneGroup) remiseTonneGroup.style.display = 'none';
                if (remiseMontantGroup) remiseMontantGroup.style.display = 'none';
                
                // Réinitialiser le champ de remise
                if (remiseTonneInput) remiseTonneInput.value = '';
            } else {
                // Masquer le groupe de nouveau prix
                if (nouveauPrixGroup) nouveauPrixGroup.style.display = 'none';
                
                // Réinitialiser le champ de nouveau prix
                if (nouveauPrixInput) nouveauPrixInput.value = '';
            }
            
            // Mettre à jour les calculs
            updateCalculations();
        });
    }
    
    // Gestionnaires d'événements pour les champs de saisie
    if (quantityInput) {
        quantityInput.addEventListener('input', function() {
            updateCalculations();
        });
    }
    
    if (remiseTonneInput) {
        remiseTonneInput.addEventListener('input', function() {
            updateCalculations();
        });
    }
    
    if (nouveauPrixInput) {
        nouveauPrixInput.addEventListener('input', function() {
            updateCalculations();
        });
    }
    
    // Exposer la fonction globalement pour qu'elle soit accessible depuis d'autres scripts
    window.updateCalculations = updateCalculations;
});
