$(function() {
    // Gestion de la suppression avec SweetAlert2
    $(document).on('click', '.delete', function(e) {
        e.preventDefault();
        var link = $(this).attr("href");
        var hasTruck = $(this).data('has-truck') === 'true';

        if (hasTruck) {
            Swal.fire({
                title: 'Action impossible',
                text: "Ce chauffeur a un véhicule assigné. Veuillez d'abord retirer l'assignation du véhicule.",
                icon: 'warning',
                confirmButtonColor: '#3085d6',
                confirmButtonText: 'Compris'
            });
            return;
        }

        Swal.fire({
            title: 'Êtes-vous sûr?',
            text: "Cette action est irréversible!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: link,
                    type: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Supprimé!',
                                text: response.message,
                                icon: 'success',
                                timer: 1500,
                                showConfirmButton: false
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Erreur!',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr) {
                        Swal.fire({
                            title: 'Erreur!',
                            text: xhr.responseJSON?.message || 'Une erreur est survenue lors de la suppression.',
                            icon: 'error'
                        });
                    }
                });
            }
        });
    });

    // Gestionnaire d'approvisionnement de ciment
    class CementSupplyManager {
        constructor() {
            this.init();
        }

        init() {
            this.bindEvents();
            this.initializeFormValidation();
        }

        bindEvents() {
            // Gestionnaire pour le bouton d'ajout de ville
            $(document).on('click', '.add-city-btn', (e) => {
                const btn = e.currentTarget;
                const regionId = btn.dataset.regionId;
                const regionName = btn.dataset.regionName;
                this.showCitySelectionModal(regionId, regionName);
            });
        }

        initializeFormValidation() {
            const form = $('#supplyForm');
            form.on('submit', (e) => {
                if (!this.validateRegions()) {
                    e.preventDefault();
                    Swal.fire({
                        title: 'Validation échouée',
                        text: 'Veuillez sélectionner au moins une ville pour chaque région.',
                        icon: 'error'
                    });
                }
            });
        }

        async showCitySelectionModal(regionId, regionName) {
            try {
                const response = await fetch(`/api/regions/${regionId}/cities`);
                const cities = await response.json();

                const $modal = $('#citySelectionModal');
                const $select = $modal.find('#citySelect');
                const $title = $modal.find('.modal-title');

                // Mise à jour du titre
                $title.text(`Sélectionner une ville - ${regionName}`);

                // Mise à jour des options
                $select.empty().append('<option value="">Choisir une ville</option>');
                cities.forEach(city => {
                    $select.append(`<option value="${city.id}" data-name="${city.name}">${city.name}</option>`);
                });

                // Gestionnaire pour le bouton Ajouter
                const $saveBtn = $modal.find('#saveCityBtn');
                $saveBtn.off('click').on('click', () => {
                    const $selectedOption = $select.find(':selected');
                    if ($selectedOption.val()) {
                        const cityData = {
                            id: $selectedOption.val(),
                            name: $selectedOption.data('name')
                        };
                        this.addSelectedCity(regionId, cityData);
                        $modal.modal('hide');
                    }
                });

                // Afficher le modal
                $modal.modal({
                    backdrop: 'static',
                    keyboard: false
                });

            } catch (error) {
                console.error('Erreur:', error);
                Swal.fire({
                    title: 'Erreur',
                    text: 'Impossible de charger les villes.',
                    icon: 'error'
                });
            }
        }

        addSelectedCity(regionId, cityData) {
            const selectedCitiesContainer = document.querySelector(`#selected-cities-${regionId}`);
            if (!selectedCitiesContainer) return;

            // Vérifier si la ville n'est pas déjà ajoutée
            if (selectedCitiesContainer.querySelector(`[data-city-id="${cityData.id}"]`)) {
                Swal.fire({
                    title: 'Information',
                    text: 'Cette ville a déjà été ajoutée.',
                    icon: 'info'
                });
                return;
            }

            // Créer le badge de la ville
            const cityBadge = document.createElement('span');
            cityBadge.className = 'city-badge';
            cityBadge.dataset.cityId = cityData.id;
            cityBadge.innerHTML = `
                ${cityData.name}
                <i class="fas fa-times remove-city" title="Retirer cette ville"></i>
            `;

            // Ajouter le gestionnaire d'événements pour la suppression
            const removeButton = cityBadge.querySelector('.remove-city');
            removeButton.onclick = () => {
                cityBadge.remove();
                this.updateRegionStatus(regionId);
            };

            selectedCitiesContainer.appendChild(cityBadge);
            this.updateRegionStatus(regionId);
        }

        updateRegionStatus(regionId) {
            const selectedCitiesContainer = document.querySelector(`#selected-cities-${regionId}`);
            const statusIndicator = document.querySelector(`.region-status[data-region-id="${regionId}"]`);
            
            if (selectedCitiesContainer && statusIndicator) {
                const hasSelectedCities = selectedCitiesContainer.children.length > 0;
                statusIndicator.classList.toggle('complete', hasSelectedCities);
            }
        }

        validateRegions() {
            let isValid = true;
            document.querySelectorAll('.region-card').forEach(regionCard => {
                const regionId = regionCard.querySelector('.add-city-btn').dataset.regionId;
                const selectedCities = document.querySelector(`#selected-cities-${regionId}`);
                if (!selectedCities || selectedCities.children.length === 0) {
                    isValid = false;
                }
            });
            return isValid;
        }
    }

    // Initialisation
    if (document.querySelector('.cement-supply-card')) {
        window.cementSupplyManager = new CementSupplyManager();
    }
});
