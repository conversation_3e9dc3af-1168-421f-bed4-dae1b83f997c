@extends('layouts.accountant')

@section('title', 'Nouveau bon de commande ciment')

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
<style>
    .select2-container--bootstrap4 .select2-selection--single {
        height: calc(2.25rem + 2px) !important;
    }
    /* Style pour les champs de sélection avec recherche */
    .select-group {
        display: flex;
        align-items: stretch;
        gap: 0;
    }
    .select-group .select2-container {
        flex: 1;
    }
    .select-group .select2-container .select2-selection {
        border: 1px solid #d1d3e2;
        border-radius: 0.35rem;
        height: 38px !important;
    }
    .select-group .btn-add-supplier {
        background-color: #4e73df;
        color: white;
        border: none;
        margin-left: 5px;
        border-radius: 0.35rem;
        padding: 0.375rem 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .select-group .btn-add-supplier:hover {
        background-color: #2e59d9;
    }
    /* Style pour le champ de recherche Select2 */
    .select2-container--default .select2-search--dropdown .select2-search__field {
        border: 1px solid #d1d3e2;
        border-radius: 0.35rem;
    }
    .select2-results__option {
        padding: 6px 12px;
    }
    /* Style pour rendre visible le contour du champ Ville */
    #destination + .select2-container .select2-selection {
        border: 1px solid #d1d3e2 !important;
        border-radius: 0.35rem !important;
        height: 38px !important;
    }
    #destination + .select2-container .select2-selection__rendered {
        line-height: 36px !important;
    }
    #destination + .select2-container .select2-selection__arrow {
        height: 36px !important;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Nouveau bon de commande ciment</h1>
        <a href="{{ route('accountant.cement-orders.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour
        </a>
    </div>

    <div class="row">
        <!-- Formulaire d'ajout de produit -->
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Ajouter un produit au bon de commande</h6>
                </div>
                <div class="card-body">
                    <form id="productForm">
                        <!-- Produit -->
                        <div class="mb-3">
                            <label for="product" class="form-label">Produit (Ciment)</label>
                            <select class="form-control" id="product" name="product" required>
                                @foreach($products as $product)
                                    <option value="{{ $product->id }}" data-price="{{ $product->price }}">
                                        {{ $product->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Fournisseur -->
                        <div class="mb-3">
                            <label for="supplier" class="form-label">Fournisseur</label>
                            <div class="select-group">
                                <select class="form-control select2" id="supplier" name="supplier" required>
                                    <option value="">Sélectionnez un fournisseur</option>
                                    @foreach($suppliers as $supplier)
                                        <option value="{{ $supplier->id }}">{{ $supplier->name }}</option>
                                    @endforeach
                                </select>
                                <button type="button" class="btn-add-supplier" data-bs-toggle="modal" data-bs-target="#supplierModal">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Destination -->
                        <div class="mb-3">
                            <label for="destination" class="form-label">Destination</label>
                            <select class="form-control select2" id="destination" name="destination" required>
                                <option value="">Sélectionnez une destination</option>
                                @foreach($cities as $city)
                                    <option value="{{ $city->id }}">{{ $city->name }}</option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Capacité du camion -->
                        <div class="mb-3">
                            <label for="truckCapacity" class="form-label">Capacité du camion</label>
                            <select class="form-control" id="truckCapacity" name="truckCapacity" required>
                                <option value="">Sélectionnez une capacité</option>
                                @foreach($truckCapacities as $capacity)
                                    <option value="{{ $capacity->id }}" data-tonnage="{{ $capacity->tonnage }}">
                                        {{ $capacity->tonnage }} Tonnes
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Nombre de tours -->
                        <div class="mb-3">
                            <label for="numberOfTrips" class="form-label">Nombre de tours</label>
                            <input type="number" class="form-control" id="numberOfTrips" name="numberOfTrips" min="1" required>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Ajouter au bon de commande
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Résumé du bon de commande -->
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Résumé du bon de commande</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive mb-3">
                        <table id="orderItemsTable" class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Fournisseur</th>
                                    <th>Destination</th>
                                    <th>Capacité</th>
                                    <th>Tours</th>
                                    <th>Quantité</th>
                                    <th>Prix unitaire</th>
                                    <th>Montant total</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Les éléments du bon de commande seront ajoutés ici dynamiquement -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="6" class="text-end"><strong>Total Quantité:</strong></td>
                                    <td colspan="2"><span id="totalQuantity">0</span></td>
                                </tr>
                                <tr>
                                    <td colspan="6" class="text-end"><strong>Total Montant:</strong></td>
                                    <td colspan="2"><span id="totalAmount">0</span> FCFA</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Notes -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>

                    <button type="button" class="btn btn-success" id="saveOrder">
                        <i class="fas fa-save"></i> Enregistrer le bon de commande
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour ajouter un fournisseur -->
<div class="modal fade" id="supplierModal" tabindex="-1" aria-labelledby="supplierModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="supplierModalLabel">Ajouter un fournisseur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="quickSupplierForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="supplierName" class="form-label">Nom du fournisseur</label>
                        <input type="text" class="form-control" id="supplierName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="supplierEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="supplierEmail" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="supplierPhone" class="form-label">Téléphone</label>
                        <input type="tel" class="form-control" id="supplierPhone" name="phone">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Ajouter</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- Select2 -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    // Variable globale pour stocker les items
    let orderItems = [];

    // Configuration de SweetAlert2 Toast
    const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true
    });

    $(document).ready(function() {
        // Initialisation de Select2
        $('.select2').select2({
            theme: 'bootstrap4'
        });

        // Fonction pour récupérer le prix du produit selon la destination
        function getProductPrice(productId, cityId) {
            return $.get(`{{ url('accountant/cement-orders/get-product-price') }}/${productId}/${cityId}`);
        }

        // Gestionnaire pour l'ajout d'un fournisseur
        $('#quickSupplierForm').on('submit', function(e) {
            e.preventDefault();

            // Récupérer les données du formulaire
            const formData = {
                name: $('#supplierName').val(),
                email: $('#supplierEmail').val(),
                phone: $('#supplierPhone').val(),
                _token: '{{ csrf_token() }}'
            };

            // Envoyer la requête
            $.ajax({
                url: '{{ route("accountant.suppliers.store") }}',
                method: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        // Ajouter le nouveau fournisseur au select
                        const newOption = new Option(response.supplier.name, response.supplier.id, true, true);
                        $('#supplier').append(newOption).trigger('change');

                        // Fermer la modale
                        $('#supplierModal').modal('hide');

                        // Réinitialiser le formulaire
                        $('#quickSupplierForm')[0].reset();

                        // Message de succès
                        Toast.fire({
                            icon: 'success',
                            title: 'Fournisseur ajouté avec succès'
                        });
                    } else {
                        Toast.fire({
                            icon: 'error',
                            title: response.message || 'Une erreur est survenue'
                        });
                    }
                },
                error: function(xhr) {
                    let message = 'Une erreur est survenue';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    Toast.fire({
                        icon: 'error',
                        title: message
                    });
                }
            });
        });

        // Gestionnaire pour l'ajout d'un produit au bon de commande
        $('#productForm').on('submit', function(e) {
            e.preventDefault();

            try {
                // Récupération des valeurs sélectionnées
                const productId = $('#product').val();
                const supplierId = $('#supplier').val();
                const destinationId = $('#destination').val();
                const truckCapacityId = $('#truckCapacity').val();
                const numberOfTrips = parseInt($('#numberOfTrips').val());

                // Validation des champs
                if (!productId || !supplierId || !destinationId || !truckCapacityId || isNaN(numberOfTrips) || numberOfTrips < 1) {
                    throw new Error('Veuillez remplir tous les champs correctement');
                }

                // Récupérer le prix selon la destination
                getProductPrice(productId, destinationId)
                    .then(response => {
                        if (!response.success) {
                            throw new Error(response.message);
                        }

                        const unit_price = response.price;
                        const tonnage = parseFloat($('#truckCapacity option:selected').data('tonnage'));

                        // Création de l'item
                        const item = {
                            productId,
                            supplierId,
                            destinationId,
                            tonnage,
                            trips: numberOfTrips,
                            unit_price,
                            productName: $('#product option:selected').text(),
                            supplierName: $('#supplier option:selected').text(),
                            destinationName: $('#destination option:selected').text()
                        };

                        // Ajouter l'item à la liste
                        orderItems.push(item);

                        // Mise à jour du tableau
                        updateOrderItemsTable();

                        // Réinitialisation du formulaire
                        this.reset();
                        $('.select2').val('').trigger('change');

                        Toast.fire({
                            icon: 'success',
                            title: 'Produit ajouté au bon de commande'
                        });

                    })
                    .catch(error => {
                        Toast.fire({
                            icon: 'error',
                            title: error.message || 'Erreur lors de la récupération du prix'
                        });
                    });

            } catch (error) {
                Toast.fire({
                    icon: 'error',
                    title: error.message
                });
            }
        });

        // Gestionnaire pour la sauvegarde du bon de commande
        $('#saveOrder').on('click', function() {
            console.log('Début de la sauvegarde du bon de commande');
            console.log('Items à enregistrer:', orderItems);

            if (orderItems.length === 0) {
                Toast.fire({
                    icon: 'error',
                    title: 'Veuillez ajouter au moins un produit au bon de commande'
                });
                return;
            }

            const data = {
                product_id: orderItems[0].productId,
                reference: 'CMD-' + Date.now(),
                details: orderItems.map(item => ({
                    supplier_id: item.supplierId,
                    destination_id: item.destinationId,
                    quantity: item.tonnage * item.trips,
                    unit_price: item.unit_price,
                    trips_count: item.trips,
                    tonnage_per_trip: item.tonnage
                })),
                notes: $('#notes').val(),
                _token: '{{ csrf_token() }}'
            };

            console.log('Données à envoyer:', data);

            // Désactiver le bouton pendant l'envoi
            const $button = $(this);
            $button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Enregistrement...');

            $.ajax({
                url: '{{ route("accountant.cement-orders.store") }}',
                method: 'POST',
                data: JSON.stringify(data),
                contentType: 'application/json',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    console.log('Réponse reçue:', response);
                    if (response.success) {
                        Toast.fire({
                            icon: 'success',
                            title: response.message
                        }).then(() => {
                            window.location.href = response.redirect;
                        });
                    } else {
                        $button.prop('disabled', false).html('<i class="fas fa-save"></i> Enregistrer le bon de commande');
                        Toast.fire({
                            icon: 'error',
                            title: response.message || 'Une erreur est survenue'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erreur lors de l\'enregistrement:', {
                        status: status,
                        error: error,
                        response: xhr.responseText
                    });

                    $button.prop('disabled', false).html('<i class="fas fa-save"></i> Enregistrer le bon de commande');

                    let message = 'Une erreur est survenue';
                    if (xhr.responseJSON) {
                        if (xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        } else if (xhr.responseJSON.errors) {
                            message = Object.values(xhr.responseJSON.errors).flat().join('\n');
                        }
                    }
                    
                    Toast.fire({
                        icon: 'error',
                        title: message
                    });
                }
            });
        });
    });

    // Fonction pour mettre à jour le tableau des produits
    function updateOrderItemsTable() {
        const tbody = $('#orderItemsTable tbody');
        tbody.empty();

        let totalQuantity = 0;
        let totalAmount = 0;

        orderItems.forEach((item, index) => {
            const quantity = item.tonnage * item.trips;
            const amount = quantity * item.unit_price;
            totalQuantity += quantity;
            totalAmount += amount;

            tbody.append(`
                <tr>
                    <td>${item.productName}</td>
                    <td>${item.supplierName}</td>
                    <td>${item.destinationName}</td>
                    <td>${item.tonnage}</td>
                    <td>${item.trips}</td>
                    <td>${quantity}</td>
                    <td>${item.unit_price.toLocaleString('fr-FR')} FCFA</td>
                    <td>${amount.toLocaleString('fr-FR')} FCFA</td>
                    <td>
                        <button type="button" class="btn btn-danger btn-sm" onclick="removeOrderItem(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `);
        });

        // Mettre à jour les totaux
        $('#totalQuantity').text(totalQuantity.toLocaleString('fr-FR'));
        $('#totalAmount').text(`${totalAmount.toLocaleString('fr-FR')} FCFA`);

        // Activer/désactiver le bouton d'enregistrement
        $('#saveOrder').prop('disabled', orderItems.length === 0);
    }

    // Fonction pour supprimer un item
    function removeOrderItem(index) {
        orderItems.splice(index, 1);
        updateOrderItemsTable();
    }
</script>
@endpush
