/**
 * Script pour le tableau de bord comptable moderne - Version améliorée
 * Ajoute des animations avancées, compteurs interactifs et fonctionnalités dynamiques
 */

/**
 * Initialise tous les éléments du tableau de bord
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialiser les animations
    initAnimations();
    
    // Initialiser les compteurs
    setTimeout(() => {
        initCounters();
    }, 500);
    
    // Initialiser les graphiques
    setTimeout(() => {
        initSalesChart();
        initPaymentsChart();
        initRecoveriesChart();
    }, 1000);
    
    // Initialiser les notifications
    setTimeout(() => {
        initNotifications();
    }, 1500);
    
    // Initialiser les filtres de période
    setTimeout(() => {
        initPeriodFilters();
    }, 2000);
    
    // Initialiser les interactions avancées
    setTimeout(() => {
        initAdvancedInteractions();
    }, 2500);
    
    // Initialiser les mises à jour en temps réel
    setTimeout(() => {
        initLiveUpdates();
    }, 3000);
});

/**
 * Initialise les animations avancées pour les éléments du tableau de bord
 */
function initAnimations() {
    // Animation séquentielle des éléments au chargement de la page
    const fadeElements = document.querySelectorAll('.fadeIn, .fadeInUp, .fadeInRight, .fadeInLeft, .fadeInDown, .zoomIn');
    fadeElements.forEach((element, index) => {
        // Ajouter un délai progressif si aucun délai n'est défini
        if (!element.classList.contains('delay-1') && 
            !element.classList.contains('delay-2') && 
            !element.classList.contains('delay-3') && 
            !element.classList.contains('delay-4') && 
            !element.classList.contains('delay-5') && 
            !element.classList.contains('delay-6') && 
            !element.classList.contains('delay-7') && 
            !element.classList.contains('delay-8')) {
            element.style.animationDelay = `${0.1 + (index * 0.05)}s`;
        }
        element.style.opacity = '1';
    });
    
    // Ajouter des effets de flottement aux icônes des cartes
    const cardIcons = document.querySelectorAll('.stat-card-icon, .stat-icon, .action-icon');
    cardIcons.forEach(icon => {
        icon.classList.add('animate-float');
    });
    
    // Animation au survol des cartes statistiques avec effet 3D
    const statCards = document.querySelectorAll('.stat-card, .stat-summary');
    statCards.forEach(card => {
        // Animation au survol avec effet 3D
        card.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left; // position x relative à la carte
            const y = e.clientY - rect.top;  // position y relative à la carte
            
            // Calculer la rotation en fonction de la position de la souris
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            const rotateX = (y - centerY) / 20; // Réduire l'effet pour qu'il soit subtil
            const rotateY = (centerX - x) / 20;
            
            // Appliquer la transformation 3D
            this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px) translateY(-7px)`;
            this.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
            this.style.zIndex = '10';
        });
        
        // Réinitialiser la transformation au départ de la souris
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0) translateY(0)';
            this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.05)';
            this.style.zIndex = '1';
        });
        
        // Réinitialiser également lorsque la souris quitte la zone
        card.addEventListener('mouseout', function() {
            this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0) translateY(0)';
            this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.05)';
            this.style.zIndex = '1';
        });
    });
    
    // Animation au survol des actions rapides avec effet de pulsation
    const quickActions = document.querySelectorAll('.quick-action');
    quickActions.forEach(action => {
        action.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
            const icon = this.querySelector('.action-icon');
            if (icon) {
                icon.classList.add('animate-pulse');
            }
        });
        
        action.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.05)';
            const icon = this.querySelector('.action-icon');
            if (icon) {
                icon.classList.remove('animate-pulse');
            }
        });
    });
    
    // Ajouter un effet de surbrillance au survol des lignes du tableau
    const tableRows = document.querySelectorAll('.table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(30, 136, 229, 0.05)';
            this.style.transform = 'translateX(5px)';
            this.style.transition = 'all 0.3s ease';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
            this.style.transform = 'translateX(0)';
        });
    });
}

/**
 * Initialise les compteurs animés pour les statistiques avec effets améliorés
 */
function initCounters() {
    const counters = document.querySelectorAll('.counter');
    
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const prefix = counter.getAttribute('data-prefix') || '';
        const suffix = counter.getAttribute('data-suffix') || '';
        const duration = 2000; // durée de l'animation en ms (augmentée pour un effet plus progressif)
        let startTimestamp = null;
        
        // Fonction d'animation avec easing pour un effet plus naturel
        const easeOutQuart = t => 1 - Math.pow(1 - t, 4); // Fonction d'easing pour ralentir à la fin
        
        const step = timestamp => {
            if (!startTimestamp) startTimestamp = timestamp;
            const progress = Math.min((timestamp - startTimestamp) / duration, 1);
            const easedProgress = easeOutQuart(progress);
            const currentValue = Math.floor(easedProgress * target);
            
            counter.textContent = `${prefix}${formatNumber(currentValue)}${suffix}`;
            
            // Ajouter un effet de couleur qui change progressivement
            const hue = 200 + (easedProgress * 20); // Variation de bleu
            counter.style.color = `hsl(${hue}, 80%, 40%)`;
            
            if (progress < 1) {
                requestAnimationFrame(step);
            } else {
                counter.textContent = `${prefix}${formatNumber(target)}${suffix}`;
                counter.style.color = ''; // Réinitialiser la couleur
                
                // Ajouter un léger effet de rebond à la fin
                counter.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    counter.style.transform = 'scale(1)';
                    counter.style.transition = 'transform 0.3s ease-out';
                }, 50);
            }
        };
        
        requestAnimationFrame(step);
    });
    
    // Ajouter des barres de progression animées si elles existent
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        const value = bar.getAttribute('aria-valuenow');
        bar.style.width = '0%';
        
        setTimeout(() => {
            bar.style.transition = 'width 1.5s cubic-bezier(0.39, 0.575, 0.565, 1)';
            bar.style.width = `${value}%`;
        }, 300);
    });
}

/**
 * Formate un nombre avec des séparateurs de milliers
 */
function formatNumber(number) {
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ");
}

/**
 * Initialise les graphiques du tableau de bord
 */
function initCharts() {
    // Vérifier si Chart.js est disponible
    if (typeof Chart === 'undefined') {
        console.warn('Chart.js n\'est pas chargé. Les graphiques ne seront pas affichés.');
        return;
    }
    
    // Graphique des ventes mensuelles
    if (document.getElementById('salesChart')) {
        initSalesChart();
    }
    
    // Graphique des paiements
    if (document.getElementById('paymentsChart')) {
        initPaymentsChart();
    }
    
    // Graphique des recouvrements
    if (document.getElementById('recoveriesChart')) {
        initRecoveriesChart();
    }
}

/**
 * Initialise le graphique des ventes mensuelles avec animations et interactivité améliorée
 */
function initSalesChart() {
    const ctx = document.getElementById('salesChart').getContext('2d');
    
    // Créer un dégradé plus riche pour le fond du graphique
    const gradient = ctx.createLinearGradient(0, 0, 0, 300);
    gradient.addColorStop(0, 'rgba(30, 136, 229, 0.6)');
    gradient.addColorStop(0.5, 'rgba(30, 136, 229, 0.3)');
    gradient.addColorStop(1, 'rgba(30, 136, 229, 0.05)');
    
    // Récupérer les données depuis l'attribut data
    const chartData = JSON.parse(document.getElementById('salesChart').getAttribute('data-sales') || '[]');
    const labels = chartData.map(item => item.month);
    const data = chartData.map(item => item.total);
    
    // Configuration des plugins
    const chartPlugins = {
        legend: {
            display: false
        },
        tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            titleColor: '#1E88E5',
            bodyColor: '#495057',
            borderColor: 'rgba(30, 136, 229, 0.2)',
            borderWidth: 1,
            padding: 12,
            boxPadding: 6,
            usePointStyle: true,
            titleFont: {
                weight: 'bold',
                size: 14
            },
            bodyFont: {
                size: 13
            },
            callbacks: {
                label: function(context) {
                    return `Ventes: ${context.raw.toLocaleString()} F`;
                },
                title: function(context) {
                    return context[0].label;
                }
            }
        },
        annotation: {
            annotations: {
                line1: {
                    type: 'line',
                    yMin: Math.max(...data) * 0.9,
                    yMax: Math.max(...data) * 0.9,
                    borderColor: 'rgba(76, 175, 80, 0.5)',
                    borderWidth: 2,
                    borderDash: [5, 5],
                    label: {
                        content: 'Objectif',
                        display: true,
                        position: 'end',
                        backgroundColor: 'rgba(76, 175, 80, 0.8)',
                        font: {
                            size: 11
                        }
                    }
                }
            }
        }
    };
    
    // Stocker la référence du graphique pour pouvoir le mettre à jour plus tard
    window.salesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Ventes',
                data: data,
                borderColor: '#1E88E5',
                backgroundColor: gradient,
                borderWidth: 3,
                pointBackgroundColor: '#1E88E5',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7,
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 2000,
                easing: 'easeOutQuart'
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            },
            plugins: chartPlugins,
            scales: {
                x: {
                    grid: {
                        display: false,
                        drawBorder: false
                    },
                    ticks: {
                        color: '#6c757d',
                        font: {
                            size: 11
                        }
                    }
                },
                y: {
                    grid: {
                        borderDash: [3, 3],
                        drawBorder: false,
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        color: '#6c757d',
                        font: {
                            size: 11
                        },
                        callback: function(value) {
                            return value.toLocaleString() + ' F';
                        }
                    }
                }
            }
        }
    });
    
    // Ajouter un effet de survol pour mettre en évidence les points du graphique
    const salesChartElement = document.getElementById('salesChart');
    if (salesChartElement) {
        salesChartElement.addEventListener('mousemove', (e) => {
            const points = window.salesChart.getElementsAtEventForMode(e, 'nearest', { intersect: true }, false);
            if (points.length) {
                salesChartElement.style.cursor = 'pointer';
            } else {
                salesChartElement.style.cursor = 'default';
            }
        });
        
        // Ajouter un effet de clic pour afficher plus de détails
        salesChartElement.addEventListener('click', (e) => {
            const points = window.salesChart.getElementsAtEventForMode(e, 'nearest', { intersect: true }, false);
            if (points.length) {
                const point = points[0];
                const label = window.salesChart.data.labels[point.index];
                const value = window.salesChart.data.datasets[point.datasetIndex].data[point.index];
                showNotification(`Détails pour ${label}: ${value.toLocaleString()} F`, 'info');
            }
        });
    }
}

/**
 * Initialise le graphique des paiements avec animations et interactivité améliorée
 */
function initPaymentsChart() {
    const ctx = document.getElementById('paymentsChart').getContext('2d');
    
    // Récupérer les données depuis l'attribut data
    const chartData = JSON.parse(document.getElementById('paymentsChart').getAttribute('data-payments') || '{}');
    
    // Définir des couleurs plus vives avec transparence pour un meilleur effet visuel
    const colors = {
        paid: {
            background: '#4CAF50',
            hover: '#43A047',
            border: '#fff'
        },
        partial: {
            background: '#FF9800',
            hover: '#F57C00',
            border: '#fff'
        },
        unpaid: {
            background: '#F44336',
            hover: '#E53935',
            border: '#fff'
        }
    };
    
    // Stocker la référence du graphique pour pouvoir le mettre à jour plus tard
    window.paymentsChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Payé', 'Partiel', 'Impayé'],
            datasets: [{
                data: [chartData.paid || 0, chartData.partial || 0, chartData.unpaid || 0],
                backgroundColor: [
                    colors.paid.background,
                    colors.partial.background,
                    colors.unpaid.background
                ],
                hoverBackgroundColor: [
                    colors.paid.hover,
                    colors.partial.hover,
                    colors.unpaid.hover
                ],
                borderColor: '#fff',
                borderWidth: 3,
                hoverOffset: 8,
                borderRadius: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '75%',
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 2000,
                easing: 'easeOutQuart'
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    titleColor: '#1E88E5',
                    bodyColor: '#495057',
                    borderColor: 'rgba(0, 0, 0, 0.1)',
                    borderWidth: 1,
                    padding: 12,
                    boxPadding: 6,
                    usePointStyle: true,
                    titleFont: {
                        weight: 'bold',
                        size: 14
                    },
                    bodyFont: {
                        size: 13
                    },
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((context.raw / total) * 100);
                            return `${context.label}: ${context.raw.toLocaleString()} (${percentage}%)`;
                        },
                        // Personnaliser le titre du tooltip
                        title: function(context) {
                            return `Statut: ${context[0].label}`;
                        }
                    }
                }
            }
        }
    });
    
    // Ajouter un effet de survol pour mettre en évidence les segments du graphique
    const paymentsChartElement = document.getElementById('paymentsChart');
    if (paymentsChartElement) {
        // Ajouter une légende interactive sous le graphique
        const legendContainer = document.createElement('div');
        legendContainer.className = 'chart-legend mt-3 d-flex justify-content-center';
        
        const legendItems = [
            { label: 'Payé', color: colors.paid.background, value: chartData.paid || 0 },
            { label: 'Partiel', color: colors.partial.background, value: chartData.partial || 0 },
            { label: 'Impayé', color: colors.unpaid.background, value: chartData.unpaid || 0 }
        ];
        
        legendItems.forEach((item, index) => {
            const legendItem = document.createElement('div');
            legendItem.className = 'legend-item mx-2 d-flex align-items-center cursor-pointer';
            legendItem.innerHTML = `
                <div class="legend-color me-2" style="width: 12px; height: 12px; border-radius: 50%; background-color: ${item.color}"></div>
                <div class="legend-label">${item.label} (${item.value})</div>
            `;
            
            // Ajouter un effet de survol sur la légende
            legendItem.addEventListener('mouseenter', () => {
                window.paymentsChart.setActiveElements([{
                    datasetIndex: 0,
                    index: index
                }]);
                window.paymentsChart.update();
                legendItem.style.opacity = '1';
                legendItem.style.fontWeight = 'bold';
            });
            
            legendItem.addEventListener('mouseleave', () => {
                window.paymentsChart.setActiveElements([]);
                window.paymentsChart.update();
                legendItem.style.opacity = '0.8';
                legendItem.style.fontWeight = 'normal';
            });
            
            legendContainer.appendChild(legendItem);
        });
        
        // Insérer la légende après le canvas
        paymentsChartElement.parentNode.insertBefore(legendContainer, paymentsChartElement.nextSibling);
    }
}

/**
 * Initialise le graphique des recouvrements avec animations et interactivité améliorée
 */
function initRecoveriesChart() {
    const ctx = document.getElementById('recoveriesChart').getContext('2d');
    
    // Récupérer les données depuis l'attribut data
    const chartData = JSON.parse(document.getElementById('recoveriesChart').getAttribute('data-recoveries') || '[]');
    const labels = chartData.map(item => item.month);
    const recovered = chartData.map(item => item.recovered);
    const pending = chartData.map(item => item.pending);
    
    // Définir des couleurs plus attrayantes
    const colors = {
        recovered: {
            background: 'rgba(76, 175, 80, 0.8)',
            border: '#43A047',
            hover: 'rgba(76, 175, 80, 1)'
        },
        pending: {
            background: 'rgba(255, 152, 0, 0.8)',
            border: '#F57C00',
            hover: 'rgba(255, 152, 0, 1)'
        }
    };
    
    // Stocker la référence du graphique pour pouvoir le mettre à jour plus tard
    window.recoveriesChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Recouvé',
                    data: recovered,
                    backgroundColor: colors.recovered.background,
                    borderColor: colors.recovered.border,
                    hoverBackgroundColor: colors.recovered.hover,
                    borderWidth: 2,
                    borderRadius: 6,
                    barPercentage: 0.6,
                    categoryPercentage: 0.8
                },
                {
                    label: 'En attente',
                    data: pending,
                    backgroundColor: colors.pending.background,
                    borderColor: colors.pending.border,
                    hoverBackgroundColor: colors.pending.hover,
                    borderWidth: 2,
                    borderRadius: 6,
                    barPercentage: 0.6,
                    categoryPercentage: 0.8
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 2000,
                easing: 'easeOutQuart'
            },
            interaction: {
                mode: 'index',
                intersect: false
            },
            plugins: {
                legend: {
                    position: 'top',
                    align: 'center',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        boxWidth: 10,
                        boxHeight: 10,
                        font: {
                            size: 12,
                            weight: 'bold'
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    titleColor: '#1E88E5',
                    bodyColor: '#495057',
                    borderColor: 'rgba(0, 0, 0, 0.1)',
                    borderWidth: 1,
                    padding: 12,
                    boxPadding: 6,
                    usePointStyle: true,
                    titleFont: {
                        weight: 'bold',
                        size: 14
                    },
                    bodyFont: {
                        size: 13
                    },
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${context.raw.toLocaleString()} F`;
                        },
                        // Ajouter le pourcentage du total
                        afterLabel: function(context) {
                            const datasetIndex = context.datasetIndex;
                            const dataIndex = context.dataIndex;
                            const value = context.dataset.data[dataIndex];
                            const total = recovered[dataIndex] + pending[dataIndex];
                            const percentage = Math.round((value / total) * 100);
                            return `${percentage}% du total`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false,
                        drawBorder: false
                    },
                    ticks: {
                        color: '#6c757d',
                        font: {
                            size: 11
                        }
                    }
                },
                y: {
                    grid: {
                        borderDash: [3, 3],
                        drawBorder: false,
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        color: '#6c757d',
                        font: {
                            size: 11
                        },
                        callback: function(value) {
                            return value.toLocaleString() + ' F';
                        }
                    }
                }
            }
        }
    });
    
    // Ajouter un effet de survol pour mettre en évidence les barres du graphique
    const recoveriesChartElement = document.getElementById('recoveriesChart');
    if (recoveriesChartElement) {
        recoveriesChartElement.addEventListener('mousemove', (e) => {
            const points = window.recoveriesChart.getElementsAtEventForMode(e, 'nearest', { intersect: true }, false);
            if (points.length) {
                recoveriesChartElement.style.cursor = 'pointer';
            } else {
                recoveriesChartElement.style.cursor = 'default';
            }
        });
        
        // Ajouter un effet de clic pour afficher plus de détails
        recoveriesChartElement.addEventListener('click', (e) => {
            const points = window.recoveriesChart.getElementsAtEventForMode(e, 'nearest', { intersect: true }, false);
            if (points.length) {
                const point = points[0];
                const label = window.recoveriesChart.data.labels[point.index];
                const datasetLabel = window.recoveriesChart.data.datasets[point.datasetIndex].label;
                const value = window.recoveriesChart.data.datasets[point.datasetIndex].data[point.index];
                showNotification(`${datasetLabel} pour ${label}: ${value.toLocaleString()} F`, 'info');
            }
        });
    }
}

/**
 * Initialise les notifications du tableau de bord
 */
function initNotifications() {
    // Afficher une notification de bienvenue
    setTimeout(() => {
        showNotification('Bienvenue dans votre tableau de bord comptable', 'success');
    }, 1000);
    
    // Gérer les boutons de notification
    const notifyButtons = document.querySelectorAll('[data-notify]');
    notifyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const message = this.getAttribute('data-notify');
            const type = this.getAttribute('data-notify-type') || 'primary';
            showNotification(message, type);
        });
    });
}

/**
 * Affiche une notification sur le tableau de bord
 */
function showNotification(message, type = 'primary') {
    // Créer l'élément de notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    // Choisir l'icône en fonction du type de notification
    let icon = 'fa-info-circle';
    if (type === 'success') icon = 'fa-check-circle';
    if (type === 'warning') icon = 'fa-exclamation-triangle';
    if (type === 'error') icon = 'fa-times-circle';
    
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas ${icon} me-3"></i>
            <div>
                <p class="mb-0">${message}</p>
            </div>
            <button type="button" class="btn-close ms-3" aria-label="Close"></button>
        </div>
    `;
    
    // Ajouter la notification au document
    document.body.appendChild(notification);
    
    // Afficher la notification avec un délai
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // Gérer la fermeture de la notification
    const closeButton = notification.querySelector('.btn-close');
    closeButton.addEventListener('click', () => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
    
    // Fermer automatiquement la notification après 5 secondes
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }
    }, 5000);
}

/**
 * Initialise les filtres de période avec animations et effets interactifs
 */
function initPeriodFilters() {
    const periodFilters = document.querySelectorAll('.period-filter');
    if (!periodFilters.length) return;
    
    // Ajouter la classe active au filtre par défaut (Tous)
    const defaultFilter = document.querySelector('.period-filter[data-period="all"]');
    if (defaultFilter) defaultFilter.classList.add('active');
    
    // Ajouter des effets de survol et de clic aux filtres
    periodFilters.forEach(filter => {
        // Effet de survol
        filter.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.classList.add('hover');
            }
        });
        
        filter.addEventListener('mouseleave', function() {
            this.classList.remove('hover');
        });
        
        // Effet de clic et filtrage AJAX
        filter.addEventListener('click', function() {
            // Supprimer la classe active de tous les filtres
            periodFilters.forEach(f => f.classList.remove('active'));
            
            // Ajouter la classe active au filtre cliqué
            this.classList.add('active');
            
            // Ajouter un effet d'onde au clic
            createRippleEffect(this);
            
            // Récupérer la période sélectionnée
            const period = this.getAttribute('data-period');
            
            // Afficher un indicateur de chargement
            const dashboardContent = document.querySelector('.dashboard-content');
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'loading-overlay fadeIn';
            loadingOverlay.innerHTML = `
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <p class="mt-2">Mise à jour des données...</p>
            `;
            dashboardContent.appendChild(loadingOverlay);
            
            // Mettre à jour les données via AJAX
            fetch(`/accountant/dashboard/data?period=${period}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erreur lors de la récupération des données');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Mettre à jour les statistiques
                    updateStatistics(data.statistics);
                    
                    // Mettre à jour les graphiques
                    updateCharts(data.chartData);
                    
                    // Mettre à jour les tableaux
                    updateTables(data.tablesData);
                    
                    // Afficher une notification de succès
                    showNotification(`Données mises à jour pour la période: ${getPeriodLabel(period)}`, 'success');
                } else {
                    showNotification('Erreur lors de la mise à jour des données', 'error');
                }
                
                // Supprimer l'indicateur de chargement avec animation
                loadingOverlay.classList.remove('fadeIn');
                loadingOverlay.classList.add('fadeOut');
                setTimeout(() => {
                    loadingOverlay.remove();
                }, 500);
            })
            .catch(error => {
                console.error('Erreur AJAX:', error);
                showNotification('Erreur lors de la mise à jour des données', 'error');
                
                // Supprimer l'indicateur de chargement en cas d'erreur
                if (document.querySelector('.loading-overlay')) {
                    document.querySelector('.loading-overlay').remove();
                }
            });
        });
    });
}

/**
 * Met à jour les statistiques avec les nouvelles données
 */
function updateStatistics(statistics) {
    // Mettre à jour les compteurs avec animation
    if (statistics.totalSales !== undefined) {
        updateCounter('totalSales', statistics.totalSales);
    }
    if (statistics.totalRevenue !== undefined) {
        updateCounter('totalRevenue', statistics.totalRevenue);
    }
    if (statistics.totalPayments !== undefined) {
        updateCounter('totalPayments', statistics.totalPayments);
    }
    if (statistics.pendingPayments !== undefined) {
        updateCounter('pendingPayments', statistics.pendingPayments);
    }
    if (statistics.totalInvoices !== undefined) {
        updateCounter('totalInvoices', statistics.totalInvoices);
    }
    if (statistics.paidInvoices !== undefined) {
        updateCounter('paidInvoices', statistics.paidInvoices);
    }
    if (statistics.partialInvoices !== undefined) {
        updateCounter('partialInvoices', statistics.partialInvoices);
    }
    if (statistics.unpaidInvoices !== undefined) {
        updateCounter('unpaidInvoices', statistics.unpaidInvoices);
    }
}

/**
 * Met à jour un compteur avec animation
 */
function updateCounter(id, newValue) {
    const counterElement = document.getElementById(id);
    if (!counterElement) return;
    
    const currentValue = parseInt(counterElement.innerText.replace(/[^0-9]/g, '')) || 0;
    
    // Ajouter une classe pour l'animation
    counterElement.classList.add('updating');
    
    // Ajouter l'animation de compteur
    const duration = 1000;
    const steps = 20;
    const stepValue = (newValue - currentValue) / steps;
    let currentStep = 0;
    
    const updateStep = () => {
        currentStep++;
        const stepDisplay = Math.round(currentValue + (stepValue * currentStep));
        
        // Formatter le nombre avec des séparateurs de milliers
        counterElement.innerText = stepDisplay.toLocaleString();
        
        if (currentStep < steps) {
            requestAnimationFrame(updateStep);
        } else {
            // Valeur finale exacte
            counterElement.innerText = newValue.toLocaleString();
            
            // Retirer la classe d'animation
            setTimeout(() => {
                counterElement.classList.remove('updating');
            }, 300);
        }
    };
    
    updateStep();
}

/**
 * Met à jour les graphiques avec les nouvelles données
 */
function updateCharts(chartData) {
    // Mettre à jour le graphique des ventes mensuelles
    if (chartData.monthlySales && window.salesChart) {
        window.salesChart.data.labels = chartData.monthlySales.labels;
        window.salesChart.data.datasets[0].data = chartData.monthlySales.data;
        window.salesChart.update();
    }
    
    // Mettre à jour le graphique des statuts de paiement
    if (chartData.paymentStatus && window.paymentsChart) {
        window.paymentsChart.data.labels = chartData.paymentStatus.labels;
        window.paymentsChart.data.datasets[0].data = chartData.paymentStatus.data;
        
        // Mettre à jour les couleurs si elles sont fournies
        if (chartData.paymentStatus.colors) {
            window.paymentsChart.data.datasets[0].backgroundColor = chartData.paymentStatus.colors;
        }
        
        window.paymentsChart.update();
        
        // Mettre à jour la légende
        updateChartLegend('paymentsChart', chartData.paymentStatus);
    }
    
    // Mettre à jour le graphique des taux de recouvrement
    if (chartData.recoveryRate && window.recoveriesChart) {
        window.recoveriesChart.data.labels = chartData.recoveryRate.labels;
        window.recoveriesChart.data.datasets[0].data = chartData.recoveryRate.data;
        window.recoveriesChart.update();
    }
}

/**
 * Met à jour la légende d'un graphique
 */
function updateChartLegend(chartId, chartData) {
    const chartElement = document.getElementById(chartId);
    if (!chartElement) return;
    
    // Trouver le conteneur de légende existant
    const legendContainer = chartElement.parentNode.querySelector('.chart-legend');
    if (!legendContainer) return;
    
    // Vider le conteneur
    legendContainer.innerHTML = '';
    
    // Créer les nouveaux éléments de légende
    chartData.labels.forEach((label, index) => {
        const color = chartData.colors ? chartData.colors[index] : window.paymentsChart.data.datasets[0].backgroundColor[index];
        const value = chartData.data[index];
        
        const legendItem = document.createElement('div');
        legendItem.className = 'legend-item mx-2 d-flex align-items-center cursor-pointer';
        legendItem.innerHTML = `
            <div class="legend-color me-2" style="width: 12px; height: 12px; border-radius: 50%; background-color: ${color}"></div>
            <div class="legend-label">${label} (${value})</div>
        `;
        
        // Ajouter un effet de survol sur la légende
        legendItem.addEventListener('mouseenter', () => {
            window.paymentsChart.setActiveElements([{
                datasetIndex: 0,
                index: index
            }]);
            window.paymentsChart.update();
            legendItem.style.opacity = '1';
            legendItem.style.fontWeight = 'bold';
        });
        
        legendItem.addEventListener('mouseleave', () => {
            window.paymentsChart.setActiveElements([]);
            window.paymentsChart.update();
            legendItem.style.opacity = '0.8';
            legendItem.style.fontWeight = 'normal';
        });
        
        legendContainer.appendChild(legendItem);
    });
}

/**
 * Met à jour les tableaux avec les nouvelles données
 */
function updateTables(tablesData) {
    // Mettre à jour le tableau des ventes récentes
    if (tablesData.recentSales) {
        updateRecentSalesTable(tablesData.recentSales);
    }
    
    // Mettre à jour le tableau des paiements récents
    if (tablesData.recentPayments) {
        updateRecentPaymentsTable(tablesData.recentPayments);
    }
}

/**
 * Met à jour le tableau des ventes récentes
 */
function updateRecentSalesTable(sales) {
    const tableBody = document.querySelector('#recentSalesTable tbody');
    if (!tableBody) return;
    
    // Vider le tableau
    tableBody.innerHTML = '';
    
    // Ajouter les nouvelles lignes
    if (sales.length === 0) {
        // Afficher un message si aucune vente
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="5" class="text-center">Aucune vente pour cette période</td>`;
        tableBody.appendChild(emptyRow);
    } else {
        sales.forEach(sale => {
            const row = document.createElement('tr');
            
            // Déterminer la classe du badge en fonction du statut
            let badgeClass = 'bg-primary';
            if (sale.payment_status === 'paid' || sale.payment_status === 'completed') {
                badgeClass = 'bg-success';
            } else if (sale.payment_status === 'partial') {
                badgeClass = 'bg-warning';
            } else if (sale.payment_status === 'unpaid') {
                badgeClass = 'bg-danger';
            }
            
            // Formater la date
            const date = new Date(sale.created_at);
            const formattedDate = `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
            
            row.innerHTML = `
                <td>#${sale.invoice_number || 'SALE-' + sale.id}</td>
                <td>${sale.customer ? sale.customer.name : 'Client inconnu'}</td>
                <td>${sale.total_amount.toLocaleString()} F</td>
                <td><span class="badge ${badgeClass}">${sale.payment_status}</span></td>
                <td>${formattedDate}</td>
            `;
            
            // Ajouter un effet d'animation à la nouvelle ligne
            row.classList.add('fadeIn');
            row.style.animationDelay = `${0.1}s`;
            
            tableBody.appendChild(row);
        });
    }
}

/**
 * Met à jour le tableau des paiements récents
 */
function updateRecentPaymentsTable(payments) {
    const tableBody = document.querySelector('#recentPaymentsTable tbody');
    if (!tableBody) return;
    
    // Vider le tableau
    tableBody.innerHTML = '';
    
    // Ajouter les nouvelles lignes
    if (payments.length === 0) {
        // Afficher un message si aucun paiement
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="5" class="text-center">Aucun paiement pour cette période</td>`;
        tableBody.appendChild(emptyRow);
    } else {
        payments.forEach(payment => {
            const row = document.createElement('tr');
            
            // Formater la date
            const date = new Date(payment.created_at);
            const formattedDate = `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
            
            row.innerHTML = `
                <td>#${payment.id}</td>
                <td>${payment.sale ? '#' + (payment.sale.invoice_number || 'SALE-' + payment.sale_id) : 'Vente inconnue'}</td>
                <td>${payment.amount.toLocaleString()} F</td>
                <td>${payment.payment_method || 'Non spécifié'}</td>
                <td>${formattedDate}</td>
            `;
            
            // Ajouter un effet d'animation à la nouvelle ligne
            row.classList.add('fadeIn');
            row.style.animationDelay = `${0.1}s`;
            
            tableBody.appendChild(row);
        });
    }
}

/**
 * Retourne le libelé d'une période à partir de son code
 */
function getPeriodLabel(period) {
    const labels = {
        'today': "Aujourd'hui",
        'week': 'Cette semaine',
        'month': 'Ce mois',
        'quarter': 'Ce trimestre',
        'year': 'Cette année',
        'all': 'Toutes les périodes'
    };
    
    return labels[period] || period;
}
        });
    });
}

/**
 * Crée un effet d'onde au clic sur un élément
 */
function createRippleEffect(element) {
    const ripple = document.createElement('span');
    ripple.classList.add('ripple-effect');
    
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    
    // Utiliser l'event passé par le gestionnaire d'événements
    ripple.style.width = ripple.style.height = `${size}px`;
    
    // Correction: utiliser l'event de la fonction parente
    element.addEventListener('click', function(e) {
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.left = `${x}px`;
        ripple.style.top = `${y}px`;
    }, { once: true });
    
    element.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * Retourne le libelé d'une période à partir de son code
 */
function getPeriodLabel(period) {
    const labels = {
        'today': "Aujourd'hui",
        'week': 'Cette semaine',
        'month': 'Ce mois',
        'quarter': 'Ce trimestre',
        'year': 'Cette année',
        'all': 'Toutes les périodes'
    };
    
    return labels[period] || period;
}

/**
 * Met à jour les statistiques avec les nouvelles données
 */
function updateStatistics(statistics) {
    // Mettre à jour les compteurs avec animation
    if (statistics.totalSales) {
        updateCounter('totalSales', statistics.totalSales);
    }
    
    if (statistics.totalRevenue) {
        updateCounter('totalRevenue', statistics.totalRevenue);
    }
    
    if (statistics.totalPayments) {
        updateCounter('totalPayments', statistics.totalPayments);
    }
    
    if (statistics.totalInvoices) {
        updateCounter('totalInvoices', statistics.totalInvoices);
    }
    
    // Mettre à jour les pourcentages et barres de progression
    if (statistics.percentages) {
        Object.keys(statistics.percentages).forEach(key => {
            const element = document.getElementById(`${key}Percentage`);
            if (element) {
                const percentage = statistics.percentages[key];
                updateProgressBar(element, percentage);
            }
        });
    }
}

/**
 * Met à jour un compteur avec animation
 */
function updateCounter(id, newValue) {
    const counterElement = document.getElementById(id);
    if (!counterElement) return;
    
    const startValue = parseInt(counterElement.getAttribute('data-value') || '0');
    const endValue = parseInt(newValue);
    
    // Mettre à jour l'attribut data-value
    counterElement.setAttribute('data-value', endValue);
    
    // Animer le compteur
    animateCounter(counterElement, startValue, endValue);
}

/**
 * Met à jour une barre de progression avec animation
 */
function updateProgressBar(element, percentage) {
    if (!element) return;
    
    const progressBar = element.querySelector('.progress-bar');
    if (!progressBar) return;
    
    const currentWidth = parseInt(progressBar.style.width || '0');
    const targetWidth = percentage;
    
    // Animer la barre de progression
    animateProgressBar(progressBar, currentWidth, targetWidth);
    
    // Mettre à jour le texte du pourcentage
    const percentageText = element.querySelector('.percentage-text');
    if (percentageText) {
        percentageText.textContent = `${percentage}%`;
    }
}

/**
 * Met à jour les graphiques avec les nouvelles données
 */
function updateCharts(chartData) {
    // Mettre à jour le graphique des ventes
    if (chartData.sales && window.salesChart) {
        const labels = chartData.sales.map(item => item.month);
        const data = chartData.sales.map(item => item.total);
        
        window.salesChart.data.labels = labels;
        window.salesChart.data.datasets[0].data = data;
        
        // Mettre à jour l'annotation de l'objectif
        if (window.salesChart.options.plugins && window.salesChart.options.plugins.annotation) {
            window.salesChart.options.plugins.annotation.annotations.line1.yMin = Math.max(...data) * 0.9;
            window.salesChart.options.plugins.annotation.annotations.line1.yMax = Math.max(...data) * 0.9;
        }
        
        window.salesChart.update();
    }
    
    // Mettre à jour le graphique des paiements
    if (chartData.payments && window.paymentsChart) {
        window.paymentsChart.data.datasets[0].data = [
            chartData.payments.paid || 0,
            chartData.payments.partial || 0,
            chartData.payments.unpaid || 0
        ];
        
        window.paymentsChart.update();
        
        // Mettre à jour la légende interactive
        const legendContainer = document.querySelector('.chart-legend');
        if (legendContainer) {
            const legendItems = legendContainer.querySelectorAll('.legend-item');
            if (legendItems.length === 3) {
                legendItems[0].querySelector('.legend-label').textContent = `Payé (${chartData.payments.paid || 0})`;
                legendItems[1].querySelector('.legend-label').textContent = `Partiel (${chartData.payments.partial || 0})`;
                legendItems[2].querySelector('.legend-label').textContent = `Impayé (${chartData.payments.unpaid || 0})`;
            }
        }
    }
    
    // Mettre à jour le graphique des recouvrements
    if (chartData.recoveries && window.recoveriesChart) {
        const labels = chartData.recoveries.map(item => item.month);
        const recovered = chartData.recoveries.map(item => item.recovered);
        const pending = chartData.recoveries.map(item => item.pending);
        
        window.recoveriesChart.data.labels = labels;
        window.recoveriesChart.data.datasets[0].data = recovered;
        window.recoveriesChart.data.datasets[1].data = pending;
        
        window.recoveriesChart.update();
    }
}

/**
 * Met à jour les tableaux avec les nouvelles données
 */
function updateTables(tablesData) {
    // Mettre à jour le tableau des ventes récentes
    if (tablesData.recentSales) {
        updateTableContent('recentSalesTable', tablesData.recentSales, [
            { key: 'reference', format: (value) => `<span class="text-primary">${value}</span>` },
            { key: 'client', format: (value) => value },
            { key: 'amount', format: (value) => `${value.toLocaleString()} F` },
            { key: 'date', format: (value) => value },
            { key: 'status', format: (value) => {
                const statusClasses = {
                    'payé': 'success',
                    'partiel': 'warning',
                    'impayé': 'danger'
                };
                const statusClass = statusClasses[value.toLowerCase()] || 'secondary';
                return `<span class="badge bg-${statusClass}">${value}</span>`;
            }}
        ]);
    }
    
    // Mettre à jour le tableau des activités récentes
    if (tablesData.recentActivities) {
        updateTableContent('recentActivitiesTable', tablesData.recentActivities, [
            { key: 'type', format: (value) => {
                const iconClasses = {
                    'vente': 'shopping-cart',
                    'paiement': 'credit-card',
                    'facture': 'file-text',
                    'client': 'user'
                };
                const iconClass = iconClasses[value.toLowerCase()] || 'activity';
                return `<i class="feather-${iconClass} me-2"></i>${value}`;
            }},
            { key: 'description', format: (value) => value },
            { key: 'date', format: (value) => value },
            { key: 'user', format: (value) => `<span class="text-primary">${value}</span>` }
        ]);
    }
}

/**
 * Met à jour le contenu d'un tableau avec de nouvelles données
 */
function updateTableContent(tableId, data, columns) {
    const tableBody = document.querySelector(`#${tableId} tbody`);
    if (!tableBody) return;
    
    // Vider le tableau actuel avec une animation de fondu
    const existingRows = tableBody.querySelectorAll('tr');
    existingRows.forEach((row, index) => {
        setTimeout(() => {
            row.classList.add('fadeOut');
        }, index * 50);
    });
    
    setTimeout(() => {
        // Vider complètement le tableau
        tableBody.innerHTML = '';
        
        // Ajouter les nouvelles lignes avec une animation d'entrée
        data.forEach((item, index) => {
            const row = document.createElement('tr');
            row.className = 'fadeIn';
            row.style.animationDelay = `${index * 50}ms`;
            
            // Créer chaque cellule selon les colonnes définies
            columns.forEach(column => {
                const cell = document.createElement('td');
                const value = item[column.key];
                cell.innerHTML = column.format ? column.format(value) : value;
                row.appendChild(cell);
            });
            
            // Ajouter la ligne au tableau
            tableBody.appendChild(row);
        });
    }, existingRows.length * 50 + 300);
}

/**
 * Initialise les interactions avancées sur le tableau de bord
 */
function initAdvancedInteractions() {
    // Ajouter des effets de survol sur les cartes statistiques
    const statCards = document.querySelectorAll('.stat-card, .data-card');
    statCards.forEach(card => {
        // Ajouter un effet de zoom au survol
        card.addEventListener('mouseenter', function() {
            this.classList.add('card-hover');
        });
        
        card.addEventListener('mouseleave', function() {
            this.classList.remove('card-hover');
        });
        
        // Ajouter un effet de clic pour afficher plus de détails
        card.addEventListener('click', function() {
            const cardTitle = this.querySelector('.card-title')?.textContent || 'Statistique';
            const cardValue = this.querySelector('.counter')?.textContent || '';
            
            showCardDetails(cardTitle, cardValue, this.getAttribute('data-details') || '');
        });
    });
    
    // Ajouter des interactions sur les lignes de tableau
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('click', function() {
            const cells = this.querySelectorAll('td');
            if (cells.length > 0) {
                const reference = cells[0].textContent;
                showRowDetails(reference, this);
            }
        });
    });
    
    // Ajouter des interactions sur les actions rapides
    const quickActions = document.querySelectorAll('.quick-action');
    quickActions.forEach(action => {
        action.addEventListener('click', function(e) {
            e.preventDefault();
            const actionType = this.getAttribute('data-action');
            const actionTitle = this.querySelector('.action-title')?.textContent || 'Action';
            
            executeQuickAction(actionType, actionTitle);
        });
    });
}

/**
 * Affiche les détails d'une carte statistique
 */
function showCardDetails(title, value, details) {
    // Créer une modal pour afficher les détails
    const modal = document.createElement('div');
    modal.className = 'modal-overlay fadeIn';
    modal.innerHTML = `
        <div class="modal-content zoomIn">
            <div class="modal-header">
                <h5 class="modal-title">${title}</h5>
                <button type="button" class="btn-close" aria-label="Fermer"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <h2 class="display-4">${value}</h2>
                </div>
                <div class="details-content">
                    ${details || 'Aucun détail supplémentaire disponible.'}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary">Voir plus</button>
                <button type="button" class="btn btn-secondary">Fermer</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Gérer la fermeture de la modal
    const closeButtons = modal.querySelectorAll('.btn-close, .btn-secondary');
    closeButtons.forEach(button => {
        button.addEventListener('click', () => {
            modal.classList.remove('fadeIn');
            modal.classList.add('fadeOut');
            setTimeout(() => {
                modal.remove();
            }, 300);
        });
    });
    
    // Gérer le bouton "Voir plus"
    const viewMoreButton = modal.querySelector('.btn-primary');
    if (viewMoreButton) {
        viewMoreButton.addEventListener('click', () => {
            // Rediriger vers une page de détails ou afficher plus d'informations
            showNotification(`Navigation vers les détails de ${title}`, 'info');
        });
    }
}

/**
 * Affiche les détails d'une ligne de tableau
 */
function showRowDetails(reference, row) {
    // Mettre en surbrillance la ligne sélectionnée
    const allRows = document.querySelectorAll('tbody tr');
    allRows.forEach(r => r.classList.remove('selected-row'));
    row.classList.add('selected-row');
    
    // Afficher une notification avec le référence
    showNotification(`Détails pour ${reference}`, 'info');
    
    // Ici, on pourrait charger des détails supplémentaires via AJAX
    // ou afficher une modal avec plus d'informations
}

/**
 * Exécute une action rapide
 */
function executeQuickAction(actionType, actionTitle) {
    // Afficher une notification pour confirmer l'action
    showNotification(`Action rapide: ${actionTitle}`, 'success');
    
    // Simuler une redirection ou une action spécifique
    console.log(`Exécution de l'action: ${actionType}`);
    
    // Ici, on pourrait implémenter des actions spécifiques selon le type
    switch (actionType) {
        case 'new-invoice':
            // Rediriger vers la page de création de facture
            // window.location.href = '/accountant/invoices/create';
            break;
        case 'new-payment':
            // Rediriger vers la page d'enregistrement de paiement
            // window.location.href = '/accountant/payments/create';
            break;
        case 'report':
            // Générer un rapport
            // window.open('/accountant/reports/generate', '_blank');
            break;
        default:
            // Action par défaut
            break;
    }
}

/**
 * Initialise les mises à jour en temps réel
 */
function initLiveUpdates() {
    // Simuler des mises à jour en temps réel toutes les 30 secondes
    setInterval(() => {
        // Vérifier s'il y a de nouvelles notifications ou activités
        checkForUpdates();
    }, 30000);
}

/**
 * Vérifie s'il y a des mises à jour à afficher
 */
function checkForUpdates() {
    // Simuler une requête AJAX pour vérifier les mises à jour
    console.log('Vérification des mises à jour...');
    
    // Dans un environnement réel, on ferait une requête AJAX ici
    // fetch('/accountant/dashboard/check-updates', ...)
    
    // Simuler l'arrivée d'une nouvelle notification aléatoire (1 chance sur 3)
    if (Math.random() > 0.7) {
        const notifications = [
            { title: 'Nouveau paiement reçu', message: 'Un paiement de 150,000 F a été reçu.', type: 'success' },
            { title: 'Facture en retard', message: 'La facture #INV-2023-042 est en retard de paiement.', type: 'warning' },
            { title: 'Nouvelle vente', message: 'Une nouvelle vente a été enregistrée.', type: 'info' }
        ];
        
        const randomNotification = notifications[Math.floor(Math.random() * notifications.length)];
        showNotification(randomNotification.title, randomNotification.type, randomNotification.message);
    }
}
