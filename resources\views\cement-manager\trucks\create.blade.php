@extends('layouts.cement-manager')

@section('title', 'Ajouter un véhicule')

@push('styles')
<style>
    .form-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .form-label {
        font-weight: 500;
        color: #2d3748;
    }
    .alert-float {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1050;
        min-width: 300px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .invalid-feedback {
        display: none;
        font-size: 0.875em;
    }
</style>
@endpush

@section('content')
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="form-card p-4">
                <h4 class="mb-4">
                    <i class="fas fa-truck-loading me-2"></i>
                    Ajouter un nouveau véhicule
                </h4>

                <form id="truckForm" class="needs-validation" novalidate>
                    @csrf
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="registration_number" class="form-label">Numéro d'immatriculation</label>
                            <input type="text" class="form-control" id="registration_number" name="registration_number" required>
                            <div class="invalid-feedback" data-field="registration_number"></div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="truck_capacity_id" class="form-label">Capacité</label>
                            <select class="form-select" id="truck_capacity_id" name="truck_capacity_id" required>
                                <option value="">Sélectionner une capacité</option>
                                @foreach($capacities as $capacity)
                                    <option value="{{ $capacity->id }}">{{ $capacity->description }}</option>
                                @endforeach
                            </select>
                            <div class="invalid-feedback" data-field="truck_capacity_id"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="brand" class="form-label">Marque</label>
                            <input type="text" class="form-control" id="brand" name="brand" required>
                            <div class="invalid-feedback" data-field="brand"></div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="model" class="form-label">Modèle</label>
                            <input type="text" class="form-control" id="model" name="model" required>
                            <div class="invalid-feedback" data-field="model"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="year" class="form-label">Année</label>
                            <input type="number" class="form-control" id="year" name="year" min="1900" max="{{ date('Y') + 1 }}" required>
                            <div class="invalid-feedback" data-field="year"></div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">État</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="">Sélectionner un état</option>
                                <option value="available">Disponible</option>
                                <option value="maintenance">En maintenance</option>
                                <option value="busy">Occupé</option>
                            </select>
                            <div class="invalid-feedback" data-field="status"></div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end mt-4">
                        <button type="button" class="btn btn-secondary me-2" onclick="window.history.back()">
                            <i class="fas fa-times me-1"></i> Annuler
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Alert Toast -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="alertToast" class="toast align-items-center text-white border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('truckForm');
    const toast = new bootstrap.Toast(document.getElementById('alertToast'));

    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Reset previous error states
        form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        form.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');

        // Préparer les données du formulaire
        const formData = new FormData(form);
        const data = {};
        formData.forEach((value, key) => {
            data[key] = value;
        });

        try {
            const response = await fetch('{{ route("cement-manager.trucks.store") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (!response.ok) {
                if (response.status === 422) {
                    // Validation errors
                    Object.keys(result.errors).forEach(field => {
                        const input = form.querySelector(`[name="${field}"]`);
                        const feedback = form.querySelector(`[data-field="${field}"]`);
                        if (input && feedback) {
                            input.classList.add('is-invalid');
                            feedback.textContent = result.errors[field][0];
                            feedback.style.display = 'block';
                        }
                    });
                } else {
                    throw new Error(result.message || 'Une erreur est survenue');
                }
                return;
            }

            // Success
            const alertToast = document.getElementById('alertToast');
            alertToast.classList.remove('bg-danger');
            alertToast.classList.add('bg-success');
            alertToast.querySelector('.toast-body').textContent = result.message;
            toast.show();

            // Reset form
            form.reset();

            // Redirect after 2 seconds
            setTimeout(() => {
                window.location.href = '{{ route("cement-manager.trucks.index") }}';
            }, 2000);

        } catch (error) {
            console.error('Error:', error);
            const alertToast = document.getElementById('alertToast');
            alertToast.classList.remove('bg-success');
            alertToast.classList.add('bg-danger');
            alertToast.querySelector('.toast-body').textContent = error.message;
            toast.show();
        }
    });
});
</script>
@endpush
