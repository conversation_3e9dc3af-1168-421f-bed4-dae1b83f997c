/* Adaptations responsives */
@media (max-width: 1199.98px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .dashboard-header h1 {
        font-size: 2rem;
    }
    
    .dashboard-header p {
        font-size: 1rem;
    }
    
    .stat-card-value {
        font-size: 1.75rem;
    }
    
    .quick-actions {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

@media (max-width: 991.98px) {
    .dashboard-header {
        padding: 2rem 0;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1rem;
    }
    
    .stat-card {
        padding: 1.25rem;
    }
    
    .stat-card-icon {
        width: 45px;
        height: 45px;
        font-size: 1.25rem;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .dashboard-card-header, .chart-card-header {
        padding: 1rem 1.25rem;
    }
    
    .dashboard-card-body, .chart-card-body {
        padding: 1.25rem;
    }
}

@media (max-width: 767.98px) {
    .dashboard-header {
        padding: 1.5rem 0;
        margin-bottom: 1.5rem;
    }
    
    .dashboard-header h1 {
        font-size: 1.75rem;
    }
    
    .dashboard-header p {
        font-size: 0.9rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 0.75rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-card-value {
        font-size: 1.5rem;
    }
    
    .stat-card-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }
    
    .quick-actions {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .quick-action {
        padding: 1.25rem;
    }
    
    .quick-action-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .table th, .table td {
        padding: 0.75rem;
    }
}

@media (max-width: 575.98px) {
    .dashboard-header {
        padding: 1.25rem 0;
        margin-bottom: 1rem;
    }
    
    .dashboard-header h1 {
        font-size: 1.5rem;
    }
    
    .dashboard-header .btn-dashboard {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .dashboard-card-header, .chart-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .chart-container {
        height: 200px;
    }
    
    .activity-timeline {
        padding-left: 1.5rem;
    }
    
    .activity-timeline::before {
        left: 6px;
    }
    
    .activity-item::before {
        width: 12px;
        height: 12px;
        left: -1.5rem;
    }
}

/* Animations supplémentaires */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
    100% { transform: translateY(0px); }
}

@keyframes glow {
    0% { box-shadow: 0 0 5px rgba(30, 136, 229, 0.3); }
    50% { box-shadow: 0 0 15px rgba(30, 136, 229, 0.5); }
    100% { box-shadow: 0 0 5px rgba(30, 136, 229, 0.3); }
}

.pulse { animation: pulse 2s infinite; }
.float { animation: float 3s ease-in-out infinite; }
.glow { animation: glow 2s infinite; }

/* Effets de glassmorphism */
.glass-card {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Pagination moderne */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 1.5rem;
    gap: 0.25rem;
}

.pagination .page-item .page-link {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 0.15rem;
    border: none;
    color: var(--gray-dark);
    background-color: rgba(0, 0, 0, 0.03);
    transition: var(--transition-fast);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 10px rgba(30, 136, 229, 0.3);
}

.pagination .page-item .page-link:hover {
    background-color: rgba(30, 136, 229, 0.1);
    color: var(--primary-color);
}

.pagination .page-item.disabled .page-link {
    color: var(--gray-light);
    background-color: transparent;
    cursor: not-allowed;
}

/* Boutons d'action */
.btn-action {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition-normal);
    text-decoration: none;
}

.btn-action.primary {
    background-color: rgba(30, 136, 229, 0.1);
    color: var(--primary-color);
}

.btn-action.primary:hover {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 10px rgba(30, 136, 229, 0.3);
}

.btn-action.success {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.btn-action.success:hover {
    background-color: var(--success-color);
    color: white;
    box-shadow: 0 4px 10px rgba(76, 175, 80, 0.3);
}

.btn-action.warning {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

.btn-action.warning:hover {
    background-color: var(--warning-color);
    color: white;
    box-shadow: 0 4px 10px rgba(255, 152, 0, 0.3);
}

.btn-action.danger {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
}

.btn-action.danger:hover {
    background-color: var(--danger-color);
    color: white;
    box-shadow: 0 4px 10px rgba(244, 67, 54, 0.3);
}

/* Utilitaires */
.text-primary { color: var(--primary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-dark { color: var(--dark-color) !important; }
.text-light { color: var(--light-color) !important; }
.text-white { color: var(--white-color) !important; }
.text-gray { color: var(--gray-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-dark { background-color: var(--dark-color) !important; }
.bg-light { background-color: var(--light-color) !important; }
.bg-white { background-color: var(--white-color) !important; }
.bg-gray { background-color: var(--gray-color) !important; }
