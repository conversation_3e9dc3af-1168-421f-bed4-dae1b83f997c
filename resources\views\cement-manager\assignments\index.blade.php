@extends('layouts.cement-manager')

@section('title', 'Liste des affectations')

@section('content')
<div class="container-fluid">
    <!-- En-tête -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Liste des affectations</h1>
    </div>

    <!-- Liste des affectations -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Affectations</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="assignments-table">
                    <thead>
                        <tr>
                            <th>Commande</th>
                            <th>Chauffeur</th>
                            <th>Camion</th>
                            <th>Voyage N°</th>
                            <th>Tonnage</th>
                            <th>Statut</th>
                            <th>Date de début</th>
                            <th>Date de fin</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($assignments as $assignment)
                            <tr>
                                <td>{{ $assignment->cementOrder->reference ?? 'N/A' }}</td>
                                <td>{{ $assignment->driver->full_name ?? 'N/A' }}</td>
                                <td>{{ $assignment->truck->registration_number ?? 'N/A' }}</td>
                                <td>{{ $assignment->trip_number }}</td>
                                <td>{{ number_format($assignment->tonnage, 2) }} T</td>
                                <td>
                                    <span class="badge bg-{{ $assignment->status === 'completed' ? 'success' : ($assignment->status === 'pending' ? 'warning' : 'primary') }}">
                                        {{ ucfirst($assignment->status) }}
                                    </span>
                                </td>
                                <td>{{ $assignment->start_date ? $assignment->start_date->format('d/m/Y H:i') : 'N/A' }}</td>
                                <td>{{ $assignment->end_date ? $assignment->end_date->format('d/m/Y H:i') : 'N/A' }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        @if($assignment->status === 'pending')
                                            <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#confirmDeliveryModal{{ $assignment->id }}">
                                                <i class="fas fa-check"></i> Confirmer la livraison
                                            </button>
                                        @endif
                                    </div>

                                    <!-- Modal de confirmation de livraison -->
                                    <div class="modal fade" id="confirmDeliveryModal{{ $assignment->id }}" tabindex="-1" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <form action="{{ route('cement-manager.assignments.confirm-delivery', $assignment) }}" method="POST">
                                                    @csrf
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">Confirmer la livraison</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="mb-3">
                                                            <label for="delivered_quantity" class="form-label">Quantité livrée (tonnes)</label>
                                                            <input type="number" step="0.01" class="form-control" id="delivered_quantity" 
                                                                   name="delivered_quantity" required 
                                                                   max="{{ $assignment->cementOrderDetail->remaining_quantity }}">
                                                        </div>
                                                        <div class="mb-3">
                                                            <label for="notes" class="form-label">Notes</label>
                                                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                                        <button type="submit" class="btn btn-success">Confirmer</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center">Aucune affectation trouvée</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4">
                {{ $assignments->links() }}
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialisation de DataTables
        $('#assignments-table').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json'
            },
            order: [[6, 'desc']], // Trier par date de début par défaut
            pageLength: 10
        });
    });
</script>
@endpush
