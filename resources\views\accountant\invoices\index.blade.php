@extends('layouts.accountant')

@section('title', 'Gestion des factures')

@section('styles')
<!-- Ajouter le CSS moderne des factures -->
<link href="{{ asset('css/modern-invoices.css') }}" rel="stylesheet">
<!-- Google Font pour les montants -->
<link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500&display=swap" rel="stylesheet">
<style>
    /* Solution directe pour le problème de la sidebar qui cache le contenu */
    .sidebar {
        width: 260px !important;
        z-index: 1040 !important;
    }
    
    #content-wrapper {
        margin-left: 260px !important;
        width: calc(100% - 260px) !important;
        padding: 0 !important;
        box-sizing: border-box !important;
    }
    
    .top-navbar {
        left: 260px !important;
        width: calc(100% - 260px) !important;
        box-sizing: border-box !important;
    }
    
    #content {
        padding-top: 90px !important;
        padding-right: 20px !important;
        padding-bottom: 20px !important;
        padding-left: 20px !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }
    
    /* Assurer que le contenu reste dans son conteneur */
    .container-fluid {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow-x: hidden !important;
    }
    
    .invoice-header,
    .filters-container,
    .card,
    .invoices-pagination {
        width: 100% !important;
        max-width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        box-sizing: border-box !important;
    }
    
    /* Version mobile */
    @media (max-width: 768px) {
        .sidebar {
            transform: translateX(-260px) !important;
        }
        
        #content-wrapper,
        .top-navbar {
            margin-left: 0 !important;
            width: 100% !important;
            left: 0 !important;
        }
        
        .sidebar.active {
            transform: translateX(0) !important;
        }
        
        .invoice-header h1 {
            font-size: 1.5rem !important;
        }
        
        .btn-invoice-primary {
            width: 100% !important;
            margin-top: 10px !important;
        }
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- En-tête de la section -->
    <div class="invoice-header fade-in-up">
        <h1>Gestion des factures</h1>
        <p>Gérez toutes les factures, suivez les paiements et téléchargez des copies des factures.</p>
    </div>
    
    <!-- Section des statistiques -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3 mb-md-0">
            <div class="stats-card fade-in-up delay-100">
                <div class="icon">
                    <i data-feather="file-text"></i>
                </div>
                <div class="stat-label">Total factures</div>
                <div class="stat-value">{{ $invoices->total() }}</div>
            </div>
        </div>
        <div class="col-md-4 mb-3 mb-md-0">
            <div class="stats-card fade-in-up delay-200">
                <div class="icon">
                    <i data-feather="dollar-sign"></i>
                </div>
                <div class="stat-label">Montant total</div>
                <div class="stat-value currency">{{ number_format($invoices->sum('total_amount'), 0, ',', ' ') }}</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card fade-in-up delay-300">
                <div class="icon">
                    <i data-feather="trending-up"></i>
                </div>
                <div class="stat-label">Moyenne par facture</div>
                <div class="stat-value currency">{{ number_format($invoices->avg('total_amount') ?: 0, 0, ',', ' ') }}</div>
            </div>
        </div>
    </div>
    
    <!-- Section de filtres et recherche -->
    <div class="filters-container fade-in-up delay-100">
        <div class="row align-items-center">
            <div class="col-md-6">
                <form action="{{ route('accountant.invoices.index') }}" method="GET" class="position-relative">
                    <i data-feather="search" class="search-icon"></i>
                    <input type="text" class="form-control search-input" placeholder="Rechercher une facture..." 
                           name="search" value="{{ request('search') }}">
                </form>
            </div>
            <div class="col-md-3">
                <div class="filter-dropdown">
                    <select class="form-control" name="payment_status" onchange="this.form.submit()">
                        <option value="">Tous les statuts</option>
                        <option value="completed" {{ request('payment_status') == 'completed' ? 'selected' : '' }}>Payées</option>
                        <option value="partial" {{ request('payment_status') == 'partial' ? 'selected' : '' }}>Partielles</option>
                        <option value="pending" {{ request('payment_status') == 'pending' ? 'selected' : '' }}>En attente</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3 text-md-end">
                <a href="{{ route('accountant.invoices.create') }}" class="btn btn-invoice btn-invoice-primary">
                    <i data-feather="plus"></i> Nouvelle facture
                </a>
            </div>
        </div>
    </div>
    
    <!-- Tableau des factures -->
    <div class="card fade-in-up delay-200">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table invoices-table mb-0">
                    <thead>
                        <tr>
                            <th>N° Facture</th>
                            <th>Client</th>
                            <th>Date</th>
                            <th>Montant</th>
                            <th>Statut</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($invoices as $invoice)
                            <tr>
                                <td>
                                    <strong>{{ $invoice->invoice_number ?? ('INV-' . str_pad($invoice->id, 6, '0', STR_PAD_LEFT)) }}</strong>
                                </td>
                                <td>
                                    <div class="fw-semibold">{{ $invoice->customer->name ?? 'N/A' }}</div>
                                    <small class="text-muted">{{ $invoice->customer->email ?? 'N/A' }}</small>
                                </td>
                                <td>{{ $invoice->created_at->format('d/m/Y') }}</td>
                                <td class="currency fw-semibold">{{ number_format($invoice->total_amount, 0, ',', ' ') }}</td>
                                <td>
                                    @if($invoice->payment_status == 'completed')
                                        <span class="badge-status badge-paid">
                                            <i data-feather="check-circle"></i> Payée
                                        </span>
                                    @elseif($invoice->payment_status == 'partial')
                                        <span class="badge-status badge-pending">
                                            <i data-feather="clock"></i> Partielle
                                        </span>
                                    @else
                                        <span class="badge-status badge-late">
                                            <i data-feather="alert-circle"></i> En attente
                                        </span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    <a href="{{ route('accountant.invoices.show', $invoice) }}" 
                                       class="btn-icon btn-invoice-view" title="Voir la facture">
                                        <i data-feather="eye"></i>
                                    </a>
                                    <a href="{{ route('accountant.invoices.download', $invoice) }}" 
                                       class="btn-icon btn-invoice-download" title="Télécharger PDF">
                                        <i data-feather="download"></i>
                                    </a>
                                    <a href="{{ route('accountant.invoices.print', $invoice) }}" 
                                       target="_blank" class="btn-icon btn-invoice-print" title="Imprimer">
                                        <i data-feather="printer"></i>
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center py-5">
                                    <div class="d-flex flex-column align-items-center">
                                        <i data-feather="file-text" style="width: 48px; height: 48px; color: #CBD5E0; margin-bottom: 15px;"></i>
                                        <h5>Aucune facture trouvée</h5>
                                        <p class="text-muted">Créez votre première facture pour commencer</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <div class="invoices-pagination fade-in-up delay-300">
        {{ $invoices->links() }}
    </div>
</div>

@push('scripts')
<script>
    // Initialiser les icônes Feather
    feather.replace({ width: 18, height: 18 });

    // Formatter les montants en FCFA
    document.querySelectorAll('.currency').forEach(element => {
        if (element.textContent) {
            const amount = parseFloat(element.textContent.replace(/[^\d.-]/g, ''));
            if (!isNaN(amount)) {
                element.textContent = new Intl.NumberFormat('fr-FR').format(amount) + ' FCFA';
            }
        }
    });
    
    // Animation au défilement
    document.addEventListener('DOMContentLoaded', () => {
        const animElements = document.querySelectorAll('.fade-in-up');
        animElements.forEach(el => {
            el.style.opacity = '1';
        });
    });
</script>

<!-- Script pour ajuster dynamiquement le contenu par rapport à la sidebar -->
<script src="{{ asset('js/invoice-sidebar-fix.js') }}"></script>
@endpush
