/**
 * JavaScript isolé pour la page d'édition des produits - Évite les conflits
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initialisation de la page d\'édition des produits');
    
    // Attendre que ProductForm soit initialisé par le fichier JS externe
    setTimeout(function() {
        try {
            // Vérifier si ProductForm existe
            if (typeof ProductForm !== 'undefined') {
                // Initialiser ProductForm seulement s'il n'est pas déjà initialisé
                if (!window.productFormInstance) {
                    window.productFormInstance = new ProductForm();
                    console.log('✅ ProductForm initialisé avec succès');
                }
            } else {
                console.warn('⚠️ ProductForm non trouvé, chargement du script de fallback');
                loadProductFormFallback();
            }
            
            // Fonction de prévisualisation simple
            window.previewProduct = function() {
                alert('Fonctionnalité de prévisualisation - À implémenter selon vos besoins');
            };

            // Effets de survol simples avec protection contre les erreurs
            const editButtons = document.querySelectorAll('.edit-btn');
            if (editButtons.length > 0) {
                editButtons.forEach(btn => {
                    btn.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateY(-2px)';
                    });

                    btn.addEventListener('mouseleave', function() {
                        this.style.transform = 'translateY(0)';
                    });
                });
                console.log(`✅ Effets de survol appliqués à ${editButtons.length} boutons`);
            }
            
            // Protection contre les erreurs de formulaire
            const productForm = document.getElementById('productForm');
            if (productForm) {
                productForm.addEventListener('submit', function(e) {
                    // Validation de base avant soumission
                    const requiredFields = productForm.querySelectorAll('[required]');
                    let hasErrors = false;
                    
                    requiredFields.forEach(field => {
                        if (!field.value.trim()) {
                            field.classList.add('is-invalid');
                            hasErrors = true;
                        } else {
                            field.classList.remove('is-invalid');
                        }
                    });
                    
                    if (hasErrors) {
                        e.preventDefault();
                        alert('Veuillez remplir tous les champs obligatoires.');
                        return false;
                    }
                });
                console.log('✅ Validation de formulaire activée');
            }
            
            // Amélioration de l'UX avec des indicateurs de chargement
            const submitButtons = document.querySelectorAll('button[type="submit"]');
            submitButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enregistrement...';
                    this.disabled = true;
                    
                    // Réactiver le bouton après 5 secondes en cas de problème
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.disabled = false;
                    }, 5000);
                });
            });
            
        } catch (error) {
            console.error('❌ Erreur lors de l\'initialisation:', error);
            // Fallback en cas d'erreur
            loadProductFormFallback();
        }
    }, 100);
});

/**
 * Fallback en cas d'échec du chargement de ProductForm
 */
function loadProductFormFallback() {
    console.log('🔄 Chargement du fallback ProductForm');
    
    // Fonctionnalité de base pour les catégories
    const categorySelect = document.getElementById('category_id');
    if (categorySelect) {
        categorySelect.addEventListener('change', function() {
            const categoryName = this.options[this.selectedIndex].text.toLowerCase();
            
            // Afficher/masquer les sections selon la catégorie
            const regionalPricing = document.getElementById('regional-pricing');
            const ironSpecifications = document.getElementById('iron-specifications');
            
            if (regionalPricing) {
                regionalPricing.style.display = categoryName.includes('ciment') ? 'block' : 'none';
            }
            
            if (ironSpecifications) {
                ironSpecifications.style.display = categoryName.includes('fer') ? 'block' : 'none';
            }
        });
        console.log('✅ Fallback pour les catégories activé');
    }
    
    // Fonctionnalité de base pour les calculs de fer
    const diameterSelect = document.getElementById('diameter');
    const lengthInput = document.getElementById('length');
    const unitPriceInput = document.getElementById('unit_price');
    
    if (diameterSelect && lengthInput && unitPriceInput) {
        function updateIronCalculations() {
            const diameter = parseFloat(diameterSelect.value) || 0;
            const length = parseFloat(lengthInput.value) || 0;
            const unitPrice = parseFloat(unitPriceInput.value) || 0;

            // Valeurs prédéfinies pour les unités par tonne selon le diamètre
            const unitsPerTonMap = {
                6: 750,
                8: 422,
                10: 270,
                12: 188,
                14: 138,
                16: 106
            };

            const unitsPerTon = unitsPerTonMap[diameter] || 0;
            const weightPerUnit = unitsPerTon > 0 ? (1000 / unitsPerTon) : 0;
            const tonPrice = unitPrice * unitsPerTon;

            // Mise à jour des affichages
            const unitsPerTonDisplay = document.getElementById('units-per-ton');
            const weightPerUnitDisplay = document.getElementById('weight-per-unit');
            const tonPriceDisplay = document.getElementById('ton-price');
            
            if (unitsPerTonDisplay) unitsPerTonDisplay.textContent = `${unitsPerTon} unités`;
            if (weightPerUnitDisplay) weightPerUnitDisplay.textContent = `${weightPerUnit.toFixed(2)} kg`;
            if (tonPriceDisplay) tonPriceDisplay.textContent = `${tonPrice.toLocaleString('fr-FR')} FCFA`;

            // Mise à jour des champs cachés
            const unitsPerTonInput = document.getElementById('units_per_ton_input');
            const weightPerUnitInput = document.getElementById('weight_per_unit_input');
            const tonPriceInput = document.getElementById('ton_price_input');
            
            if (unitsPerTonInput) unitsPerTonInput.value = unitsPerTon;
            if (weightPerUnitInput) weightPerUnitInput.value = weightPerUnit;
            if (tonPriceInput) tonPriceInput.value = tonPrice;
        }
        
        diameterSelect.addEventListener('change', updateIronCalculations);
        lengthInput.addEventListener('input', updateIronCalculations);
        unitPriceInput.addEventListener('input', updateIronCalculations);
        
        console.log('✅ Fallback pour les calculs de fer activé');
    }
}

/**
 * Fonction utilitaire pour afficher des messages d'erreur
 */
function showError(message) {
    console.error('❌ Erreur:', message);
    
    // Créer une notification d'erreur simple
    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    errorDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    errorDiv.innerHTML = `
        <strong>Erreur!</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(errorDiv);
    
    // Supprimer automatiquement après 5 secondes
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    }, 5000);
}

/**
 * Fonction utilitaire pour afficher des messages de succès
 */
function showSuccess(message) {
    console.log('✅ Succès:', message);
    
    // Créer une notification de succès simple
    const successDiv = document.createElement('div');
    successDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    successDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    successDiv.innerHTML = `
        <strong>Succès!</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(successDiv);
    
    // Supprimer automatiquement après 3 secondes
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.parentNode.removeChild(successDiv);
        }
    }, 3000);
}

// Protection globale contre les erreurs JavaScript
window.addEventListener('error', function(e) {
    console.error('❌ Erreur JavaScript globale:', e.error);
    // Ne pas afficher d'alerte pour éviter de spammer l'utilisateur
});

// Protection contre les erreurs de promesses non gérées
window.addEventListener('unhandledrejection', function(e) {
    console.error('❌ Promesse rejetée non gérée:', e.reason);
    e.preventDefault(); // Empêcher l'affichage dans la console du navigateur
});

console.log('📦 Script admin-product-edit.js chargé avec succès');
