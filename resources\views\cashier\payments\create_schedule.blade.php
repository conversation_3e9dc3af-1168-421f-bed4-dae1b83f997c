@extends('layouts.app')

@section('title', 'Création d\'échéancier de paiement')

@section('styles')
<style>
    .schedule-item {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        position: relative;
    }
    
    .remove-schedule {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        cursor: pointer;
        color: #dc3545;
    }
    
    .payment-summary {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
    }
    
    .payment-progress {
        height: 10px;
        border-radius: 5px;
        margin: 1rem 0;
    }
</style>
@endsection

@section('content')
<div class="container-fluid px-4">
    <h1 class="mt-4">Création d'échéancier de paiement</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route('cashier.dashboard') }}">Tableau de bord</a></li>
        <li class="breadcrumb-item"><a href="{{ route('cashier.payments.pending') }}">Ventes en attente</a></li>
        <li class="breadcrumb-item"><a href="{{ route('cashier.payments.process', $sale->id) }}">Règlement</a></li>
        <li class="breadcrumb-item active">Échéancier</li>
    </ol>

    @include('partials.alerts')

    <div class="row">
        <!-- Informations sur la vente -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-info-circle me-1"></i>
                    Détails de la vente
                </div>
                <div class="card-body">
                    <h5 class="card-title">Référence: {{ $sale->invoice_number ?? 'VNT-' . str_pad($sale->id, 6, '0', STR_PAD_LEFT) }}</h5>
                    <p class="text-muted mb-3">{{ $sale->created_at->format('d/m/Y H:i') }}</p>
                    
                    <div class="mb-3">
                        <h6 class="fw-bold">Client</h6>
                        <p class="mb-0">{{ $sale->customer_name }}</p>
                        <p class="mb-0">{{ $sale->customer_phone }}</p>
                        <p class="mb-0">{{ $sale->customer_address }}</p>
                    </div>
                    
                    <div class="payment-summary">
                        <h6 class="fw-bold">Résumé financier</h6>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Montant total:</span>
                            <span class="fw-bold">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Montant payé:</span>
                            <span class="text-success">{{ number_format($sale->amount_paid, 0, ',', ' ') }} FCFA</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Reste à payer:</span>
                            <span class="text-danger fw-bold" id="remainingAmount">{{ number_format($sale->total_amount - $sale->amount_paid, 0, ',', ' ') }} FCFA</span>
                        </div>
                        
                        <!-- Barre de progression du paiement -->
                        <div class="progress payment-progress">
                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ $sale->payment_progress }}%" 
                                aria-valuenow="{{ $sale->payment_progress }}" aria-valuemin="0" aria-valuemax="100">
                            </div>
                        </div>
                        <div class="text-center">
                            <small>{{ number_format($sale->payment_progress, 0) }}% payé</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <i class="fas fa-lightbulb me-1"></i>
                    Conseils pour l'échéancier
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li class="mb-2">Définissez des échéances réalistes en fonction de la capacité de paiement du client.</li>
                        <li class="mb-2">Espacez les échéances de manière régulière pour faciliter le suivi.</li>
                        <li class="mb-2">Le montant total des échéances doit correspondre au montant restant à payer.</li>
                        <li class="mb-2">Vous pouvez créer autant d'échéances que nécessaire.</li>
                        <li>N'oubliez pas de communiquer clairement les dates d'échéance au client.</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Formulaire d'échéancier -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-calendar-alt me-1"></i>
                    Définir les échéances de paiement
                </div>
                <div class="card-body">
                    <form id="scheduleForm" action="{{ route('cashier.payments.schedule.store', $sale->id) }}" method="POST">
                        @csrf
                        
                        <div id="scheduleContainer">
                            <!-- Les échéances seront ajoutées ici dynamiquement -->
                            <div class="schedule-item" data-index="0">
                                <i class="fas fa-times remove-schedule" title="Supprimer cette échéance"></i>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="schedules[0][due_date]" class="form-label">Date d'échéance <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control due-date" id="schedules[0][due_date]" name="schedules[0][due_date]" 
                                                value="{{ date('Y-m-d', strtotime('+7 days')) }}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="schedules[0][amount]" class="form-label">Montant <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="number" class="form-control schedule-amount" id="schedules[0][amount]" name="schedules[0][amount]" 
                                                    step="0.01" min="1" value="{{ $sale->total_amount - $sale->amount_paid }}" required>
                                                <span class="input-group-text">FCFA</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="schedules[0][notes]" class="form-label">Notes</label>
                                            <input type="text" class="form-control" id="schedules[0][notes]" name="schedules[0][notes]" 
                                                placeholder="Informations complémentaires...">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <button type="button" id="addScheduleBtn" class="btn btn-outline-primary">
                                <i class="fas fa-plus-circle me-1"></i> Ajouter une échéance
                            </button>
                            
                            <div class="text-end">
                                <span class="me-2">Total échéancier:</span>
                                <span id="totalScheduleAmount" class="fw-bold">{{ number_format($sale->total_amount - $sale->amount_paid, 0, ',', ' ') }} FCFA</span>
                                <div id="amountWarning" class="text-danger d-none">
                                    <small><i class="fas fa-exclamation-triangle me-1"></i> Le total ne correspond pas au montant restant à payer</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('cashier.payments.process', $sale->id) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Retour
                            </a>
                            <button type="submit" id="submitBtn" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Enregistrer l'échéancier
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const scheduleContainer = document.getElementById('scheduleContainer');
        const addScheduleBtn = document.getElementById('addScheduleBtn');
        const totalScheduleAmount = document.getElementById('totalScheduleAmount');
        const amountWarning = document.getElementById('amountWarning');
        const submitBtn = document.getElementById('submitBtn');
        
        // Montant restant à payer
        const remainingAmount = {{ $sale->total_amount - $sale->amount_paid }};
        
        // Compteur pour les indices des échéances
        let scheduleIndex = 1;
        
        // Ajouter une nouvelle échéance
        addScheduleBtn.addEventListener('click', function() {
            const newSchedule = document.createElement('div');
            newSchedule.className = 'schedule-item';
            newSchedule.dataset.index = scheduleIndex;
            
            // Calculer la date par défaut (1 mois après la dernière échéance)
            const lastDateInput = document.querySelector('.schedule-item:last-child .due-date');
            let defaultDate = new Date();
            if (lastDateInput) {
                defaultDate = new Date(lastDateInput.value);
                defaultDate.setMonth(defaultDate.getMonth() + 1);
            } else {
                defaultDate.setDate(defaultDate.getDate() + 30);
            }
            
            // Formater la date au format YYYY-MM-DD
            const formattedDate = defaultDate.toISOString().split('T')[0];
            
            // Calculer le montant par défaut (montant restant divisé par 2)
            const remainingScheduleAmount = calculateRemainingScheduleAmount();
            const defaultAmount = remainingScheduleAmount > 0 ? remainingScheduleAmount : 0;
            
            newSchedule.innerHTML = `
                <i class="fas fa-times remove-schedule" title="Supprimer cette échéance"></i>
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="schedules[${scheduleIndex}][due_date]" class="form-label">Date d'échéance <span class="text-danger">*</span></label>
                            <input type="date" class="form-control due-date" id="schedules[${scheduleIndex}][due_date]" name="schedules[${scheduleIndex}][due_date]" 
                                value="${formattedDate}" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="schedules[${scheduleIndex}][amount]" class="form-label">Montant <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control schedule-amount" id="schedules[${scheduleIndex}][amount]" name="schedules[${scheduleIndex}][amount]" 
                                    step="0.01" min="1" value="${defaultAmount}" required>
                                <span class="input-group-text">FCFA</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="schedules[${scheduleIndex}][notes]" class="form-label">Notes</label>
                            <input type="text" class="form-control" id="schedules[${scheduleIndex}][notes]" name="schedules[${scheduleIndex}][notes]" 
                                placeholder="Informations complémentaires...">
                        </div>
                    </div>
                </div>
            `;
            
            scheduleContainer.appendChild(newSchedule);
            scheduleIndex++;
            
            // Mettre à jour le total
            updateTotalAmount();
            
            // Ajouter l'événement de suppression
            addRemoveEventListener(newSchedule.querySelector('.remove-schedule'));
            
            // Ajouter l'événement de mise à jour du montant
            newSchedule.querySelector('.schedule-amount').addEventListener('input', updateTotalAmount);
        });
        
        // Fonction pour ajouter l'événement de suppression
        function addRemoveEventListener(element) {
            element.addEventListener('click', function() {
                const scheduleItem = this.closest('.schedule-item');
                
                // Ne pas supprimer si c'est la seule échéance
                if (document.querySelectorAll('.schedule-item').length > 1) {
                    scheduleItem.remove();
                    updateTotalAmount();
                } else {
                    alert('Vous devez définir au moins une échéance.');
                }
            });
        }
        
        // Ajouter l'événement de suppression à l'échéance initiale
        addRemoveEventListener(document.querySelector('.remove-schedule'));
        
        // Ajouter l'événement de mise à jour du montant à l'échéance initiale
        document.querySelector('.schedule-amount').addEventListener('input', updateTotalAmount);
        
        // Fonction pour mettre à jour le montant total
        function updateTotalAmount() {
            const amountInputs = document.querySelectorAll('.schedule-amount');
            let total = 0;
            
            amountInputs.forEach(input => {
                total += parseFloat(input.value) || 0;
            });
            
            // Formater le montant total
            totalScheduleAmount.textContent = formatAmount(total) + ' FCFA';
            
            // Vérifier si le total correspond au montant restant à payer
            if (Math.abs(total - remainingAmount) > 0.01) {
                amountWarning.classList.remove('d-none');
                submitBtn.disabled = true;
            } else {
                amountWarning.classList.add('d-none');
                submitBtn.disabled = false;
            }
        }
        
        // Fonction pour calculer le montant restant à planifier
        function calculateRemainingScheduleAmount() {
            const amountInputs = document.querySelectorAll('.schedule-amount');
            let totalScheduled = 0;
            
            amountInputs.forEach(input => {
                totalScheduled += parseFloat(input.value) || 0;
            });
            
            return remainingAmount - totalScheduled;
        }
        
        // Fonction pour formater un montant
        function formatAmount(amount) {
            return new Intl.NumberFormat('fr-FR').format(amount);
        }
        
        // Valider le formulaire avant soumission
        document.getElementById('scheduleForm').addEventListener('submit', function(e) {
            const amountInputs = document.querySelectorAll('.schedule-amount');
            let total = 0;
            
            amountInputs.forEach(input => {
                total += parseFloat(input.value) || 0;
            });
            
            if (Math.abs(total - remainingAmount) > 0.01) {
                e.preventDefault();
                alert('Le total des échéances doit être égal au montant restant à payer.');
            }
        });
    });
</script>
@endsection
