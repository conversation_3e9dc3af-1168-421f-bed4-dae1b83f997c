/* Styles améliorés pour les rapports GRADIS */
/* <PERSON><PERSON>er CSS réutilisable pour toutes les pages de rapports */

/* Variables CSS pour la cohérence */
:root {
    /* Palette de couleurs enrichie */
    --primary-blue: #2563eb;
    --primary-red: #dc2626;
    --primary-green: #059669;
    --primary-orange: #ea580c;
    --primary-purple: #7c3aed;
    --primary-indigo: #4f46e5;
    
    /* Gradients */
    --gradient-blue: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    --gradient-red: linear-gradient(135deg, #ef4444 0%, #b91c1c 100%);
    --gradient-green: linear-gradient(135deg, #10b981 0%, #047857 100%);
    --gradient-orange: linear-gradient(135deg, #f59e0b 0%, #c2410c 100%);
    --gradient-purple: linear-gradient(135deg, #8b5cf6 0%, #5b21b6 100%);
    --gradient-indigo: linear-gradient(135deg, #6366f1 0%, #4338ca 100%);
    
    /* Couleurs de fond */
    --bg-light: #f8fafc;
    --bg-white: #ffffff;
    --bg-gray: #f1f5f9;
    
    /* Texte */
    --text-dark: #0f172a;
    --text-medium: #475569;
    --text-light: #64748b;
    
    /* Ombres */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Rayons de bordure */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
}

/* Styles pour les étiquettes colorées */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.status-badge.success {
    background: var(--gradient-green);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.status-badge.warning {
    background: var(--gradient-orange);
    color: white;
    box-shadow: 0 4px 15px rgba(234, 88, 12, 0.3);
}

.status-badge.danger {
    background: var(--gradient-red);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
}

.status-badge.info {
    background: var(--gradient-blue);
    color: white;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.status-badge.primary {
    background: var(--gradient-purple);
    color: white;
    box-shadow: 0 4px 15px rgba(124, 58, 237, 0.3);
}

/* Cartes améliorées pour les rapports */
.enhanced-card {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    padding: 24px;
    box-shadow: var(--shadow-md);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.enhanced-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.enhanced-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-blue);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.enhanced-card.green::before { background: var(--gradient-green); }
.enhanced-card.red::before { background: var(--gradient-red); }
.enhanced-card.orange::before { background: var(--gradient-orange); }
.enhanced-card.purple::before { background: var(--gradient-purple); }

/* En-têtes de cartes */
.card-header-enhanced {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f1f5f9;
}

.card-title-enhanced {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-dark);
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-title-enhanced i {
    font-size: 1.1rem;
}

/* Boutons d'action améliorés */
.btn-enhanced {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-enhanced.primary {
    background: var(--gradient-blue);
    color: white;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.btn-enhanced.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
    color: white;
    text-decoration: none;
}

.btn-enhanced.success {
    background: var(--gradient-green);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-enhanced.success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
    color: white;
    text-decoration: none;
}

/* Tableaux améliorés */
.table-enhanced {
    width: 100%;
    border-collapse: collapse;
    margin-top: 16px;
}

.table-enhanced th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 16px;
    text-align: left;
    font-weight: 700;
    color: var(--text-dark);
    border-bottom: 2px solid #e2e8f0;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-enhanced td {
    padding: 16px;
    border-bottom: 1px solid #f1f5f9;
    color: var(--text-medium);
    font-size: 14px;
}

.table-enhanced tr:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 51, 234, 0.02) 100%);
}

/* Métriques avec icônes */
.metric-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: var(--bg-light);
    border-radius: var(--radius-md);
    margin-bottom: 12px;
    transition: all 0.3s ease;
}

.metric-item:hover {
    transform: translateX(4px);
    background: var(--bg-white);
    box-shadow: var(--shadow-md);
}

.metric-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
}

.metric-icon.blue { background: var(--gradient-blue); }
.metric-icon.green { background: var(--gradient-green); }
.metric-icon.orange { background: var(--gradient-orange); }
.metric-icon.red { background: var(--gradient-red); }

.metric-content h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 700;
    color: var(--text-dark);
}

.metric-content p {
    margin: 0;
    font-size: 14px;
    color: var(--text-light);
}

/* Filtres de période améliorés */
.period-filters {
    display: flex;
    gap: 8px;
    margin-bottom: 24px;
    flex-wrap: wrap;
}

.period-filter {
    padding: 8px 16px;
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    border: 2px solid #e2e8f0;
    color: var(--text-medium);
}

.period-filter.active {
    background: var(--gradient-blue);
    color: white;
    border-color: transparent;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.period-filter:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    text-decoration: none;
    color: var(--text-dark);
}

.period-filter.active:hover {
    color: white;
}

/* Responsive */
@media (max-width: 768px) {
    .enhanced-card {
        padding: 16px;
    }
    
    .card-header-enhanced {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .period-filters {
        justify-content: center;
    }
    
    .table-enhanced {
        font-size: 12px;
    }
    
    .table-enhanced th,
    .table-enhanced td {
        padding: 12px 8px;
    }
}
