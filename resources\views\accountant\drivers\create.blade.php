@extends('layouts.accountant')

@section('title', 'Nouveau chauffeur')

@push('styles')
<style>
    .modern-driver-form {
        background: #f8f9fa;
        min-height: 100vh;
        padding: 2rem 0;
    }

    .form-container {
        max-width: 1000px;
        margin: 0 auto;
    }

    .form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        border: 1px solid #e9ecef;
    }

    .form-header {
        background: white;
        padding: 2rem;
        text-align: center;
        color: #495057;
        border-bottom: 3px solid #e9ecef;
        position: relative;
    }

    .form-header h1 {
        font-size: 2rem;
        font-weight: 600;
        margin: 0;
        color: #495057;
    }

    .form-header .subtitle {
        font-size: 1rem;
        color: #6c757d;
        margin-top: 0.5rem;
    }

    .form-body {
        padding: 3rem;
    }

    .input-group-modern {
        position: relative;
        margin-bottom: 2rem;
    }

    .input-icon {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 1.2rem;
        z-index: 3;
        transition: all 0.3s ease;
    }

    .form-control-modern {
        border: 2px solid #e1e5e9;
        border-radius: 12px;
        padding: 15px 15px 15px 50px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8f9fa;
        height: auto;
    }

    .form-control-modern:focus {
        border-color: #4facfe;
        box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        background: white;
        transform: translateY(-2px);
    }

    .form-control-modern:focus + .input-icon {
        color: #4facfe;
        transform: translateY(-50%) scale(1.1);
    }

    .form-label-modern {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
        display: flex;
        align-items: center;
    }

    .label-icon {
        margin-right: 8px;
        font-size: 1rem;
    }

    .required-star {
        color: #e53e3e;
        margin-left: 4px;
    }

    .btn-modern {
        padding: 12px 30px;
        border-radius: 10px;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn-primary-modern {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
    }

    .btn-primary-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(79, 172, 254, 0.6);
        color: white;
    }

    .btn-secondary-modern {
        background: #e2e8f0;
        color: #4a5568;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-secondary-modern:hover {
        background: #cbd5e0;
        transform: translateY(-2px);
        color: #2d3748;
    }

    .btn-back {
        background: #6c757d;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .btn-back:hover {
        background: #5a6268;
        color: white;
        transform: translateX(-3px);
    }

    .alert-modern {
        border: none;
        border-radius: 12px;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
        color: #742a2a;
        border-left: 4px solid #e53e3e;
    }

    .section-divider {
        height: 2px;
        background: linear-gradient(90deg, transparent 0%, #e2e8f0 50%, transparent 100%);
        margin: 2rem 0;
    }

    .select-modern {
        border: 2px solid #e1e5e9;
        border-radius: 12px;
        padding: 15px 15px 15px 50px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8f9fa;
        height: auto;
        appearance: none;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 12px center;
        background-repeat: no-repeat;
        background-size: 16px 12px;
    }

    .select-modern:focus {
        border-color: #4facfe;
        box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        background-color: white;
        transform: translateY(-2px);
    }

    /* Icônes colorées */
    .icon-user { color: #4299e1; }
    .icon-email { color: #ed8936; }
    .icon-phone { color: #9f7aea; }
    .icon-license { color: #48bb78; }
    .icon-calendar { color: #f56565; }
    .icon-address { color: #38b2ac; }
    .icon-truck { color: #667eea; }
    .icon-notes { color: #a0aec0; }
</style>
@endpush

@section('content')
<div class="modern-driver-form">
    <div class="container-fluid">
        <div class="form-container">
            <a href="{{ route('accountant.drivers.index') }}" class="btn-back">
                <i class="fas fa-arrow-left me-2"></i>Retour à la liste
            </a>

            <div class="form-card">
                <div class="form-header">
                    <h1><i class="fas fa-user-tie me-3"></i>Nouveau Chauffeur</h1>
                    <p class="subtitle">Ajoutez un nouveau chauffeur à votre équipe de transport</p>
                </div>

                <div class="form-body">
                    @if ($errors->any())
                        <div class="alert-modern">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Erreurs de validation</strong>
                            </div>
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('accountant.drivers.store') }}" method="POST">
                        @csrf
                        <div class="row">
                            <!-- Informations personnelles -->
                            <div class="col-12">
                                <h4 class="text-primary mb-3">
                                    <i class="fas fa-user me-2"></i>Informations personnelles
                                </h4>
                            </div>

                            <!-- Prénom -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="first_name" class="form-label-modern">
                                        <i class="fas fa-user label-icon icon-user"></i>
                                        Prénom
                                        <span class="required-star">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control-modern @error('first_name') is-invalid @enderror"
                                           id="first_name"
                                           name="first_name"
                                           value="{{ old('first_name') }}"
                                           placeholder="Ex: Jean"
                                           required>
                                    <i class="fas fa-user input-icon icon-user"></i>
                                    @error('first_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Nom -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="last_name" class="form-label-modern">
                                        <i class="fas fa-user-tag label-icon icon-user"></i>
                                        Nom de famille
                                        <span class="required-star">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control-modern @error('last_name') is-invalid @enderror"
                                           id="last_name"
                                           name="last_name"
                                           value="{{ old('last_name') }}"
                                           placeholder="Ex: Dupont"
                                           required>
                                    <i class="fas fa-user-tag input-icon icon-user"></i>
                                    @error('last_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="section-divider"></div>
                                <h4 class="text-primary mb-3">
                                    <i class="fas fa-address-book me-2"></i>Coordonnées
                                </h4>
                            </div>

                            <!-- Email -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="email" class="form-label-modern">
                                        <i class="fas fa-envelope label-icon icon-email"></i>
                                        Adresse email
                                        <span class="required-star">*</span>
                                    </label>
                                    <input type="email"
                                           class="form-control-modern @error('email') is-invalid @enderror"
                                           id="email"
                                           name="email"
                                           value="{{ old('email') }}"
                                           placeholder="<EMAIL>"
                                           required>
                                    <i class="fas fa-envelope input-icon icon-email"></i>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Téléphone -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="phone" class="form-label-modern">
                                        <i class="fas fa-phone label-icon icon-phone"></i>
                                        Numéro de téléphone
                                        <span class="required-star">*</span>
                                    </label>
                                    <input type="tel"
                                           class="form-control-modern @error('phone') is-invalid @enderror"
                                           id="phone"
                                           name="phone"
                                           value="{{ old('phone') }}"
                                           placeholder="+228 XX XX XX XX"
                                           required>
                                    <i class="fas fa-phone input-icon icon-phone"></i>
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="section-divider"></div>
                                <h4 class="text-primary mb-3">
                                    <i class="fas fa-id-card me-2"></i>Informations de conduite
                                </h4>
                            </div>

                            <!-- Numéro de permis -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="license_number" class="form-label-modern">
                                        <i class="fas fa-id-card label-icon icon-license"></i>
                                        Numéro de permis
                                        <span class="required-star">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control-modern @error('license_number') is-invalid @enderror"
                                           id="license_number"
                                           name="license_number"
                                           value="{{ old('license_number') }}"
                                           placeholder="Ex: TG123456789"
                                           required>
                                    <i class="fas fa-id-card input-icon icon-license"></i>
                                    @error('license_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Date d'expiration -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="license_expiry" class="form-label-modern">
                                        <i class="fas fa-calendar-alt label-icon icon-calendar"></i>
                                        Date d'expiration du permis
                                        <span class="required-star">*</span>
                                    </label>
                                    <input type="date"
                                           class="form-control-modern @error('license_expiry') is-invalid @enderror"
                                           id="license_expiry"
                                           name="license_expiry"
                                           value="{{ old('license_expiry') }}"
                                           required>
                                    <i class="fas fa-calendar-alt input-icon icon-calendar"></i>
                                    @error('license_expiry')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="section-divider"></div>
                                <h4 class="text-primary mb-3">
                                    <i class="fas fa-map-marker-alt me-2"></i>Adresse et affectation
                                </h4>
                            </div>

                            <!-- Adresse -->
                            <div class="col-md-12">
                                <div class="input-group-modern">
                                    <label for="address" class="form-label-modern">
                                        <i class="fas fa-map-marker-alt label-icon icon-address"></i>
                                        Adresse de résidence
                                        <span class="required-star">*</span>
                                    </label>
                                    <textarea class="form-control-modern @error('address') is-invalid @enderror"
                                              id="address"
                                              name="address"
                                              rows="3"
                                              placeholder="Adresse complète du chauffeur..."
                                              required>{{ old('address') }}</textarea>
                                    <i class="fas fa-map-marker-alt input-icon icon-address"></i>
                                    @error('address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Véhicule -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="truck_id" class="form-label-modern">
                                        <i class="fas fa-truck label-icon icon-truck"></i>
                                        Véhicule assigné
                                    </label>
                                    <select class="select-modern @error('truck_id') is-invalid @enderror"
                                            id="truck_id"
                                            name="truck_id">
                                        <option value="">Assigner automatiquement</option>
                                        @foreach($trucks as $truck)
                                            <option value="{{ $truck->id }}" {{ old('truck_id') == $truck->id ? 'selected' : '' }}>
                                                {{ $truck->registration_number }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <i class="fas fa-truck input-icon icon-truck"></i>
                                    <small class="form-text text-muted mt-2">
                                        Si aucun véhicule n'est sélectionné, un véhicule disponible sera assigné automatiquement.
                                    </small>
                                    @error('truck_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="notes" class="form-label-modern">
                                        <i class="fas fa-sticky-note label-icon icon-notes"></i>
                                        Notes et observations
                                    </label>
                                    <textarea class="form-control-modern @error('notes') is-invalid @enderror"
                                              id="notes"
                                              name="notes"
                                              rows="3"
                                              placeholder="Informations complémentaires...">{{ old('notes') }}</textarea>
                                    <i class="fas fa-sticky-note input-icon icon-notes"></i>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                        </div>

                        <div class="section-divider"></div>

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="{{ route('accountant.drivers.index') }}" class="btn btn-secondary-modern">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-primary-modern">
                                <i class="fas fa-save me-2"></i>Enregistrer le chauffeur
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation d'entrée pour les champs
    const inputs = document.querySelectorAll('.form-control-modern, .select-modern');
    inputs.forEach((input, index) => {
        input.style.opacity = '0';
        input.style.transform = 'translateY(20px)';
        setTimeout(() => {
            input.style.transition = 'all 0.5s ease';
            input.style.opacity = '1';
            input.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Validation en temps réel
    const emailInput = document.getElementById('email');
    const phoneInput = document.getElementById('phone');
    const licenseInput = document.getElementById('license_number');
    const expiryInput = document.getElementById('license_expiry');

    // Validation email
    emailInput.addEventListener('input', function() {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (this.value && !emailRegex.test(this.value)) {
            this.style.borderColor = '#e53e3e';
        } else if (this.value) {
            this.style.borderColor = '#48bb78';
        } else {
            this.style.borderColor = '#e1e5e9';
        }
    });

    // Validation téléphone
    phoneInput.addEventListener('input', function() {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
        if (this.value && !phoneRegex.test(this.value)) {
            this.style.borderColor = '#e53e3e';
        } else if (this.value) {
            this.style.borderColor = '#48bb78';
        } else {
            this.style.borderColor = '#e1e5e9';
        }
    });

    // Validation numéro de permis
    licenseInput.addEventListener('input', function() {
        if (this.value.length < 5) {
            this.style.borderColor = '#e53e3e';
        } else {
            this.style.borderColor = '#48bb78';
        }
    });

    // Validation date d'expiration
    expiryInput.addEventListener('change', function() {
        const today = new Date();
        const expiryDate = new Date(this.value);

        if (expiryDate <= today) {
            this.style.borderColor = '#e53e3e';
            // Afficher un avertissement
            if (!document.getElementById('expiry-warning')) {
                const warning = document.createElement('small');
                warning.id = 'expiry-warning';
                warning.className = 'text-danger';
                warning.textContent = 'Attention: Le permis est expiré ou expire aujourd\'hui';
                this.parentNode.appendChild(warning);
            }
        } else {
            this.style.borderColor = '#48bb78';
            const warning = document.getElementById('expiry-warning');
            if (warning) {
                warning.remove();
            }
        }
    });

    // Formatage automatique du numéro de téléphone
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.startsWith('228')) {
            value = '+' + value;
        } else if (value.length === 8) {
            value = '+228 ' + value;
        }
        this.value = value;
    });
});
</script>
@endsection
