/* Force White Background - Solution définitive */

/* Reset complet pour tous les éléments */
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
    background-image: none !important;
}

/* Forcer le fond blanc sur le html et body */
html, body {
    background: white !important;
    background-color: white !important;
    background-image: none !important;
    background-attachment: unset !important;
    background-position: unset !important;
    background-repeat: unset !important;
    background-size: unset !important;
}

/* Ciblage spécifique des éléments avec le dégradé bleu problématique */
[style*="linear-gradient(to right bottom, rgb(30, 136, 229), rgb(13, 71, 161))"],
[style*="linear-gradient(to bottom right, rgb(30, 136, 229), rgb(13, 71, 161))"],
[style*="linear-gradient(to bottom right, #1E88E5, #0D47A1)"],
[style*="linear-gradient(to right bottom, #1E88E5, #0D47A1)"] {
    background: white !important;
    background-image: none !important;
    background-color: white !important;
}

/* Forcer le fond blanc pour la carte principale */
.main-card {
    background: white !important;
    background-color: white !important;
    background-image: none !important;
    color: #333 !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
    animation: none !important;
}

/* Désactiver toutes les animations de dégradé */
@keyframes cardGlow {
    0%, 50%, 100% {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
        background: white !important;
        background-image: none !important;
    }
}

/* Forcer le blanc pour la section des filtres */
.filters-section {
    background: white !important;
    background-color: white !important;
    background-image: none !important;
    color: #333 !important;
}

/* Adapter les couleurs du texte pour le fond blanc */
.main-card h1, .main-card .text-white, .main-card .text-light,
.filters-section h5, .filters-section h6 {
    color: #1976D2 !important;
}

.main-card p, .main-card .text-white-50, .main-card .text-light-50 {
    color: #666 !important;
}

/* Préserver les styles des colonnes Type et Validation - Version renforcée */
.sales-table th.highlight-header,
.header-cell.highlight-header {
    background: linear-gradient(135deg, #1976D2, #2196F3) !important;
    color: white !important;
}

.highlight-header .header-content span {
    color: white !important;
}

.highlight-header .header-icon {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

.sales-table td.type-column,
.sales-table td.validation-column,
.type-column,
.validation-column {
    background-color: rgba(25, 118, 210, 0.08) !important;
    position: relative !important;
    border-radius: 0.5rem !important;
}

.type-column::before,
.validation-column::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(to bottom, rgba(25, 118, 210, 0.15), rgba(25, 118, 210, 0.05)) !important;
    pointer-events: none !important;
    border-radius: 0.5rem !important;
    z-index: 0 !important;
}

/* S'assurer que les badges restent visibles au-dessus du fond */
.type-column .badge,
.validation-column .badge,
.type-column *,
.validation-column * {
    position: relative !important;
    z-index: 1 !important;
}
