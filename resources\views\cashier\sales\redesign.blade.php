@extends('layouts.cashier')

<!-- Script pour forcer le fond blanc immédiatement -->
<script>
// Supprimer le fond bleu dès le chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    // Appliquer directement sur le body et les éléments principaux
    document.body.style.background = 'white';
    document.body.style.backgroundImage = 'none';
    document.body.style.backgroundColor = 'white';
    
    // Supprimer les classes qui pourraient appliquer un fond bleu
    document.body.classList.remove('bg-gradient-primary', 'bg-primary', 'bg-blue');
});
</script>

@section('styles')
<!-- Styles de base -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<!-- CSS pour forcer le fond blanc - Priorité maximale -->
<link rel="stylesheet" href="{{ asset('css/force-white-bg-redesign.css') }}?v={{ time() }}">
<!-- Notre CSS de refonte avec paramètre de version pour éviter la mise en cache -->
<link rel="stylesheet" href="{{ asset('css/sales-redesign.css') }}?v={{ time() }}">
<!-- Style inline pour forcer le fond blanc -->
<style>
/* Force le fond blanc pour la vue redessinée - Priorité maximale */
html, body, body::before, body::after,
.main-panel, .main-panel::before, .main-panel::after,
.content-wrapper, .content-wrapper::before, .content-wrapper::after,
.container-fluid, .container-fluid::before, .container-fluid::after,
.card, .card::before, .card::after,
.main-card, .main-card::before, .main-card::after {
    background: white !important;
    background-color: white !important;
    background-image: none !important;
    background-attachment: unset !important;
    background-position: unset !important;
    background-repeat: unset !important;
    background-size: unset !important;
    animation: none !important;
}

/* Supprimer tous les dégradés bleus */
[style*="linear-gradient"],
[style*="radial-gradient"],
[class*="bg-gradient"],
[class*="bg-primary"],
[class*="bg-blue"] {
    background: white !important;
    background-image: none !important;
    background-color: white !important;
}

/* Conserver le fond bleu uniquement pour les colonnes Type et Validation */
.sales-table td.type-column,
.sales-table td.validation-column {
    background-color: rgba(37, 99, 235, 0.08) !important;
}
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- En-tête de la page -->
    <div class="dashboard-header animate-fade-in">
        <div>
            <h1 class="page-title"><i class="fas fa-shopping-cart"></i> Gestion des Ventes</h1>
            <p class="page-subtitle">Consultez et gérez toutes vos transactions commerciales</p>
        </div>
        <div>
            <a href="{{ route('cashier.sales.create') }}" class="btn btn-primary rounded-lg shadow">
                <i class="fas fa-plus-circle me-2"></i> Nouvelle Vente
            </a>
        </div>
    </div>

    <!-- Cartes de statistiques -->
    <div class="stats-grid animate-fade-in">
        <!-- Statistique: Total des ventes -->
        <div class="stat-card stat-card-sales">
            <div class="stat-header">
                <div>
                    <div class="stat-title">Total des ventes</div>
                    <div class="stat-value" data-value="{{ $stats['total_sales'] }}">{{ $stats['total_sales'] }}</div>
                    <div class="stat-trend positive">
                        <i class="fas fa-arrow-up"></i> +{{ $stats['weekly_growth'] }}% cette semaine
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
            </div>
            <div class="stat-progress">
                <div class="stat-progress-bar" data-width="75%"></div>
            </div>
        </div>

        <!-- Statistique: Chiffre d'affaires -->
        <div class="stat-card stat-card-revenue">
            <div class="stat-header">
                <div>
                    <div class="stat-title">Chiffre d'affaires</div>
                    <div class="stat-value" data-value="{{ $stats['total_amount'] }}">
                        {{ number_format($stats['total_amount'], 0, ',', ' ') }} XOF
                    </div>
                    <div class="stat-trend positive">
                        <i class="fas fa-arrow-up"></i> +{{ $stats['monthly_growth'] }}% ce mois
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
            </div>
            <div class="stat-progress">
                <div class="stat-progress-bar" data-width="85%"></div>
            </div>
        </div>

        <!-- Statistique: Remises accordées -->
        <div class="stat-card stat-card-discount">
            <div class="stat-header">
                <div>
                    <div class="stat-title">Remises accordées</div>
                    <div class="stat-value" data-value="{{ $stats['discount_sales'] }}">
                        {{ $stats['discount_sales'] }}
                    </div>
                    <div class="stat-trend negative">
                        <i class="fas fa-arrow-down"></i> -3% ce mois
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-tags"></i>
                </div>
            </div>
            <div class="stat-progress">
                <div class="stat-progress-bar" data-width="45%"></div>
            </div>
        </div>

        <!-- Statistique: Clients servis -->
        <div class="stat-card stat-card-customers">
            <div class="stat-header">
                <div>
                    <div class="stat-title">Clients servis</div>
                    <div class="stat-value" data-value="{{ $stats['unique_customers'] }}">
                        {{ $stats['unique_customers'] }}
                    </div>
                    <div class="stat-trend positive">
                        <i class="fas fa-arrow-up"></i> +8% ce mois
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>
            <div class="stat-progress">
                <div class="stat-progress-bar" data-width="65%"></div>
            </div>
        </div>
    </div>

    <!-- Graphiques (si Chart.js est disponible) -->
    <div class="row mb-4 animate-fade-in">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0 fw-semibold"><i class="fas fa-chart-pie text-primary me-2"></i> Ventes par type</h5>
                </div>
                <div class="card-body">
                    <div style="height: 250px;">
                        <canvas id="salesByTypeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0 fw-semibold"><i class="fas fa-chart-pie text-primary me-2"></i> Ventes par statut</h5>
                </div>
                <div class="card-body">
                    <div style="height: 250px;">
                        <canvas id="salesByStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section de filtres -->
    <div class="filters-container animate-fade-in">
        <div class="filters-header">
            <h5 class="filters-title"><i class="fas fa-filter"></i> Filtrer les ventes</h5>
            <div id="resultsCounter" class="badge badge-primary">{{ $sales->count() }}</div>
        </div>
        
        <div class="row">
            <div class="col-md-6 mb-3">
                <h6 class="smaller fw-semibold text-muted mb-2">Type de vente</h6>
                <div class="filter-pills">
                    <div class="filter-pill active" data-filter="all-types" data-filter-group="type">
                        <i class="fas fa-globe"></i> Tous les types
                    </div>
                    <div class="filter-pill" data-filter="direct-sale" data-filter-group="type">
                        <i class="fas fa-shopping-bag"></i> Vente directe
                    </div>
                    <div class="filter-pill" data-filter="discount-sale" data-filter-group="type">
                        <i class="fas fa-tags"></i> Vente avec remise
                    </div>
                    <div class="filter-pill" data-filter="price-increase" data-filter-group="type">
                        <i class="fas fa-arrow-trend-up"></i> Augmentation de prix
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-3">
                <h6 class="smaller fw-semibold text-muted mb-2">Statut de validation</h6>
                <div class="filter-pills">
                    <div class="filter-pill active" data-filter="all-status" data-filter-group="status">
                        <i class="fas fa-globe"></i> Tous les statuts
                    </div>
                    <div class="filter-pill" data-filter="approved" data-filter-group="status">
                        <i class="fas fa-check-circle"></i> Approuvé
                    </div>
                    <div class="filter-pill" data-filter="pending" data-filter-group="status">
                        <i class="fas fa-clock"></i> En attente
                    </div>
                    <div class="filter-pill" data-filter="rejected" data-filter-group="status">
                        <i class="fas fa-times-circle"></i> Rejeté
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-md-12">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" id="salesSearch" class="search-input" placeholder="Rechercher une vente par référence, client, montant...">
                </div>
            </div>
        </div>
    </div>

    <!-- Légende des statuts -->
    <div class="legend-container animate-fade-in">
        <div class="legend-header">
            <h5 class="legend-title"><i class="fas fa-info-circle"></i> Légende</h5>
        </div>
        
        <div class="legend-section">
            <h6 class="legend-section-title"><i class="fas fa-tag"></i> Types de ventes</h6>
            <div class="legend-items">
                <div class="legend-item legend-item-direct-sale">
                    <div class="legend-item-title">Vente directe</div>
                    <div class="legend-item-desc">Vente standard sans modification de prix</div>
                </div>
                <div class="legend-item legend-item-discount-sale">
                    <div class="legend-item-title">Vente avec remise</div>
                    <div class="legend-item-desc">Vente avec une réduction de prix</div>
                </div>
                <div class="legend-item legend-item-price-increase">
                    <div class="legend-item-title">Augmentation de prix</div>
                    <div class="legend-item-desc">Vente avec un prix supérieur au tarif standard</div>
                </div>
            </div>
        </div>
        
        <div class="legend-section">
            <h6 class="legend-section-title"><i class="fas fa-clipboard-check"></i> Statuts de validation</h6>
            <div class="legend-items">
                <div class="legend-item legend-item-approved">
                    <div class="legend-item-title">Approuvé</div>
                    <div class="legend-item-desc">Vente validée et confirmée</div>
                </div>
                <div class="legend-item legend-item-pending">
                    <div class="legend-item-title">En attente</div>
                    <div class="legend-item-desc">Vente en attente de validation</div>
                </div>
                <div class="legend-item legend-item-rejected">
                    <div class="legend-item-title">Rejeté</div>
                    <div class="legend-item-desc">Vente refusée ou annulée</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des ventes -->
    <div class="table-container animate-fade-in">
        <div class="table-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="table-title"><i class="fas fa-list"></i> Liste des ventes</h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-sm btn-outline-primary rounded-lg" id="exportCsv">
                        <i class="fas fa-file-csv me-1"></i> Exporter CSV
                    </button>
                    <button class="btn btn-sm btn-outline-primary rounded-lg" id="printTable">
                        <i class="fas fa-print me-1"></i> Imprimer
                    </button>
                </div>
            </div>
        </div>
        
        <div class="table-wrapper">
            <table class="sales-table" id="salesTable">
                <thead>
                    <tr>
                        <th style="width: 4px;"></th>
                        <th><i class="fas fa-hashtag me-1"></i> Référence</th>
                        <th><i class="fas fa-user me-1"></i> Client</th>
                        <th class="highlight-header"><i class="fas fa-tag me-1"></i> Type</th>
                        <th><i class="fas fa-calendar me-1"></i> Date</th>
                        <th><i class="fas fa-money-bill me-1"></i> Montant</th>
                        <th class="highlight-header"><i class="fas fa-clipboard-check me-1"></i> Validation</th>
                        <th><i class="fas fa-user-shield me-1"></i> Agent</th>
                        <th style="width: 120px;"><i class="fas fa-cogs me-1"></i> Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($sales as $sale)
                        @php
                            // Déterminer la classe de ligne en fonction du type de vente
                            $saleType = $sale->is_discount_sale ? 'discount-sale' : ($sale->is_price_increase ? 'price-increase' : 'direct-sale');
                            
                            // Déterminer le statut de validation
                            $validationClass = '';
                            if ($sale->is_validated === 1) {
                                $validationClass = 'approved';
                            } elseif ($sale->is_validated === 0) {
                                $validationClass = 'rejected';
                            } else {
                                $validationClass = 'pending';
                            }
                            
                            // Formater le montant
                            $formattedAmount = number_format($sale->amount, 0, ',', ' ') . ' XOF';
                            
                            // Formater la date
                            $formattedDate = date('d/m/Y H:i', strtotime($sale->created_at));
                        @endphp
                        
                        <tr data-sale-type="{{ $saleType }}" data-validation-status="{{ $validationClass }}">
                            <td>
                                <div class="status-indicator status-{{ $validationClass }}"></div>
                            </td>
                            <td>{{ $sale->reference }}</td>
                            <td>{{ $sale->customer->name ?? 'Client inconnu' }}</td>
                            <td class="type-column">
                                @if($sale->is_discount_sale)
                                    <span class="badge badge-discount-sale">
                                        <i class="fas fa-tags"></i> Remise
                                    </span>
                                @elseif($sale->is_price_increase)
                                    <span class="badge badge-price-increase">
                                        <i class="fas fa-arrow-trend-up"></i> Augmentation
                                    </span>
                                @else
                                    <span class="badge badge-direct-sale">
                                        <i class="fas fa-shopping-bag"></i> Directe
                                    </span>
                                @endif
                            </td>
                            <td>{{ $formattedDate }}</td>
                            <td>{{ $formattedAmount }}</td>
                            <td class="validation-column">
                                @if($sale->is_validated === 1)
                                    <span class="badge badge-approved">
                                        <i class="fas fa-check-circle"></i> Approuvé
                                    </span>
                                @elseif($sale->is_validated === 0)
                                    <span class="badge badge-rejected">
                                        <i class="fas fa-times-circle"></i> Rejeté
                                    </span>
                                @else
                                    <span class="badge badge-pending">
                                        <i class="fas fa-clock"></i> En attente
                                    </span>
                                @endif
                            </td>
                            <td>{{ $sale->user->name ?? 'Agent inconnu' }}</td>
                            <td>
                                <div class="actions-cell">
                                    <a href="{{ route('cashier.sales.show', $sale) }}" class="action-btn action-btn-view" title="Voir les détails">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if($sale->is_validated === null)
                                    <a href="{{ route('cashier.sales.edit', $sale) }}" class="action-btn action-btn-edit" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('cashier.sales.destroy', $sale) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="action-btn action-btn-delete" title="Supprimer" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette vente?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="9" class="text-center py-5">
                                <div class="no-results" id="noResultsMessage">
                                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                    <h5 class="fw-semibold">Aucune vente trouvée</h5>
                                    <p class="text-muted">Essayez de modifier vos filtres ou créez une nouvelle vente</p>
                                    <a href="{{ route('cashier.sales.create') }}" class="btn btn-primary mt-3">
                                        <i class="fas fa-plus-circle me-2"></i> Nouvelle Vente
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="pagination-container">
            {{ $sales->links() }}
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- Inclure Chart.js si disponible -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<!-- Notre script de refonte basé sur remove-blue-bg.js avec paramètre de version -->
<script src="{{ asset('js/sales-redesign.js') }}?v={{ time() }}"></script>
<!-- Script inline pour forcer le fond blanc immédiatement -->
<script>
// Exécuter immédiatement pour supprimer le fond bleu
document.addEventListener('DOMContentLoaded', function() {
    // Appliquer directement sur le body et les éléments principaux
    document.body.style.background = 'white';
    document.body.style.backgroundImage = 'none';
    document.body.style.backgroundColor = 'white';
    
    // Supprimer les classes qui pourraient appliquer un fond bleu
    document.body.classList.remove('bg-gradient-primary', 'bg-primary', 'bg-blue');
});
</script>
<script>
    // Initialisation supplémentaire spécifique à cette vue
    document.addEventListener('DOMContentLoaded', function() {
        // Bouton d'export CSV
        const exportCsvBtn = document.getElementById('exportCsv');
        if (exportCsvBtn) {
            exportCsvBtn.addEventListener('click', function() {
                exportTableToCSV('ventes_export.csv');
            });
        }
        
        // Bouton d'impression
        const printTableBtn = document.getElementById('printTable');
        if (printTableBtn) {
            printTableBtn.addEventListener('click', function() {
                printTable();
            });
        }
    });
    
    // Fonction pour exporter le tableau en CSV
    function exportTableToCSV(filename) {
        const table = document.getElementById('salesTable');
        const rows = table.querySelectorAll('tr:not(.d-none)');
        let csv = [];
        
        // En-têtes (sans la première et dernière colonne)
        const headers = Array.from(rows[0].querySelectorAll('th')).slice(1, -1);
        csv.push(headers.map(header => header.textContent.trim()).join(','));
        
        // Lignes de données (sans la première et dernière colonne)
        for (let i = 1; i < rows.length; i++) {
            if (rows[i].style.display !== 'none') {
                const row = Array.from(rows[i].querySelectorAll('td')).slice(1, -1);
                const rowData = row.map(cell => {
                    // Nettoyer le texte des cellules
                    return '"' + cell.textContent.trim().replace(/"/g, '""') + '"';
                });
                csv.push(rowData.join(','));
            }
        }
        
        // Télécharger le fichier CSV
        const csvContent = csv.join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.setAttribute('download', filename);
        link.click();
    }
    
    // Fonction pour imprimer le tableau
    function printTable() {
        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Impression des ventes</title>');
        
        // Inclure les styles
        printWindow.document.write(`
            <style>
                body { font-family: Arial, sans-serif; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
                th { background-color: #f5f5f5; font-weight: bold; }
                .badge {
                    display: inline-block;
                    padding: 3px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                    font-weight: 500;
                }
                .badge-direct-sale { background-color: rgba(37, 99, 235, 0.1); color: #2563eb; }
                .badge-discount-sale { background-color: rgba(236, 72, 153, 0.1); color: #ec4899; }
                .badge-price-increase { background-color: rgba(249, 115, 22, 0.1); color: #f97316; }
                .badge-approved { background-color: rgba(16, 185, 129, 0.1); color: #10b981; }
                .badge-pending { background-color: rgba(245, 158, 11, 0.1); color: #f59e0b; }
                .badge-rejected { background-color: rgba(239, 68, 68, 0.1); color: #ef4444; }
                .header { margin-bottom: 20px; }
                .title { font-size: 24px; margin-bottom: 5px; }
                .subtitle { font-size: 14px; color: #666; margin-bottom: 20px; }
                @media print {
                    .no-print { display: none; }
                }
            </style>
        `);
        
        printWindow.document.write('</head><body>');
        
        // En-tête
        printWindow.document.write(`
            <div class="header">
                <h1 class="title">Liste des ventes</h1>
                <p class="subtitle">Exporté le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}</p>
            </div>
        `);
        
        // Tableau
        const table = document.getElementById('salesTable');
        const clonedTable = table.cloneNode(true);
        
        // Supprimer la colonne d'actions et la colonne d'indicateur
        const rows = clonedTable.querySelectorAll('tr');
        rows.forEach(row => {
            const cells = row.querySelectorAll('th, td');
            if (cells.length > 0) {
                cells[0].remove(); // Première colonne (indicateur)
                if (cells.length > 1) {
                    cells[cells.length - 1].remove(); // Dernière colonne (actions)
                }
            }
        });
        
        // Supprimer les lignes masquées
        Array.from(clonedTable.querySelectorAll('tbody tr')).forEach(row => {
            if (row.style.display === 'none') {
                row.remove();
            }
        });
        
        printWindow.document.write(clonedTable.outerHTML);
        
        // Pied de page
        printWindow.document.write(`
            <div class="footer">
                <p>© ${new Date().getFullYear()} Gradis - Tous droits réservés</p>
            </div>
        `);
        
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        
        // Imprimer après chargement
        printWindow.onload = function() {
            printWindow.print();
        };
    }
</script>
@endpush
