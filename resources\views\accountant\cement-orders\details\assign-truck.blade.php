@extends('layouts.accountant')

@section('title', 'Affecter un véhicule')

@push('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<style>
    .product-card {
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .info-item {
        padding: 0.5rem;
        background-color: #fff;
        border-radius: 0.25rem;
        border: 1px solid #e3e6f0;
    }

    .info-item i {
        width: 20px;
        color: #4e73df;
        margin-right: 0.5rem;
    }

    .product-header {
        background-color: #4e73df;
        color: white;
        padding: 1rem;
        border-radius: 0.35rem;
        margin-bottom: 1rem;
    }

    .trip-section {
        background-color: #f8f9fc;
        border-radius: 0.35rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border: 1px solid #e3e6f0;
    }
</style>
@endpush

@section('content')
<div class="container-fluid mt-4">
    <!-- En-tête -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Affecter un véhicule</h1>
        <a href="{{ route('accountant.cement-orders.show', $cement_order) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour
        </a>
    </div>

    <!-- Carte principale -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Bon de commande #{{ $cement_order->reference }}</h6>
            <span class="badge bg-{{ $cement_order->status === 'completed' ? 'success' : 'primary' }}">
                {{ ucfirst($cement_order->status) }}
            </span>
        </div>
        <div class="card-body">
            <div class="product-card">
                <!-- En-tête du produit -->
                <div class="product-header">
                    <h5 class="m-0">{{ $cement_order->product->name ?? 'Produit non défini' }}</h5>
                </div>

                <!-- Informations principales -->
                <div class="info-grid">
                    <div class="info-item">
                        <i class="fas fa-box"></i>
                        <strong>Quantité totale:</strong> {{ $detail->formatted_total_tonnage }}
                    </div>
                    <div class="info-item">
                        <i class="fas fa-check"></i>
                        <strong>Déjà livré:</strong> {{ number_format($detail->delivered_quantity ?? 0, 2) }} T
                    </div>
                    <div class="info-item">
                        <i class="fas fa-clock"></i>
                        <strong>Restant:</strong> {{ number_format($detail->remaining_quantity ?? $detail->quantity, 2) }} T
                    </div>
                </div>

                <!-- Destination et client -->
                <div class="info-grid mb-4">
                    <div class="info-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <strong>Destination:</strong> {{ $detail->destination?->name ?? 'N/A' }}
                    </div>
                    <div class="info-item">
                        <i class="fas fa-user"></i>
                        <strong>Client:</strong> {{ $detail->customer?->name ?? 'N/A' }}
                    </div>
                </div>

                <!-- Formulaires d'affectation par voyage -->
                @php
                    $assignedTrips = $detail->tripAssignments->pluck('trip_number')->toArray();
                @endphp

                @for($trip = 1; $trip <= $detail->trips_count; $trip++)
                    <div class="trip-section mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0 font-weight-bold text-primary">
                                Voyage #{{ $trip }}
                                @if(in_array($trip, $assignedTrips))
                                    <span class="badge bg-success ms-2">Affecté</span>
                                @endif
                            </h6>
                            <div class="text-primary">
                                <i class="fas fa-truck"></i> Par voyage: {{ number_format($detail->tonnage_per_trip, 2) }} T
                            </div>
                        </div>

                        @if(in_array($trip, $assignedTrips))
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Ce voyage a déjà été affecté à un camion et un chauffeur.
                            </div>
                        @else
                            <form action="{{ route('accountant.cement-orders.details.assign-truck.store', ['cement_order' => $cement_order->id, 'detail' => $detail->id]) }}" 
                                  method="POST" 
                                  class="assignment-form">
                                @csrf
                                <input type="hidden" name="trip_number" value="{{ $trip }}">
                                <input type="hidden" name="trip_id" value="{{ $trip }}">

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="truck_id_{{ $trip }}" class="form-label">Camion</label>
                                        <select name="truck_id" 
                                                id="truck_id_{{ $trip }}" 
                                                class="form-select @error('truck_id') is-invalid @enderror" 
                                                required>
                                            <option value="">Sélectionner un camion</option>
                                            @foreach($trucks as $truck)
                                                <option value="{{ $truck->id }}" 
                                                        data-capacity="{{ $truck->capacity->tonnage }}"
                                                        {{ old('truck_id') == $truck->id ? 'selected' : '' }}>
                                                    {{ $truck->formatted_name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('truck_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="driver_id_{{ $trip }}" class="form-label">
                                            <i class="fas fa-user me-2"></i>Chauffeur
                                        </label>
                                        <select name="driver_id" 
                                                id="driver_id_{{ $trip }}" 
                                                class="form-select @error('driver_id') is-invalid @enderror" 
                                                required>
                                            <option value="">Sélectionner un chauffeur</option>
                                            @foreach($drivers as $driver)
                                                <option value="{{ $driver->id }}" 
                                                        {{ old('driver_id') == $driver->id ? 'selected' : '' }}>
                                                    {{ $driver->full_name }} ({{ $driver->phone_number }})
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('driver_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="planned_loading_date_{{ $trip }}" class="form-label">
                                            <i class="fas fa-calendar-alt me-2"></i>Date de chargement prévue
                                        </label>
                                        <input type="datetime-local" 
                                               class="form-control @error('planned_loading_date') is-invalid @enderror" 
                                               id="planned_loading_date_{{ $trip }}" 
                                               name="planned_loading_date" 
                                               value="{{ old('planned_loading_date') }}"
                                               required>
                                        @error('planned_loading_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="planned_delivery_date_{{ $trip }}" class="form-label">
                                            <i class="fas fa-truck-loading me-2"></i>Date de livraison prévue
                                        </label>
                                        <input type="datetime-local" 
                                               class="form-control @error('planned_delivery_date') is-invalid @enderror" 
                                               id="planned_delivery_date_{{ $trip }}" 
                                               name="planned_delivery_date" 
                                               value="{{ old('planned_delivery_date') }}"
                                               required>
                                        @error('planned_delivery_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="notes_{{ $trip }}" class="form-label">Notes</label>
                                    <textarea class="form-control @error('notes') is-invalid @enderror" 
                                              id="notes_{{ $trip }}" 
                                              name="notes" 
                                              rows="3">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row mt-3">
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Affecter
                                        </button>
                                    </div>
                                </div>
                            </form>
                        @endif
                    </div>
                @endfor
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Configuration de flatpickr pour les dates
        flatpickr('input[type="datetime-local"]', {
            enableTime: true,
            dateFormat: "Y-m-d H:i",
            minDate: "today"
        });

        // Récupérer le tonnage requis par voyage
        const tonnagePerTrip = {{ $detail->tonnage_per_trip }};

        // Ajouter des écouteurs d'événements pour chaque sélecteur de camion
        document.querySelectorAll('select[name="truck_id"]').forEach(select => {
            select.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                const form = this.closest('form');
                const submitButton = form.querySelector('button[type="submit"]');
                const errorDiv = form.querySelector('.tonnage-error') || document.createElement('div');
                errorDiv.className = 'tonnage-error alert alert-danger mt-2';

                if (selectedOption.value) {
                    const truckCapacity = parseFloat(selectedOption.dataset.capacity);
                    
                    if (truckCapacity < tonnagePerTrip) {
                        errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> Attention : La capacité du camion (${truckCapacity} T) est inférieure au tonnage requis par voyage (${tonnagePerTrip} T)`;
                        if (!form.querySelector('.tonnage-error')) {
                            this.parentNode.appendChild(errorDiv);
                        }
                        submitButton.disabled = true;
                    } else {
                        if (form.querySelector('.tonnage-error')) {
                            form.querySelector('.tonnage-error').remove();
                        }
                        submitButton.disabled = false;
                    }
                }
            });
        });
    });
</script>
@endpush
