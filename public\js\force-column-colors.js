// Script pour forcer la coloration des colonnes Type et Validation
document.addEventListener('DOMContentLoaded', function() {
    function forceColumnColors() {
        // Forcer les styles des en-têtes
        const typeHeader = document.querySelector('.sales-table th:nth-child(6), .header-cell:nth-child(6)');
        const validationHeader = document.querySelector('.sales-table th:nth-child(8), .header-cell:nth-child(8)');
        
        if (typeHeader) {
            typeHeader.classList.add('highlight-header');
            typeHeader.style.background = 'linear-gradient(135deg, #1976D2, #2196F3)';
            typeHeader.style.color = 'white';
        }
        
        if (validationHeader) {
            validationHeader.classList.add('highlight-header');
            validationHeader.style.background = 'linear-gradient(135deg, #1976D2, #2196F3)';
            validationHeader.style.color = 'white';
        }
        
        // Forcer les styles des cellules
        const typeCells = document.querySelectorAll('.type-column, .sales-table td:nth-child(6)');
        const validationCells = document.querySelectorAll('.validation-column, .sales-table td:nth-child(8)');
        
        typeCells.forEach(cell => {
            cell.classList.add('type-column');
            cell.style.backgroundColor = 'rgba(25, 118, 210, 0.08)';
            cell.style.position = 'relative';
            cell.style.borderRadius = '0.5rem';
            
            // Ajouter un pseudo-élément pour le dégradé
            if (!cell.querySelector('.column-gradient')) {
                const gradient = document.createElement('div');
                gradient.className = 'column-gradient';
                gradient.style.position = 'absolute';
                gradient.style.top = '0';
                gradient.style.left = '0';
                gradient.style.width = '100%';
                gradient.style.height = '100%';
                gradient.style.background = 'linear-gradient(to bottom, rgba(25, 118, 210, 0.15), rgba(25, 118, 210, 0.05))';
                gradient.style.pointerEvents = 'none';
                gradient.style.borderRadius = '0.5rem';
                gradient.style.zIndex = '0';
                
                // Insérer au début pour qu'il soit derrière le contenu
                cell.insertBefore(gradient, cell.firstChild);
                
                // S'assurer que le contenu est au-dessus du dégradé
                const content = cell.querySelector('.badge') || cell.querySelector('div');
                if (content) {
                    content.style.position = 'relative';
                    content.style.zIndex = '1';
                }
            }
        });
        
        validationCells.forEach(cell => {
            cell.classList.add('validation-column');
            cell.style.backgroundColor = 'rgba(25, 118, 210, 0.08)';
            cell.style.position = 'relative';
            cell.style.borderRadius = '0.5rem';
            
            // Ajouter un pseudo-élément pour le dégradé
            if (!cell.querySelector('.column-gradient')) {
                const gradient = document.createElement('div');
                gradient.className = 'column-gradient';
                gradient.style.position = 'absolute';
                gradient.style.top = '0';
                gradient.style.left = '0';
                gradient.style.width = '100%';
                gradient.style.height = '100%';
                gradient.style.background = 'linear-gradient(to bottom, rgba(25, 118, 210, 0.15), rgba(25, 118, 210, 0.05))';
                gradient.style.pointerEvents = 'none';
                gradient.style.borderRadius = '0.5rem';
                gradient.style.zIndex = '0';
                
                // Insérer au début pour qu'il soit derrière le contenu
                cell.insertBefore(gradient, cell.firstChild);
                
                // S'assurer que le contenu est au-dessus du dégradé
                const content = cell.querySelector('.badge') || cell.querySelector('div');
                if (content) {
                    content.style.position = 'relative';
                    content.style.zIndex = '1';
                }
            }
        });
        
        console.log('Column colors enforced');
    }
    
    // Exécuter immédiatement
    forceColumnColors();
    
    // Exécuter après le chargement complet
    window.addEventListener('load', forceColumnColors);
    
    // Exécuter périodiquement pour s'assurer que les couleurs restent
    setInterval(forceColumnColors, 500);
});
