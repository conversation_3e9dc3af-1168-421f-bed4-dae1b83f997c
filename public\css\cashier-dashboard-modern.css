/* 
 * TABLEAU DE BORD CAISSIER MODERNE
 * Design professionnel et intuitif avec palette de couleurs bleue
 */

:root {
    /* Palette de couleurs principale (tons de bleu) */
    --primary-color: #1E88E5;
    --primary-light: #64B5F6;
    --primary-dark: #0D47A1;
    --primary-gradient: linear-gradient(135deg, #1E88E5, #0D47A1);
    
    /* Couleurs secondaires */
    --success-color: #2E7D32;
    --success-light: #4CAF50;
    --warning-color: #FF8F00;
    --warning-light: #FFB74D;
    --danger-color: #C62828;
    --danger-light: #EF5350;
    
    /* Couleurs neutres */
    --background-color: #F5F7FA;
    --card-bg: #FFFFFF;
    --border-color: #E0E6ED;
    --text-primary: #2D3748;
    --text-secondary: #718096;
    --text-muted: #A0AEC0;
}

/* Styles généraux */
body {
    background-color: var(--background-color);
    color: var(--text-primary);
    font-family: 'Inter', 'Segoe UI', <PERSON>o, sans-serif;
}

/* Bouton de bascule de la sidebar */
.sidebar-toggle-btn {
    background: transparent;
    border: none;
    color: var(--primary-color);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    display: none;
}

.sidebar-toggle-btn:hover {
    background-color: rgba(30, 136, 229, 0.1);
    color: var(--primary-dark);
}

.sidebar-hidden {
    transform: translateX(-100%);
}

/* Optimisations pour les petits écrans */
@media (max-width: 768px) {
    /* Ajuster le menu latéral sur mobile */
    body {
        padding-left: 60px;
    }
    
    .sidebar {
        width: 60px !important;
        min-width: 60px !important;
        overflow-x: hidden;
        z-index: 1050 !important;
        transition: all 0.3s ease-in-out;
        position: fixed;
        top: 0;
        left: 0;
        height: 100%;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    }
    
    /* Quand la sidebar est cachée */
    .sidebar-hidden {
        transform: translateX(-60px) !important;
    }
    
    body.sidebar-hidden-body {
        padding-left: 0;
    }
    
    .sidebar-header {
        padding: 1rem 0.5rem !important;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .sidebar-header img {
        max-width: 28px !important;
        max-height: 28px !important;
    }
    
    .sidebar .menu-item span,
    .sidebar .nav-link span {
        display: none !important;
        opacity: 0;
        width: 0;
        height: 0;
        overflow: hidden;
    }
    
    .sidebar .menu-item i,
    .sidebar .nav-link i {
        font-size: 1.25rem !important;
        margin-right: 0 !important;
        width: 100% !important;
        text-align: center !important;
    }
    
    .sidebar .menu-item {
        padding: 0.75rem !important;
        justify-content: center !important;
        text-align: center !important;
    }
    
    .sidebar-menu {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    
    /* Ajuster le contenu principal */
    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
        padding: 1rem !important;
        transition: all 0.3s ease-in-out;
    }
    
    /* Pour les layouts qui utilisent .content-wrapper */
    .content-wrapper {
        margin-left: 0 !important;
        width: 100% !important;
        transition: all 0.3s ease-in-out;
    }
    
    /* Ajuster la navbar supérieure */
    .top-navbar {
        left: 60px !important;
        right: 0 !important;
        width: calc(100% - 60px) !important;
        padding: 0 1rem !important;
        transition: all 0.3s ease-in-out;
    }
    
    /* Quand la sidebar est cachée */
    body.sidebar-hidden-body .top-navbar {
        left: 0 !important;
        width: 100% !important;
    }
    
    /* Rendre le bouton de bascule plus visible */
    .sidebar-toggle-btn {
        display: block !important;
        font-size: 1.2rem;
        padding: 0.4rem 0.6rem;
        background-color: rgba(30, 136, 229, 0.1);
        border-radius: 4px;
        color: var(--primary-color);
    }
}

/* Optimisations pour les très petits écrans */
@media (max-width: 480px) {
    /* Sidebar encore plus compacte */
    .sidebar {
        width: 50px !important;
        min-width: 50px !important;
    }
    
    .sidebar-header {
        padding: 0.75rem 0.25rem !important;
    }
    
    .sidebar-header img {
        max-width: 32px !important;
        max-height: 32px !important;
    }
    
    .sidebar .menu-item i,
    .sidebar .nav-link i {
        font-size: 1.1rem !important;
    }
    
    .sidebar .menu-item {
        padding: 0.6rem !important;
    }
    
    /* Ajuster le contenu principal */
    .main-content {
        margin-left: 50px !important;
        width: calc(100% - 50px) !important;
        padding: 0.75rem !important;
    }
    
    /* Pour les layouts qui utilisent .content-wrapper */
    .content-wrapper {
        margin-left: 50px !important;
        width: calc(100% - 50px) !important;
    }
    
    /* Ajuster la navbar supérieure */
    .top-navbar {
        left: 50px !important;
        height: 50px !important;
    }
}

.dashboard-container {
    padding: 1.5rem;
    animation: fadeIn 0.5s ease-out;
    max-width: 100%;
    overflow-x: hidden;
}

@media (max-width: 576px) {
    .dashboard-container {
        padding: 0.75rem;
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
    
    .dashboard-title {
        font-size: 1.25rem;
    }
    
    .dashboard-subtitle {
        font-size: 0.8rem;
    }
    
    /* Optimiser l'espace pour les petits écrans */
    .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
    
    /* Ajuster les boutons dans l'en-tête */
    .dashboard-actions .btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.8rem;
    }
    
    /* Réduire l'espacement entre les sections */
    .stats-overview, .quick-actions, .dashboard-sections {
        margin-bottom: 1rem;
        gap: 0.75rem;
    }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* En-tête du tableau de bord */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .dashboard-actions {
        width: 100%;
        justify-content: space-between;
    }
}

.dashboard-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-dark);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
}

.dashboard-title i {
    color: var(--primary-color);
}

.dashboard-subtitle {
    color: var(--text-secondary);
    font-size: 0.95rem;
    margin-top: 0.5rem;
    font-weight: 400;
}

.dashboard-actions {
    display: flex;
    gap: 1rem;
}

/* Cartes de statistiques */
.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

@media (max-width: 768px) {
    .stats-overview {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    }
}

@media (max-width: 576px) {
    .stats-overview {
        grid-template-columns: 1fr;
    }
}

.stat-card {
    background: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.stat-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.stat-card-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-card-icon.sales {
    background: var(--primary-gradient);
}

.stat-card-icon.payments {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
}

.stat-card-icon.credit {
    background: linear-gradient(135deg, #FF9800, #FF6F00);
}

.stat-card-icon.products {
    background: linear-gradient(135deg, #5E35B1, #3949AB);
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 16px;
}

.stat-trend.positive {
    color: var(--success-color);
    background-color: rgba(46, 125, 50, 0.1);
}

.stat-trend.negative {
    color: var(--danger-color);
    background-color: rgba(198, 40, 40, 0.1);
}

.stat-card-title {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.stat-card-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    letter-spacing: -0.02em;
}

@media (max-width: 480px) {
    .stat-card-value {
        font-size: 1.25rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-card-icon {
        width: 36px;
        height: 36px;
        font-size: 0.9rem;
    }
    
    .stat-card-subtitle {
        font-size: 0.75rem;
    }
    
    .stat-trend {
        font-size: 0.75rem;
        padding: 0.2rem 0.4rem;
    }
    
    /* Optimiser les cartes d'action rapide */
    .quick-action-icon {
        width: 50px;
        height: 50px;
        margin-bottom: 0.75rem;
    }
    
    .quick-action-icon i {
        font-size: 1.5rem;
    }
    
    .quick-action-title {
        font-size: 0.9rem;
    }
    
    .quick-action-description {
        font-size: 0.75rem;
    }
    
    .quick-action-card {
        padding: 1rem;
    }
}

.stat-card-subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.stat-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-container {
    height: 6px;
    background-color: #E2E8F0;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 0.5rem;
    width: 100%;
}

.progress-bar {
    height: 100%;
    border-radius: 3px;
    position: relative;
    overflow: hidden;
    transition: width 0.6s ease;
}

.progress-bar.sales {
    background: var(--primary-gradient);
}

.progress-bar.payments {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
}

.progress-bar.credit {
    background: linear-gradient(135deg, #FF9800, #FF6F00);
}

.progress-bar.products {
    background: linear-gradient(135deg, #5E35B1, #3949AB);
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

/* Cartes d'actions rapides */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.25rem;
    margin-bottom: 2rem;
}

@media (max-width: 768px) {
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .quick-actions {
        grid-template-columns: 1fr;
    }
}

.quick-action-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.quick-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--primary-gradient);
}

.quick-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.quick-action-icon {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: rgba(30, 136, 229, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.quick-action-card:hover .quick-action-icon {
    background: var(--primary-gradient);
    transform: scale(1.1);
}

.quick-action-icon i {
    font-size: 1.75rem;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.quick-action-card:hover .quick-action-icon i {
    color: white;
}

.quick-action-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.quick-action-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Sections du tableau de bord */
.dashboard-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

@media (max-width: 768px) {
    .dashboard-sections {
        grid-template-columns: 1fr;
    }
}

.dashboard-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.dashboard-section:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.dashboard-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background-color: #FCFCFD;
}

.dashboard-section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dashboard-section-title i {
    color: var(--primary-color);
}

.view-all-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
}

.view-all-button:hover {
    color: var(--primary-dark);
    transform: translateX(3px);
}

.dashboard-section-body {
    padding: 1.25rem 1.5rem;
}

/* Tableau moderne */
.modern-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

@media (max-width: 768px) {
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        max-width: 100%;
        margin-bottom: 1rem;
    }
    
    .modern-table {
        min-width: 650px; /* Assurer que le tableau a une largeur minimale pour être lisible */
    }
}

.modern-table thead th {
    background-color: #F9FAFB;
    color: var(--text-secondary);
    font-weight: 600;
    padding: 0.875rem 1.25rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.modern-table tbody tr {
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--border-color);
}

.modern-table tbody tr:last-child {
    border-bottom: none;
}

.modern-table tbody tr:hover {
    background-color: #F9FAFB;
}

.modern-table td {
    padding: 1rem 1.25rem;
    vertical-align: middle;
    color: var(--text-primary);
    font-size: 0.875rem;
}

/* Badges de statut */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.35rem 0.75rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
    gap: 0.35rem;
}

.status-badge.paid {
    background-color: rgba(46, 125, 50, 0.1);
    color: var(--success-color);
}

.status-badge.pending {
    background-color: rgba(255, 143, 0, 0.1);
    color: var(--warning-color);
}

.status-badge.failed {
    background-color: rgba(198, 40, 40, 0.1);
    color: var(--danger-color);
}

/* Boutons d'action */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-button {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    background-color: white;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
    text-decoration: none;
}

.action-button:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Carte de produit */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1.25rem;
}

.product-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.product-image {
    height: 160px;
    background-color: #F9FAFB;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid var(--border-color);
}

.product-image i {
    font-size: 3rem;
    color: var(--primary-color);
}

.product-details {
    padding: 1.25rem;
}

.product-category {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.product-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.product-price {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.product-stock {
    font-size: 0.875rem;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Graphiques */
.chart-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
}

@media (max-width: 768px) {
    .chart-container {
        padding: 1rem;
    }
    
    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .chart-filters {
        width: 100%;
        justify-content: space-between;
    }
    
    .chart-body {
        height: 250px;
    }
}

@media (max-width: 576px) {
    .chart-filter {
        padding: 0.4rem 0.5rem;
        font-size: 0.8rem;
    }
    
    .chart-title {
        font-size: 1rem;
    }
    
    .chart-body {
        height: 200px;
    }
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.chart-filters {
    display: flex;
    gap: 0.75rem;
}

.chart-filter {
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    background-color: white;
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-filter.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Footer style similaire à l'administration */
.admin-footer {
    width: 100%;
    color: #6c757d;
    font-size: 0.85rem;
    border-top: 1px solid #eaeaea;
    margin-top: 2rem;
    background-color: #f8f9fa;
    position: relative;
    bottom: 0;
}

@media (max-width: 768px) {
    .admin-footer {
        font-size: 0.75rem;
        padding: 0.5rem 0;
    }
    
    .admin-footer .text-center {
        padding: 0.5rem !important;
    }
}

/* Animations */
.fade-in {
    opacity: 0;
    animation: fadeIn 0.5s ease-out forwards;
}

.fade-in-delay-1 {
    animation-delay: 0.1s;
}

.fade-in-delay-2 {
    animation-delay: 0.2s;
}

.fade-in-delay-3 {
    animation-delay: 0.3s;
}

.fade-in-delay-4 {
    animation-delay: 0.4s;
}

/* Responsive */
@media (max-width: 992px) {
    .stats-overview {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .dashboard-sections {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .stats-overview {
        grid-template-columns: 1fr;
    }
    
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .product-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .product-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .dashboard-actions {
        width: 100%;
    }
}

/* Utilitaires */
.text-success {
    color: var(--success-color);
}

.text-warning {
    color: var(--warning-color);
}

.text-danger {
    color: var(--danger-color);
}

.text-primary {
    color: var(--primary-color);
}

.bg-light {
    background-color: #F9FAFB;
}

.d-flex {
    display: flex;
}

.align-items-center {
    align-items: center;
}

.justify-content-between {
    justify-content: space-between;
}

.gap-2 {
    gap: 0.5rem;
}

.mb-4 {
    margin-bottom: 1rem;
}
