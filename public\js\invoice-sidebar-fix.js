/**
 * Script pour ajuster dynamiquement la vue des factures avec la sidebar
 * Ce script détecte la largeur réelle de la sidebar et ajuste le contenu en conséquence
 */
document.addEventListener('DOMContentLoaded', function() {
    // Fonction pour ajuster le contenu en fonction de la sidebar
    function adjustContentForSidebar() {
        const sidebar = document.getElementById('sidebar');
        const contentWrapper = document.getElementById('content-wrapper');
        const content = document.getElementById('content');
        
        if (sidebar && contentWrapper && content) {
            // Obtenir la largeur réelle de la sidebar
            const sidebarWidth = sidebar.offsetWidth;
            const windowWidth = window.innerWidth;
            
            // Si l'écran est assez grand pour afficher la sidebar
            if (windowWidth > 768) {
                // Réserver l'espace pour la sidebar
                content.style.maxWidth = `calc(100% - ${sidebarWidth}px)`;
                content.style.width = '100%';
                
                // Vérifier si la sidebar est collapsée
                if (sidebar.classList.contains('collapsed')) {
                    contentWrapper.style.marginLeft = '70px';
                    document.querySelector('.top-navbar').style.left = '70px';
                } else {
                    contentWrapper.style.marginLeft = `${sidebarWidth}px`;
                    document.querySelector('.top-navbar').style.left = `${sidebarWidth}px`;
                }
            } else {
                // En mode mobile, pas besoin de décalage car la sidebar est soit cachée soit en overlay
                content.style.maxWidth = '100%';
                contentWrapper.style.marginLeft = '0';
                document.querySelector('.top-navbar').style.left = '0';
            }
        }
    }

    // Exécuter l'ajustement au chargement
    adjustContentForSidebar();
    
    // Réajuster lors du redimensionnement de la fenêtre
    window.addEventListener('resize', adjustContentForSidebar);
    
    // Écouter les changements de classe sur la sidebar (pour détecter collapse/expand)
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            // Attendre que l'animation de la sidebar soit terminée
            setTimeout(adjustContentForSidebar, 300);
        });
    }
});

// Fonction pour optimiser la vue sur les petits écrans
function optimizeTablesForMobile() {
    const tables = document.querySelectorAll('.invoices-table');
    const windowWidth = window.innerWidth;
    
    tables.forEach(table => {
        // Si l'écran est petit, simplifier l'affichage du tableau
        if (windowWidth < 768) {
            // Réduire la taille des caractères
            table.style.fontSize = '0.85rem';
            
            // Cacher certaines colonnes moins importantes sur mobile
            const dateHeaders = table.querySelectorAll('th:nth-child(3), td:nth-child(3)');
            dateHeaders.forEach(el => el.style.display = 'none');
        } else {
            // Restaurer l'affichage normal
            table.style.fontSize = '';
            const dateHeaders = table.querySelectorAll('th:nth-child(3), td:nth-child(3)');
            dateHeaders.forEach(el => el.style.display = '');
        }
    });
}

// Exécuter au chargement et au redimensionnement
document.addEventListener('DOMContentLoaded', optimizeTablesForMobile);
window.addEventListener('resize', optimizeTablesForMobile);
