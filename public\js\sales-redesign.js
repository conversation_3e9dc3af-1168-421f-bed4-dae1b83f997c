/**
 * Script de refonte complète pour la vue des ventes
 * Utilise remove-blue-bg.js comme base et ajoute des fonctionnalités modernes
 */

// Fonction principale exécutée au chargement du document
document.addEventListener('DOMContentLoaded', function() {
    console.log('Script de refonte des ventes chargé avec succès');
    
    // Appliquer la suppression du fond bleu (basé sur remove-blue-bg.js)
    removeBlueBackground();
    
    // Initialiser les composants interactifs
    initializeFilters();
    initializeDataTable();
    initializeCharts();
    initializeAnimations();
    
    // Vérifier périodiquement que le fond reste blanc
    setInterval(removeBlueBackground, 1000);
});

/**
 * Supprime le fond bleu dégradé de manière agressive
 * Basé sur le script remove-blue-bg.js existant
 */
function removeBlueBackground() {
    // Créer une feuille de style pour remplacer tous les dégradés bleus
    if (!document.getElementById('override-blue-styles')) {
        const styleSheet = document.createElement('style');
        styleSheet.id = 'override-blue-styles';
        styleSheet.innerHTML = `
            html, body, body::before, body::after {
                background: white !important;
                background-color: white !important;
                background-image: none !important;
                background-attachment: unset !important;
                background-position: unset !important;
                background-repeat: unset !important;
                background-size: unset !important;
            }
            
            .main-card, .main-card::before, .main-card::after {
                background: white !important;
                background-color: white !important;
                background-image: none !important;
                color: #333 !important;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
                animation: none !important;
            }
            
            @keyframes cardGlow {
                0%, 50%, 100% {
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
                }
            }
            
            /* Assurer que les colonnes Type et Validation gardent leur fond bleu */
            .sales-table td.type-column,
            .sales-table td.validation-column {
                background-color: rgba(37, 99, 235, 0.08) !important;
            }
        `;
        document.head.appendChild(styleSheet);
    }
    
    // Appliquer directement sur le body et les éléments principaux
    document.body.style.background = 'white';
    document.body.style.backgroundImage = 'none';
    document.body.style.backgroundColor = 'white';
    
    // Supprimer les classes qui pourraient appliquer un fond bleu
    document.body.classList.remove('bg-gradient-primary', 'bg-primary', 'bg-blue');
    
    // Cibler tous les éléments qui pourraient avoir un fond bleu
    const elementsToCheck = document.querySelectorAll('.main-card, .card, .container, .container-fluid');
    elementsToCheck.forEach(element => {
        element.style.background = 'white';
        element.style.backgroundImage = 'none';
        element.style.backgroundColor = 'white';
    });
}

/**
 * Initialise les filtres interactifs
 */
function initializeFilters() {
    const filterPills = document.querySelectorAll('.filter-pill');
    
    filterPills.forEach(pill => {
        pill.addEventListener('click', function() {
            const filterType = this.getAttribute('data-filter');
            const filterGroup = this.getAttribute('data-filter-group');
            
            // Si c'est un filtre de groupe, désactiver les autres du même groupe
            if (filterGroup) {
                document.querySelectorAll(`.filter-pill[data-filter-group="${filterGroup}"]`).forEach(groupPill => {
                    groupPill.classList.remove('active');
                });
            }
            
            // Activer/désactiver ce filtre
            this.classList.toggle('active');
            
            // Appliquer les filtres
            applyFilters();
            
            // Animation de feedback
            animateElement(this, 'pulse');
        });
    });
    
    // Recherche en temps réel
    const searchInput = document.getElementById('salesSearch');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            applyFilters();
        });
    }
}

/**
 * Applique les filtres actifs aux lignes du tableau
 */
function applyFilters() {
    const rows = document.querySelectorAll('#salesTable tbody tr');
    const activeTypeFilter = document.querySelector('.filter-pill[data-filter-group="type"].active');
    const activeStatusFilter = document.querySelector('.filter-pill[data-filter-group="status"].active');
    const searchInput = document.getElementById('salesSearch');
    const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
    
    let visibleCount = 0;
    
    rows.forEach(row => {
        const saleType = row.getAttribute('data-sale-type');
        const validationStatus = row.getAttribute('data-validation-status');
        let showRow = true;
        
        // Filtre par type
        if (activeTypeFilter && activeTypeFilter.getAttribute('data-filter') !== 'all-types') {
            showRow = showRow && (saleType === activeTypeFilter.getAttribute('data-filter'));
        }
        
        // Filtre par statut
        if (activeStatusFilter && activeStatusFilter.getAttribute('data-filter') !== 'all-status') {
            showRow = showRow && (validationStatus === activeStatusFilter.getAttribute('data-filter'));
        }
        
        // Filtre par recherche
        if (searchTerm) {
            const rowText = row.textContent.toLowerCase();
            showRow = showRow && rowText.includes(searchTerm);
        }
        
        // Appliquer la visibilité
        if (showRow) {
            row.style.display = '';
            visibleCount++;
            
            // Animation d'apparition
            row.style.animation = 'fadeIn 0.3s ease-out forwards';
        } else {
            row.style.display = 'none';
        }
    });
    
    // Mettre à jour le compteur de résultats
    const resultsCounter = document.getElementById('resultsCounter');
    if (resultsCounter) {
        resultsCounter.textContent = visibleCount;
        resultsCounter.parentElement.style.display = 'block';
    }
    
    // Afficher un message si aucun résultat
    const noResultsMessage = document.getElementById('noResultsMessage');
    if (noResultsMessage) {
        noResultsMessage.style.display = visibleCount === 0 ? 'block' : 'none';
    }
}

/**
 * Initialise DataTable pour des fonctionnalités avancées de tableau
 */
function initializeDataTable() {
    const table = document.getElementById('salesTable');
    if (!table) return;
    
    // Vérifier si DataTable est disponible
    if (typeof $.fn.DataTable !== 'undefined') {
        try {
            // Initialiser DataTable avec des options modernes
            $(table).DataTable({
                paging: false, // Gestion de pagination personnalisée
                searching: false, // Recherche personnalisée déjà implémentée
                info: false,
                ordering: true,
                responsive: true,
                language: {
                    emptyTable: "Aucune vente trouvée",
                    zeroRecords: "Aucun résultat correspondant"
                },
                columnDefs: [
                    { orderable: false, targets: [0, -1] } // Désactiver le tri sur la première et dernière colonne
                ],
                initComplete: function() {
                    console.log('DataTable initialisé avec succès');
                }
            });
        } catch (error) {
            console.warn('Erreur lors de l\'initialisation de DataTable:', error);
        }
    } else {
        console.warn('DataTable n\'est pas disponible, fonctionnalités de tableau limitées');
        
        // Ajouter tri basique si DataTable n'est pas disponible
        addBasicSorting(table);
    }
}

/**
 * Ajoute un tri basique aux en-têtes de tableau si DataTable n'est pas disponible
 */
function addBasicSorting(table) {
    const headers = table.querySelectorAll('thead th');
    
    headers.forEach((header, index) => {
        // Ignorer la première et dernière colonne
        if (index === 0 || index === headers.length - 1) return;
        
        header.style.cursor = 'pointer';
        header.classList.add('sortable');
        
        // Ajouter l'icône de tri
        const sortIcon = document.createElement('i');
        sortIcon.className = 'fas fa-sort ms-1';
        sortIcon.style.fontSize = '0.75rem';
        sortIcon.style.opacity = '0.5';
        header.appendChild(sortIcon);
        
        // Ajouter l'événement de clic
        header.addEventListener('click', function() {
            sortTable(table, index);
            
            // Mettre à jour les icônes
            headers.forEach(h => {
                const icon = h.querySelector('i.fas');
                if (icon) {
                    icon.className = 'fas fa-sort ms-1';
                    icon.style.opacity = '0.5';
                }
            });
            
            // Mettre à jour l'icône de cette colonne
            sortIcon.className = this.getAttribute('data-sort') === 'asc' 
                ? 'fas fa-sort-up ms-1' 
                : 'fas fa-sort-down ms-1';
            sortIcon.style.opacity = '1';
        });
    });
}

/**
 * Trie le tableau par colonne
 */
function sortTable(table, columnIndex) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const header = table.querySelectorAll('thead th')[columnIndex];
    
    // Déterminer la direction du tri
    const currentSort = header.getAttribute('data-sort') || 'none';
    const newSort = currentSort === 'asc' ? 'desc' : 'asc';
    header.setAttribute('data-sort', newSort);
    
    // Trier les lignes
    rows.sort((rowA, rowB) => {
        const cellA = rowA.querySelectorAll('td')[columnIndex].textContent.trim();
        const cellB = rowB.querySelectorAll('td')[columnIndex].textContent.trim();
        
        // Vérifier si c'est un nombre
        if (!isNaN(cellA) && !isNaN(cellB)) {
            return newSort === 'asc' 
                ? parseFloat(cellA) - parseFloat(cellB)
                : parseFloat(cellB) - parseFloat(cellA);
        }
        
        // Tri alphabétique
        return newSort === 'asc'
            ? cellA.localeCompare(cellB)
            : cellB.localeCompare(cellA);
    });
    
    // Réorganiser les lignes
    rows.forEach(row => tbody.appendChild(row));
}

/**
 * Initialise les graphiques et visualisations
 */
function initializeCharts() {
    // Vérifier si Chart.js est disponible
    if (typeof Chart === 'undefined') {
        console.warn('Chart.js n\'est pas disponible, aucun graphique ne sera affiché');
        return;
    }
    
    // Graphique des ventes par type
    const salesByTypeCtx = document.getElementById('salesByTypeChart');
    if (salesByTypeCtx) {
        new Chart(salesByTypeCtx, {
            type: 'doughnut',
            data: {
                labels: ['Vente directe', 'Vente avec remise', 'Augmentation de prix'],
                datasets: [{
                    data: collectChartData('type'),
                    backgroundColor: [
                        'rgba(37, 99, 235, 0.8)',
                        'rgba(236, 72, 153, 0.8)',
                        'rgba(249, 115, 22, 0.8)'
                    ],
                    borderColor: [
                        'rgba(37, 99, 235, 1)',
                        'rgba(236, 72, 153, 1)',
                        'rgba(249, 115, 22, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '70%',
                animation: {
                    animateScale: true,
                    animateRotate: true
                }
            }
        });
    }
    
    // Graphique des ventes par statut
    const salesByStatusCtx = document.getElementById('salesByStatusChart');
    if (salesByStatusCtx) {
        new Chart(salesByStatusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Approuvé', 'En attente', 'Rejeté'],
                datasets: [{
                    data: collectChartData('status'),
                    backgroundColor: [
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(239, 68, 68, 0.8)'
                    ],
                    borderColor: [
                        'rgba(16, 185, 129, 1)',
                        'rgba(245, 158, 11, 1)',
                        'rgba(239, 68, 68, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '70%',
                animation: {
                    animateScale: true,
                    animateRotate: true
                }
            }
        });
    }
}

/**
 * Collecte les données pour les graphiques à partir du tableau
 */
function collectChartData(dataType) {
    const rows = document.querySelectorAll('#salesTable tbody tr');
    const counts = {};
    
    if (dataType === 'type') {
        counts.direct = 0;
        counts.discount = 0;
        counts.increase = 0;
        
        rows.forEach(row => {
            const type = row.getAttribute('data-sale-type');
            if (type === 'direct-sale') counts.direct++;
            else if (type === 'discount-sale') counts.discount++;
            else if (type === 'price-increase') counts.increase++;
        });
        
        return [counts.direct, counts.discount, counts.increase];
    }
    
    if (dataType === 'status') {
        counts.approved = 0;
        counts.pending = 0;
        counts.rejected = 0;
        
        rows.forEach(row => {
            const status = row.getAttribute('data-validation-status');
            if (status === 'approved') counts.approved++;
            else if (status === 'pending') counts.pending++;
            else if (status === 'rejected') counts.rejected++;
        });
        
        return [counts.approved, counts.pending, counts.rejected];
    }
    
    return [0, 0, 0];
}

/**
 * Initialise les animations et effets visuels
 */
function initializeAnimations() {
    // Animation des cartes de statistiques
    animateStatCards();
    
    // Animation des barres de progression
    animateProgressBars();
    
    // Animation au survol des lignes du tableau
    initializeTableRowEffects();
    
    // Animation des badges
    animateBadges();
}

/**
 * Anime les cartes de statistiques
 */
function animateStatCards() {
    const statCards = document.querySelectorAll('.stat-card');
    
    statCards.forEach((card, index) => {
        // Délai progressif pour l'animation
        setTimeout(() => {
            card.classList.add('animate-scale-in');
        }, index * 100);
        
        // Animation de la valeur
        const statValue = card.querySelector('.stat-value');
        if (statValue) {
            animateCounter(statValue);
        }
    });
}

/**
 * Anime les compteurs numériques
 */
function animateCounter(element) {
    const target = parseInt(element.getAttribute('data-value') || element.textContent);
    const duration = 1500;
    const step = target / duration * 10;
    let current = 0;
    
    const updateCounter = () => {
        current += step;
        if (current < target) {
            element.textContent = Math.floor(current).toLocaleString();
            requestAnimationFrame(updateCounter);
        } else {
            element.textContent = target.toLocaleString();
        }
    };
    
    updateCounter();
}

/**
 * Anime les barres de progression
 */
function animateProgressBars() {
    const progressBars = document.querySelectorAll('.stat-progress-bar');
    
    progressBars.forEach(bar => {
        const targetWidth = bar.getAttribute('data-width') || '75%';
        
        // Initialiser à 0
        bar.style.width = '0%';
        
        // Animer jusqu'à la valeur cible
        setTimeout(() => {
            bar.style.width = targetWidth;
        }, 300);
    });
}

/**
 * Initialise les effets au survol des lignes du tableau
 */
function initializeTableRowEffects() {
    const rows = document.querySelectorAll('#salesTable tbody tr');
    
    rows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = 'var(--shadow-md)';
            this.style.zIndex = '1';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
            this.style.zIndex = '';
        });
    });
}

/**
 * Anime les badges
 */
function animateBadges() {
    const badges = document.querySelectorAll('.badge');
    
    badges.forEach(badge => {
        // Animation au survol
        badge.addEventListener('mouseenter', function() {
            animateElement(this, 'pulse');
        });
    });
}

/**
 * Anime un élément avec l'effet spécifié
 */
function animateElement(element, effect) {
    // Supprimer l'animation précédente
    element.style.animation = 'none';
    
    // Forcer le reflow
    void element.offsetWidth;
    
    // Appliquer la nouvelle animation
    if (effect === 'pulse') {
        element.style.animation = 'pulse 0.5s ease-in-out';
    } else if (effect === 'fadeIn') {
        element.style.animation = 'fadeIn 0.5s ease-out forwards';
    } else if (effect === 'scaleIn') {
        element.style.animation = 'scaleIn 0.3s ease-out forwards';
    }
}

/**
 * Fonction utilitaire pour formater les nombres
 */
function formatNumber(number) {
    return new Intl.NumberFormat('fr-FR').format(number);
}

/**
 * Fonction utilitaire pour formater les montants
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'XOF',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
}

/**
 * Fonction utilitaire pour formater les dates
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(date);
}

// Exécuter la suppression du fond bleu immédiatement
removeBlueBackground();
