@extends('layouts.admin_minimal')

@section('title', 'Modifier le produit - ' . $product->name)

@section('content')
<div class="container-fluid py-0">
    <!-- En-tête moderne et attrayant -->
    <div class="modern-header-wrapper">
        <div class="modern-header-background">
            <div class="header-gradient-overlay"></div>
            <div class="header-pattern"></div>
        </div>

        <div class="modern-header-content">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <!-- Navigation breadcrumb moderne -->
                    <nav aria-label="breadcrumb" class="modern-breadcrumb">
                        <ol class="breadcrumb-modern">
                            <li class="breadcrumb-item-modern">
                                <a href="{{ route('admin.dashboard') }}" class="breadcrumb-link">
                                    <i class="fas fa-home"></i>
                                    <span>Dashboard</span>
                                </a>
                            </li>
                            <li class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </li>
                            <li class="breadcrumb-item-modern">
                                <a href="{{ route('admin.products.index') }}" class="breadcrumb-link">
                                    <i class="fas fa-boxes"></i>
                                    <span>Produits</span>
                                </a>
                            </li>
                            <li class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </li>
                            <li class="breadcrumb-item-modern">
                                <a href="{{ route('admin.products.show', $product) }}" class="breadcrumb-link">
                                    @php
                                        $categoryName = strtolower($product->category->name ?? '');
                                        $iconClass = match($categoryName) {
                                            'ciment' => 'fas fa-industry',
                                            'fer' => 'fas fa-hammer',
                                            'brique' => 'fas fa-th-large',
                                            'sable' => 'fas fa-mountain',
                                            'gravier' => 'fas fa-gem',
                                            'bois' => 'fas fa-tree',
                                            default => 'fas fa-cube'
                                        };
                                    @endphp
                                    <i class="{{ $iconClass }}"></i>
                                    <span>{{ Str::limit($product->name, 25) }}</span>
                                </a>
                            </li>
                            <li class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </li>
                            <li class="breadcrumb-item-modern active">
                                <span>Modifier</span>
                            </li>
                        </ol>
                    </nav>

                    <!-- Titre principal avec animation -->
                    <div class="header-title-section">
                        <div class="title-icon-wrapper">
                            <div class="title-icon-bg">
                                <i class="fas fa-edit"></i>
                            </div>
                        </div>
                        <div class="title-content">
                            <h1 class="header-title">
                                Modifier le produit
                                <span class="title-highlight">{{ $product->name }}</span>
                            </h1>
                            <p class="header-subtitle">
                                <i class="fas fa-info-circle me-2"></i>
                                Modification des informations et spécifications du produit
                            </p>
                        </div>
                    </div>

                    <!-- Informations rapides du produit -->
                    <div class="product-quick-info">
                        <div class="quick-info-item">
                            <div class="info-icon">
                                <i class="fas fa-barcode"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">Code produit</span>
                                <span class="info-value">{{ $product->code }}</span>
                            </div>
                        </div>
                        <div class="quick-info-item">
                            <div class="info-icon">
                                <i class="fas fa-tag"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">Catégorie</span>
                                <span class="info-value">{{ $product->category->name ?? 'Non définie' }}</span>
                            </div>
                        </div>
                        <div class="quick-info-item">
                            <div class="info-icon">
                                <i class="fas fa-cube"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">Unité</span>
                                <span class="info-value">{{ $product->unit }}</span>
                            </div>
                        </div>
                        <div class="quick-info-item">
                            <div class="info-icon status-icon">
                                <i class="fas fa-{{ $product->is_active ? 'check-circle' : 'times-circle' }}"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">Statut</span>
                                <span class="info-value status-{{ $product->is_active ? 'active' : 'inactive' }}">
                                    {{ $product->is_active ? 'Actif' : 'Inactif' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Actions rapides modernes -->
                    <div class="header-actions">
                        <div class="action-buttons">
                            <a href="{{ route('admin.products.show', $product) }}" class="btn-modern btn-view">
                                <div class="btn-icon">
                                    <i class="fas fa-eye"></i>
                                </div>
                                <span>Voir le produit</span>
                            </a>
                            <a href="{{ route('admin.products.index') }}" class="btn-modern btn-back">
                                <div class="btn-icon">
                                    <i class="fas fa-arrow-left"></i>
                                </div>
                                <span>Retour à la liste</span>
                            </a>
                        </div>

                        <!-- Indicateur de dernière modification -->
                        <div class="last-modified-info">
                            <div class="modified-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="modified-content">
                                <span class="modified-label">Dernière modification</span>
                                <span class="modified-date">{{ $product->updated_at->format('d/m/Y à H:i') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Séparateur avec animation -->
    <div class="header-separator">
        <div class="separator-line"></div>
        <div class="separator-icon">
            <i class="fas fa-chevron-down"></i>
        </div>
    </div>

    <form action="{{ route('admin.products.update', $product) }}" method="POST" id="productForm">
        @csrf
        @method('PUT')

        <div class="row">
            <!-- Informations principales -->
            <div class="col-lg-8">
                <div class="card modern-card mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0 fw-bold">
                            <i class="fas fa-info-circle text-primary me-2"></i>
                            Informations Générales
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label text-muted">Nom du produit</label>
                                    <input type="text" name="name" class="form-control @error('name') is-invalid @enderror"
                                           value="{{ old('name', $product->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="form-group mb-3">
                                    <label class="form-label text-muted">Code produit</label>
                                    <input type="text" name="code" class="form-control @error('code') is-invalid @enderror"
                                           value="{{ old('code', $product->code) }}">
                                    @error('code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="form-group mb-3">
                                    <label class="form-label text-muted">Catégorie</label>
                                    <select name="category_id" class="form-select @error('category_id') is-invalid @enderror" required>
                                        <option value="">Sélectionner une catégorie</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}"
                                                {{ old('category_id', $product->category_id) == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label text-muted">Unité de mesure</label>
                                    <input type="text" name="unit" class="form-control @error('unit') is-invalid @enderror"
                                           value="{{ old('unit', $product->unit) }}" required>
                                    @error('unit')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="form-group mb-3">
                                    <label class="form-label text-muted">Stock actuel</label>
                                    <input type="number" name="stock_quantity" step="0.01"
                                           class="form-control @error('stock_quantity') is-invalid @enderror"
                                           value="{{ old('stock_quantity', $product->stock_quantity) }}" required>
                                    @error('stock_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="form-group mb-3">
                                    <label class="form-label text-muted">Statut</label>
                                    <select name="is_active" class="form-select @error('is_active') is-invalid @enderror">
                                        <option value="1" {{ old('is_active', $product->is_active) == 1 ? 'selected' : '' }}>Actif</option>
                                        <option value="0" {{ old('is_active', $product->is_active) == 0 ? 'selected' : '' }}>Inactif</option>
                                    </select>
                                    @error('is_active')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section des prix selon la catégorie -->
                @if(strtolower($product->category->name) === 'ciment')
                    <div class="card modern-card mb-4">
                        <div class="card-header bg-white border-0">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-map-marker-alt text-info me-2"></i>
                                Prix par Ville
                            </h5>
                            <p class="text-muted mb-0 small">Configurez des prix spécifiques pour chaque ville selon les coûts de transport</p>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach($regions as $region)
                                <div class="col-lg-6 mb-4">
                                    <div class="region-card-modern">
                                        <div class="region-header-gradient">
                                            <div class="region-title">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>{{ $region->name }}</span>
                                            </div>
                                            @php
                                                $regionCitiesCount = $product->prices->whereIn('city_id', $region->cities->pluck('id'))->count();
                                            @endphp
                                            <div class="region-badge">
                                                {{ $regionCitiesCount }} ville(s)
                                            </div>
                                        </div>
                                        <div class="card-body p-3">
                                            <button type="button" class="add-city-btn-modern add-city-btn"
                                                    data-region-id="{{ $region->id }}"
                                                    data-region-name="{{ $region->name }}">
                                                <i class="fas fa-plus-circle"></i>
                                                Ajouter une ville
                                            </button>

                                            <div class="selected-cities" data-region="{{ strtolower($region->name) }}">
                                                @foreach($region->cities as $city)
                                                    @php
                                                        $existingPrice = $product->prices->where('city_id', $city->id)->first();
                                                    @endphp
                                                    @if($existingPrice)
                                                        <div class="city-card-modern" data-city-id="{{ $city->id }}">
                                                            <div class="city-header-modern">
                                                                <div class="city-name-modern">
                                                                    <i class="fas fa-building text-primary"></i>
                                                                    {{ $city->name }}
                                                                </div>
                                                                <button type="button" class="remove-city-btn-modern remove-city">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                            </div>
                                                            <div class="input-group-modern">
                                                                <input type="number" class="form-control"
                                                                       name="prices[{{ $city->id }}]"
                                                                       value="{{ $existingPrice->price }}"
                                                                       placeholder="Prix en FCFA"
                                                                       min="0" step="0.01" required>
                                                                <span class="input-group-text">FCFA</span>
                                                            </div>
                                                        </div>
                                                    @endif
                                                @endforeach
                                            </div>

                                            @if($regionCitiesCount == 0)
                                            <div class="text-center py-4 empty-state">
                                                <div class="empty-icon">
                                                    <i class="fas fa-map-marked-alt"></i>
                                                </div>
                                                <p class="mb-0 fw-bold">Aucune ville configurée</p>
                                                <small class="text-muted">Cliquez sur "Ajouter une ville" pour commencer</small>
                                            </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @elseif(strtolower($product->category->name) === 'fer')
                    <div class="card modern-card mb-4">
                        <div class="card-header bg-white border-0">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-hammer text-info me-2"></i>
                                Spécifications Fer
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label class="form-label text-muted">Diamètre (mm)</label>
                                        <input type="number" name="iron_diameter" step="0.01" class="form-control"
                                               value="{{ old('iron_diameter', $product->ironSpecification->diameter ?? '') }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label class="form-label text-muted">Longueur (m)</label>
                                        <input type="number" name="iron_length" step="0.01" class="form-control"
                                               value="{{ old('iron_length', $product->ironSpecification->length ?? '') }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label class="form-label text-muted">Poids unitaire (kg)</label>
                                        <input type="number" name="iron_unit_weight" step="0.01" class="form-control"
                                               value="{{ old('iron_unit_weight', $product->ironSpecification->unit_weight ?? '') }}">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label class="form-label text-muted">Prix unitaire (FCFA)</label>
                                        <input type="number" name="iron_unit_price" step="0.01" class="form-control"
                                               value="{{ old('iron_unit_price', $product->ironSpecification->unit_price ?? '') }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label class="form-label text-muted">Prix par tonne (FCFA)</label>
                                        <input type="number" name="iron_ton_price" step="0.01" class="form-control"
                                               value="{{ old('iron_ton_price', $product->ironSpecification->ton_price ?? '') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="card modern-card mb-4">
                        <div class="card-header bg-white border-0">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-tag text-success me-2"></i>
                                Prix du Produit
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label class="form-label text-muted">Prix de base (FCFA)</label>
                                <input type="number" name="price" step="0.01"
                                       class="form-control @error('price') is-invalid @enderror"
                                       value="{{ old('price', $product->price) }}">
                                @error('price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Actions et informations -->
            <div class="col-lg-4">
                <!-- Actions rapides -->
                <div class="card modern-card mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0 fw-bold">
                            <i class="fas fa-bolt text-warning me-2"></i>
                            Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>Enregistrer les modifications
                            </button>
                            <a href="{{ route('admin.products.show', $product) }}" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-2"></i>Voir le produit
                            </a>
                            <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                            </a>
                            <button type="button" class="btn btn-outline-danger" onclick="resetForm()">
                                <i class="fas fa-undo me-2"></i>Réinitialiser
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Informations du produit -->
                <div class="card modern-card">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0 fw-bold">
                            <i class="fas fa-info-circle text-info me-2"></i>
                            Informations
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="info-item mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="info-label">
                                    <i class="fas fa-calendar text-primary me-2"></i>
                                    Créé le
                                </div>
                                <div class="info-value">{{ $product->created_at->format('d/m/Y') }}</div>
                            </div>
                        </div>
                        <div class="info-item mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="info-label">
                                    <i class="fas fa-edit text-success me-2"></i>
                                    Modifié le
                                </div>
                                <div class="info-value">{{ $product->updated_at->format('d/m/Y') }}</div>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="info-label">
                                    <i class="fas fa-tag text-warning me-2"></i>
                                    Catégorie
                                </div>
                                <div class="info-value">
                                    <span class="badge bg-gradient-primary text-white">
                                        <i class="{{ $iconClass }} me-1"></i>{{ $product->category->name }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Modal pour sélectionner une ville -->
<div class="modal fade" id="citySelectionModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    Ajouter une ville - <span id="selectedRegionName"></span>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="citySelect" class="form-label">Sélectionnez une ville</label>
                    <select class="form-select" id="citySelect">
                        <option value="">Choisissez une ville</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="cityPrice" class="form-label">Prix de vente (FCFA)</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="cityPrice" min="0" step="0.01" required>
                        <span class="input-group-text">FCFA</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="confirmCitySelection">
                    <i class="fas fa-plus-circle me-2"></i>Ajouter
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* ===== EN-TÊTE MODERNE ET ATTRAYANT ===== */

/* Wrapper principal de l'en-tête */
.modern-header-wrapper {
    position: relative;
    margin: -1.5rem -1.5rem 2rem -1.5rem;
    overflow: hidden;
    border-radius: 0 0 24px 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

/* Arrière-plan avec gradient */
.modern-header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: 1;
}

/* Overlay avec gradient subtil */
.header-gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
    z-index: 2;
}

/* Motif décoratif */
.header-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    z-index: 3;
}

/* Contenu de l'en-tête */
.modern-header-content {
    position: relative;
    z-index: 4;
    padding: 2.5rem 2rem;
    color: white;
}

/* ===== BREADCRUMB MODERNE ===== */
.modern-breadcrumb {
    margin-bottom: 1.5rem;
}

.breadcrumb-modern {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin: 0;
    padding: 0;
    list-style: none;
}

.breadcrumb-item-modern {
    display: flex;
    align-items: center;
}

.breadcrumb-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.breadcrumb-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.breadcrumb-link i {
    font-size: 0.85rem;
}

.breadcrumb-separator {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.75rem;
    margin: 0 0.25rem;
}

.breadcrumb-item-modern.active span {
    color: white;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    font-size: 0.9rem;
}

/* ===== SECTION TITRE ===== */
.header-title-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.title-icon-wrapper {
    flex-shrink: 0;
}

.title-icon-bg {
    width: 64px;
    height: 64px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.title-icon-bg i {
    font-size: 1.75rem;
    color: white;
}

.title-content {
    flex: 1;
}

.header-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: white;
    margin: 0 0 0.5rem 0;
    line-height: 1.2;
}

.title-highlight {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    margin-top: 0.25rem;
}

.header-subtitle {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    display: flex;
    align-items: center;
    font-weight: 500;
}

/* ===== INFORMATIONS RAPIDES ===== */
.product-quick-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.quick-info-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
}

.quick-info-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.info-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.info-icon i {
    font-size: 1.1rem;
    color: white;
}

.status-icon.info-icon {
    background: rgba(16, 185, 129, 0.3);
}

.info-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 0.95rem;
    color: white;
    font-weight: 600;
}

.status-active {
    color: #10b981 !important;
}

.status-inactive {
    color: #ef4444 !important;
}

/* ===== ACTIONS DE L'EN-TÊTE ===== */
.header-actions {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: flex-end;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
    max-width: 250px;
}

.btn-modern {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.25rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-view {
    background: rgba(59, 130, 246, 0.2);
    color: white;
    border-color: rgba(59, 130, 246, 0.3);
}

.btn-view:hover {
    background: rgba(59, 130, 246, 0.3);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn-back {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: rgba(255, 255, 255, 0.2);
}

.btn-back:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.btn-icon {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.btn-icon i {
    font-size: 0.9rem;
}

/* Informations de modification */
.last-modified-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    max-width: 250px;
    width: 100%;
}

.modified-icon {
    width: 36px;
    height: 36px;
    background: rgba(251, 191, 36, 0.2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.modified-icon i {
    font-size: 1rem;
    color: #fbbf24;
}

.modified-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.modified-label {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modified-date {
    font-size: 0.85rem;
    color: white;
    font-weight: 600;
}

/* ===== SÉPARATEUR ANIMÉ ===== */
.header-separator {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 2rem 0;
    height: 40px;
}

.separator-line {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #e5e7eb 20%, #d1d5db 50%, #e5e7eb 80%, transparent 100%);
    transform: translateY(-50%);
}

.separator-icon {
    background: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 2px solid #f3f4f6;
    animation: bounce 2s infinite;
}

.separator-icon i {
    color: #6b7280;
    font-size: 0.9rem;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}

/* ===== RESPONSIVE ===== */
@media (max-width: 991.98px) {
    .modern-header-content {
        padding: 2rem 1.5rem;
    }

    .header-title {
        font-size: 1.75rem;
    }

    .title-highlight {
        font-size: 1.25rem;
    }

    .product-quick-info {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    .header-actions {
        align-items: stretch;
        margin-top: 1.5rem;
    }

    .action-buttons {
        max-width: none;
    }

    .last-modified-info {
        max-width: none;
    }
}

@media (max-width: 767.98px) {
    .modern-header-wrapper {
        margin: -1rem -1rem 1.5rem -1rem;
    }

    .modern-header-content {
        padding: 1.5rem 1rem;
    }

    .header-title-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
        text-align: left;
    }

    .title-icon-bg {
        width: 56px;
        height: 56px;
    }

    .title-icon-bg i {
        font-size: 1.5rem;
    }

    .header-title {
        font-size: 1.5rem;
    }

    .title-highlight {
        font-size: 1.1rem;
    }

    .product-quick-info {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .breadcrumb-modern {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .breadcrumb-separator {
        display: none;
    }
}

/* ===== CARTES MODERNES ===== */
.modern-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    background: white;
    position: relative;
}

.modern-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    z-index: 1;
}

.modern-card:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
    transform: translateY(-4px);
}

.modern-card .card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: none;
    padding: 1.5rem 2rem;
    border-radius: 0;
    position: relative;
    z-index: 2;
}

.modern-card .card-header h5 {
    margin: 0;
    font-weight: 700;
    color: #1e293b;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.modern-card .card-body {
    padding: 2rem;
    background: white;
}

/* Styles pour les formulaires */
.form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-label {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.75rem;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-control, .form-select {
    border-radius: 12px;
    border: 2px solid #e5e7eb;
    padding: 0.875rem 1.25rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: white;
    font-weight: 500;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
    background: white;
    outline: none;
}

.form-control:hover, .form-select:hover {
    border-color: #d1d5db;
}

/* Boutons modernes */
.btn {
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-color: #10b981;
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.btn-outline-primary {
    border-color: #667eea;
    color: #667eea;
    background: transparent;
}

.btn-outline-primary:hover {
    background: #667eea;
    border-color: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-outline-secondary {
    border-color: #6b7280;
    color: #6b7280;
    background: transparent;
}

.btn-outline-secondary:hover {
    background: #6b7280;
    border-color: #6b7280;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
}

/* Styles pour les cartes de prix */
.city-price-card {
    background: rgba(0,123,255,0.05);
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid rgba(0,123,255,0.2);
    transition: all 0.3s ease;
}

.city-price-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,123,255,0.15);
}

.region-section {
    background: linear-gradient(135deg, rgba(0,123,255,0.1) 0%, rgba(0,123,255,0.05) 100%);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(0,123,255,0.2);
}

/* Styles pour les informations */
.info-item {
    background: rgba(0,0,0,0.02);
    padding: 0.75rem;
    border-radius: 8px;
    border-left: 3px solid #007bff;
    transition: all 0.3s ease;
}

.info-item:hover {
    background: rgba(0,123,255,0.05);
    transform: translateX(5px);
    border-left-color: #0056b3;
}

.info-label {
    font-size: 0.9rem;
    color: #6c757d;
}

.info-value {
    font-size: 1rem;
    color: #495057;
    font-weight: 600;
}

/* Boutons d'action */
.btn {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modern-card {
    animation: fadeInUp 0.6s ease-out;
}

.modern-card:nth-child(2) { animation-delay: 0.1s; }
.modern-card:nth-child(3) { animation-delay: 0.2s; }
.modern-card:nth-child(4) { animation-delay: 0.3s; }

/* ===== STYLES POUR LES CARTES DE RÉGION MODERNES ===== */
.region-card-modern {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.region-card-modern:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.region-header-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.region-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 1.1rem;
}

.region-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Bouton "Ajouter une ville" moderne */
.add-city-btn-modern {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.25rem;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    width: 100%;
    justify-content: center;
    margin-bottom: 1rem;
}

.add-city-btn-modern:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
    color: white;
}

/* Cartes de ville sélectionnées */
.city-card-modern {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.city-card-modern:hover {
    border-color: #007bff;
    background: #fff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.1);
}

.city-header-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.city-name-modern {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.remove-city-btn-modern {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.remove-city-btn-modern:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.input-group-modern {
    display: flex;
    align-items: center;
}

.input-group-modern .form-control {
    border-radius: 8px 0 0 8px;
    border-right: none;
    flex: 1;
}

.input-group-modern .input-group-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 0 8px 8px 0;
    font-weight: 600;
    padding: 0.75rem 1rem;
}

/* État vide */
.empty-state {
    color: #6c757d;
}

.empty-icon {
    background: rgba(108, 117, 125, 0.1);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    opacity: 0.7;
}

/* Responsive */
@media (max-width: 768px) {
    .city-price-card {
        margin-bottom: 1rem;
    }

    .region-section {
        margin-bottom: 1rem;
    }

    .region-card-modern .card-body {
        padding: 1rem;
    }

    .add-city-btn-modern {
        font-size: 0.8rem;
        padding: 0.6rem 1rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation d'entrée pour les cartes
    const cards = document.querySelectorAll('.modern-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Données des villes par région
    const citiesByRegion = @json($regions->mapWithKeys(function($region) {
        return [$region->id => $region->cities->map(function($city) {
            return ['id' => $city->id, 'name' => $city->name];
        })];
    }));

    let currentRegionId = null;

    // Gestion des boutons "Ajouter une ville"
    document.addEventListener('click', function(e) {
        if (e.target.closest('.add-city-btn-modern')) {
            const button = e.target.closest('.add-city-btn-modern');
            currentRegionId = button.dataset.regionId;
            const regionName = button.dataset.regionName;

            // Mettre à jour le modal
            document.getElementById('selectedRegionName').textContent = regionName;

            // Remplir le select avec les villes de la région
            const citySelect = document.getElementById('citySelect');
            citySelect.innerHTML = '<option value="">Choisissez une ville</option>';

            if (citiesByRegion[currentRegionId]) {
                citiesByRegion[currentRegionId].forEach(city => {
                    // Vérifier si la ville n'est pas déjà ajoutée
                    const regionCard = button.closest('.region-card-modern');
                    const existingCity = regionCard.querySelector(`[data-city-id="${city.id}"]`);

                    if (!existingCity) {
                        const option = document.createElement('option');
                        option.value = city.id;
                        option.textContent = city.name;
                        citySelect.appendChild(option);
                    }
                });
            }

            // Réinitialiser le prix
            document.getElementById('cityPrice').value = '';

            // Afficher le modal
            const modal = new bootstrap.Modal(document.getElementById('citySelectionModal'));
            modal.show();
        }

        // Gestion des boutons de suppression
        if (e.target.closest('.remove-city-btn-modern')) {
            const button = e.target.closest('.remove-city-btn-modern');
            const cityCard = button.closest('.city-card-modern');
            const regionCard = button.closest('.region-card-modern');

            // Animation de suppression
            cityCard.style.transform = 'translateX(100%)';
            cityCard.style.opacity = '0';

            setTimeout(() => {
                cityCard.remove();

                // Vérifier s'il faut afficher l'état vide
                const selectedCities = regionCard.querySelector('.selected-cities');
                const remainingCities = selectedCities.querySelectorAll('.city-card-modern');

                if (remainingCities.length === 0) {
                    const emptyState = regionCard.querySelector('.empty-state');
                    if (emptyState) {
                        emptyState.style.display = 'block';
                    }
                }

                // Mettre à jour le badge de comptage
                updateRegionBadge(regionCard);
            }, 300);
        }
    });

    // Confirmation d'ajout de ville
    document.getElementById('confirmCitySelection').addEventListener('click', function() {
        const citySelect = document.getElementById('citySelect');
        const cityPrice = document.getElementById('cityPrice');

        if (!citySelect.value || !cityPrice.value) {
            alert('Veuillez sélectionner une ville et saisir un prix.');
            return;
        }

        const cityId = citySelect.value;
        const cityName = citySelect.options[citySelect.selectedIndex].text;
        const price = cityPrice.value;

        // Trouver la carte de région correspondante
        const regionCard = document.querySelector(`[data-region-id="${currentRegionId}"]`).closest('.region-card-modern');
        const selectedCities = regionCard.querySelector('.selected-cities');
        const emptyState = regionCard.querySelector('.empty-state');

        // Masquer l'état vide
        if (emptyState) {
            emptyState.style.display = 'none';
        }

        // Créer la nouvelle carte de ville
        const cityCardHTML = `
            <div class="city-card-modern" data-city-id="${cityId}" style="opacity: 0; transform: translateY(20px);">
                <div class="city-header-modern">
                    <div class="city-name-modern">
                        <i class="fas fa-building text-primary"></i>
                        ${cityName}
                    </div>
                    <button type="button" class="remove-city-btn-modern remove-city">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="input-group-modern">
                    <input type="number" class="form-control"
                           name="prices[${cityId}]"
                           value="${price}"
                           placeholder="Prix en FCFA"
                           min="0" step="0.01" required>
                    <span class="input-group-text">FCFA</span>
                </div>
            </div>
        `;

        selectedCities.insertAdjacentHTML('beforeend', cityCardHTML);

        // Animation d'apparition
        const newCityCard = selectedCities.lastElementChild;
        setTimeout(() => {
            newCityCard.style.opacity = '1';
            newCityCard.style.transform = 'translateY(0)';
        }, 50);

        // Mettre à jour le badge de comptage
        updateRegionBadge(regionCard);

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('citySelectionModal'));
        modal.hide();
    });

    // Fonction pour mettre à jour le badge de comptage des régions
    function updateRegionBadge(regionCard) {
        const selectedCities = regionCard.querySelectorAll('.city-card-modern');
        const badge = regionCard.querySelector('.region-badge');
        badge.textContent = `${selectedCities.length} ville(s)`;
    }

    // Validation du formulaire
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('Veuillez remplir tous les champs obligatoires.');
            }
        });
    }

    // Effet de survol pour les cartes d'information
    const infoItems = document.querySelectorAll('.info-item');
    infoItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(10px) scale(1.02)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0) scale(1)';
        });
    });
});
</script>
@endpush
