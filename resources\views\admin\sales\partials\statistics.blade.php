<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100 sales-stat-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-icon bg-primary">
                            <i class="fas fa-shopping-cart text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="stat-value">{{ number_format($totalSales ?? $sales->total()) }}</div>
                        <div class="stat-label">Total Ventes</div>
                        @if(isset($monthlySales))
                            <span class="percentage-badge info">+{{ $monthlySales }} ce mois</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100 sales-stat-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-icon bg-success">
                            <i class="fas fa-check-circle text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="stat-value">{{ number_format($paidSales ?? $sales->where('payment_status', 'paid')->count()) }}</div>
                        <div class="stat-label">Ventes Payées</div>
                        @php
                            $paidPercentage = $totalSales > 0 ? round(($paidSales / $totalSales) * 100) : 0;
                        @endphp
                        <span class="percentage-badge success">{{ $paidPercentage }}% du total</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100 sales-stat-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-icon bg-warning">
                            <i class="fas fa-clock text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="stat-value">{{ number_format($pendingSales ?? $sales->where('payment_status', 'pending')->count()) }}</div>
                        <div class="stat-label">En Attente</div>
                        @if(isset($pendingDiscountSales) && $pendingDiscountSales->count() > 0)
                            <span class="percentage-badge warning">{{ $pendingDiscountSales->count() }} à valider</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100 sales-stat-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-icon bg-info">
                            <i class="fas fa-euro-sign text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        @php
                            $totalRevenue = $totalRevenue ?? $sales->sum(function($sale) { 
                                return $sale->quantity * $sale->unit_price - ($sale->discount_total ?? 0); 
                            });
                        @endphp
                        <div class="stat-value">{{ number_format($totalRevenue, 0, ',', ' ') }}</div>
                        <div class="stat-label">CA Total (FCFA)</div>
                        @if(isset($monthlyRevenue))
                            <span class="percentage-badge primary">{{ number_format($monthlyRevenue, 0, ',', ' ') }} ce mois</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Graphique de tendance (optionnel) -->
@if(isset($chartData))
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-area me-2 text-primary"></i>Tendance des Ventes
                </h6>
            </div>
            <div class="card-body">
                <canvas id="salesChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Alertes de performance -->
<div class="row mb-4">
    @if(isset($pendingSales) && $pendingSales > 0)
        <div class="col-md-6">
            <div class="alert alert-warning border-0 shadow-sm alert-card-warning">
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                    <div>
                        <h6 class="alert-heading mb-1">Ventes en attente</h6>
                        <p class="mb-0">{{ $pendingSales }} vente(s) nécessitent un suivi pour le paiement.</p>
                    </div>
                </div>
            </div>
        </div>
    @endif
    
    @if(isset($pendingDiscountSales) && $pendingDiscountSales->count() > 0)
        <div class="col-md-6">
            <div class="alert alert-info border-0 shadow-sm alert-card-info">
                <div class="d-flex align-items-center">
                    <i class="fas fa-tag fa-2x me-3"></i>
                    <div>
                        <h6 class="alert-heading mb-1">Remises à valider</h6>
                        <p class="mb-0">{{ $pendingDiscountSales->count() }} vente(s) avec remise en attente de validation.</p>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
