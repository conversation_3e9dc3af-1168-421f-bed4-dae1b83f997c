/* Styles modernes pour le tableau de bord comptable - Version 2025 */
:root {
    /* Palette de couleurs bleues */
    --primary-color: #1E88E5;
    --primary-dark: #0D47A1;
    --primary-light: #64B5F6;
    --secondary-color: #0288D1;
    --accent-color: #00BCD4;
    
    /* Couleurs fonctionnelles */
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --danger-color: #F44336;
    --info-color: #2196F3;
    
    /* Couleurs neutres */
    --dark-color: #263238;
    --light-color: #ECEFF1;
    --white-color: #FFFFFF;
    --gray-color: #607D8B;
    --gray-light: #B0BEC5;
    --gray-dark: #455A64;
    
    /* Ombres */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.2);
    
    /* Dégradés */
    --gradient-primary: linear-gradient(135deg, #1E88E5 0%, #0D47A1 100%);
    --gradient-secondary: linear-gradient(135deg, #0288D1 0%, #01579B 100%);
    --gradient-success: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
    --gradient-warning: linear-gradient(135deg, #FF9800 0%, #E65100 100%);
    --gradient-danger: linear-gradient(135deg, #F44336 0%, #B71C1C 100%);
    
    /* Rayons de bordure */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 1rem;
    --border-radius-xl: 1.5rem;
    
    /* Transitions */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
}

/* Styles généraux */
body {
    font-family: 'Inter', 'Poppins', sans-serif;
    background-color: #f8f9fa;
    color: var(--dark-color);
}

.dashboard-wrapper {
    position: relative;
    overflow-x: hidden;
    padding-top: 20px;
}

/* En-tête avec dégradé bleu et éléments 3D */
.dashboard-header {
    position: relative;
    background: var(--gradient-primary);
    padding: 2.5rem 0;
    margin-bottom: 2rem;
    color: var(--white-color);
    overflow: hidden;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.3;
}

.header-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.header-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
    width: 300px;
    height: 300px;
    top: -150px;
    right: -100px;
}

.circle-2 {
    width: 200px;
    height: 200px;
    bottom: -100px;
    left: 10%;
    background: rgba(255, 255, 255, 0.05);
}

.circle-3 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 20%;
    background: rgba(255, 255, 255, 0.07);
}

.dashboard-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 600px;
    margin-bottom: 1.5rem;
}

.dashboard-header .date-display {
    font-size: 1.1rem;
    opacity: 0.8;
    margin-bottom: 1rem;
}

.dashboard-header .btn-dashboard {
    padding: 0.6rem 1.5rem;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: var(--transition-normal);
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.dashboard-header .btn-dashboard:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.dashboard-header .search-container {
    position: relative;
    max-width: 400px;
    margin-left: auto;
}

.dashboard-header .search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border-radius: 50px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.15);
    color: white;
    backdrop-filter: blur(10px);
    transition: var(--transition-normal);
}

.dashboard-header .search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.dashboard-header .search-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.25);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
}

.dashboard-header .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
}
