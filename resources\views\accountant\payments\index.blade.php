@extends('layouts.accountant')

@section('title', 'Gestion des paiements')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="card-title mb-0">Liste des paiements</h4>
                        <div class="d-flex gap-2">
                            <!-- Barre de recherche -->
                            <form action="{{ route('accountant.payments.index') }}" method="GET" class="d-flex">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Rechercher..." name="search" value="{{ request('search') }}">
                                    <button class="btn btn-primary" type="submit">
                                        <i data-feather="search"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-centered table-hover">
                            <thead>
                                <tr>
                                    <th>Référence</th>
                                    <th>Client</th>
                                    <th>N° Commande</th>
                                    <th>Date</th>
                                    <th>Montant</th>
                                    <th>Méthode</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($payments as $payment)
                                    <tr>
                                        <td>{{ $payment->reference }}</td>
                                        <td>
                                            <div>{{ $payment->order->user->name }}</div>
                                            <small class="text-muted">{{ $payment->order->user->email }}</small>
                                        </td>
                                        <td>{{ $payment->order->number }}</td>
                                        <td>{{ $payment->created_at->format('d/m/Y H:i') }}</td>
                                        <td class="currency">{{ number_format($payment->amount, 0, ',', ' ') }}</td>
                                        <td>{{ $payment->method }}</td>
                                        <td>
                                            @if($payment->status === 'completed')
                                                <span class="badge bg-success">Complété</span>
                                            @elseif($payment->status === 'pending')
                                                <span class="badge bg-warning">En attente</span>
                                            @elseif($payment->status === 'failed')
                                                <span class="badge bg-danger">Échoué</span>
                                            @endif
                                        </td>
                                        <td>
                                            <a href="{{ route('accountant.payments.show', $payment) }}" 
                                               class="btn btn-sm btn-info" 
                                               title="Voir les détails">
                                                <i data-feather="eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center">Aucun paiement trouvé</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $payments->links() }}
                    </div>

                    <!-- Statistiques des paiements -->
                    <div class="row mt-4">
                        <div class="col-md-3">
                            <div class="card bg-primary bg-opacity-10 border-0">
                                <div class="card-body">
                                    <div class="d-flex align-items-start">
                                        <div class="avatar-sm">
                                            <span class="avatar-title bg-primary bg-opacity-10 text-primary rounded">
                                                <i data-feather="credit-card" class="icon-dual-primary"></i>
                                            </span>
                                        </div>
                                        <div class="ms-3">
                                            <h4 class="mb-1">{{ $payments->total() }}</h4>
                                            <p class="text-muted mb-0">Total paiements</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success bg-opacity-10 border-0">
                                <div class="card-body">
                                    <div class="d-flex align-items-start">
                                        <div class="avatar-sm">
                                            <span class="avatar-title bg-success bg-opacity-10 text-success rounded">
                                                <i data-feather="check-circle" class="icon-dual-success"></i>
                                            </span>
                                        </div>
                                        <div class="ms-3">
                                            <h4 class="mb-1">{{ $payments->where('status', 'completed')->count() }}</h4>
                                            <p class="text-muted mb-0">Paiements complétés</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning bg-opacity-10 border-0">
                                <div class="card-body">
                                    <div class="d-flex align-items-start">
                                        <div class="avatar-sm">
                                            <span class="avatar-title bg-warning bg-opacity-10 text-warning rounded">
                                                <i data-feather="clock" class="icon-dual-warning"></i>
                                            </span>
                                        </div>
                                        <div class="ms-3">
                                            <h4 class="mb-1">{{ $payments->where('status', 'pending')->count() }}</h4>
                                            <p class="text-muted mb-0">Paiements en attente</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info bg-opacity-10 border-0">
                                <div class="card-body">
                                    <div class="d-flex align-items-start">
                                        <div class="avatar-sm">
                                            <span class="avatar-title bg-info bg-opacity-10 text-info rounded">
                                                <i data-feather="dollar-sign" class="icon-dual-info"></i>
                                            </span>
                                        </div>
                                        <div class="ms-3">
                                            <h4 class="mb-1 currency">{{ number_format($payments->sum('amount'), 0, ',', ' ') }}</h4>
                                            <p class="text-muted mb-0">Montant total</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Initialiser les icônes Feather
    feather.replace();

    // Formatter les montants en FCFA
    document.querySelectorAll('.currency').forEach(element => {
        const amount = parseFloat(element.textContent.replace(/[^\d.-]/g, ''));
        element.textContent = new Intl.NumberFormat('fr-FR').format(amount) + ' FCFA';
    });
</script>
@endpush
