/* Styles modernes pour les filtres de ventes */

/* Conteneur principal des filtres */
.filters-section {
    margin-bottom: 2rem;
    background-color: white;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    border: 1px solid #EAECF0;
    animation: fadeIn 0.5s ease-out;
}

/* Titre des filtres */
.filters-title {
    font-size: 1rem;
    font-weight: 600;
    color: #101828;
    margin-bottom: 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filters-title i {
    color: #2196F3;
}

/* Rangées de filtres */
.filters-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1.25rem;
    margin-bottom: 1rem;
}

.filters-row:last-child {
    margin-bottom: 0;
}

/* Labels des filtres */
.filter-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #344054;
    margin-bottom: 0.5rem;
}

/* Conteneur des boutons de filtre */
.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

/* Boutons de filtre */
.filter-button {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.625rem 1.25rem;
    background-color: white;
    border: 1px solid #D0D5DD;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #344054;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05);
    position: relative;
    overflow: hidden;
}

/* Effet de ripple au clic */
.filter-button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.filter-button:active::after {
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(25, 25);
        opacity: 0;
    }
}

.filter-button:hover {
    border-color: #98A2B3;
    background-color: #F9FAFB;
}

.filter-button.active {
    background-color: #EFF8FF;
    border-color: #2196F3;
    color: #1976D2;
}

/* Icônes dans les boutons */
.filter-button i {
    font-size: 0.875rem;
}

/* Styles spécifiques pour les types de filtres */
.filter-type-ventes .filter-button.active {
    background-color: #EFF8FF;
    border-color: #2196F3;
    color: #1976D2;
}

.filter-type-status .filter-button.active {
    background-color: #ECFDF3;
    border-color: #4CAF50;
    color: #027A48;
}

.filter-type-status .filter-button.active.waiting {
    background-color: #FFF8ED;
    border-color: #FF9800;
    color: #B93815;
}

.filter-type-status .filter-button.active.rejected {
    background-color: #FEF3F2;
    border-color: #F44336;
    color: #B42318;
}

/* Bouton d'exportation */
.export-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1rem;
    background-color: white;
    border: 1px solid #D0D5DD;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #344054;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05);
    margin-left: auto;
}

.export-button:hover {
    background-color: #F9FAFB;
    border-color: #98A2B3;
}

.export-button i {
    color: #2196F3;
}

/* Responsive design */
@media (max-width: 768px) {
    .filters-row {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .filter-button {
        width: 100%;
        justify-content: space-between;
    }
    
    .export-button {
        margin-top: 1rem;
        width: 100%;
        justify-content: center;
    }
}
