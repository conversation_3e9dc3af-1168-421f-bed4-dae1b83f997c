@extends('layouts.accountant')

@section('title', 'Rapport Clients')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<style>
    :root {
        --primary-blue: #3498db;
        --primary-green: #2ecc71;
        --primary-orange: #f39c12;
        --primary-purple: #9b59b6;
        --primary-red: #e74c3c;
        --text-dark: #2c3e50;
        --text-light: #7f8c8d;
        --bg-light: #ecf0f1;
        --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
        --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
        --border-radius: 12px;
    }

    .enhanced-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-light);
        border: none;
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .enhanced-card:hover {
        box-shadow: var(--shadow-medium);
        transform: translateY(-2px);
    }

    .card-header-enhanced {
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
        color: white;
        padding: 20px;
        border: none;
        display: flex;
        justify-content: between;
        align-items: center;
    }

    .card-title-enhanced {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .metric-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 20px;
    }

    .metric-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
    }

    .metric-icon.blue { background: linear-gradient(135deg, var(--primary-blue), #5dade2); }
    .metric-icon.green { background: linear-gradient(135deg, var(--primary-green), #58d68d); }
    .metric-icon.orange { background: linear-gradient(135deg, var(--primary-orange), #f7dc6f); }
    .metric-icon.purple { background: linear-gradient(135deg, var(--primary-purple), #bb8fce); }

    .metric-content h4 {
        margin: 0;
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-dark);
    }

    .metric-content p {
        margin: 0;
        color: var(--text-light);
        font-size: 0.9rem;
    }

    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .status-badge.success { background: #d5f4e6; color: var(--primary-green); }
    .status-badge.info { background: #d6eaf8; color: var(--primary-blue); }
    .status-badge.warning { background: #fdeaa7; color: var(--primary-orange); }
    .status-badge.trending { background: #e8daef; color: var(--primary-purple); }

    .table-enhanced {
        width: 100%;
        margin: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table-enhanced thead th {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        color: var(--text-dark);
        font-weight: 600;
        padding: 15px;
        border: none;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .table-enhanced tbody td {
        padding: 15px;
        border-bottom: 1px solid #f1f2f6;
        vertical-align: middle;
    }

    .table-enhanced tbody tr:hover {
        background: #f8f9fa;
    }

    .period-selector {
        display: flex;
        gap: 8px;
        background: white;
        padding: 8px;
        border-radius: 25px;
        box-shadow: var(--shadow-light);
    }

    .period-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 20px;
        background: transparent;
        color: var(--text-light);
        text-decoration: none;
        transition: all 0.3s ease;
        font-size: 0.85rem;
        font-weight: 500;
    }

    .period-btn.active {
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
        color: white;
        box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
    }

    .period-btn:hover:not(.active) {
        background: #f8f9fa;
        color: var(--text-dark);
    }
</style>
@endpush

@section('content')
<div class="container-fluid" style="background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); min-height: 100vh; padding: 20px;">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 style="color: var(--text-dark); font-weight: 700; margin: 0;">
                        <i class="fas fa-users" style="color: var(--primary-orange); margin-right: 10px;"></i>
                        Rapport Clients
                    </h1>
                    <p style="color: var(--text-light); margin: 5px 0 0 0;">Analyse détaillée du comportement client</p>
                </div>
                <div class="period-selector">
                    <a href="{{ route('accountant.reports.customers', ['period' => 'today']) }}"
                       class="period-btn {{ $period === 'today' ? 'active' : '' }}">
                       Aujourd'hui
                    </a>
                    <a href="{{ route('accountant.reports.customers', ['period' => 'week']) }}"
                       class="period-btn {{ $period === 'week' ? 'active' : '' }}">
                       Cette semaine
                    </a>
                    <a href="{{ route('accountant.reports.customers', ['period' => 'month']) }}"
                       class="period-btn {{ $period === 'month' ? 'active' : '' }}">
                       Ce mois
                    </a>
                    <a href="{{ route('accountant.reports.customers', ['period' => 'year']) }}"
                       class="period-btn {{ $period === 'year' ? 'active' : '' }}">
                       Cette année
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Métriques de résumé -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="enhanced-card orange animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                <div class="metric-item">
                    <div class="metric-icon orange">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="metric-content">
                        <h4>{{ $customers->total() }}</h4>
                        <p>Clients actifs</p>
                    </div>
                </div>
                <div class="status-badge success">
                    <i class="fas fa-arrow-up"></i>
                    Clients
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="enhanced-card blue animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                <div class="metric-item">
                    <div class="metric-icon blue">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="metric-content">
                        <h4>{{ $customers->sum('orders_count') }}</h4>
                        <p>Commandes totales</p>
                    </div>
                </div>
                <div class="status-badge info">
                    <i class="fas fa-chart-line"></i>
                    Commandes
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="enhanced-card green animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                <div class="metric-item">
                    <div class="metric-icon green">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="metric-content">
                        <h4>{{ number_format($customers->sum('orders_sum_total_amount'), 0, ',', ' ') }}</h4>
                        <p>Chiffre d'affaires</p>
                    </div>
                </div>
                <div class="status-badge success">
                    <i class="fas fa-dollar-sign"></i>
                    FCFA
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="enhanced-card purple animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                <div class="metric-item">
                    <div class="metric-icon purple">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="metric-content">
                        <h4>{{ $customers->count() > 0 ? number_format($customers->sum('orders_sum_total_amount') / $customers->sum('orders_count'), 0, ',', ' ') : 0 }}</h4>
                        <p>Panier moyen</p>
                    </div>
                </div>
                <div class="status-badge trending">
                    <i class="fas fa-trending-up"></i>
                    Moyenne
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des clients détaillé -->
    <div class="enhanced-card animate__animated animate__fadeInRight" style="animation-delay: 0.5s;">
        <div class="card-header-enhanced">
            <h3 class="card-title-enhanced">
                <i class="fas fa-table" style="color: white;"></i>
                Détail des Clients par Période
            </h3>
            <div class="status-badge success">
                <i class="fas fa-list"></i>
                {{ $customers->count() }} clients
            </div>
        </div>
        <div class="table-responsive">
            <table class="table-enhanced">
                <thead>
                    <tr>
                        <th>
                            <i class="fas fa-user" style="margin-right: 8px; color: var(--primary-orange);"></i>
                            Client
                        </th>
                        <th>
                            <i class="fas fa-envelope" style="margin-right: 8px; color: var(--primary-blue);"></i>
                            Email
                        </th>
                        <th>
                            <i class="fas fa-phone" style="margin-right: 8px; color: var(--primary-green);"></i>
                            Téléphone
                        </th>
                        <th>
                            <i class="fas fa-shopping-cart" style="margin-right: 8px; color: var(--primary-purple);"></i>
                            Commandes
                        </th>
                        <th>
                            <i class="fas fa-coins" style="margin-right: 8px; color: var(--primary-green);"></i>
                            Montant total
                        </th>
                        <th>
                            <i class="fas fa-chart-line" style="margin-right: 8px; color: var(--primary-blue);"></i>
                            Panier moyen
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($customers as $customer)
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <div style="width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, var(--primary-orange), #f7dc6f); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
                                        {{ strtoupper(substr($customer->name, 0, 1)) }}
                                    </div>
                                    <div>
                                        <strong>{{ $customer->name }}</strong>
                                        <br>
                                        <small style="color: var(--text-light);">Client</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span style="color: var(--primary-blue);">{{ $customer->email }}</span>
                            </td>
                            <td>
                                <span style="color: var(--primary-green);">{{ $customer->phone ?? 'N/A' }}</span>
                            </td>
                            <td>
                                <div class="status-badge info" style="font-size: 11px;">
                                    <i class="fas fa-shopping-cart"></i>
                                    {{ $customer->orders_count }} commandes
                                </div>
                            </td>
                            <td>
                                <strong style="color: var(--primary-green); font-size: 16px;">
                                    {{ number_format($customer->orders_sum_total_amount, 0, ',', ' ') }} FCFA
                                </strong>
                            </td>
                            <td>
                                <strong style="color: var(--primary-blue); font-size: 14px;">
                                    {{ number_format($customer->orders_count > 0 ? $customer->orders_sum_total_amount / $customer->orders_count : 0, 0, ',', ' ') }} FCFA
                                </strong>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="text-center" style="padding: 40px; color: var(--text-light);">
                                <i class="fas fa-users" style="font-size: 48px; margin-bottom: 15px; opacity: 0.3;"></i>
                                <br>
                                Aucun client pour cette période
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="mt-4" style="padding: 20px;">
            {{ $customers->links() }}
        </div>
    </div>

    <!-- Bouton de retour -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="text-center">
                <a href="{{ route('accountant.reports.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left"></i>
                    Retour aux rapports
                </a>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Configuration des données pour les graphiques
    const customersData = {
        totalCustomers: {{ $customers->total() }},
        totalOrders: {{ $customers->sum('orders_count') }},
        totalRevenue: {{ $customers->sum('orders_sum_total_amount') }},
        averageOrderValue: {{ $customers->count() > 0 ? round($customers->sum('orders_sum_total_amount') / $customers->sum('orders_count'), 0) : 0 }}
    };

    // Animation des compteurs
    function animateCounter(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);

        function updateCounter() {
            start += increment;
            if (start < target) {
                element.textContent = Math.floor(start).toLocaleString('fr-FR');
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target.toLocaleString('fr-FR');
            }
        }

        updateCounter();
    }

    // Démarrer les animations au chargement
    document.addEventListener('DOMContentLoaded', function() {
        // Animer les métriques principales
        const metrics = document.querySelectorAll('.metric-content h4');
        metrics.forEach((metric, index) => {
            const value = parseInt(metric.textContent.replace(/[^\d]/g, ''));
            if (value > 0) {
                setTimeout(() => {
                    animateCounter(metric, value);
                }, index * 200);
            }
        });

        // Effet de survol sur les lignes du tableau
        const tableRows = document.querySelectorAll('.table-enhanced tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.02)';
                this.style.transition = 'all 0.3s ease';
            });

            row.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // Tooltip pour les métriques
        const statusBadges = document.querySelectorAll('.status-badge');
        statusBadges.forEach(badge => {
            badge.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.1)';
                this.style.transition = 'all 0.3s ease';
            });

            badge.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
    });

    // Fonction pour exporter les données (optionnel)
    function exportCustomersData() {
        const data = {
            period: '{{ $period }}',
            totalCustomers: customersData.totalCustomers,
            totalOrders: customersData.totalOrders,
            totalRevenue: customersData.totalRevenue,
            averageOrderValue: customersData.averageOrderValue,
            exportDate: new Date().toISOString()
        };

        console.log('Données clients:', data);
        // Ici vous pourriez ajouter la logique d'export
    }
</script>
@endpush
