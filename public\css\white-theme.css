/* Styles pour supprimer le fond bleu et appliquer un fond blanc - Version renforcée */

/* Sélecteur universel pour supprimer tous les dégradés bleus */
* {
    background-image: none !important;
}

/* Ciblage spécifique du body et de ses pseudo-éléments */
html, body, body::before, body::after {
    background: white !important;
    background-color: white !important;
    background-image: none !important;
    background-attachment: unset !important;
    background-position: unset !important;
    background-repeat: unset !important;
    background-size: unset !important;
}

/* Ciblage spécifique de la carte principale */
.main-card, .main-card::before, .main-card::after {
    background: white !important;
    background-color: white !important;
    background-image: none !important;
    color: #333 !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
    animation: none !important;
}

/* Désactiver toutes les animations de dégradé */
@keyframes cardGlow {
    0%, 50%, 100% {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
        background: white !important;
        background-image: none !important;
    }
}

/* Styles pour adapter les textes au fond blanc */
.main-card h1, .main-card .text-white, .main-card .text-light {
    color: #1976D2 !important;
}

.main-card p, .main-card .text-white-50, .main-card .text-light-50 {
    color: #666 !important;
}

/* Styles pour s'assurer que les badges sont visibles */
.badge {
    display: inline-flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Cibler spécifiquement les éléments avec le dégradé bleu problématique */
[style*="linear-gradient"][style*="rgb(30, 136, 229)"][style*="rgb(13, 71, 161)"],
[style*="background-image: linear-gradient"][style*="rgb(30, 136, 229)"][style*="rgb(13, 71, 161)"] {
    background: white !important;
    background-image: none !important;
}

/* Cibler les sections avec des filtres */
.filters-section {
    background: white !important;
    background-image: none !important;
    color: #333 !important;
}

.filters-section h5, .filters-section h6, .filters-section p {
    color: #1976D2 !important;
}

/* Assurer que les styles des colonnes Type et Validation restent intacts */
.sales-table th:nth-child(6),
.sales-table th:nth-child(8) {
    background-color: #1976D2 !important;
    color: white !important;
}

.sales-table td:nth-child(6),
.sales-table td:nth-child(8),
.type-column,
.validation-column {
    background-color: rgba(25, 118, 210, 0.1) !important;
}
