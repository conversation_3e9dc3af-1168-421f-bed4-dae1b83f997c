@extends('layouts.cashier')

@section('styles')
<style>
    .detail-card { transition: all 0.3s ease; }
    .detail-card:hover { transform: translateY(-5px); }
    .amount-card {
        border-radius: 10px;
        padding: 15px;
    }
    .amount-card h6 {
        color: #4e73df;
        margin-bottom: 5px;
    }
    .amount-card .amount {
        font-size: 1.5rem;
        font-weight: bold;
    }
    .status-badge {
        font-size: 0.8rem;
        padding: 0.5em 1em;
    }
    .status-pending { background-color: #f6c23e; }
    .status-validated { background-color: #1cc88a; }
    .status-rejected { background-color: #e74a3b; }
    .progress {
        background-color: #eaecf4;
        border-radius: 0.35rem;
        height: 1rem;
    }
    .progress-bar {
        border-radius: 0.35rem;
    }
    .bg-opacity-10 {
        --bs-bg-opacity: 0.1;
    }
    .client-info {
        padding: 10px;
        border-radius: 5px;
        background-color: rgba(78, 115, 223, 0.05);
        margin-bottom: 15px;
    }
    .detail-card.disabled {
        opacity: 0.6;
        pointer-events: none;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- En-tête -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3">
                <i class="fas fa-file-invoice me-2"></i>
                Bon #{{ $order->reference }}
            </h1>
            <p class="text-muted mb-0">
                <i class="fas fa-calendar me-1"></i>
                {{ $order->created_at->format('d/m/Y H:i') }}
            </p>
        </div>
    </div>

    <div class="row">
        <!-- Détails de la commande -->
        <div class="col-md-8">
            @foreach($order->details as $detail)
                @if($detail->tripAssignments->isNotEmpty())
                <div class="card shadow mb-4 detail-card">
                    <div class="card-header py-3">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h5 class="mb-1 font-weight-bold text-primary">
                                    <i class="fas fa-box me-2"></i>
                                    {{ $order->product->name ?? 'Produit non défini' }}
                                </h5>
                                <!-- Informations du client -->
                                <div class="client-info">
                                    <div class="mb-2">
                                        <span class="text-primary">
                                            <i class="fas fa-user me-1"></i>
                                            <strong>Client(s):</strong>
                                            @foreach($order->customers as $customer)
                                                {{ $customer->name }}@if(!$loop->last), @endif
                                            @endforeach
                                        </span>
                                    </div>
                                    <div class="d-flex flex-wrap">
                                        @if($order->customers->first()->phone)
                                            <span class="text-muted me-3">
                                                <i class="fas fa-phone me-1"></i>
                                                {{ $order->customers->first()->phone }}
                                            </span>
                                        @endif
                                        @if($order->customers->first()->email)
                                            <span class="text-muted">
                                                <i class="fas fa-envelope me-1"></i>
                                                {{ $order->customers->first()->email }}
                                            </span>
                                        @endif
                                    </div>
                                </div>
                                <!-- Badges -->
                                <div>
                                    <span class="badge bg-info me-2">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        {{ $detail->city->name }}
                                    </span>
                                    @php
                                        $convertedCount = $detail->tripAssignments->where('status', 'converted_to_sale')->count();
                                        $totalCount = $detail->tripAssignments->count();
                                    @endphp
                                    @if($convertedCount === $totalCount)
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>
                                            Entièrement converti
                                        </span>
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ $convertedCount }}/{{ $totalCount }} convertis
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Informations du produit -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card amount-card bg-light">
                                    <h6>
                                        <i class="fas fa-weight me-1"></i>
                                        Tonnage total
                                    </h6>
                                    <div class="amount">
                                        {{ number_format($detail->total_tonnage, 2) }} T
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card amount-card bg-light">
                                    <h6>
                                        <i class="fas fa-truck me-1"></i>
                                        Capacité camion
                                    </h6>
                                    <div class="amount">
                                        {{ number_format($detail->truck_capacity, 2) }} T
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card amount-card bg-light">
                                    <h6>
                                        <i class="fas fa-exchange-alt me-1"></i>
                                        Nombre de voyages
                                    </h6>
                                    <div class="amount">
                                        {{ $detail->trips_count }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Liste des affectations -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>
                                            <i class="fas fa-hashtag me-1"></i>
                                            N° Voyage
                                        </th>
                                        <th>
                                            <i class="fas fa-truck me-1"></i>
                                            Véhicule
                                        </th>
                                        <th>
                                            <i class="fas fa-user me-1"></i>
                                            Chauffeur
                                        </th>
                                        <th>
                                            <i class="fas fa-clock me-1"></i>
                                            Statut
                                        </th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($detail->tripAssignments as $assignment)
                                        @php
                                            $attrs = $assignment->getAttributes();
                                        @endphp
                                        <tr>
                                            <td>V{{ str_pad($attrs['trip_number'] ?? 0, 3, '0', STR_PAD_LEFT) }}</td>
                                            <td>{{ $attrs['truck_number'] ?? 'Non assigné' }}</td>
                                            <td>{{ isset($attrs['driver_first_name'], $attrs['driver_last_name']) ? 
                                                    $attrs['driver_first_name'] . ' ' . $attrs['driver_last_name'] : 
                                                    'Non assigné' }}</td>
                                            <td>
                                                @if($assignment->status === 'pending')
                                                    <span class="badge bg-warning">En attente</span>
                                                @elseif($assignment->status === 'completed')
                                                    <span class="badge bg-success">Terminé</span>
                                                @elseif($assignment->status === 'processing')
                                                    <span class="badge bg-info">En cours</span>
                                                @elseif($assignment->status === 'converted_to_sale')
                                                    <span class="badge bg-success">Converti en vente</span>
                                                @elseif($assignment->status === 'cancelled')
                                                    <span class="badge bg-danger">Annulé</span>
                                                @else
                                                    <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $assignment->status)) }}</span>
                                                @endif
                                            </td>
                                            <td class="text-end">
                                                @if($assignment->status !== 'converted_to_sale')
                                                    <button type="button"
                                                            class="btn btn-primary btn-sm convert-btn"
                                                            data-assignment-id="{{ $assignment->id }}"
                                                            data-detail-id="{{ $detail->id }}">
                                                        <i class="fas fa-exchange-alt me-1"></i>
                                                        Convertir
                                                    </button>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Résumé des conversions -->
                        <div class="card bg-light mt-4">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="text-center p-3">
                                            <h6 class="mb-1">Total des voyages</h6>
                                            <div class="h4 mb-0">{{ $totalCount }}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center p-3">
                                            <h6 class="mb-1">Voyages convertis</h6>
                                            <div class="h4 mb-0">{{ $convertedCount }}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center p-3">
                                            <h6 class="mb-1">Voyages en attente</h6>
                                            <div class="h4 mb-0">{{ $totalCount - $convertedCount }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            @endforeach
        </div>

        <!-- Sidebar -->
        <div class="col-md-4">
            <!-- Informations de la commande -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        Informations commande
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-muted mb-1">Date de création</h6>
                        <p class="mb-0">
                            <i class="fas fa-calendar me-1"></i>
                            {{ $order->created_at->format('d/m/Y H:i') }}
                        </p>
                    </div>
                    <div class="mb-3">
                        <h6 class="text-muted mb-1">Statut</h6>
                        <p class="mb-0">
                            @if($order->validated_at)
                                @if($order->status === 'validated')
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>
                                        Validé
                                    </span>
                                @else
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times me-1"></i>
                                        Rejeté
                                    </span>
                                @endif
                            @else
                                <span class="badge bg-warning">
                                    <i class="fas fa-clock me-1"></i>
                                    En attente
                                </span>
                            @endif
                        </p>
                    </div>
                    @if($order->validated_at)
                        <div class="mb-3">
                            <h6 class="text-muted mb-1">Date de validation</h6>
                            <p class="mb-0">
                                <i class="fas fa-calendar-check me-1"></i>
                                {{ $order->validated_at->format('d/m/Y H:i') }}
                            </p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Récapitulatif financier -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calculator me-2"></i>
                        Récapitulatif financier
                    </h6>
                </div>
                <div class="card-body">
                    <!-- Montant total -->
                    <div class="mb-3">
                        <div class="card amount-card bg-primary bg-opacity-10">
                            <h6 class="text-primary">Montant total</h6>
                            <div class="amount">
                                {{ number_format($order->total_amount, 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                    </div>

                    <!-- Montant payé -->
                    <div class="mb-3">
                        <div class="card amount-card bg-success bg-opacity-10">
                            <h6 class="text-success">Montant payé</h6>
                            <div class="amount text-success">
                                {{ number_format($order->paid_amount, 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                    </div>

                    <!-- Reste à payer -->
                    <div class="mb-3">
                        <div class="card amount-card {{ $order->remaining_amount > 0 ? 'bg-warning' : 'bg-success' }} bg-opacity-10">
                            <h6 class="{{ $order->remaining_amount > 0 ? 'text-warning' : 'text-success' }}">
                                Reste à payer
                            </h6>
                            <div class="amount {{ $order->remaining_amount > 0 ? 'text-warning' : 'text-success' }}">
                                {{ number_format($order->remaining_amount, 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                    </div>

                    <!-- Barre de progression -->
                    <div class="mt-4">
                        <div class="d-flex justify-content-between mb-1">
                            <span>Progression du paiement</span>
                            <span>
                                @php
                                    $percentage = $order->total_amount > 0 
                                        ? number_format(($order->paid_amount / $order->total_amount) * 100, 0) 
                                        : 0;
                                @endphp
                                {{ $percentage }}%
                            </span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" 
                                 role="progressbar" 
                                 style="width: {{ $percentage }}%" 
                                 aria-valuenow="{{ $percentage }}" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Gestionnaire pour les boutons de conversion
        document.querySelectorAll('.convert-btn').forEach(button => {
            button.addEventListener('click', async function() {
                const assignmentId = this.dataset.assignmentId;
                const button = this;
                const card = button.closest('.detail-card');
                
                try {
                    // Désactiver le bouton et la carte pendant la conversion
                    button.disabled = true;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Conversion...';
                    card.classList.add('disabled');
                    
                    // Appel AJAX pour convertir l'affectation
                    const response = await fetch(`/cashier/cement-orders/convert-assignment/${assignmentId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                            'Accept': 'application/json'
                        }
                    });

                    const data = await response.json();

                    if (data.success) {
                        // Recharger la page pour afficher les changements et le message flash
                        window.location.reload();
                    } else {
                        throw new Error(data.message || 'Une erreur est survenue lors de la conversion.');
                    }
                } catch (error) {
                    console.error('Erreur:', error);
                    // Réactiver le bouton et la carte
                    button.disabled = false;
                    button.innerHTML = '<i class="fas fa-exchange-alt me-1"></i> Convertir';
                    card.classList.remove('disabled');
                    
                    // Afficher l'erreur
                    alert(error.message || 'Une erreur est survenue lors de la conversion.');
                }
            });
        });
    });
</script>
@endpush
