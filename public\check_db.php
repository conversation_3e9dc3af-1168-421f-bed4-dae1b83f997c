<?php

try {
    // Connexion à la base de données
    $pdo = new PDO(
        'mysql:host=127.0.0.1;dbname=gradis_db;charset=utf8mb4',
        'root',
        ''
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connexion réussie à la base de données gradis_db<br>";
    
    // Vérifier si la table existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'categories'");
    $tableExists = $stmt->rowCount() > 0;
    
    echo "La table categories existe : " . ($tableExists ? 'oui' : 'non') . "<br>";
    
    if ($tableExists) {
        // Afficher la structure de la table
        $stmt = $pdo->query("DESCRIBE categories");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Structure de la table categories :<br>";
        echo "<pre>";
        print_r($columns);
        echo "</pre>";
        
        // Afficher les données
        $stmt = $pdo->query("SELECT * FROM categories");
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Contenu de la table categories :<br>";
        echo "<pre>";
        print_r($categories);
        echo "</pre>";
    }
    
} catch (PDOException $e) {
    echo "Erreur : " . $e->getMessage();
}
