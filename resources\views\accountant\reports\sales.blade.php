@extends('layouts.accountant')

@section('title', 'Rapport des ventes')

@push('styles')
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="{{ asset('css/reports-enhanced.css') }}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<!-- Styles spécifiques pour surcharger les conflits CSS -->
<style>
    /* SURCHARGE FORCÉE DES STYLES CONFLICTUELS POUR LA PAGE SALES */

    /* Variables CSS pour la cohérence avec la page principale */
    :root {
        --primary-blue: #2563eb;
        --primary-red: #dc2626;
        --primary-green: #059669;
        --primary-orange: #ea580c;
        --primary-purple: #7c3aed;
        --primary-indigo: #4f46e5;

        --gradient-blue: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        --gradient-red: linear-gradient(135deg, #ef4444 0%, #b91c1c 100%);
        --gradient-green: linear-gradient(135deg, #10b981 0%, #047857 100%);
        --gradient-orange: linear-gradient(135deg, #f59e0b 0%, #c2410c 100%);

        --bg-white: #ffffff;
        --text-dark: #0f172a;
        --text-medium: #475569;
        --text-light: #64748b;

        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

        --radius-lg: 16px;
        --radius-xl: 20px;
    }

    /* Fond de la page */
    body {
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
        min-height: 100vh !important;
    }

    /* Container principal */
    .sales-container {
        padding: 32px !important;
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
        min-height: 100vh !important;
    }

    /* Titre principal en blanc */
    .sales-main-title {
        font-size: 2.5rem !important;
        font-weight: 800 !important;
        color: white !important;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3) !important;
        margin-bottom: 8px !important;
        text-align: center !important;
    }

    .sales-subtitle {
        font-size: 1.1rem !important;
        color: white !important;
        font-weight: 500 !important;
        text-align: center !important;
        margin-bottom: 32px !important;
        opacity: 0.9 !important;
    }

    /* Réinitialisation des cartes pour éviter les conflits */
    .sales-container .enhanced-card,
    .sales-container .enhanced-card.green,
    .sales-container .enhanced-card.blue,
    .sales-container .enhanced-card.orange,
    .sales-container .enhanced-card.red {
        background: var(--bg-white) !important;
        border-radius: var(--radius-lg) !important;
        padding: 24px !important;
        box-shadow: var(--shadow-md) !important;
        border: 1px solid #e2e8f0 !important;
        position: relative !important;
        overflow: hidden !important;
        transition: all 0.3s ease !important;
        margin-bottom: 24px !important;

        /* Annulation des styles conflictuels */
        animation: none !important;
        opacity: 1 !important;
        display: block !important;
        flex-direction: initial !important;
        justify-content: initial !important;
        align-items: initial !important;
        height: auto !important;
        border-left: none !important;
    }

    .sales-container .enhanced-card:hover {
        transform: translateY(-4px) !important;
        box-shadow: var(--shadow-xl) !important;
    }

    /* Bordures colorées pour les cartes */
    .sales-container .enhanced-card.green::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        height: 4px !important;
        background: var(--gradient-green) !important;
        border-radius: var(--radius-lg) var(--radius-lg) 0 0 !important;
    }

    .sales-container .enhanced-card.blue::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        height: 4px !important;
        background: var(--gradient-blue) !important;
        border-radius: var(--radius-lg) var(--radius-lg) 0 0 !important;
    }

    .sales-container .enhanced-card.orange::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        height: 4px !important;
        background: var(--gradient-orange) !important;
        border-radius: var(--radius-lg) var(--radius-lg) 0 0 !important;
    }

    .sales-container .enhanced-card.red::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        height: 4px !important;
        background: var(--gradient-red) !important;
        border-radius: var(--radius-lg) var(--radius-lg) 0 0 !important;
    }

    /* Styles pour les métriques */
    .sales-container .metric-item {
        display: flex !important;
        align-items: center !important;
        gap: 16px !important;
        padding: 0 !important;
        background: transparent !important;
        border-radius: 0 !important;
        margin-bottom: 16px !important;
        transition: none !important;
    }

    .sales-container .metric-item:hover {
        transform: none !important;
        background: transparent !important;
        box-shadow: none !important;
    }

    .sales-container .metric-icon {
        width: 56px !important;
        height: 56px !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 24px !important;
        color: white !important;
        flex-shrink: 0 !important;
        transition: all 0.3s ease !important;
    }

    .sales-container .metric-icon.green { background: var(--gradient-green) !important; }
    .sales-container .metric-icon.blue { background: var(--gradient-blue) !important; }
    .sales-container .metric-icon.orange { background: var(--gradient-orange) !important; }
    .sales-container .metric-icon.red { background: var(--gradient-red) !important; }

    .sales-container .metric-content h4 {
        margin: 0 !important;
        font-size: 1.8rem !important;
        font-weight: 700 !important;
        color: var(--text-dark) !important;
        line-height: 1.2 !important;
    }

    .sales-container .metric-content p {
        margin: 0 !important;
        font-size: 14px !important;
        color: var(--text-light) !important;
        font-weight: 500 !important;
    }

    /* Styles pour les badges de statut */
    .sales-container .status-badge {
        display: inline-flex !important;
        align-items: center !important;
        gap: 6px !important;
        padding: 6px 12px !important;
        border-radius: 20px !important;
        font-size: 12px !important;
        font-weight: 600 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        transition: all 0.3s ease !important;
    }

    .sales-container .status-badge.success {
        background: var(--gradient-green) !important;
        color: white !important;
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3) !important;
    }

    .sales-container .status-badge.info {
        background: var(--gradient-blue) !important;
        color: white !important;
        box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3) !important;
    }

    .sales-container .status-badge.warning {
        background: var(--gradient-orange) !important;
        color: white !important;
        box-shadow: 0 4px 15px rgba(234, 88, 12, 0.3) !important;
    }

    .sales-container .status-badge.danger {
        background: var(--gradient-red) !important;
        color: white !important;
        box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3) !important;
    }

    .sales-container .status-badge.primary {
        background: var(--gradient-blue) !important;
        color: white !important;
        box-shadow: 0 4px 15px rgba(124, 58, 237, 0.3) !important;
    }

    /* Styles pour les tableaux */
    .sales-container .table-enhanced {
        width: 100% !important;
        border-collapse: collapse !important;
        margin-top: 16px !important;
        background: white !important;
    }

    .sales-container .table-enhanced th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
        padding: 16px !important;
        text-align: left !important;
        font-weight: 700 !important;
        color: var(--text-dark) !important;
        border-bottom: 2px solid #e2e8f0 !important;
        font-size: 14px !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }

    .sales-container .table-enhanced td {
        padding: 16px !important;
        border-bottom: 1px solid #f1f5f9 !important;
        color: var(--text-medium) !important;
        font-size: 14px !important;
        vertical-align: middle !important;
    }

    .sales-container .table-enhanced tr:hover {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 51, 234, 0.02) 100%) !important;
    }

    /* Styles pour les filtres de période */
    .sales-container .period-filters {
        display: flex !important;
        gap: 8px !important;
        margin-bottom: 0 !important;
        flex-wrap: wrap !important;
        justify-content: center !important;
    }

    .sales-container .period-filter {
        padding: 10px 20px !important;
        border-radius: 12px !important;
        text-decoration: none !important;
        font-weight: 600 !important;
        font-size: 14px !important;
        transition: all 0.3s ease !important;
        border: 2px solid #e2e8f0 !important;
        color: var(--text-medium) !important;
        background: white !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
    }

    .sales-container .period-filter.active {
        background: var(--gradient-blue) !important;
        color: white !important;
        border-color: transparent !important;
        box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3) !important;
    }

    .sales-container .period-filter:hover {
        transform: translateY(-2px) !important;
        box-shadow: var(--shadow-md) !important;
        text-decoration: none !important;
        color: var(--text-dark) !important;
    }

    .sales-container .period-filter.active:hover {
        color: white !important;
    }
</style>
@endpush

@section('content')
<div class="sales-container">
    <!-- En-tête du tableau de bord -->
    <div class="dashboard-header animate__animated animate__fadeInDown">
        <h1 class="sales-main-title">Rapport des Ventes</h1>
        <p class="sales-subtitle">Analyse détaillée des performances de vente par période</p>
    </div>

    <div class="row">
        <div class="col-12">
            <!-- En-tête avec navigation -->
            <div class="enhanced-card animate__animated animate__fadeInDown" style="margin-bottom: 32px;">
                <div class="card-header-enhanced">
                    <h2 class="card-title-enhanced">
                        <i class="fas fa-chart-line" style="color: var(--primary-blue);"></i>
                        Filtres et Navigation
                    </h2>
                    <div class="status-badge info">
                        <i class="fas fa-calendar-alt"></i>
                        Période: {{ ucfirst($period) }}
                    </div>
                </div>

                <!-- Filtres de période améliorés -->
                <div class="period-filters">
                    <a href="{{ route('accountant.reports.sales', ['period' => 'today']) }}"
                       class="period-filter {{ $period === 'today' ? 'active' : '' }}">
                        <i class="fas fa-clock"></i>
                        Aujourd'hui
                    </a>
                    <a href="{{ route('accountant.reports.sales', ['period' => 'week']) }}"
                       class="period-filter {{ $period === 'week' ? 'active' : '' }}">
                        <i class="fas fa-calendar-week"></i>
                        Cette semaine
                    </a>
                    <a href="{{ route('accountant.reports.sales', ['period' => 'month']) }}"
                       class="period-filter {{ $period === 'month' ? 'active' : '' }}">
                        <i class="fas fa-calendar-alt"></i>
                        Ce mois
                    </a>
                    <a href="{{ route('accountant.reports.sales', ['period' => 'year']) }}"
                       class="period-filter {{ $period === 'year' ? 'active' : '' }}">
                        <i class="fas fa-calendar"></i>
                        Cette année
                    </a>
                </div>
            </div>

            <!-- Métriques de résumé -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="enhanced-card green animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                        <div class="metric-item">
                            <div class="metric-icon green">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="metric-content">
                                <h4>{{ $sales->sum('count') }}</h4>
                                <p>Total des ventes</p>
                            </div>
                        </div>
                        <div class="status-badge success">
                            <i class="fas fa-arrow-up"></i>
                            Performance
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="enhanced-card blue animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                        <div class="metric-item">
                            <div class="metric-icon blue">
                                <i class="fas fa-coins"></i>
                            </div>
                            <div class="metric-content">
                                <h4>{{ number_format($sales->sum('total'), 0, ',', ' ') }} FCFA</h4>
                                <p>Chiffre d'affaires</p>
                            </div>
                        </div>
                        <div class="status-badge info">
                            <i class="fas fa-chart-line"></i>
                            Revenus
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="enhanced-card orange animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                        <div class="metric-item">
                            <div class="metric-icon orange">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <div class="metric-content">
                                <h4>{{ $sales->count() > 0 ? number_format($sales->sum('total') / $sales->sum('count'), 0, ',', ' ') : 0 }} FCFA</h4>
                                <p>Panier moyen</p>
                            </div>
                        </div>
                        <div class="status-badge warning">
                            <i class="fas fa-chart-bar"></i>
                            Moyenne
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="enhanced-card red animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                        <div class="metric-item">
                            <div class="metric-icon red">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                            <div class="metric-content">
                                <h4>{{ $sales->count() }}</h4>
                                <p>Jours d'activité</p>
                            </div>
                        </div>
                        <div class="status-badge primary">
                            <i class="fas fa-clock"></i>
                            Période
                        </div>
                    </div>
                </div>
            </div>

            <!-- Graphique des ventes -->
            <div class="enhanced-card animate__animated animate__fadeInLeft" style="margin-bottom: 32px; animation-delay: 0.5s;">
                <div class="card-header-enhanced">
                    <h3 class="card-title-enhanced">
                        <i class="fas fa-chart-area" style="color: var(--primary-blue);"></i>
                        Évolution des Ventes
                    </h3>
                    <div class="status-badge info">
                        <i class="fas fa-eye"></i>
                        Graphique interactif
                    </div>
                </div>
                <div class="chart-container" style="height: 350px; padding: 20px; background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 51, 234, 0.02) 100%); border-radius: 12px; border: 1px solid rgba(59, 130, 246, 0.1);">
                    <canvas id="salesChart"></canvas>
                </div>
            </div>

            <!-- Tableau des ventes détaillé -->
            <div class="enhanced-card animate__animated animate__fadeInRight" style="animation-delay: 0.6s;">
                <div class="card-header-enhanced">
                    <h3 class="card-title-enhanced">
                        <i class="fas fa-table" style="color: var(--primary-green);"></i>
                        Détail des Ventes par Période
                    </h3>
                    <div class="status-badge success">
                        <i class="fas fa-list"></i>
                        {{ $sales->count() }} entrées
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table-enhanced">
                        <thead>
                            <tr>
                                <th>
                                    <i class="fas fa-calendar-alt" style="margin-right: 8px; color: var(--primary-blue);"></i>
                                    Date
                                </th>
                                <th>
                                    <i class="fas fa-shopping-cart" style="margin-right: 8px; color: var(--primary-green);"></i>
                                    Nombre de ventes
                                </th>
                                <th>
                                    <i class="fas fa-coins" style="margin-right: 8px; color: var(--primary-orange);"></i>
                                    Montant total
                                </th>
                                <th>
                                    <i class="fas fa-chart-line" style="margin-right: 8px; color: var(--primary-purple);"></i>
                                    Statut
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($sales as $sale)
                                <tr>
                                    <td>
                                        <strong>{{ \Carbon\Carbon::parse($sale->date)->format('d/m/Y') }}</strong>
                                        <br>
                                        <small style="color: var(--text-light);">{{ \Carbon\Carbon::parse($sale->date)->format('l') }}</small>
                                    </td>
                                    <td>
                                        <div class="status-badge info" style="font-size: 11px;">
                                            <i class="fas fa-shopping-cart"></i>
                                            {{ $sale->count }} ventes
                                        </div>
                                    </td>
                                    <td>
                                        <strong style="color: var(--primary-green); font-size: 16px;">
                                            {{ number_format($sale->total, 0, ',', ' ') }} FCFA
                                        </strong>
                                    </td>
                                    <td>
                                        @if($sale->total > 50000)
                                            <div class="status-badge success">
                                                <i class="fas fa-arrow-up"></i>
                                                Excellent
                                            </div>
                                        @elseif($sale->total > 20000)
                                            <div class="status-badge warning">
                                                <i class="fas fa-minus"></i>
                                                Moyen
                                            </div>
                                        @else
                                            <div class="status-badge danger">
                                                <i class="fas fa-arrow-down"></i>
                                                Faible
                                            </div>
                                        @endif
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="4" class="text-center" style="padding: 40px;">
                                        <div style="color: var(--text-light);">
                                            <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 16px; display: block;"></i>
                                            <strong>Aucune vente pour cette période</strong>
                                            <br>
                                            <small>Essayez de sélectionner une autre période</small>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                        <tfoot>
                            <tr style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); font-weight: 700;">
                                <td><strong>TOTAL GÉNÉRAL</strong></td>
                                <td>
                                    <div class="status-badge success">
                                        <i class="fas fa-shopping-cart"></i>
                                        {{ $sales->sum('count') }} ventes
                                    </div>
                                </td>
                                <td>
                                    <strong style="color: var(--primary-green); font-size: 18px;">
                                        {{ number_format($sales->sum('total'), 0, ',', ' ') }} FCFA
                                    </strong>
                                </td>
                                <td>
                                    <div class="status-badge primary">
                                        <i class="fas fa-chart-line"></i>
                                        Résumé
                                    </div>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Configuration des données pour les graphiques
    const salesData = {
        labels: @json($sales->pluck('date')->map(function($date) { return \Carbon\Carbon::parse($date)->format('d/m'); })),
        counts: @json($sales->pluck('count')),
        totals: @json($sales->pluck('total')),
        totalSales: {{ $sales->sum('count') }},
        totalRevenue: {{ $sales->sum('total') }},
        averageOrder: {{ $sales->count() > 0 ? round($sales->sum('total') / $sales->sum('count'), 0) : 0 }},
        activeDays: {{ $sales->count() }}
    };

    // Graphique des ventes avec Chart.js amélioré
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('salesChart');
        if (ctx) {
            const salesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: salesData.labels,
                    datasets: [
                        {
                            label: 'Revenus (FCFA)',
                            data: salesData.totals,
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#3b82f6',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 6,
                            pointHoverRadius: 8,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Nombre de ventes',
                            data: salesData.counts,
                            borderColor: '#f59e0b',
                            backgroundColor: 'rgba(245, 158, 11, 0.1)',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.4,
                            pointBackgroundColor: '#f59e0b',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 6,
                            pointHoverRadius: 8,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20,
                                font: {
                                    size: 14,
                                    weight: '600'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            cornerRadius: 8,
                            displayColors: true,
                            callbacks: {
                                label: function(context) {
                                    if (context.datasetIndex === 0) {
                                        return 'Revenus: ' + new Intl.NumberFormat('fr-FR').format(context.parsed.y) + ' FCFA';
                                    } else {
                                        return 'Ventes: ' + context.parsed.y + ' commandes';
                                    }
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            border: {
                                display: false
                            },
                            ticks: {
                                color: '#64748b',
                                font: {
                                    size: 12,
                                    weight: '500'
                                }
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            beginAtZero: true,
                            grid: {
                                color: '#f1f5f9',
                                borderDash: [2, 2]
                            },
                            border: {
                                display: false
                            },
                            ticks: {
                                color: '#64748b',
                                font: {
                                    size: 12,
                                    weight: '500'
                                },
                                callback: function(value) {
                                    return new Intl.NumberFormat('fr-FR', {
                                        notation: 'compact',
                                        compactDisplay: 'short'
                                    }).format(value) + ' FCFA';
                                }
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            beginAtZero: true,
                            grid: {
                                drawOnChartArea: false,
                            },
                            ticks: {
                                color: '#64748b',
                                font: {
                                    size: 12,
                                    weight: '500'
                                },
                                callback: function(value) {
                                    return value + ' ventes';
                                }
                            }
                        }
                    },
                    elements: {
                        point: {
                            hoverBackgroundColor: '#ffffff'
                        }
                    }
                }
            });
        }

        // Animation des compteurs
        animateCounters();

        // Formatter les montants en FCFA
        document.querySelectorAll('.currency').forEach(element => {
            const amount = parseFloat(element.textContent.replace(/[^\d.-]/g, ''));
            element.textContent = new Intl.NumberFormat('fr-FR').format(amount) + ' FCFA';
        });
    });

    // Fonction d'animation des compteurs
    function animateCounters() {
        const counters = [
            { element: document.querySelector('.metric-content h4'), target: salesData.totalSales, suffix: '' },
            { element: document.querySelectorAll('.metric-content h4')[1], target: salesData.totalRevenue, suffix: ' FCFA', format: true },
            { element: document.querySelectorAll('.metric-content h4')[2], target: salesData.averageOrder, suffix: ' FCFA', format: true },
            { element: document.querySelectorAll('.metric-content h4')[3], target: salesData.activeDays, suffix: '' }
        ];

        counters.forEach((counter, index) => {
            if (counter.element) {
                setTimeout(() => {
                    animateCounter(counter.element, counter.target, counter.suffix, counter.format);
                }, index * 200);
            }
        });
    }

    function animateCounter(element, target, suffix = '', format = false) {
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }

            let displayValue = Math.floor(current);
            if (format) {
                displayValue = new Intl.NumberFormat('fr-FR').format(displayValue);
            }
            element.textContent = displayValue + suffix;
        }, 20);
    }
</script>
@endpush

@endsection
