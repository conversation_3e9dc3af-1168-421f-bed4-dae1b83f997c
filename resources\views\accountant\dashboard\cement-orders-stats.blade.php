<!-- Statistiques des bons de commande -->
<div class="row g-3 mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Bons de commande</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_cement_orders'] ?? ($stats['monthly_cement_orders'] ?? 0) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            En attente</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending_cement_orders'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Tonnage</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total_cement_tonnage'], 0, ',', ' ') }} T</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-weight-hanging fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Commandes du mois</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['monthly_cement_orders'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Graphiques -->
<div class="row g-3">
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex align-items-center">
                <i class="fas fa-chart-line me-1"></i>
                <h6 class="m-0 font-weight-bold text-primary">Évolution des commandes</h6>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 300px;">
                    <canvas id="ordersChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex align-items-center">
                <i class="fas fa-chart-pie me-1"></i>
                <h6 class="m-0 font-weight-bold text-primary">Tonnage par produit</h6>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 300px;">
                    <canvas id="tonnageChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Derniers bons de commande -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-file-invoice me-1"></i>
                    Derniers bons de commande
                </h6>
                <a href="{{ route('accountant.cement-orders.index') }}" class="btn btn-sm btn-primary">
                    Voir tout
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Référence</th>
                                <th>Client/Partenaire</th>
                                <th>Ville</th>
                                <th>Produit</th>
                                <th>Tonnage</th>
                                <th>Statut</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($latestCementOrders as $order)
                                <tr>
                                    <td>{{ $order->reference }}</td>
                                    <td>{{ $order->destination->name }}</td>
                                    <td>{{ $order->city?->name ?? 'N/A' }}</td>
                                    <td>{{ $order->product->name }}</td>
                                    <td>{{ number_format($order->total_tonnage, 0, ',', ' ') }} T</td>
                                    <td>
                                        @switch($order->status)
                                            @case('pending')
                                                <span class="badge bg-warning text-dark">En attente</span>
                                                @break
                                            @case('approved')
                                                <span class="badge bg-success">Approuvé</span>
                                                @break
                                            @case('rejected')
                                                <span class="badge bg-danger">Rejeté</span>
                                                @break
                                            @default
                                                <span class="badge bg-secondary">{{ $order->status }}</span>
                                        @endswitch
                                    </td>
                                    <td>{{ $order->created_at->format('d/m/Y H:i') }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="7" class="text-center">Aucun bon de commande trouvé</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configuration commune des graphiques
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    usePointStyle: true,
                    padding: 20
                }
            }
        }
    };

    // Graphique des commandes par jour
    const ordersCtx = document.getElementById('ordersChart').getContext('2d');
    new Chart(ordersCtx, {
        type: 'line',
        data: {
            labels: {!! json_encode($dailyOrders->pluck('date')->map(function($date) {
                return \Carbon\Carbon::parse($date)->format('d/m');
            })) !!},
            datasets: [{
                label: 'Nombre de commandes',
                data: {{ json_encode($dailyOrders->pluck('count')) }},
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78,115,223,0.05)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            ...chartOptions,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // Graphique des tonnages par produit
    const tonnageCtx = document.getElementById('tonnageChart').getContext('2d');
    new Chart(tonnageCtx, {
        type: 'doughnut',
        data: {
            labels: {!! json_encode($tonnageByProduct->pluck('name')) !!},
            datasets: [{
                data: {{ json_encode($tonnageByProduct->pluck('total')) }},
                backgroundColor: [
                    '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'
                ]
            }]
        },
        options: chartOptions
    });
});
</script>
@endpush
