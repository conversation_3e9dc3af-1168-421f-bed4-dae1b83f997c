@extends('layouts.cement_manager')

@section('title', 'Tableau de bord - Gestionnaire Ciment')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Tableau de bord</h1>
    </div>

    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Bons de Commande</h5>
                    <h2 class="card-text">{{ $stats['total_supplies'] }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Montant Total</h5>
                    <h2 class="card-text">{{ number_format($stats['total_amount'], 2, ',', ' ') }} FCFA</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Articles</h5>
                    <h2 class="card-text">{{ $stats['total_products'] }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des approvisionnements -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Approvisionnements Validés</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Référence</th>
                            <th>Date</th>
                            <th>Fournisseur</th>
                            <th>Articles</th>
                            <th>Montant Total</th>
                            <th>Validé par</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($supplies as $supply)
                            <tr>
                                <td>{{ $supply->reference }}</td>
                                <td>{{ $supply->created_at->format('d/m/Y') }}</td>
                                <td>{{ $supply->supplier->name }}</td>
                                <td>
                                    <ul class="list-unstyled mb-0">
                                        @foreach($supply->details as $item)
                                            <li>
                                                @if($item->product->category->name === 'Ciment')
                                                    {{ number_format($item->tonnage, 2, ',', ' ') }} T
                                                @else
                                                    {{ $item->quantity }}
                                                @endif
                                                {{ $item->product->name }}
                                            </li>
                                        @endforeach
                                    </ul>
                                </td>
                                <td>{{ number_format($supply->total_sale_amount, 2, ',', ' ') }} FCFA</td>
                                <td>
                                    @if($supply->validator)
                                        {{ $supply->validator->first_name }} {{ $supply->validator->last_name }}
                                    @else
                                        <span class="text-muted">Non spécifié</span>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('cement-manager.supplies.show', $supply->id) }}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i> Détails
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center">Aucun approvisionnement validé trouvé</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection
