class ProductForm {
    constructor() {
        this.initializeElements();
        this.initializeEventListeners();
        this.updateFormBasedOnCategory();
    }

    initializeElements() {
        // Éléments principaux
        this.form = document.getElementById('productForm');
        this.categorySelect = document.getElementById('category_id');
        this.unitSelect = document.getElementById('unit');
        this.regionalPricing = document.getElementById('regional-pricing');
        this.ironSpecifications = document.getElementById('iron-specifications');
        
        // Modal de sélection de ville
        this.cityModal = new bootstrap.Modal(document.getElementById('citySelectionModal'));
        this.citySelect = document.getElementById('citySelect');
        this.cityPrice = document.getElementById('cityPrice');
        this.confirmCityBtn = document.getElementById('confirmCitySelection');
        this.selectedRegionName = document.getElementById('selectedRegionName');
        
        // Variables de contexte
        this.currentRegionId = null;
        this.currentRegionName = null;
        this.cities = {};
    }

    initializeEventListeners() {
        // Événements principaux
        this.categorySelect.addEventListener('change', () => this.updateFormBasedOnCategory());
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));

        // Événements pour le fer
        if (document.getElementById('diameter')) {
            document.getElementById('diameter').addEventListener('change', () => this.updateIronCalculations());
        }
        if (document.getElementById('length')) {
            document.getElementById('length').addEventListener('input', () => this.updateIronCalculations());
        }
        if (document.getElementById('unit_price')) {
            document.getElementById('unit_price').addEventListener('input', () => this.updateIronCalculations());
        }

        // Événements pour le ciment
        document.querySelectorAll('.add-city-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.handleAddCity(e));
        });
        this.confirmCityBtn.addEventListener('click', () => this.handleConfirmCity());

        // Délégation d'événements pour les boutons de suppression de ville
        document.addEventListener('click', (e) => {
            if (e.target.closest('.remove-city')) {
                const card = e.target.closest('.city-price-card');
                if (card) {
                    card.remove();
                    this.updateCityCount();
                }
            }
        });

        // Réinitialiser le modal quand il est fermé
        document.getElementById('citySelectionModal').addEventListener('hidden.bs.modal', () => {
            this.citySelect.value = '';
            this.cityPrice.value = '';
        });
    }

    updateFormBasedOnCategory() {
        const categoryName = this.getSelectedCategoryName().toLowerCase();
        
        // Mise à jour de l'affichage des sections
        this.regionalPricing.style.display = categoryName === 'ciment' ? 'block' : 'none';
        this.ironSpecifications.style.display = categoryName === 'fer' ? 'block' : 'none';
        
        // Mise à jour des unités disponibles
        this.updateUnitOptions(categoryName);
    }

    getSelectedCategoryName() {
        const selectedOption = this.categorySelect.options[this.categorySelect.selectedIndex];
        return selectedOption ? selectedOption.textContent.trim() : '';
    }

    updateUnitOptions(categoryName) {
        const unitOptions = {
            'ciment': ['Tonne', 'Kg'],
            'fer': ['Barre'],
            'fil de fer': ['Kg'],
            'default': ['Unité', 'Pièce', 'Mètre cube', 'Mètre carré']
        };

        const options = unitOptions[categoryName] || unitOptions.default;
        
        // Sauvegarder l'unité sélectionnée actuelle
        const currentValue = this.unitSelect.value;
        
        // Vider et remplir le select
        this.unitSelect.innerHTML = '<option value="">Sélectionnez une unité</option>';
        options.forEach(unit => {
            const option = new Option(unit, unit);
            this.unitSelect.add(option);
        });

        // Restaurer la valeur si elle existe dans les nouvelles options
        if (options.includes(currentValue)) {
            this.unitSelect.value = currentValue;
        }
    }

    // Gestion des villes pour le ciment
    handleAddCity(e) {
        const btn = e.target.closest('.add-city-btn');
        this.currentRegionId = btn.dataset.regionId;
        this.currentRegionName = btn.dataset.regionName;
        
        // Mettre à jour le titre du modal avec le nom de la région
        this.selectedRegionName.textContent = this.currentRegionName;
        
        // Charger les villes de la région
        this.loadCities(this.currentRegionId);
    }

    async loadCities(regionId) {
        try {
            const response = await fetch(`/api/regions/${regionId}/cities`);
            const cities = await response.json();
            
            // Filtrer les villes déjà sélectionnées
            const selectedCities = new Set(Array.from(document.querySelectorAll('.city-price-card'))
                .map(card => card.dataset.cityId));
            
            // Mettre à jour le select des villes
            this.citySelect.innerHTML = '<option value="">Choisissez une ville</option>';
            cities.forEach(city => {
                if (!selectedCities.has(city.id.toString())) {
                    const option = new Option(city.name, city.id);
                    this.citySelect.add(option);
                }
            });
            
            this.cityModal.show();
        } catch (error) {
            console.error('Erreur lors du chargement des villes:', error);
            alert('Une erreur est survenue lors du chargement des villes. Veuillez réessayer.');
        }
    }

    handleConfirmCity() {
        const cityId = this.citySelect.value;
        const cityName = this.citySelect.options[this.citySelect.selectedIndex].text;
        const price = this.cityPrice.value;
        
        if (!cityId) {
            alert('Veuillez sélectionner une ville');
            return;
        }
        
        if (!price || price <= 0) {
            alert('Veuillez entrer un prix valide');
            return;
        }
        
        const container = document.querySelector(`.selected-cities[data-region="${this.currentRegionName.toLowerCase()}"]`);
        
        const cityCard = document.createElement('div');
        cityCard.className = 'city-price-card';
        cityCard.dataset.cityId = cityId;
        cityCard.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">${cityName}</h6>
                <button type="button" class="btn btn-danger btn-sm remove-city">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="input-group">
                <input type="number" 
                       class="form-control" 
                       name="cities[${cityId}][price]" 
                       value="${price}"
                       required>
                <span class="input-group-text">FCFA</span>
            </div>
        `;
        
        container.appendChild(cityCard);
        this.updateCityCount();
        this.cityModal.hide();
    }

    updateCityCount() {
        document.querySelectorAll('.accordion-item').forEach(item => {
            const citiesContainer = item.querySelector('.selected-cities');
            const countDisplay = item.querySelector('.selected-cities-count');
            const count = citiesContainer.querySelectorAll('.city-price-card').length;
            countDisplay.textContent = `${count} ville(s)`;
            
            // Mise à jour du statut
            const status = item.querySelector('.region-status');
            status.className = `region-status ${count > 0 ? 'status-complete' : 'status-incomplete'} me-2`;
        });
    }

    // Calculs pour le fer
    updateIronCalculations() {
        const diameter = parseFloat(document.getElementById('diameter').value) || 0;
        const length = parseFloat(document.getElementById('length').value) || 0;
        const unitPrice = parseFloat(document.getElementById('unit_price').value) || 0;

        // Valeurs prédéfinies pour les unités par tonne selon le diamètre
        const unitsPerTonMap = {
            6: 750,
            8: 422,
            10: 270,
            12: 188,
            14: 138,
            16: 106
        };

        const unitsPerTon = unitsPerTonMap[diameter] || 0;
        const weightPerUnit = unitsPerTon > 0 ? (1000 / unitsPerTon) : 0;
        const tonPrice = unitPrice * unitsPerTon;

        // Mise à jour des affichages
        document.getElementById('units-per-ton').textContent = `${unitsPerTon} unités`;
        document.getElementById('weight-per-unit').textContent = `${weightPerUnit.toFixed(2)} kg`;
        document.getElementById('ton-price').textContent = `${tonPrice.toLocaleString('fr-FR')} FCFA`;

        // Mise à jour des champs cachés
        document.getElementById('units_per_ton_input').value = unitsPerTon;
        document.getElementById('weight_per_unit_input').value = weightPerUnit;
        document.getElementById('ton_price_input').value = tonPrice;
    }

    // Gestion de la soumission du formulaire
    handleSubmit(e) {
        const categoryName = this.getSelectedCategoryName().toLowerCase();
        const isCement = categoryName === 'ciment';
        const isIron = categoryName === 'fer';

        // Validation spécifique selon la catégorie
        if (isCement) {
            const hasSelectedCities = document.querySelectorAll('.city-price-card').length > 0;
            if (!hasSelectedCities) {
                e.preventDefault();
                alert('Veuillez sélectionner au moins une ville pour ce produit.');
                return;
            }

            // Vérifier que tous les prix sont valides
            const priceInputs = document.querySelectorAll('input[name^="cities["][name$="][price]"]');
            for (const input of priceInputs) {
                const price = parseFloat(input.value);
                if (!price || price <= 0) {
                    e.preventDefault();
                    alert('Veuillez entrer des prix valides pour toutes les villes.');
                    return;
                }
            }
        } else if (isIron) {
            const requiredFields = ['diameter', 'length', 'unit_price'];
            for (const field of requiredFields) {
                const input = document.getElementById(field);
                if (!input || !input.value) {
                    e.preventDefault();
                    alert(`Veuillez remplir tous les champs requis pour le fer.`);
                    return;
                }
            }
        }
    }
}
