<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

<!-- Bootstrap Bundle with <PERSON><PERSON> -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<!-- Gestion du menu latéral -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const content = document.getElementById('content');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                content.classList.toggle('expanded');
            });
        }
    });

    // Gestion des dropdowns
    document.addEventListener('click', function(event) {
        const dropdowns = document.querySelectorAll('.dropdown-menu.show');
        if (dropdowns.length > 0) {
            const clickedElement = event.target;
            const isDropdownButton = clickedElement.matches('[data-bs-toggle="dropdown"]');
            const isInsideDropdown = clickedElement.closest('.dropdown-menu');

            if (!isDropdownButton && !isInsideDropdown) {
                dropdowns.forEach(openDropdown => {
                    openDropdown.classList.remove('show');
                });
            }
        }
    });
</script>

@stack('scripts')
