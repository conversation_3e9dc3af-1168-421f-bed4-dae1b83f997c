@extends('layouts.accountant')

@section('title', 'Gestion des commandes')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Onglets de navigation -->
            <ul class="nav nav-tabs mb-4">
                <li class="nav-item">
                    <a class="nav-link active" id="orders-tab" data-bs-toggle="tab" href="#orders">
                        Commandes générales
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="cement-orders-tab" data-bs-toggle="tab" href="#cement-orders">
                        Bons de commande ciment
                    </a>
                </li>
            </ul>

            <!-- Contenu des onglets -->
            <div class="tab-content">
                <!-- Onglet des commandes générales -->
                <div class="tab-pane fade show active" id="orders">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h4 class="card-title mb-0">Liste des commandes</h4>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-centered table-hover">
                                    <thead>
                                        <tr>
                                            <th>N° Commande</th>
                                            <th>Client</th>
                                            <th>Date</th>
                                            <th>Montant</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($orders as $order)
                                            <tr>
                                                <td>{{ $order->reference }}</td>
                                                <td>{{ $order->user->name }}</td>
                                                <td>{{ $order->created_at->format('d/m/Y H:i') }}</td>
                                                <td class="currency">{{ number_format($order->total_amount, 0, ',', ' ') }}</td>
                                                <td>
                                                    @if($order->status === 'pending')
                                                        <span class="badge bg-warning">En attente</span>
                                                    @elseif($order->status === 'completed')
                                                        <span class="badge bg-success">Terminée</span>
                                                    @elseif($order->status === 'cancelled')
                                                        <span class="badge bg-danger">Annulée</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#orderModal{{ $order->id }}">
                                                        <i data-feather="eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="6" class="text-center">Aucune commande trouvée</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>

                            <div class="mt-4">
                                {{ $orders->links() }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Onglet des bons de commande ciment -->
                <div class="tab-pane fade" id="cement-orders">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h4 class="card-title mb-0">Bons de commande ciment</h4>
                                <a href="{{ route('accountant.cement-orders.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Nouveau bon de commande
                                </a>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-centered table-hover" id="cementOrdersTable">
                                    <thead>
                                        <tr>
                                            <th>Référence</th>
                                            <th>Date</th>
                                            <th>Client</th>
                                            <th>Ville</th>
                                            <th>Quantité</th>
                                            <th>Statut</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($cementOrders ?? [] as $order)
                                            <tr>
                                                <td>{{ $order->reference }}</td>
                                                <td>{{ $order->created_at->format('d/m/Y') }}</td>
                                                <td>{{ $order->customer?->name ?? 'N/A' }}</td>
                                                <td>{{ $order->city?->name ?? 'N/A' }}</td>
                                                <td>{{ number_format($order->quantity, 2) }} T</td>
                                                <td>
                                                    @if($order->status === 'pending')
                                                        <span class="badge bg-warning">En attente</span>
                                                    @elseif($order->status === 'approved')
                                                        <span class="badge bg-success">Approuvé</span>
                                                    @else
                                                        <span class="badge bg-danger">Rejeté</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <a href="{{ route('accountant.cement-orders.show', $order) }}" 
                                                       class="btn btn-sm btn-info">
                                                        <i data-feather="eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="7" class="text-center">Aucun bon de commande trouvé</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@foreach($orders as $order)
    <!-- Modal pour les détails de la commande -->
    <div class="modal fade" id="orderModal{{ $order->id }}" tabindex="-1" aria-labelledby="orderModalLabel{{ $order->id }}" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="orderModalLabel{{ $order->id }}">Détails de la commande #{{ $order->reference }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>Informations client</h6>
                            <p class="mb-1">Nom: {{ $order->user->name }}</p>
                            <p class="mb-1">Email: {{ $order->user->email }}</p>
                            <p class="mb-1">Téléphone: {{ $order->user->phone }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Informations commande</h6>
                            <p class="mb-1">Date: {{ $order->created_at->format('d/m/Y H:i') }}</p>
                            <p class="mb-1">Status: 
                                @if($order->status === 'pending')
                                    <span class="badge bg-warning">En attente</span>
                                @elseif($order->status === 'completed')
                                    <span class="badge bg-success">Terminée</span>
                                @elseif($order->status === 'cancelled')
                                    <span class="badge bg-danger">Annulée</span>
                                @endif
                            </p>
                        </div>
                    </div>

                    <h6>Produits commandés</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Quantité</th>
                                    <th>Prix unitaire</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($order->items as $item)
                                    <tr>
                                        <td>{{ $item->product->name }}</td>
                                        <td>{{ $item->quantity }}</td>
                                        <td class="currency">{{ number_format($item->unit_price, 0, ',', ' ') }}</td>
                                        <td class="currency">{{ number_format($item->total_price, 0, ',', ' ') }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="3" class="text-end"><strong>Total</strong></td>
                                    <td class="currency"><strong>{{ number_format($order->total_amount, 0, ',', ' ') }}</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>
@endforeach

@push('scripts')
<script>
    // Initialiser les icônes Feather
    feather.replace();

    // Formatter les montants en FCFA
    document.querySelectorAll('.currency').forEach(element => {
        const amount = parseFloat(element.textContent.replace(/[^\d.-]/g, ''));
        element.textContent = new Intl.NumberFormat('fr-FR').format(amount) + ' FCFA';
    });

    $(document).ready(function() {
        // Initialiser DataTables pour les deux tableaux
        $('.table').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
            }
        });

        // Gérer le changement d'onglet
        $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
            // Réajuster les colonnes de DataTables
            $.fn.dataTable.tables({ visible: true, api: true }).columns.adjust();
        });
    });
</script>
@endpush
