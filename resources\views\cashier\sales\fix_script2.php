<?php
// Script pour corriger l'erreur de syntaxe dans show.blade.php
$filePath = 'c:/xampp/htdocs/gradis/resources/views/cashier/sales/show.blade.php';
$content = file_get_contents($filePath);

// Supprimer le @endif en trop à la ligne 620
$lines = explode("\n", $content);
if (isset($lines[619]) && trim($lines[619]) === '@endif') {
    // Supprimer cette ligne
    unset($lines[619]);
    // Reconstruire le contenu
    $newContent = implode("\n", $lines);
    // Écrire le contenu modifié dans le fichier
    file_put_contents($filePath, $newContent);
    echo "Correction appliquée avec succès! Le @endif en trop a été supprimé.\n";
} else {
    echo "Impossible de trouver la ligne exacte à corriger. Vérifiez manuellement le fichier.\n";
}
?>
