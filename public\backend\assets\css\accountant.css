/* Layout Structure */
body {
    margin: 0;
    font-family: var(--bs-body-font-family);
    font-size: var(--bs-body-font-size);
    font-weight: var(--bs-body-font-weight);
    line-height: var(--bs-body-line-height);
    color: var(--bs-body-color);
    text-align: var(--bs-body-text-align);
    background-color: var(--bs-body-bg);
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}

.sb-nav-fixed {
    padding-top: 56px;
}

.sb-nav-fixed #layoutSidenav #layoutSidenav_nav {
    width: 225px;
    height: 100vh;
    z-index: 1038;
    position: fixed;
    left: 0;
    top: 56px;
}

.sb-nav-fixed #layoutSidenav #layoutSidenav_content {
    padding-left: 225px;
    margin-top: 0;
    top: 56px;
}

/* Navigation */
.sb-topnav {
    padding-left: 0;
    height: 56px;
    z-index: 1039;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
}

.sb-topnav .navbar-brand {
    width: 225px;
    margin: 0;
}

/* Sidenav */
#layoutSidenav {
    display: flex;
}

#layoutSidenav #layoutSidenav_nav {
    flex-basis: 225px;
    flex-shrink: 0;
}

#layoutSidenav #layoutSidenav_content {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    flex-grow: 1;
    min-height: calc(100vh - 56px);
}

.sb-sidenav {
    display: flex;
    flex-direction: column;
    height: 100%;
    flex-wrap: nowrap;
}

.sb-sidenav .sb-sidenav-menu {
    flex-grow: 1;
    padding-top: 0;
}

.sb-sidenav .sb-sidenav-menu .nav {
    flex-direction: column;
    flex-wrap: nowrap;
}

.sb-sidenav .sb-sidenav-menu .nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    position: relative;
}

.sb-sidenav .sb-sidenav-menu .nav-link .sb-nav-link-icon {
    font-size: 0.9rem;
    padding-right: 0.5rem;
}

.sb-sidenav .sb-sidenav-menu .sb-sidenav-menu-heading {
    padding: 1.75rem 1rem 0.75rem;
    font-size: 0.75rem;
    font-weight: bold;
    text-transform: uppercase;
}

.sb-sidenav .sb-sidenav-footer {
    padding: 0.75rem;
    flex-shrink: 0;
}

/* Dark Theme */
.sb-sidenav-dark {
    background-color: #212529;
    color: rgba(255, 255, 255, 0.5);
}

.sb-sidenav-dark .sb-sidenav-menu .nav-link {
    color: rgba(255, 255, 255, 0.5);
}

.sb-sidenav-dark .sb-sidenav-menu .nav-link:hover {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
}

.sb-sidenav-dark .sb-sidenav-menu .nav-link.active {
    color: #fff;
    background: rgba(255, 255, 255, 0.2);
}

.sb-sidenav-dark .sb-sidenav-menu .sb-sidenav-menu-heading {
    color: rgba(255, 255, 255, 0.25);
}

.sb-sidenav-dark .sb-sidenav-footer {
    background-color: #343a40;
}

/* Dashboard Cards */
.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(33, 40, 50, 0.15);
    margin-bottom: 1.5rem;
}

.card .card-header {
    font-weight: 500;
    padding: 1rem 1.35rem;
    margin-bottom: 0;
    background-color: transparent;
    border-bottom: 1px solid rgba(33, 40, 50, 0.125);
}

.stats-card {
    transition: transform 0.2s ease-in-out;
}

.stats-card:hover {
    transform: translateY(-0.25rem);
}

/* Tables */
.table > :not(caption) > * > * {
    padding: 0.75rem 1.25rem;
}

.table thead th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.1em;
    background-color: #f8f9fa;
}

.table tbody tr:hover {
    background-color: rgba(33, 40, 50, 0.02);
}

/* Badges */
.badge {
    padding: 0.5em 1em;
    font-weight: 500;
    letter-spacing: 0.05em;
}

/* Responsive */
@media (max-width: 768px) {
    .sb-nav-fixed #layoutSidenav #layoutSidenav_nav {
        transform: translateX(-225px);
    }
    
    .sb-nav-fixed #layoutSidenav #layoutSidenav_content {
        padding-left: 0;
    }
    
    .sb-sidenav-toggled #layoutSidenav #layoutSidenav_nav {
        transform: translateX(0);
    }
    
    .sb-sidenav-toggled #layoutSidenav #layoutSidenav_content {
        padding-left: 225px;
    }
}
