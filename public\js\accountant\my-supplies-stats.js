/**
 * Script pour récupérer et afficher les statistiques des approvisionnements du comptable connecté
 * dans le tableau de bord moderne
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initialisation du script my-supplies-stats.js');
    
    // Vérifier que les éléments nécessaires sont présents dans le DOM
    // Cibler la carte avec le titre "Mes approvisionnements"
    const mySuppliesCard = document.querySelector('.stat-card.primary');
    if (!mySuppliesCard) {
        console.error('Erreur: Impossible de trouver la carte des approvisionnements (.stat-card.primary)');
        return;
    }
    
    const mySuppliesValue = mySuppliesCard.querySelector('.stat-card-value.counter');
    const mySuppliesSubtitle = mySuppliesCard.querySelector('.stat-card-subtitle');
    
    if (!mySuppliesValue || !mySuppliesSubtitle) {
        console.error('Erreur: Éléments manquants dans la carte des approvisionnements');
        return;
    }
    
    // Initialisation
    loadMySuppliesData();
    
    // Écouteurs d'événements pour les filtres de période
    document.querySelectorAll('.period-filter').forEach(function(filter) {
        filter.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Récupérer la période sélectionnée
            const period = this.getAttribute('data-period');
            
            // Mettre à jour la classe active
            document.querySelectorAll('.period-filter').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Charger les données pour la période sélectionnée
            loadMySuppliesData(period);
        });
    });
    
    /**
     * Fonction pour charger les données des approvisionnements du comptable connecté
     * @param {number} period - Période en jours (7, 30, 90, 365, 0 pour tout)
     */
    function loadMySuppliesData(period = 7) {
        console.log(`Chargement des données pour la période: ${period} jours`);
        
        // Afficher un indicateur de chargement
        mySuppliesCard.classList.add('loading');
        
        // Sauvegarder les valeurs actuelles pour les restaurer en cas d'erreur
        const currentValue = mySuppliesValue.textContent;
        const currentSubtitle = mySuppliesSubtitle.textContent;
        
        // Construire l'URL avec la période
        const url = `/accountant/supplies/my-supplies?period=${period}`;
        
        // Effectuer la requête AJAX
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Erreur réseau: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Afficher les données reçues dans la console pour débogage
                console.log('Données reçues de l\'API:', data);
                
                if (data.success) {
                    // Vérifier si ce sont des données de test
                    if (data.isTestData) {
                        console.warn('Attention: Utilisation de données de test car aucun approvisionnement n\'a été trouvé');
                        // Ajouter un indicateur visuel pour les données de test
                        mySuppliesCard.classList.add('test-data');
                    } else {
                        mySuppliesCard.classList.remove('test-data');
                    }
                    
                    // Mettre à jour les valeurs
                    mySuppliesValue.setAttribute('data-target', data.mySupplies);
                    mySuppliesValue.textContent = data.mySupplies;
                    
                    // Mettre à jour le pourcentage
                    mySuppliesSubtitle.textContent = `${data.percentage}% du total`;
                    
                    // Animer le compteur
                    animateCounter(mySuppliesValue);
                } else {
                    console.error('Erreur lors de la récupération des données:', data.message);
                    mySuppliesValue.textContent = currentValue;
                    mySuppliesSubtitle.textContent = currentSubtitle;
                    
                    // Afficher un message d'erreur
                    showError('Erreur lors de la récupération des données');
                }
            })
            .catch(error => {
                console.error('Erreur lors de la requête AJAX:', error);
                mySuppliesValue.textContent = currentValue;
                mySuppliesSubtitle.textContent = currentSubtitle;
                
                // Afficher un message d'erreur
                showError('Erreur de connexion au serveur');
            })
            .finally(() => {
                // Retirer la classe de chargement
                mySuppliesCard.classList.remove('loading');
            });
    }
    
    /**
     * Fonction pour animer le compteur
     * @param {HTMLElement} element - Élément contenant la valeur à animer
     */
    function animateCounter(element) {
        const target = parseInt(element.getAttribute('data-target'), 10);
        const duration = 1000; // 1 seconde
        const start = parseInt(element.textContent, 10) || 0;
        const increment = (target - start) / (duration / 16);
        let current = start;
        
        const animate = () => {
            current += increment;
            if ((increment > 0 && current >= target) || (increment < 0 && current <= target)) {
                element.textContent = target;
                return;
            }
            
            element.textContent = Math.round(current);
            requestAnimationFrame(animate);
        };
        
        animate();
    }
    
    /**
     * Affiche un message d'erreur temporaire
     * @param {string} message - Message d'erreur à afficher
     */
    function showError(message) {
        // Créer un élément pour afficher l'erreur
        const errorElement = document.createElement('div');
        errorElement.className = 'error-message';
        errorElement.textContent = message;
        errorElement.style.cssText = 'position: fixed; top: 20px; right: 20px; background-color: #f44336; color: white; padding: 15px; border-radius: 5px; z-index: 1000; box-shadow: 0 2px 10px rgba(0,0,0,0.2);';
        
        document.body.appendChild(errorElement);
        
        // Supprimer le message après 5 secondes
        setTimeout(() => {
            errorElement.style.opacity = '0';
            errorElement.style.transition = 'opacity 0.5s';
            
            setTimeout(() => {
                document.body.removeChild(errorElement);
            }, 500);
        }, 5000);
    }
});
